package com.logisteq.common.component.excel.resolver;

import com.logisteq.common.component.excel.exception.PxlCellResolveException;
import com.logisteq.common.component.excel.info.PxlExportColumnInfo;
import com.logisteq.common.component.excel.info.PxlImportColumnInfo;
import com.logisteq.common.component.excel.utils.PxlCellUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;

final class PxlPrimitiveShortResolver {

    private PxlPrimitiveShortResolver() {
        throw new AssertionError("no instances of this class");
    }

    static short parsePrimitiveShortValue(final Cell cell,
                                          final PxlImportColumnInfo columnInfo) throws PxlCellResolveException {

        short shortValue = 0;

        final CellType cellType = cell.getCellType();
        switch (cellType) {
            case NUMERIC:
                shortValue = (short) cell.getNumericCellValue();
                break;

            case STRING:
                final String stringCellValue = cell.getStringCellValue();
                final String cellValue = PxlStringResolver.parseStringValue(stringCellValue, columnInfo);
                shortValue = parsePrimitiveShortValue(cellValue, columnInfo);
                break;

            case BOOLEAN:
                final boolean booleanCellValue = cell.getBooleanCellValue();
                shortValue = (short) BooleanUtils.toInteger(booleanCellValue);
                break;

            case BLANK:
                // empty
                break;

            default:
                throw new PxlCellResolveException(
                        cellType.toString() + "은 지원되지 않는 셀 타입입니다.");
        }

        return shortValue;
    }

    static short parsePrimitiveShortValue(final String s,
                                          final PxlImportColumnInfo columnInfo) throws PxlCellResolveException {

        short shortValue;

        final DecimalFormat importDecimalFormatter = columnInfo.getImportDecimalFormatter();

        if (StringUtils.isBlank(s)) {
            shortValue = 0;
        } else if (Objects.nonNull(importDecimalFormatter)) {
            try {
                shortValue = importDecimalFormatter.parse(s).shortValue();
            } catch (ParseException parseException) {
                throw new PxlCellResolveException(
                        s + "은 올바른 형식의 숫자 값이 아닙니다.", parseException);
            }
        } else {
            try {
                shortValue = Short.parseShort(s);
            } catch (NumberFormatException numberFormatException) {
                throw new PxlCellResolveException(
                        s + "은 올바른 형식의 숫자 값이 아닙니다.", numberFormatException);
            }
        }

        return shortValue;
    }

    static String buildPrimitiveShortCell(final Cell cell,
                                          final Object object,
                                          final PxlExportColumnInfo columnInfo) throws PxlCellResolveException {

        Short shortValue;

        if (object instanceof String) {
            final String stringValue = (String) object;

            try {
                shortValue = Short.parseShort(stringValue);
            } catch (NumberFormatException numberFormatException) {
                throw new PxlCellResolveException(
                        stringValue + "은 올바른 형식의 숫자 값이 아닙니다.", numberFormatException);
            }
        } else if (object instanceof Short) {
            shortValue = (Short) object;
        } else {
            throw new PxlCellResolveException(
                    "'" + object.getClass().getSimpleName() + "' 타입의 값을 숫자로 변환할 수 없습니다.");
        }

        if (columnInfo.isExportedToString()) {
            final String cellString = makePrimitiveShortExportString(shortValue, columnInfo);
            Optional.ofNullable(cell).ifPresent(c -> {
                PxlCellUtils.setQuotePrefixed(c);
                c.setCellValue(cellString);
            });
            return cellString;
        } else {
            Optional.ofNullable(cell).ifPresent(c -> c.setCellValue(shortValue));
            return String.valueOf(shortValue);
        }
    }

    static String makePrimitiveShortExportString(final Short shortValue,
                                                 final PxlExportColumnInfo columnInfo) throws PxlCellResolveException {


        final DecimalFormat exportDecimalFormatter = columnInfo.getExportDecimalFormatter();
        final Pattern exportMaskingPattern = columnInfo.getExportMaskingPattern();

        if (Objects.nonNull(exportDecimalFormatter)) {
            try {
                final String stringValue = exportDecimalFormatter.format(shortValue);
                return PxlStringResolver.makeExportString(stringValue, columnInfo);
            } catch (IllegalArgumentException illegalArgumentException) {
                throw new PxlCellResolveException(
                        shortValue + "에 출력패턴을 적용할 수 없습니다.", illegalArgumentException);
            }
        } else if (Objects.nonNull(exportMaskingPattern)) {
            final String stringValue = String.valueOf(shortValue);
            return PxlStringResolver.makeExportString(stringValue, columnInfo);
        } else {
            return String.valueOf(shortValue);
        }
    }

}
