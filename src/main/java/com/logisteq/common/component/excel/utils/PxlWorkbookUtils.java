package com.logisteq.common.component.excel.utils;

import com.logisteq.common.component.excel.annotation.PxlWorkbookName;
import com.logisteq.common.component.excel.constant.PxlConstant;
import com.logisteq.common.component.excel.constant.PxlFileFormat;
import com.logisteq.common.component.excel.exception.PxlIOException;
import com.logisteq.common.component.excel.info.PxlImportWorkbookInfo;
import com.monitorjbl.xlsx.StreamingReader;
import com.monitorjbl.xlsx.exceptions.OpenException;
import com.monitorjbl.xlsx.exceptions.ReadException;
import com.monitorjbl.xlsx.impl.StreamingWorkbook;
import org.apache.commons.io.input.CloseShieldInputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.UnsupportedFileFormatException;
import org.apache.poi.hssf.record.crypto.Biff8EncryptionKey;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ooxml.POIXMLProperties;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.exceptions.OLE2NotOfficeXmlFileException;
import org.apache.poi.poifs.crypt.EncryptionInfo;
import org.apache.poi.poifs.crypt.EncryptionMode;
import org.apache.poi.poifs.crypt.Encryptor;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.poifs.filesystem.OfficeXmlFileException;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.io.*;
import java.lang.reflect.Field;
import java.nio.file.InvalidPathException;
import java.nio.file.Paths;
import java.security.GeneralSecurityException;
import java.util.Objects;
import java.util.Optional;

/**
 * 워크북 관련 유틸리티
 *
 * <AUTHOR>
 */
public final class PxlWorkbookUtils {

    private PxlWorkbookUtils() {
        throw new AssertionError("no instances of this class");
    }

    /**
     * 워크북 클래스로부터 워크북 이름을 구한다.
     *
     * @param workbookClass
     * @return
     */
    public static Field getWorkbookNameField(final Class<?> workbookClass) {

//        return Arrays.stream(workbookClass.getDeclaredFields())
//                .filter(o -> Objects.nonNull(o.getAnnotation(PxlWorkbookName.class)))
//                .findFirst()
//                .orElse(null);
        return PxlReflectionUtils.getAllFields(workbookClass).stream()
                .filter(o -> Objects.nonNull(o.getAnnotation(PxlWorkbookName.class)))
                .findFirst()
                .orElse(null);
    }

    /**
     * 워크북 객체를 생성한다.
     *
     * @param exportFileFormat
     * @param exportSXSSFRowAccessWindowSize
     * @return
     * @throws InvalidFormatException
     */
    public static Workbook createWorkbook(final PxlFileFormat exportFileFormat,
                                          final int exportSXSSFRowAccessWindowSize)
            throws InvalidFormatException {

        Workbook workbook;

        switch (exportFileFormat) {
            case HSSF: {
                workbook = new HSSFWorkbook();

                ((HSSFWorkbook) workbook).createInformationProperties();
                ((HSSFWorkbook) workbook).getSummaryInformation().setAuthor(PxlConstant.PXL_CREATOR);
                ((HSSFWorkbook) workbook).getSummaryInformation().setApplicationName(PxlConstant.PXL_APPLICATION);

                break;
            }

            case XSSF: {
                workbook = new XSSFWorkbook();

                final POIXMLProperties properties = ((XSSFWorkbook) workbook).getProperties();
                properties.getCoreProperties().setCreator(PxlConstant.PXL_CREATOR);
                properties.getExtendedProperties().getUnderlyingProperties().setApplication(PxlConstant.PXL_APPLICATION);

                break;
            }

            case SXSSF: {
                // turn on auto-flushing, no need to call SXSSFSheet::flushRows()
                workbook = new SXSSFWorkbook(exportSXSSFRowAccessWindowSize);

                ((SXSSFWorkbook) workbook).setCompressTempFiles(false/*compressTmpFiles*/);
                final POIXMLProperties properties = ((SXSSFWorkbook) workbook).getXSSFWorkbook().getProperties();
                properties.getCoreProperties().setCreator(PxlConstant.PXL_CREATOR);
                properties.getExtendedProperties().getUnderlyingProperties().setApplication(PxlConstant.PXL_APPLICATION);

                break;
            }

            default:
                throw new InvalidFormatException("지원하지 않는 파일 형식입니다.");
        }
        //WorkbookFactory.create(xssf);

        return workbook;
    }

    /**
     * 엑셀파일을 연다.
     *
     * @param workbookFile
     * @param password
     * @param readOnly
     * @return
     * @throws InvalidFormatException
     * @throws IOException
     */
    public static Workbook openWorkbook(final File workbookFile,
                                        final String password,
                                        final boolean readOnly)
            throws IOException, InvalidFormatException {

        try {
            return WorkbookFactory.create(workbookFile, password, readOnly);
        } catch (EncryptedDocumentException e) {
            throw new InvalidFormatException("파일에 설정된 암호 해제에 실패했습니다. 암호 확인하십시오.", e);
        } catch (UnsupportedFileFormatException e) {
            // NOTE: DRM 걸린 파일일 수도 있다.
            throw new InvalidFormatException("지원하지 않는 파일 형식입니다.");
        } catch (FileNotFoundException e) {
            throw new FileNotFoundException("파일이 존재하지 않습니다. " + e.getMessage());
        } catch (IOException | OpenException | ReadException e) {
            throw new IOException("파일을 읽을 수 없습니다. " + e.getMessage());
        }
    }

    /**
     * 엑셀파일을 연다.
     *
     * @param inputStream
     * @param password
     * @return
     * @throws InvalidFormatException
     * @throws IOException
     */
    public static Workbook openWorkbook(final InputStream inputStream,
                                        final String password)
            throws IOException, InvalidFormatException {

        try {
            final BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);

            return WorkbookFactory.create(bufferedInputStream, password);
        } catch (EncryptedDocumentException e) {
            throw new InvalidFormatException("파일에 설정된 암호 해제에 실패했습니다. 암호 확인하십시오.", e);
        } catch (UnsupportedFileFormatException e) {
            // NOTE: DRM 걸린 파일일 수도 있다.
            throw new InvalidFormatException("지원하지 않는 파일 형식입니다.");
        } catch (IOException | ReadException e) {
            throw new IOException("파일을 읽을 수 없습니다. " + e.getMessage());
        }
    }

    /**
     * 엑셀파일을 연다.
     *
     * @param workbookFile
     * @param workbookInfo
     * @return
     * @throws InvalidFormatException
     * @throws IOException
     */
    public static Workbook openWorkbook(final File workbookFile,
                                        final PxlImportWorkbookInfo workbookInfo)
            throws IOException, InvalidFormatException {

        try {
            Workbook workbook;

            if (Objects.isNull(workbookInfo)) {
                workbook = WorkbookFactory.create(workbookFile, null, true);
            } else {
                final String importPassword = StringUtils.defaultIfEmpty(workbookInfo.getImportPassword(), null);

                // Excel Stream Reader only supports reading XLSX files.
                final boolean importUsingStreamReader = workbookInfo.isImportUsingStreamReader();
                if (importUsingStreamReader) {
                    try {
                        final int importStreamReaderRowCacheSize = workbookInfo.getImportStreamReaderRowCacheSize();
                        final int importStreamReaderBufferSize = workbookInfo.getImportStreamReaderBufferSize();

                        workbook = StreamingReader.builder()
                                .rowCacheSize(importStreamReaderRowCacheSize)
                                .bufferSize(importStreamReaderBufferSize)
                                .password(importPassword)
                                .open(workbookFile);
                    } catch (OLE2NotOfficeXmlFileException e) {
                        workbook = WorkbookFactory.create(workbookFile, importPassword, true);
                    }
                } else {
                    workbook = WorkbookFactory.create(workbookFile, importPassword, true);
                }

                if (workbook instanceof HSSFWorkbook) {
                    workbookInfo.setImportFileFormat(PxlFileFormat.HSSF);
                } else {
                    workbookInfo.setImportFileFormat(PxlFileFormat.XSSF);
                }
            }

            return workbook;
        } catch (EncryptedDocumentException e) {
            throw new InvalidFormatException("파일에 설정된 암호 해제에 실패했습니다. 암호 확인하십시오.", e);
        } catch (UnsupportedFileFormatException e) {
            // NOTE: DRM 걸린 파일일 수도 있다.
            throw new InvalidFormatException("지원하지 않는 파일 형식입니다.");
        } catch (FileNotFoundException e) {
            throw new FileNotFoundException("파일이 존재하지 않습니다. " + e.getMessage());
        } catch (IOException | OpenException | ReadException e) {
            throw new IOException("파일을 읽을 수 없습니다. " + e.getMessage());
        }
    }

    /**
     * 엑셀파일을 연다.
     *
     * @param inputStream
     * @param workbookInfo
     * @return
     * @throws InvalidFormatException
     * @throws IOException
     */
    public static Workbook openWorkbook(final InputStream inputStream,
                                        final PxlImportWorkbookInfo workbookInfo)
            throws IOException, InvalidFormatException {

        try {
            final BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);

            Workbook workbook;

            if (Objects.isNull(workbookInfo)) {
                workbook = WorkbookFactory.create(bufferedInputStream);
            } else {
                final String importPassword = StringUtils.defaultIfEmpty(workbookInfo.getImportPassword(), null);

                // Excel Stream Reader only supports reading XLSX files.
                final boolean importUsingStreamReader = workbookInfo.isImportUsingStreamReader();
                if (importUsingStreamReader) {
                    try {
                        bufferedInputStream.mark(Integer.MAX_VALUE);
                        final CloseShieldInputStream csis = CloseShieldInputStream.wrap(bufferedInputStream);

                        final int importStreamReaderRowCacheSize = workbookInfo.getImportStreamReaderRowCacheSize();
                        final int importStreamReaderBufferSize = workbookInfo.getImportStreamReaderBufferSize();

                        workbook = StreamingReader.builder()
                                .rowCacheSize(importStreamReaderRowCacheSize)
                                .bufferSize(importStreamReaderBufferSize)
                                .password(importPassword)
                                .open(csis);
                    } catch (OLE2NotOfficeXmlFileException e) {
                        bufferedInputStream.reset();
                        workbook = WorkbookFactory.create(bufferedInputStream, importPassword);
                    }
                } else {
                    workbook = WorkbookFactory.create(bufferedInputStream, importPassword);
                }

                if (workbook instanceof HSSFWorkbook) {
                    workbookInfo.setImportFileFormat(PxlFileFormat.HSSF);
                } else {
                    workbookInfo.setImportFileFormat(PxlFileFormat.XSSF);
                }
            }

            return workbook;
        } catch (EncryptedDocumentException e) {
            throw new InvalidFormatException("파일에 설정된 암호 해제에 실패했습니다. 암호 확인하십시오.", e);
        } catch (UnsupportedFileFormatException e) {
            // NOTE: DRM 걸린 파일일 수도 있다.
            throw new InvalidFormatException("지원하지 않는 파일 형식입니다.");
        } catch (IOException | ReadException e) {
            throw new IOException("파일을 읽을 수 없습니다. " + e.getMessage());
        }
    }

    /**
     * 워크북을 닫는다.
     *
     * @param workbook
     */
    public static void closeWorkbook(final Workbook workbook) {

        if (Objects.isNull(workbook)) {
            return;
        }

        try {
            if (workbook instanceof SXSSFWorkbook) {
                // dispose of temporary files backing this workbook on disk
                ((SXSSFWorkbook) workbook).dispose();
            }
            workbook.close();
        } catch (Exception ignored) {
        }
    }

    /**
     * 워크북을 엑셀 출력 스트림에 쓴다. (export)
     *
     * @param workbook
     * @param outputStream
     * @param password
     * @throws PxlIOException
     */
    public static void writeToStream(@NotNull final Workbook workbook,
                                     @NotNull final OutputStream outputStream,
                                     @Nullable final String password)
            throws PxlIOException {

        try {
            if (StringUtils.isNotEmpty(password)) {
                if (workbook instanceof XSSFWorkbook || workbook instanceof SXSSFWorkbook) {

                    try (final POIFSFileSystem fileSystem = new POIFSFileSystem()) {
                        final EncryptionInfo encryptionInfo = new EncryptionInfo(EncryptionMode.agile);
                        final Encryptor encryptor = encryptionInfo.getEncryptor();
                        encryptor.confirmPassword(password);

                        try (final OutputStream os = encryptor.getDataStream(fileSystem)) {
                            // 1. first way
//                            final ByteArrayOutputStream baos = new ByteArrayOutputStream();
//                            workbook.write(baos);
//                            try (final OPCPackage opc = OPCPackage.open(new ByteArrayInputStream(baos.toByteArray()))) {
//                                opc.save(os);
//                            }

                            // 2. second way
                            workbook.write(os);
                        }

                        fileSystem.writeFilesystem(outputStream);
                    }
                } else if (workbook instanceof HSSFWorkbook) {
                    Biff8EncryptionKey.setCurrentUserPassword(password);
                    workbook.write(outputStream);
                    Biff8EncryptionKey.setCurrentUserPassword(null);
                }
            } else {
                workbook.write(outputStream);
            }

            outputStream.flush();
        } catch (IOException | GeneralSecurityException e) {
            throw new PxlIOException(e);
        }
    }

    public static FormulaEvaluator createFormulaEvaluator(final Workbook workbook) {

        // NOTE: StreamingWorkbook getCreationHelper()이 지원되지 않는다.
        // https://poi.apache.org/components/spreadsheet/eval.html
        final FormulaEvaluator formulaEvaluator = (workbook instanceof StreamingWorkbook)
                ? null
                : Optional.ofNullable(workbook.getCreationHelper())
                .map(CreationHelper::createFormulaEvaluator)
                .orElse(null);

        return formulaEvaluator;
    }

    public static boolean isValidPath(final String path) {

        try {
            Paths.get(path);
        } catch (InvalidPathException | NullPointerException ignored) {
            return false;
        }
        return true;
    }

    @Deprecated
    private static boolean isEncrypted(final File file) throws IOException {

        try (final POIFSFileSystem fileSystem = new POIFSFileSystem(file)) {
            return true;
        } catch (OfficeXmlFileException e) {
            return false;
        }
    }

    @Deprecated
    private static boolean isEncrypted(final InputStream is) throws IOException {

        try (final POIFSFileSystem fileSystem = new POIFSFileSystem(is)) {
            return true;
        } catch (OfficeXmlFileException e) {
            return false;
        }
    }

    @Deprecated
    private static boolean isXLSX(final File file) throws IOException {

        return FileMagic.OOXML.equals(FileMagic.valueOf(file));
    }

    @Deprecated
    private static boolean isXLSX(final InputStream is) throws IOException {

        return FileMagic.OOXML.equals(FileMagic.valueOf(is));
    }

}