package com.logisteq.common.component.excel.resolver;

import com.logisteq.common.component.excel.exception.PxlCellResolveException;
import com.logisteq.common.component.excel.info.PxlExportColumnInfo;
import com.logisteq.common.component.excel.info.PxlImportColumnInfo;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;

import java.time.Duration;
import java.time.format.DateTimeParseException;
import java.util.Optional;

final class PxlDurationResolver {

    private PxlDurationResolver() {
        throw new AssertionError("no instances of this class");
    }

    static Duration parseDurationValue(final Cell cell,
                                       final PxlImportColumnInfo columnInfo) throws PxlCellResolveException {

        Duration durationValue = null;

        final CellType cellType = cell.getCellType();
        switch (cellType) {
            case NUMERIC:
                try {
                    // 임의로 (분)이 주어졌다고 간주하자.
                    durationValue = Duration.ofMinutes((long) cell.getNumericCellValue());
                } catch (NumberFormatException numberFormatException) {
                    throw new PxlCellResolveException(
                            "'" + cell + "'은 올바른 형식의 시간간격 값이 아닙니다.", numberFormatException);
                } catch (ArithmeticException arithmeticException) {
                    throw new PxlCellResolveException(
                            "'" + cell + "'은 너무 큰 시간간격 값입니다.", arithmeticException);
                }
                break;

            case STRING:
                final String stringCellValue = cell.getStringCellValue();
                final String cellValue = PxlStringResolver.parseStringValue(stringCellValue, columnInfo);
                durationValue = parseDurationValue(cellValue);
                break;

            case BOOLEAN:
                final boolean booleanCellValue = cell.getBooleanCellValue();
                durationValue = Duration.ofMinutes(BooleanUtils.toInteger(booleanCellValue));
                break;

            case BLANK:
                // empty
                break;

            default:
                throw new PxlCellResolveException(
                        cellType.toString() + "은 지원되지 않는 셀 타입입니다.");
        }

        return durationValue;
    }

    static Duration parseDurationValue(final String s) throws PxlCellResolveException {

        Duration durationValue;

        if (StringUtils.isBlank(s)) {
            durationValue = null;
        } else {
            try {
                durationValue = Duration.parse(s);
            } catch (DateTimeParseException dateTimeParseException) {
                throw new PxlCellResolveException(
                        "'" + s + "'은 올바른 형식의 시간간격 값이 아닙니다.", dateTimeParseException);
            }
        }

        return durationValue;
    }

    static String buildDurationCell(final Cell cell,
                                    final Object object,
                                    final PxlExportColumnInfo columnInfo) throws PxlCellResolveException {

        Duration durationValue;

        if (object instanceof String) {
            final String stringValue = (String) object;
            try {
                durationValue = Duration.parse(stringValue);
            } catch (DateTimeParseException dateTimeParseException) {
                throw new PxlCellResolveException(
                        stringValue + "은 올바른 형식의 시간간격 값이 아닙니다.", dateTimeParseException);
            }
        } else if (object instanceof Duration) {
            durationValue = (Duration) object;
        } else {
            throw new PxlCellResolveException(
                    "'" + object.getClass().getSimpleName() + "' 타입의 값을 시간간격으로 변환할 수 없습니다.");
        }

        final String cellString = makeDurationExportString(durationValue, columnInfo);
        Optional.ofNullable(cell).ifPresent(c -> c.setCellValue(cellString));
        return cellString;
    }

    static String makeDurationExportString(final Duration durationValue,
                                           final PxlExportColumnInfo columnInfo) throws PxlCellResolveException {

        final String exportPattern = columnInfo.getExportPattern();

        try {
            final long milliSeconds = durationValue.toMillis();
            String stringValue;

            if (StringUtils.isNotBlank(exportPattern)) {
                stringValue = DurationFormatUtils.formatDuration(milliSeconds, exportPattern);
            } else {
                // stringValue = durationValue.toString();
                stringValue = DurationFormatUtils.formatDurationISO(milliSeconds);
            }

            return PxlStringResolver.makeExportString(stringValue, columnInfo);
        } catch (ArithmeticException arithmeticException) {
            throw new PxlCellResolveException(
                    "'" + durationValue + "'은 너무 큰 시간간격 값입니다.", arithmeticException);
        } catch (IllegalArgumentException illegalArgumentException) {
            throw new PxlCellResolveException(
                    "'" + durationValue + "'은 올바른 형식의 시간간격 값이 아닙니다.", illegalArgumentException);
        }
    }

}
