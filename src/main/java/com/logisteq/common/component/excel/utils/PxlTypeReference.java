package com.logisteq.common.component.excel.utils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * Super type token
 *
 * <AUTHOR>
 */
@Deprecated
public abstract class PxlTypeReference<T> {

    private final Type type;

    protected PxlTypeReference() {

        final Type superClass = getClass().getGenericSuperclass();
        if (superClass instanceof Class<?>) {
            throw new IllegalArgumentException("PxlTypeReference는 항상 실제 타입 파라미터 정보가 있어야 합니다.");
        }

        type = ((ParameterizedType) superClass).getActualTypeArguments()[0];
    }

    private PxlTypeReference(Type type) {
        this.type = type;
    }

    public Type getType() {
        return type;
    }

}
