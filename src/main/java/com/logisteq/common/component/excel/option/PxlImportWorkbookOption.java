package com.logisteq.common.component.excel.option;

import com.logisteq.common.component.excel.utils.PxlCollectionUtils;
import lombok.*;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 엑셀 워크북 Import 옵션
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public final class PxlImportWorkbookOption {

    /**
     * Import시에 문서보호 해제할 Password를 지정한다.
     */
    @Builder.Default
    private final String importPassword = null;

    /**
     * Import될 데이터에 대해 Validation할지 지정한다.
     */
    @Builder.Default
    private final Boolean importDataValidation = null;

    /**
     * Import시에 Stream Reader를 사용할지 지정한다. (XSSF 형식의 엑셀파일에서만 동작한다.)
     * https://github.com/monitorjbl/excel-streaming-reader
     */
    @Builder.Default
    private final Boolean importUsingStreamReader = null;

    /**
     * Stream Reader로 Import할 때 RowCacheSize의 값을 지정한다.
     */
    @Builder.Default
    private final Integer importStreamReaderRowCacheSize = null;

    /**
     * Stream Reader로 Import할 때 BufferSize의 값을 지정한다.
     */
    @Builder.Default
    private final Integer importStreamReaderBufferSize = null;

    /**
     * Import할 CSV의 Character Encoding Set를 지정한다.
     * https://docs.oracle.com/javase/8/docs/technotes/guides/intl/encoding.doc.html
     */
    @Builder.Default
    private final String importCsvCharset = null;

    /**
     * Import할 CSV의 Delimiter를 지정한다.
     */
    @Builder.Default
    private final Character importCsvDelimiter = null;

    @Builder.Default
    private final List<PxlImportSheetOption> importSheetOptions = new ArrayList<>();

    public PxlImportSheetOption getImportSheetOption(final int index) {

        return PxlCollectionUtils.get(this.importSheetOptions, index);
    }

    public static PxlImportSheetOption getImportSheetOption(@Nullable final PxlImportWorkbookOption importWorkbookOption,
                                                            final int index) {

        return Optional.ofNullable(importWorkbookOption)
                .map(workbookOption -> workbookOption.getImportSheetOption(index))
                .orElse(null);
    }

    public static List<PxlImportSheetOption> getImportSheetOptions(@Nullable final PxlImportWorkbookOption importWorkbookOption) {

        return Optional.ofNullable(importWorkbookOption)
                .map(workbookOption -> workbookOption.getImportSheetOptions())
                .orElseGet(ArrayList::new);
    }

    public boolean addImportSheetOption(@NotNull final PxlImportSheetOption importSheetOption) {

        return this.importSheetOptions.add(importSheetOption);
    }

}
