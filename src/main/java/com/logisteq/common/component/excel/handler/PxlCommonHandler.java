package com.logisteq.common.component.excel.handler;

import com.logisteq.common.component.excel.exception.PxlValidationException;
import com.logisteq.common.component.excel.info.PxlImportColumnInfo;
import com.logisteq.common.component.excel.utils.PxlCollectionUtils;
import com.logisteq.common.component.excel.utils.PxlReflectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 2021-11-02
 * @project tms-service
 */
abstract class PxlCommonHandler {

    /**
     * 데이터 유효성을 점검한다.
     *
     * @param validator
     * @param object
     * @param sheetName
     * @param rowIndex
     * @throws PxlValidationException
     */
    protected static void validateDataConstraint(final Validator validator,
                                                 final Object object,
                                                 final String sheetName,
                                                 final Integer rowIndex) throws PxlValidationException {

        if (Objects.isNull(validator) || Objects.isNull(object)) {
            return;
        }

        final Set<ConstraintViolation<Object>> violations = validator.validate(object);
        if (PxlCollectionUtils.isNotEmpty(violations)) {
            final String errMsg = violations.stream()
                    .filter(Objects::nonNull)
                    .map(ConstraintViolation::getMessage)
                    .distinct()
                    .collect(Collectors.joining(", "));

            throw new PxlValidationException(sheetName, rowIndex, null, null, errMsg);
        }
    }

    /**
     * 데이터 유효성을 점검한다.
     *
     * @param validator
     * @param objects
     * @param sheetName
     * @param rowIndex
     * @throws PxlValidationException
     */
    protected static void validateDataConstraint(final Validator validator,
                                                 final Collection<?> objects,
                                                 final String sheetName,
                                                 final Integer rowIndex) throws PxlValidationException {

        if (Objects.isNull(validator) || PxlCollectionUtils.isEmpty(objects)) {
            return;
        }

        // TODO: rowObjects를 한번에 validate하는 방법을 찾아보자.
        // final Set<ConstraintViolation<Collection<Object>>> violations = validator.validate(objects);

        for (final Object object : objects) {
            validateDataConstraint(validator, object, sheetName, rowIndex);
        }
    }

    /**
     * 데이터 유일성을 점검한다.
     *
     * @param objects
     * @param columnInfos
     * @param sheetName
     * @throws PxlValidationException
     */
    protected static void validateDataUniqueness(final Collection<?> objects,
                                                 final List<PxlImportColumnInfo> columnInfos,
                                                 final String sheetName) throws PxlValidationException {

        if (PxlCollectionUtils.isEmpty(objects)) {
            return;
        }

        for (final PxlImportColumnInfo columnInfo : columnInfos) {
            if (!columnInfo.isImportEnabled()) {
                continue;
            }

            final int importColumnIndex = columnInfo.getActualImportColumnIndex();
            if (importColumnIndex < 0) {
                continue;
            }

            if (!columnInfo.isImportUnique()) {
                continue;
            }

            final Function<Object, Object> mapper = rowObject -> {
                try {
                    return PxlReflectionUtils.getFieldValue(columnInfo.getColumnField(), rowObject);
                } catch (Exception ignored) {
                    return null;
                }
            };
            final Set<Object> duplicates = PxlCollectionUtils.findDuplicates(objects, mapper);

            if (PxlCollectionUtils.isNotEmpty(duplicates)) {
                final String errMsg = duplicates.stream()
                        .map(Object::toString)
                        .collect(Collectors.joining(", "))
                        + "가 중복 존재합니다.";

                throw new PxlValidationException(sheetName, null, columnInfo.getActualImportColumnName(), null, errMsg);
            }
        }
    }

}
