package com.logisteq.common.component.excel.validator;

import com.logisteq.common.component.excel.validator.ByteSize.List;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * The annotated element byte length must be between the specified boundaries (included).
 * <p>
 * Supported types are:
 * <ul>
 *     <li>{@code CharSequence} (byte length of character sequence is evaluated)</li>
 * </ul>
 * <p>
 * {@code null} elements are considered valid. Determine the byte length by encoding the string in the specified
 * {@link ByteSize#charset()}. If not specify, encode with charset {@code "UTF-8"}.
 */
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Repeatable(List.class)
@Documented
@Constraint(validatedBy = {ByteSizeValidator.class})
public @interface ByteSize {

    String message() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * @return byte length the element must be higher or equal to
     */
    int min() default 0;

    /**
     * @return byte length the element must be lower or equal to
     */
    int max() default Integer.MAX_VALUE;

    /**
     * @return the charset name used in parse to a string
     */
    String charset() default "UTF-8";

    /**
     * Defines several {@link ByteSize} annotations on the same element.
     *
     * @see ByteSize
     */
    @Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
    @Retention(RUNTIME)
    @Documented
    @interface List {

        ByteSize[] value();
    }

}
