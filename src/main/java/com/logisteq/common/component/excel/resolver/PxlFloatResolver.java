package com.logisteq.common.component.excel.resolver;

import com.logisteq.common.component.excel.exception.PxlCellResolveException;
import com.logisteq.common.component.excel.info.PxlExportColumnInfo;
import com.logisteq.common.component.excel.info.PxlImportColumnInfo;
import com.logisteq.common.component.excel.utils.PxlCellUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.util.NumberToTextConverter;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;

final class PxlFloatResolver {

    private PxlFloatResolver() {
        throw new AssertionError("no instances of this class");
    }

    static Float parseFloatValue(final Cell cell,
                                 final PxlImportColumnInfo columnInfo) throws PxlCellResolveException {

        Float floatValue = null;

        final CellType cellType = cell.getCellType();
        switch (cellType) {
            case NUMERIC:
                floatValue = (float) cell.getNumericCellValue();
                break;

            case STRING:
                final String stringCellValue = cell.getStringCellValue();
                final String cellValue = PxlStringResolver.parseStringValue(stringCellValue, columnInfo);
                floatValue = parseFloatValue(cellValue, columnInfo);
                break;

            case BOOLEAN:
                final boolean booleanCellValue = cell.getBooleanCellValue();
                floatValue = (float) BooleanUtils.toInteger(booleanCellValue);
                break;

            case BLANK:
                // empty
                break;

            default:
                throw new PxlCellResolveException(
                        cellType.toString() + "은 지원되지 않는 셀 타입입니다.");
        }

        return floatValue;
    }

    static Float parseFloatValue(final String s,
                                 final PxlImportColumnInfo columnInfo) throws PxlCellResolveException {

        Float floatValue;

        final DecimalFormat importDecimalFormatter = columnInfo.getImportDecimalFormatter();

        if (StringUtils.isBlank(s)) {
            floatValue = null;
        } else if (Objects.nonNull(importDecimalFormatter)) {
            try {
                floatValue = importDecimalFormatter.parse(s).floatValue();
            } catch (ParseException parseException) {
                throw new PxlCellResolveException(
                        s + "은 올바른 형식의 숫자 값이 아닙니다.", parseException);
            }
        } else {
            try {
                floatValue = Float.parseFloat(s);
            } catch (NumberFormatException numberFormatException) {
                throw new PxlCellResolveException(
                        s + "은 올바른 형식의 숫자 값이 아닙니다.", numberFormatException);
            }
        }

        return floatValue;
    }

    static String buildFloatCell(final Cell cell,
                                 final Object object,
                                 final PxlExportColumnInfo columnInfo) throws PxlCellResolveException {

        Float floatValue;

        if (object instanceof String) {
            final String stringValue = (String) object;

            try {
                floatValue = Float.parseFloat(stringValue);
            } catch (NumberFormatException numberFormatException) {
                throw new PxlCellResolveException(
                        stringValue + "은 올바른 형식의 숫자 값이 아닙니다.", numberFormatException);
            }
        } else if (object instanceof Float) {
            floatValue = (Float) object;
        } else {
            throw new PxlCellResolveException(
                    "'" + object.getClass().getSimpleName() + "' 타입의 값을 숫자로 변환할 수 없습니다.");
        }

        if (columnInfo.isExportedToString()) {
            final String cellString = makeFloatExportString(floatValue, columnInfo);
            Optional.ofNullable(cell).ifPresent(c -> {
                PxlCellUtils.setQuotePrefixed(c);
                c.setCellValue(cellString);
            });
            return cellString;
        } else {
            Optional.ofNullable(cell).ifPresent(c -> c.setCellValue(floatValue));
            return NumberToTextConverter.toText(floatValue);
        }
    }

    static String makeFloatExportString(final Float floatValue,
                                        final PxlExportColumnInfo columnInfo) throws PxlCellResolveException {


        final DecimalFormat exportDecimalFormatter = columnInfo.getExportDecimalFormatter();
        final Pattern exportMaskingPattern = columnInfo.getExportMaskingPattern();

        if (Objects.nonNull(exportDecimalFormatter)) {
            try {
                final String stringValue = exportDecimalFormatter.format(floatValue);
                return PxlStringResolver.makeExportString(stringValue, columnInfo);
            } catch (IllegalArgumentException illegalArgumentException) {
                throw new PxlCellResolveException(
                        floatValue + "에 출력패턴을 적용할 수 없습니다.", illegalArgumentException);
            }
        } else if (Objects.nonNull(exportMaskingPattern)) {
            final String stringValue = NumberToTextConverter.toText(floatValue);
            return PxlStringResolver.makeExportString(stringValue, columnInfo);
        } else {
            return NumberToTextConverter.toText(floatValue);
        }
    }

}
