package com.logisteq.common.component.excel.exception;

/**
 * 유효성 익셉션
 *
 * <AUTHOR>
 */
public final class PxlValidationException extends PxlException {

    public PxlValidationException() {
        super();
    }

    public PxlValidationException(final String message) {
        super(message);
    }

    public PxlValidationException(final String message, final Throwable cause) {
        super(message, cause);
    }

    public PxlValidationException(final Throwable cause) {
        super(cause);
    }

    public PxlValidationException(final String sheetName,
                                  final Integer rowIndex,
                                  final String columnName,
                                  final Integer columnIndex,
                                  final String message) {
        super(sheetName, rowIndex, columnName, columnIndex, message);
    }

    public PxlValidationException(final String sheetName,
                                  final Integer rowIndex,
                                  final String columnName,
                                  final Integer columnIndex,
                                  final String message,
                                  final Throwable cause) {
        super(sheetName, rowIndex, columnName, columnIndex, message, cause);
    }

    public PxlValidationException(final String sheetName,
                                  final Integer rowIndex,
                                  final String columnName,
                                  final Integer columnIndex,
                                  final Throwable cause) {
        super(sheetName, rowIndex, columnName, columnIndex, cause);
    }

}
