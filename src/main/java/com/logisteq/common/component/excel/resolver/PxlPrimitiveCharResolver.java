package com.logisteq.common.component.excel.resolver;

import com.logisteq.common.component.excel.exception.PxlCellResolveException;
import com.logisteq.common.component.excel.info.PxlImportColumnInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;

import java.util.Optional;

final class PxlPrimitiveCharResolver {

    private PxlPrimitiveCharResolver() {
        throw new AssertionError("no instances of this class");
    }

    static char parsePrimitiveCharValue(final Cell cell,
                                        final PxlImportColumnInfo columnInfo) throws PxlCellResolveException {

        char charValue = '\0';

        final CellType cellType = cell.getCellType();
        switch (cellType) {
            case NUMERIC:
                charValue = (char) cell.getNumericCellValue();
                break;

            case STRING:
                final String stringCellValue = cell.getStringCellValue();
                final String cellValue = PxlStringResolver.parseStringValue(stringCellValue, columnInfo);
                charValue = parsePrimitiveCharValue(cellValue);
                break;

            case BOOLEAN:
                final boolean booleanCellValue = cell.getBooleanCellValue();
                charValue = booleanCellValue ? '1' : '0';
                break;

            case BLANK:
                // empty
                break;

            default:
                throw new PxlCellResolveException(
                        cellType.toString() + "은 지원되지 않는 셀 타입입니다.");
        }

        return charValue;
    }

    static char parsePrimitiveCharValue(final String s) throws PxlCellResolveException {

        char charValue;

        if (StringUtils.isBlank(s)) {
            charValue = '\0';
        } else {
            try {
                charValue = s.charAt(0);
            } catch (IndexOutOfBoundsException indexOutOfBoundsException) {
                throw new PxlCellResolveException(
                        s + "은 올바른 형식의 문자 값이 아닙니다.", indexOutOfBoundsException);
            }
        }

        return charValue;
    }

    static String buildPrimitiveCharCell(final Cell cell,
                                         final Object object) throws PxlCellResolveException {

        Character charValue;

        if (object instanceof String) {
            final String stringValue = (String) object;

            try {
                charValue = stringValue.charAt(0);
            } catch (IndexOutOfBoundsException indexOutOfBoundsException) {
                throw new PxlCellResolveException(
                        stringValue + "은 올바른 형식의 문자 값이 아닙니다.", indexOutOfBoundsException);
            }
        } else if (object instanceof Character) {
            charValue = (Character) object;
        } else {
            throw new PxlCellResolveException(
                    "'" + object.getClass().getSimpleName() + "' 타입의 값을 문자로 변환할 수 없습니다.");
        }

        final String cellString = makePrimitiveCharExportString(charValue);
        Optional.ofNullable(cell).ifPresent(c -> c.setCellValue(cellString));
        return cellString;
    }

    static String makePrimitiveCharExportString(final Character charValue) {

        return charValue.toString();
    }

}
