package com.logisteq.common.component.excel.annotation;

import com.logisteq.common.component.excel.constant.PxlConstant;
import com.logisteq.common.component.excel.styler.PxlStyler;

import java.lang.annotation.*;

/**
 * 엑셀 열에 속성을 지정하기 위한 어노테이션
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Documented
public @interface PxlColumn {

    /**
     * 엑셀 열 이름을 지정한다.
     *
     * @return
     */
    String[] name() default {};

    /**
     * 엑셀 열에 대한 샘플 값을 지정한다.
     *
     * @return
     */
    @Deprecated
    String sample() default "";

    /**
     * 엑셀 열 Import/Export할 때 셀 포맷팅 문자열을 지정한다.
     * importPattern이 비어있으면 이 값이 사용되고, exportPattern이 비어있으면 이 값이 사용된다.
     *
     * @return
     */

    String pattern() default "";

    /**
     * Collection을 Import/Export시에 각 Element의 분리자를 지정한다.
     * importCollectionSeparator이 비어있으면 이 값이 사용되고, exportCollectionSeparator이 비어있으면 이 값이 사용된다.
     *
     * @return
     */
    String collectionSeparator() default PxlConstant.DEFAULT_COLLECTION_SEPARATOR;

    /**
     * Import 여부를 지정한다.
     *
     * @return
     */
    boolean importEnabled() default true;

    /**
     * Import시에 문자열 trim을 수행할지 지정한다.
     *
     * @return
     */
    boolean importTrim() default PxlConstant.DEFAULT_IMPORT_TRIM;

    /**
     * Import시에 열의 값들의 유일성 점검 여부를 지정한다.
     *
     * @return
     */
    boolean importUnique() default PxlConstant.DEFAULT_IMPORT_UNIQUE;

    /**
     * Import시에 셀 포맷팅 문자열을 지정한다.
     * Numeric, Date, LocalTime, LocalDate, LocalDateTime, ZonedDateTime, OffsetTime, OffsetDateTime 타입의 필드에 대해서만 유효하다.
     *
     * @return
     */
    String importPattern() default "";

    /**
     * Import시에 BOOLEAN 셀형식의 참의 논리값을 표현할 문자열을 지정한다.
     *
     * @return
     */
    String importTrueString() default PxlConstant.DEFAULT_IMPORT_TRUE_STRING;

    /**
     * Import시에 BOOLEAN 셀형식의 거짓의 논리값을 표현할 문자열을 지정한다.
     *
     * @return
     */
    String importFalseString() default PxlConstant.DEFAULT_IMPORT_FALSE_STRING;

    /**
     * Collection으로 Import시에 각 Element의 분리자를 지정한다.
     *
     * @return
     */
    String importCollectionSeparator() default PxlConstant.DEFAULT_IMPORT_COLLECTION_SEPARATOR;

    /**
     * Import시에 슈퍼클래스의 필드 중 동일 시트명을 사용하는 필드가 있다면 이를 Override할지 여부를 지정한다.
     *
     * @return
     */
    boolean importOverrideSuperClassColumn() default PxlConstant.DEFAULT_IMPORT_OVERRIDE_SUPER_CLASS_COLUMN;

    /**
     * Export 여부를 지정한다.
     *
     * @return
     */
    boolean exportEnabled() default true;

    /**
     * Sample Export 여부를 지정한다.
     *
     * @return
     */
    boolean exportSampleEnabled() default true;

    /**
     * Export시에 샘플 값을 지정한다.
     *
     * @return
     */
    String exportSample() default "";

    /**
     * Export시에 문자열 trim을 수행할지 지정한다.
     *
     * @return
     */
    boolean exportTrim() default PxlConstant.DEFAULT_EXPORT_TRIM;

    /**
     * Export시에 셀 포맷팅 문자열을 지정한다.
     * Numeric, Date, LocalTime, LocalDate, LocalDateTime, ZonedDateTime, OffsetTime, OffsetDateTime, Duration 타입의 필드에 대해서만 유효하다.
     *
     * @return
     */

    String exportPattern() default "";

    /**
     * Export시에 열의 너비를 지정한다.
     * in units of 1/256th of a character width (maximum: 255 * 256)
     *
     * @return
     */
    int exportColumnWidth() default PxlConstant.DEFAULT_EXPORT_COLUMN_WIDTH;

    /**
     * Collection을 Export시에 각 Element의 분리자를 지정한다.
     *
     * @return
     */
    String exportCollectionSeparator() default PxlConstant.DEFAULT_EXPORT_COLLECTION_SEPARATOR;

    /**
     * Export시에 슈퍼클래스의 필드 중 동일 시트명을 사용하는 필드가 있다면 이를 Override할지 여부를 지정한다.
     *
     * @return
     */
    boolean exportOverrideSuperClassColumn() default PxlConstant.DEFAULT_EXPORT_OVERRIDE_SUPER_CLASS_COLUMN;

    /**
     * Export시에 열 간의 순서를 지정한다. (알파벳 순으로)
     *
     * @return
     */
    String exportOrder() default "";

    /**
     * Export시에 마스킹 규칙을 Regular Expression으로 지정한다.
     *
     * @return
     */
    String exportMasking() default "";

    /**
     * Export시에 Enum 필드를 DropDownList로 설정한다.
     *
     * @return
     */
    boolean exportEnumDropDownList() default PxlConstant.DEFAULT_EXPORT_ENUM_DROP_DOWN_LIST;

    /**
     * Export시에 null을 표현할 문자열을 지정한다.
     *
     * @return
     */
    String exportNullString() default PxlConstant.DEFAULT_EXPORT_NULL_STRING;

    /**
     * Export시에 참의 논리값을 표현할 문자열을 지정한다.
     *
     * @return
     */
    String exportTrueString() default PxlConstant.DEFAULT_EXPORT_TRUE_STRING;

    /**
     * Export시에 거짓의 논리값을 표현할 문자열을 지정한다.
     *
     * @return
     */
    String exportFalseString() default PxlConstant.DEFAULT_EXPORT_FALSE_STRING;

    /**
     * Export시에 문자열을 이미지에 대한 경로로 해석하고 그 이미지 자체를 셀에 적용한다.
     *
     * @return
     */
    boolean exportStringAsPicture() default PxlConstant.DEFAULT_EXPORT_STRING_AS_PICTURE;

    /**
     * Export시에 문자열을 수식으로 해석하고 그 계산 결과 자체를 셀에 적용한다.
     *
     * @return
     */
    boolean exportStringAsFormula() default PxlConstant.DEFAULT_EXPORT_STRING_AS_FORMULA;

    /**
     * Export시에 필수적인 Title Cell에 적용할 스타일을 지정한다.
     *
     * @return
     */
    Class<? extends PxlStyler> exportColumnMandatoryTitleCellStyler() default PxlStyler.class;

    /**
     * Export시에 선택적인 Title Cell에 적용할 스타일을 지정한다.
     *
     * @return
     */
    Class<? extends PxlStyler> exportColumnOptionalTitleCellStyler() default PxlStyler.class;

    /**
     * Export시에 Data Cell에 적용할 스타일을 지정한다.
     *
     * @return
     */
    Class<? extends PxlStyler> exportColumnDataCellStyler() default PxlStyler.class;

}
