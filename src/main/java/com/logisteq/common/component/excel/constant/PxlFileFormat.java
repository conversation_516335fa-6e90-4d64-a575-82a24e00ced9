package com.logisteq.common.component.excel.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.poi.ss.SpreadsheetVersion;

/**
 * 엑셀/CSV 파일 형식
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PxlFileFormat {

    // Horrible SpreadSheet Format (Excel '97)
    HSSF(
            PxlConstant.FILENAME_EXTENSION_XLS,
            PxlConstant.CONTENT_TYPE_MICROSOFT_XLS,

            PxlConstant.IMPORT_MAX_NUMBER_OF_EXCEL_SHEETS,
            SpreadsheetVersion.EXCEL97.getMaxRows(),
            SpreadsheetVersion.EXCEL97.getMaxColumns(),

            PxlConstant.EXPORT_MAX_NUMBER_OF_EXCEL_SHEETS,
            SpreadsheetVersion.EXCEL97.getMaxRows(),
            SpreadsheetVersion.EXCEL97.getMaxColumns()
    ),

    // XML SpreadSheet Format (Excel 2007)
    XSSF(
            PxlConstant.FILENAME_EXTENSION_XLSX,
            PxlConstant.CONTENT_TYPE_MICROSOFT_XLSX,

            PxlConstant.IMPORT_MAX_NUMBER_OF_EXCEL_SHEETS,
            SpreadsheetVersion.EXCEL2007.getMaxRows(),
            SpreadsheetVersion.EXCEL2007.getMaxColumns(),

            PxlConstant.EXPORT_MAX_NUMBER_OF_EXCEL_SHEETS,
            SpreadsheetVersion.EXCEL2007.getMaxRows(),
            SpreadsheetVersion.EXCEL2007.getMaxColumns()
    ),

    // Streaming XML SpreadSheet Format
    SXSSF(
            PxlConstant.FILENAME_EXTENSION_XLSX,
            PxlConstant.CONTENT_TYPE_MICROSOFT_XLSX,

            PxlConstant.IMPORT_MAX_NUMBER_OF_EXCEL_SHEETS,
            SpreadsheetVersion.EXCEL2007.getMaxRows(),
            SpreadsheetVersion.EXCEL2007.getMaxColumns(),

            PxlConstant.EXPORT_MAX_NUMBER_OF_EXCEL_SHEETS,
            SpreadsheetVersion.EXCEL2007.getMaxRows(),
            SpreadsheetVersion.EXCEL2007.getMaxColumns()
    ),

    // not supported yet
    // Comma Separated Values
    CSV(
            PxlConstant.FILENAME_EXTENSION_CSV,
            PxlConstant.CONTENT_TYPE_CSV,

            PxlConstant.IMPORT_MAX_NUMBER_OF_CSV_SHEETS,
            PxlConstant.IMPORT_MAX_NUMBER_OF_CSV_ROWS,
            PxlConstant.IMPORT_MAX_NUMBER_OF_CSV_COLUMNS,

            PxlConstant.EXPORT_MAX_NUMBER_OF_CSV_SHEETS,
            PxlConstant.EXPORT_MAX_NUMBER_OF_CSV_ROWS,
            PxlConstant.EXPORT_MAX_NUMBER_OF_CSV_COLUMNS
    ),
    ;

    private final String filenameExtension;
    private final String contentType;

    private final int maxImportSheets;
    private final int maxImportRows;
    private final int maxImportColumns;

    private final int maxExportSheets;
    private final int maxExportRows;
    private final int maxExportColumns;

}
