package com.logisteq.common.component.excel.constant;

import com.logisteq.common.component.excel.utils.PxlDateTimeUtils;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public final class PxlResolverConstant {

    public static final String javaDateWritePattern = "yyyy/MM/dd HH:mm";
    public static final String[] javaDateReadPatterns = {"y/M/d H:m", "y/M/d H:m:s"};

    public static final String localTimeWritePattern = "HH:mm";
    public static final String[] localTimeReadPatterns = {"H:m", "H:m:s"};

    public static final String localDateWritePattern = "yyyy/MM/dd";
    public static final String[] localDateReadPatterns = {"y/M/d"};

    public static final String localDateTimeWritePattern = "yyyy/MM/dd HH:mm";
    public static final String[] localDateTimeReadPatterns = {"y/M/d H:m", "y/M/d H:m:s"};

    public static final SimpleDateFormat javaDateWriteFormatter;
    public static final List<SimpleDateFormat> javaDateReadFormatters;

    static {
        javaDateWriteFormatter = PxlDateTimeUtils.getCellSimpleDateFormatter(javaDateWritePattern);

        javaDateReadFormatters = Arrays.stream(javaDateReadPatterns)
                .map(PxlDateTimeUtils::getCellSimpleDateFormatter)
                .collect(Collectors.toList());
    }

    public static final DateTimeFormatter localTimeWriteFormatter;
    public static final List<DateTimeFormatter> localTimeReadFormatters;

    static {
        localTimeWriteFormatter = PxlDateTimeUtils.getCellDateTimeFormatter(localTimeWritePattern);

        localTimeReadFormatters = Arrays.stream(localTimeReadPatterns)
                .map(PxlDateTimeUtils::getCellDateTimeFormatter)
                .collect(Collectors.toList());
    }

    public static final DateTimeFormatter localDateWriteFormatter;
    public static final List<DateTimeFormatter> localDateReadFormatters;

    static {
        localDateWriteFormatter = PxlDateTimeUtils.getCellDateTimeFormatter(localDateWritePattern);

        localDateReadFormatters = Arrays.stream(localDateReadPatterns)
                .map(PxlDateTimeUtils::getCellDateTimeFormatter)
                .collect(Collectors.toList());
    }

    public static final DateTimeFormatter localDateTimeWriteFormatter;
    public static final List<DateTimeFormatter> localDateTimeReadFormatters;

    static {
        localDateTimeWriteFormatter = PxlDateTimeUtils.getCellDateTimeFormatter(localDateTimeWritePattern);

        localDateTimeReadFormatters = Arrays.stream(localDateTimeReadPatterns)
                .map(PxlDateTimeUtils::getCellDateTimeFormatter)
                .collect(Collectors.toList());
    }

}
