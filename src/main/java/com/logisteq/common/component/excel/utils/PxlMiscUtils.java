package com.logisteq.common.component.excel.utils;

import com.logisteq.common.component.excel.constant.PxlConstant;
import com.logisteq.common.component.excel.styler.PxlStyler;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;

import java.util.Objects;

/**
 * <AUTHOR>
 * @created 2024-07-22
 * @project tms-service
 */
public class PxlMiscUtils {

    private PxlMiscUtils() {
        throw new AssertionError("no instances of this class");
    }

    public static String convertColumnIndexToColumnString(final int columnIndex) {

        return CellReference.convertNumToColString(columnIndex);
    }

    public static int convertColumnStringToColumnIndex(final String columnString) {

        return CellReference.convertColStringToIndex(columnString);
    }

    public static String convertIndexesToCellReferenceString(final int rowIndex,
                                                             final int columnIndex) {

        final CellReference cellRef = new CellReference(rowIndex, columnIndex);

        return cellRef.formatAsString(false);
    }

    public static String convertIndexesToCellRangeAddressString(final int startRowIndex,
                                                                int startColumnIndex,
                                                                int endRowIndex,
                                                                int endColumnIndex) {

        final CellRangeAddress cellRangeAddress = new CellRangeAddress(startRowIndex, endRowIndex, startColumnIndex, endColumnIndex);

        return cellRangeAddress.formatAsString();
    }

    public static Pair<Integer, Integer> convertCellReferenceStringToIndexes(final String cellRefStr) {

        final CellReference cellRef = new CellReference(cellRefStr);

        return Pair.of(cellRef.getRow(), (int) cellRef.getCol());
    }

    public static boolean isEffectiveCellStylerClass(final Class<? extends PxlStyler> cellStylerClass) {

        if (Objects.isNull(cellStylerClass)) {
            return false;
        }

        if (cellStylerClass.isInterface()) {
            return false;
        }

        if (PxlConstant.VOID_CELL_STYLER.equals(cellStylerClass)) {
            return false;
        }

        return true;
    }

}
