package com.logisteq.common.component.excel.info;

import com.logisteq.common.component.excel.annotation.PxlWorkbook;
import com.logisteq.common.component.excel.constant.PxlConstant;
import com.logisteq.common.component.excel.constant.PxlFileFormat;
import com.logisteq.common.component.excel.option.PxlImportSheetOption;
import com.logisteq.common.component.excel.option.PxlImportWorkbookOption;
import com.logisteq.common.component.excel.utils.PxlCollectionUtils;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 엑셀 워크북 Import 정보
 *
 * <AUTHOR>
 */
@Getter
public final class PxlImportWorkbookInfo {

    @Setter
    private PxlFileFormat importFileFormat;    // Annotation나 Option으로 입력받지 않고, Detect한다.

    private final String importPassword;

    private final boolean importDataValidation;

    private final boolean importUsingStreamReader;

    private final int importStreamReaderRowCacheSize;

    private final int importStreamReaderBufferSize;

    private final String importCsvCharset;

    private final char importCsvDelimiter;

    private final List<PxlImportSheetOption> importSheetOptions;

    private final List<PxlImportSheetInfo> importSheetInfos;

    private PxlImportWorkbookInfo(final String importPassword,
                                  final boolean importDataValidation,
                                  final boolean importUsingStreamReader,
                                  final int importStreamReaderRowCacheSize,
                                  final int importStreamReaderBufferSize,
                                  final String importCsvCharset,
                                  final char importCsvDelimiter,
                                  final List<PxlImportSheetOption> importSheetOptions,
                                  final List<PxlImportSheetInfo> importSheetInfos) {

        this.importPassword = importPassword;
        this.importDataValidation = importDataValidation;
        this.importUsingStreamReader = importUsingStreamReader;
        this.importStreamReaderRowCacheSize = importStreamReaderRowCacheSize;
        this.importStreamReaderBufferSize = importStreamReaderBufferSize;
        this.importCsvCharset = importCsvCharset;
        this.importCsvDelimiter = importCsvDelimiter;
        this.importSheetOptions = importSheetOptions;
        this.importSheetInfos = importSheetInfos;
    }

    /**
     * Import시 워크북 옵션과 워크북 클래스로부터 워크북 정보들을 수집한다.
     * 워크북 클래스보다 워크북 옵션에 우선순위가 있다.
     *
     * @param workbookClass
     * @param workbookOption
     * @return
     */
    public static PxlImportWorkbookInfo makeImportWorkbookInfo(@Nullable final Class<?> workbookClass,
                                                               @Nullable final PxlImportWorkbookOption workbookOption) {

        final PxlWorkbook workbookAnnotation = Optional.ofNullable(workbookClass)
                .map(c -> c.getAnnotation(PxlWorkbook.class))
                .orElse(null);

        final String importPassword = Optional.ofNullable(workbookOption)
                .flatMap(option -> Optional.ofNullable(option.getImportPassword()))
                .orElseGet(() -> Optional.ofNullable(workbookAnnotation)
                        .map(PxlWorkbook::importPassword)
                        .orElse(PxlConstant.DEFAULT_IMPORT_PASSWORD));

        final boolean importDataValidation = Optional.ofNullable(workbookOption)
                .flatMap(option -> Optional.ofNullable(option.getImportDataValidation()))
                .orElseGet(() -> Optional.ofNullable(workbookAnnotation)
                        .map(PxlWorkbook::importDataValidation)
                        .orElse(PxlConstant.DEFAULT_IMPORT_DATA_VALIDATION));

        final boolean importUsingStreamReader = Optional.ofNullable(workbookOption)
                .flatMap(option -> Optional.ofNullable(option.getImportUsingStreamReader()))
                .orElseGet(() -> Optional.ofNullable(workbookAnnotation)
                        .map(PxlWorkbook::importUsingStreamReader)
                        .orElse(PxlConstant.DEFAULT_IMPORT_USING_STREAM_READER));

        final int importStreamReaderRowCacheSize = Optional.ofNullable(workbookOption)
                .flatMap(option -> Optional.ofNullable(option.getImportStreamReaderRowCacheSize()))
                .orElseGet(() -> Optional.ofNullable(workbookAnnotation)
                        .map(PxlWorkbook::importStreamReaderRowCacheSize)
                        .orElse(PxlConstant.DEFAULT_IMPORT_STREAM_READER_ROW_CACHE_SIZE));

        final int importStreamReaderBufferSize = Optional.ofNullable(workbookOption)
                .flatMap(option -> Optional.ofNullable(option.getImportStreamReaderBufferSize()))
                .orElseGet(() -> Optional.ofNullable(workbookAnnotation)
                        .map(PxlWorkbook::importStreamReaderBufferSize)
                        .orElse(PxlConstant.DEFAULT_IMPORT_STREAM_READER_BUFFER_SIZE));

        final String importCsvCharset = Optional.ofNullable(workbookOption)
                .flatMap(option -> Optional.ofNullable(option.getImportCsvCharset()))
                .orElseGet(() -> Optional.ofNullable(workbookAnnotation)
                        .map(PxlWorkbook::importCsvCharset)
                        .orElse(PxlConstant.DEFAULT_IMPORT_CSV_CHARSET));

        final char importCsvDelimiter = Optional.ofNullable(workbookOption)
                .flatMap(option -> Optional.ofNullable(option.getImportCsvDelimiter()))
                .orElseGet(() -> Optional.ofNullable(workbookAnnotation)
                        .map(PxlWorkbook::importCsvDelimiter)
                        .orElse(PxlConstant.DEFAULT_IMPORT_CSV_DELIMITER));

        final List<PxlImportSheetOption> importSheetOptions = Optional.ofNullable(workbookOption)
                .map(option -> option.getImportSheetOptions())
                .orElseGet(ArrayList::new);

        final List<PxlImportSheetInfo> importSheetInfos = new ArrayList<>();

        return new PxlImportWorkbookInfo(
                importPassword,
                importDataValidation,
                importUsingStreamReader,
                importStreamReaderRowCacheSize,
                importStreamReaderBufferSize,
                importCsvCharset,
                importCsvDelimiter,
                importSheetOptions,
                importSheetInfos
        );
    }

    public PxlImportSheetOption getImportSheetOption(final int index) {

        return PxlCollectionUtils.get(this.importSheetOptions, index);
    }

    public void addImportSheetInfos(final List<PxlImportSheetInfo> importSheetInfos) {

        this.importSheetInfos.addAll(importSheetInfos);
    }

    public void addImportSheetInfo(final PxlImportSheetInfo importSheetInfo) {

        this.importSheetInfos.add(importSheetInfo);
    }

}
