package com.logisteq.common.util;

import com.logisteq.common.constant.Constant;
import com.logisteq.common.dto.PageableDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.Random;

public final class CommonUtil {

    private CommonUtil() {
        throw new AssertionError();
    }

    public static Pageable getPageable(Integer start, Integer size, Sort sort) {
        if (start == null) {
            start = 0;
        }

        if (size == null || size > Constant.PAGEABLE_SIZE_MAX) {
            size = Constant.PAGEABLE_SIZE_MAX;
        }

        return PageRequest.of(start, size, sort);
    }

    public static Pageable getPageable(PageableDTO pageable) {
        if (pageable.getSort() != null) {
            return CommonUtil.getPageable(pageable.getStart(), pageable.getSize(), pageable.getSort());
        } else {
            return CommonUtil.getPageable(pageable.getStart(), pageable.getSize(), getDefaultSort());
        }
    }

    public static Pageable getDefaultPageable(Integer start, Integer size) {
        return CommonUtil.getPageable(start, size, getDefaultSort());
    }

    public static Sort getSort(boolean ascending, String... fields) {
        return ascending ? Sort.by(fields).ascending()
                : Sort.by(fields).descending();
    }

    public static Sort getDefaultSort() {
        return CommonUtil.getSort(true, "createAt");
    }


    /**
     * null을 고려한  compare ,  null과 빈문자열은 같은 것으로 간주
     *
     * @param cmp1
     * @param cmp2
     * @return
     */
    public static boolean stringEquals(String cmp1, String cmp2) {
        if (cmp1 == null)
            cmp1 = "";

        if (cmp2 == null)
            cmp2 = "";

        return StringUtils.equals(StringUtils.trim(cmp1), StringUtils.trim(cmp2));
    }

    public static String byteArrayToHexString(final byte[] byteArray) {
        final StringBuilder sb = new StringBuilder();
        for (final byte b : byteArray) {
            sb.append(String.format("0x%02X ", b & 0xff));
        }
        return sb.toString();
    }

    public static void delayMs(long ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException ignored) {
        }
    }

    private static final Random random = new Random();

    static {
        random.setSeed(System.currentTimeMillis());
    }

    public static String getRandomIntegerString(){
        int randomInt = Math.abs( random.nextInt() ) ;
        return Integer.toString( randomInt );
    }
}
