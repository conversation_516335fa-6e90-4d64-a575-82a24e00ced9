package com.logisteq.common.util;

import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

/**
 * Message Utility (MessageSource, ...)
 *
 * <AUTHOR>
 */
@Component
public class MessageUtil implements MessageSourceAware {

    private static MessageSource messageSource;

    @Override
    public void setMessageSource(MessageSource messageSource) {
        MessageUtil.messageSource = messageSource;
    }

    public static String getMessage(final String code) {
        if (messageSource == null) {
            return code;
        }
        return messageSource.getMessage(code, null, code, LocaleContextHolder.getLocale());
    }

    public static String getMessage(final String code, final Object... args) {
        if (messageSource == null) {
            return code;
        }
        return messageSource.getMessage(code, args, code, LocaleContextHolder.getLocale());
    }

    public static String getMessage(final String code, final Object arg) {
        return getMessage(code, new Object[]{arg});
    }

}
