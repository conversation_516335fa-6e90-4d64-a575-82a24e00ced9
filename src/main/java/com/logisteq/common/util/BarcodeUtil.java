package com.logisteq.common.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import net.sourceforge.barbecue.Barcode;
import net.sourceforge.barbecue.BarcodeException;
import net.sourceforge.barbecue.BarcodeFactory;
import net.sourceforge.barbecue.BarcodeImageHandler;
import net.sourceforge.barbecue.linear.code128.Code128Barcode;
import net.sourceforge.barbecue.linear.code39.Code39Barcode;
import net.sourceforge.barbecue.output.OutputException;
import org.krysalis.barcode4j.impl.code128.Code128Bean;
import org.krysalis.barcode4j.impl.code39.Code39Bean;
import org.krysalis.barcode4j.output.bitmap.BitmapCanvasProvider;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @created 2021-01-27
 * @project tms-service
 */
public class BarcodeUtil {

    // https://www.baeldung.com/java-generating-barcodes-qr-codes
    // ZXing library will not display barcode text under the image.

    private static final int BARCODE4J_IMAGE_RESOLUTION = 160;

    private static final int BARBECUE_THINNEST_BAR_WIDTH = 2;   // Barbecue default: 2
    private static final int BARBECUE_BAR_CODE_HEIGHT = 150;    // Barbecue default: 50
    private static final int BARBECUE_IMAGE_RESOLUTION = 160;

    private static final int ZXING_BAR_CODE_WIDTH = 300;
    private static final int ZXING_BAR_CODE_HEIGHT = 150;
    private static final int ZXING_BAR_ON_COLOR = Color.BLACK.getRGB();
    private static final int ZXING_BAR_OFF_COLOR = Color.WHITE.getRGB();

    private static final int ZXING_QR_CODE_WIDTH = 200;
    private static final int ZXING_QR_CODE_HEIGHT = 200;
    private static final int ZXING_QR_ON_COLOR = Color.BLACK.getRGB();
    private static final int ZXING_QR_OFF_COLOR = Color.WHITE.getRGB();

    private BarcodeUtil() {
        throw new AssertionError();
    }

    public static BufferedImage getBarcode4JBarcode39Image(final String contents) {

        final Code39Bean barcodeGenerator = new Code39Bean();
        final BitmapCanvasProvider canvas = new BitmapCanvasProvider(BARCODE4J_IMAGE_RESOLUTION,
                BufferedImage.TYPE_BYTE_BINARY,
                false,
                0);

        barcodeGenerator.generateBarcode(canvas, contents);

        return canvas.getBufferedImage();
    }

    public static BufferedImage getBarcode4JBarcode128Image(final String contents) {

        final Code128Bean barcodeGenerator = new Code128Bean();
        final BitmapCanvasProvider canvas = new BitmapCanvasProvider(BARCODE4J_IMAGE_RESOLUTION,
                BufferedImage.TYPE_BYTE_BINARY,
                false,
                0);

        barcodeGenerator.generateBarcode(canvas, contents);

        return canvas.getBufferedImage();
    }

    public static BufferedImage getBarbecueBarcode39Image(final String contents) throws IOException {

        try {
//            final Barcode barcode = BarcodeFactory.createCode39(contents, false);
            final Barcode barcode = new Code39Barcode(contents, false, true);

            barcode.setBarWidth(BARBECUE_THINNEST_BAR_WIDTH);
            barcode.setBarHeight(BARBECUE_BAR_CODE_HEIGHT);
            barcode.setResolution(BARBECUE_IMAGE_RESOLUTION);

            return BarcodeImageHandler.getImage(barcode);
        } catch (BarcodeException | OutputException e) {
            throw new IOException(e);
        }
    }

    public static BufferedImage getBarbecueBarcode128Image(final String contents) throws IOException {

        try {
//            final Barcode barcode = BarcodeFactory.createCode128(contents);
            final Barcode barcode = new Code128Barcode(contents, Code128Barcode.O);

            barcode.setBarWidth(BARBECUE_THINNEST_BAR_WIDTH);
            barcode.setBarHeight(BARBECUE_BAR_CODE_HEIGHT);
            barcode.setResolution(BARBECUE_IMAGE_RESOLUTION);

            return BarcodeImageHandler.getImage(barcode);
        } catch (BarcodeException | OutputException e) {
            throw new IOException(e);
        }
    }

    // 숫자(0~9)
    // 알파벳 대문자
    // 기호 (-, ., 스페이스, $, /, +, %)
    // 스타트/스톱 캐릭터 (*:별표)
    public static BufferedImage getZXingBarcode39Image(final String contents) throws IOException {

        return BarcodeUtil.getZXingBarcodeImage(contents,
                BarcodeFormat.CODE_39,
                ZXING_BAR_CODE_WIDTH,
                ZXING_BAR_CODE_HEIGHT,
                ZXING_BAR_ON_COLOR,
                ZXING_BAR_OFF_COLOR,
                null);
    }

    // 아스키 코드 전체 문자
    // 숫자(0~9)
    // 알파벳
    // 대문자/소문자
    // 기호
    // 제어 문자
    // ([CR][STX] 등)
    public static BufferedImage getZXingBarcode128Image(final String contents) throws IOException {

        return BarcodeUtil.getZXingBarcodeImage(contents,
                BarcodeFormat.CODE_128,
                ZXING_BAR_CODE_WIDTH,
                ZXING_BAR_CODE_HEIGHT,
                ZXING_BAR_ON_COLOR,
                ZXING_BAR_OFF_COLOR,
                null);
    }

    public static BufferedImage getZXingQRcodeImage(final String contents) throws IOException {

        final Map<EncodeHintType, Object> hints = new HashMap<>();

        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");  // Default "ISO-8859-1"
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.L);   // Default L

        return BarcodeUtil.getZXingBarcodeImage(contents,
                BarcodeFormat.QR_CODE,
                ZXING_QR_CODE_WIDTH,
                ZXING_QR_CODE_HEIGHT,
                ZXING_QR_ON_COLOR,
                ZXING_QR_OFF_COLOR,
                hints);
    }

    public static BufferedImage getZXingBarcodeImage(final String contents,
                                                     final BarcodeFormat barcodeFormat,
                                                     final int width,
                                                     final int height,
                                                     final int onColor,
                                                     final int offColor,
                                                     final Map<EncodeHintType, Object> hints) throws IOException {

        try {
            final BitMatrix bitMatrix = new MultiFormatWriter().encode(contents,
                    barcodeFormat,
                    width,
                    height,
                    hints);
            final MatrixToImageConfig matrixToImageConfig = new MatrixToImageConfig(onColor, offColor);

            return MatrixToImageWriter.toBufferedImage(bitMatrix, matrixToImageConfig);
        } catch (WriterException e) {
            throw new IOException(e);
        }
    }

}
