package com.logisteq.common.util;

import org.apache.commons.math3.util.Precision;

/**
 * <AUTHOR>
 * @created 2024-06-03
 * @project tms-service
 */
public class NumberUtil {

    // https://www.baeldung.com/java-double-round-two-decimal-places
    // https://www.baeldung.com/java-round-decimal-number

    public static double roundDouble(final double value, final int places) {
        return Precision.round(value, places);
    }

    public static double roundDouble2(final double value, final int places) {
        final double scale = Math.pow(10, places);
        return Math.round(value * scale) / scale;
    }

}
