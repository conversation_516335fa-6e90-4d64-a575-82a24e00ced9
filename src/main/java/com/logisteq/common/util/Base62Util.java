package com.logisteq.common.util;

/**
 * <AUTHOR>
 * @created 2021-10-15
 * @project tms-service
 */
public final class Base62Util {

    private Base62Util() {
        throw new AssertionError();
    }

    private static final String BASE62STR = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final int BASE62LEN = BASE62STR.length();

    public static String encode(long value) {

        final StringBuilder sb = new StringBuilder();
        do {
            sb.append(BASE62STR.charAt((int) (value % BASE62LEN)));
            value /= BASE62LEN;
        } while (value > 0L);

        return sb.toString();
    }

    public static long decode(final String value) {

        long result = 0L;
        long power = 1L;
        for (int i = 0; i < value.length(); i++) {
            result += BASE62STR.indexOf(value.charAt(i)) * power;
            power *= BASE62LEN;
        }

        return result;
    }

}
