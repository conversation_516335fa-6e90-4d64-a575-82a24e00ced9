package com.logisteq.common.constant;

/**
 * 유효성 상수
 */
public final class ValidationConst {

    private ValidationConst() {
        throw new AssertionError();
    }

    /**
     * LocalDateTime 포맷
     */
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";

    public static final int ID_MIN = 1;

    public static final int USER_NAME_MIN = 1;

    public static final int USER_NAME_MAX = 50;

    public static final int PASSWORD_MIN = 6;

    public static final int PASSWORD_MAX = 10;

    public static final int ADDRESS1_MAX = 255;

    public static final int ADDRESS2_MAX = 255;

    public static final int EMAIL_MAX = 50;

    public static final int ZIP_CODE_MAX = 10;

    public static final int MOBILE_MIN = 10;

    public static final int MOBILE_MAX = 11;

    public static final String MOBILE_PATTERN = "\\d{" + MOBILE_MIN + "," + MOBILE_MAX + "}";

    public static final int USER_DESCRIPTION_MAX = 1024;

    public static final int USER_PRIVILEGE_ID_MAX = 255;

    public static final int USER_PRIVILEGE_SUMMARY_MAX = 255;

    public static final int USER_ROLE_ID_MAX = 255;

    public static final int USER_ROLE_SUMMARY_MAX = 255;

    public static final int OFFICE_REGION_MAX = 100;

    public static final int RIDER_GRADE_MAX = 100;

    public static final int RIDER_GRADE_MIN = 0;

    public static final int ETA_MIN = 1;

    public static final int EDA_MIN = 1;

}
