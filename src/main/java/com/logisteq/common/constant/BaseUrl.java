package com.logisteq.common.constant;

/**
 * API URL  클래스
 */
public final class BaseUrl {
	private BaseUrl() {
		throw new AssertionError();
	}

	public static class Params {
		public static final String PROJECT_ID 		= "projectId";			// 프로젝트 아이디
	}

	public static final String BASE_URL = "/api";
	public static final String RIDERS_URL = BASE_URL + "/riders";
	public static final String TRACKS_URL = BASE_URL + "/tracks";
	public static final String TMSCORE_URL = BASE_URL + "/tms-core";
	public static final String VEHICLE_URL = BASE_URL + "/vehicle";
	public static final String VEHICLE_MODEL_URL = BASE_URL + "/vehicleModel";
	public static final String VEHICLE_CHARACTERISTICS_URL = BASE_URL + "/vehicleCharacteristics";
	public static final String VEHICLE_DRIVE_INFO_URL = BASE_URL + "/vehicleDriveInfo";
	public static final String PROJECTS_URL = BASE_URL + "/projects";
	public static final String GEOFENCE_URL = BASE_URL + "/geofence";
	public static final String GAS_STATION_URL = BASE_URL + "/gasstation";
	public static final String EV_STATION_URL = BASE_URL + "/evstation";
	public static final String INCIDENT_URL = BASE_URL + "/incidents";
	public static final String FILES_URL = BASE_URL + "/files";
	public static final String DEPARTMENTS_URL = BASE_URL + "/departments";
	public static final String HUBS_URL = BASE_URL + "/hubs";

	public static final String DRIVING_URL = BASE_URL + "/driving";			// 운행 관련 API 추가 (redmine-#911)


	public static final String MQTT_SERVICE_URL = BASE_URL + "/mqtt";
	public static final String FCM_SERVICE_URL = BASE_URL + "/fcm";

	public static final String DEMO_URL = BASE_URL + "/demo";
	public static final String NOTICE_URL = BASE_URL + "/notice";

	public static final String PRODUCTS_URL = BASE_URL + "/products";

	public static final String SERVER_STATUS_URL = "/lbs-server";

	public static final String LBS_CORE_SEARCH = "/lbs/search";
	public static final String LBS_CORE_ROUTE = "/lbs/route";
	public static final String LBS_CORE_CLUSTER = "/lbs/cluster";


}
