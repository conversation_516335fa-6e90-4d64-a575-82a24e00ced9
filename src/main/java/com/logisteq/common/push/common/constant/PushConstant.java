package com.logisteq.common.push.common.constant;

import org.springframework.retry.backoff.ExponentialBackOffPolicy;

public interface PushConstant {

    // 2nd: 200ms, 3rd: 400ms, 4th: 800ms, 5th: 1600ms
    int retryableMaxAttempts = 5;
    long retryableBackoffDelay = 200L;
    long retryableBackoffMaxDelay = ExponentialBackOffPolicy.DEFAULT_MAX_INTERVAL;
    double retryableBackoffMultiplier = ExponentialBackOffPolicy.DEFAULT_MULTIPLIER;

}
