package com.logisteq.common.feign.auth;

import com.logisteq.common.dto.auth.OauthToken;
import com.logisteq.common.feign.FormFeignEncoder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@FeignClient(
        name = "auth-service",
        url = "${feign.client.config.auth-service.url:}",
        configuration = FormFeignEncoder.class
)
public interface AuthServiceClient {

    @PostMapping(value = "/oauth/token", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    OauthToken postOauthToken(
            @RequestHeader(value = HttpHeaders.AUTHORIZATION, required = true) String bearer,
            @RequestBody Map<String, ?> formData);

}
