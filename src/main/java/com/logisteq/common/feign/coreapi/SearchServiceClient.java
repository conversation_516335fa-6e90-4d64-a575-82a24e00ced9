package com.logisteq.common.feign.coreapi;

import com.logisteq.common.constant.CommonServiceApiUrl;
import com.logisteq.common.constant.Constant;
import com.logisteq.common.dto.coreapi.search.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(
        name = "common-service",
        url = "${feign.client.config.common-service.url:}"
)
public interface SearchServiceClient {

    /**
     * 통합검색(search)
     * 주소(지번 주소 및 도로명 주소)와 명칭의 통합 검색을 수행하기 위한 API
     *
     * @param code
     * @param x       통합검색을 수행하는 기준 좌표, 다수의 검색결과 존재 시 정렬 기준이 된다.
     * @param y       통합검색을 수행하는 기준 좌표, 다수의 검색결과 존재 시 정렬 기준이 된다.
     * @param keyword 검색어는 건물명칭, 상호명, 지번 주소, 도로명 주소를 모두 포함한다.
     * @return
     */
    @GetMapping(value = CommonServiceApiUrl.SEARCH_URL)
    SearchExtendedResponseInfo search(
            @RequestHeader(value = "code", required = false, defaultValue = Constant.LocationCode.KR) String code,
            @RequestParam(value = CommonServiceApiUrl.Params.X_POS, required = false) Double x,
            @RequestParam(value = CommonServiceApiUrl.Params.Y_POS, required = false) Double y,
            @RequestParam(value = CommonServiceApiUrl.Params.KEYWORD, required = true) String keyword);

    /**
     * 통합검색 Extend(extsearch)
     * 주소(지번 주소 및 도로명 주소)와 명칭의 통합 검색을 수행하기 위한 API
     * 기존 통합검색과 요청은 동일하지만 검색결과 항목이 확장된 API
     *
     * @param code
     * @param x       통합검색을 수행하는 기준 좌표, 다수의 검색결과 존재 시 정렬 기준이 된다.
     * @param y       통합검색을 수행하는 기준 좌표, 다수의 검색결과 존재 시 정렬 기준이 된다.
     * @param keyword 검색어는 건물명칭, 상호명, 지번 주소, 도로명 주소를 모두 포함한다.
     * @return
     */
    @GetMapping(value = CommonServiceApiUrl.EXTEND_SEARCH_URL)
    SearchExtendedResponseInfo extSearch(
            @RequestHeader(value = "code", required = false, defaultValue = Constant.LocationCode.KR) String code,
            @RequestParam(value = CommonServiceApiUrl.Params.X_POS, required = false) Double x,
            @RequestParam(value = CommonServiceApiUrl.Params.Y_POS, required = false) Double y,
            @RequestParam(value = CommonServiceApiUrl.Params.KEYWORD, required = true) String keyword);

    /**
     * 좌표검색(searchcoord)
     * X, Y 좌표를 이용하여 주소 검색을 수행하기 위한 API
     *
     * @param code
     * @param x    주소 검색을 위한 X좌표
     * @param y    주소 검색을 위한 Y좌표
     * @return
     */
    @GetMapping(value = CommonServiceApiUrl.SEARCH_COORD_URL)
    SearchCoordResponseInfo searchCoord(
            @RequestHeader(value = "code", required = false, defaultValue = Constant.LocationCode.KR) String code,
            @RequestParam(value = CommonServiceApiUrl.Params.X_POS, required = true) Double x,
            @RequestParam(value = CommonServiceApiUrl.Params.Y_POS, required = true) Double y);

    /**
     * 좌표검색 Extend(extsearchcoord)
     * X, Y 좌표를 이용하여 주소 검색을 수행하기 위한 API
     * 기존 좌표 검색과 요청은 동일하지만 검색결과 항목이 확장 된 API
     *
     * @param code
     * @param x    주소 검색을 위한 X좌표
     * @param y    주소 검색을 위한 Y좌표
     * @return
     */
    @GetMapping(value = CommonServiceApiUrl.EXTEND_SEARCH_COORD_URL)
    ExtendSearchCoordResponseInfo extSearchCoord(
            @RequestHeader(value = "code", required = false, defaultValue = Constant.LocationCode.KR) String code,
            @RequestParam(value = CommonServiceApiUrl.Params.X_POS, required = true) Double x,
            @RequestParam(value = CommonServiceApiUrl.Params.Y_POS, required = true) Double y);

    /**
     * 입구점 검색(searchentrance)
     * X, Y 좌표를 이용하여 건물의 입구점 좌표를 검색하기 위한 API
     *
     * @param code
     * @param x    입구점 검색을 위한 X좌표
     * @param y    입구점 검색을 위한 Y좌표
     * @return
     */
    @GetMapping(value = CommonServiceApiUrl.SEARCH_ENTRANCE_URL)
    SearchEntrance searchEntrance(
            @RequestHeader(value = "code", required = false, defaultValue = Constant.LocationCode.KR) String code,
            @RequestParam(value = CommonServiceApiUrl.Params.X_POS, required = true) Double x,
            @RequestParam(value = CommonServiceApiUrl.Params.Y_POS, required = true) Double y);

    /**
     * 영역검색(searcharea)
     * X, Y 좌표를 이용하여 해당 위치의 건물이나 단지의 Polygon 정보를 검색하기 위한 API
     *
     * @param code
     * @param x      영역 검색을 수행하는 기준 좌표 정보
     * @param y      영역 검색을 수행하는 기준 좌표 정보
     * @param expand 확장 거리 정보 미터
     * @param type  영역 검색 종류 (0: Geo fence Polygon 영역 정보를 건물과 단지 Polygon 보다 우선 검색 (Default), 1 : Geo-fence Polygon 영역을 검색하지 않음 건물과 단지 Polygon 만 검색)
     * @return
     */
    @GetMapping(value = CommonServiceApiUrl.SEARCH_AREA_URL)
    SearchAreaResponseInfo searchArea(
            @RequestHeader(value = "code", required = false, defaultValue = Constant.LocationCode.KR) String code,
            @RequestParam(value = CommonServiceApiUrl.Params.X_POS, required = true) Double x,
            @RequestParam(value = CommonServiceApiUrl.Params.Y_POS, required = true) Double y,
            @RequestParam(value = CommonServiceApiUrl.Params.EXPAND, required = false) Double expand,
            @RequestParam(value = CommonServiceApiUrl.Params.TYPE, required = false) Integer type);

    /**
     * 주소검색(searchaddress)
     * 주소(지번 주소 및 도로명 주소)를 이용하여 위치의 상세정보 검색을 수행하기 위한 API
     *
     * @param code
     * @param x       주소검색을 수행하는 기준 좌표 다수의 검색결과 존재 시 정렬 기준이 된다.
     * @param y       주소검색을 수행하는 기준 좌표 다수의 검색결과 존재 시 정렬 기준이 된다.
     * @param keyword 주소검색을 수행하기 위한 주소를 입력한다. 주소는 도로명 주소와 지번 주소를 모두 포함한다.
     * @return
     */
    @GetMapping(value = CommonServiceApiUrl.SEARCH_ADDRESS_URL)
    SearchExtendedResponseInfo searchAddress(
            @RequestHeader(value = "code", required = false, defaultValue = Constant.LocationCode.KR) String code,
            @RequestParam(value = CommonServiceApiUrl.Params.X_POS, required = false) Double x,
            @RequestParam(value = CommonServiceApiUrl.Params.Y_POS, required = false) Double y,
            @RequestParam(value = CommonServiceApiUrl.Params.KEYWORD, required = true) String keyword);

    /**
     * 복합 검색
     * searchAddress 실패시 extSearch로 전환한다.
     *
     * @param code
     * @param x
     * @param y
     * @param keyword
     * @param allowedSearchCount extSearch 시 몇개의 검색 결과까지 유효한 값으로 판단할지
     * @return
     */
    @GetMapping(value = CommonServiceApiUrl.SEARCH_SMART_URL)
    SearchExtendedResponseInfo smartSearch(
            @RequestHeader(value = "code", required = false, defaultValue = Constant.LocationCode.KR) String code,
            @RequestParam(value = CommonServiceApiUrl.Params.X_POS, required = false) Double x,
            @RequestParam(value = CommonServiceApiUrl.Params.Y_POS, required = false) Double y,
            @RequestParam(value = CommonServiceApiUrl.Params.KEYWORD, required = true) String keyword,
            @RequestParam(value = CommonServiceApiUrl.Params.ALLOWED_SEARCH_COUNT, required = false) Integer allowedSearchCount);

    /**
     * 지오코딩(geocoding)
     * 대량의 주소 문자열 리스트를 이용하여 위치의 좌표 및 상세정보 검색을 한번에 수행하기 위한 API
     *
     * @param requestInfo
     * @return
     */
    @PostMapping(value = CommonServiceApiUrl.SEARCH_GEOCODING_URL, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    GeocodingResponseInfo searchGeoCoding(@RequestBody GeocodingRequestInfo requestInfo);

    // #4991
    @GetMapping(value = CommonServiceApiUrl.SEARCH_BASE_HANG_ADDRESS_URL)
    List<String> searchBaseHangAddress(@RequestParam(value = "containingHang2", required = false) String containingHang2,
                                       @RequestParam(value = "startingHang3Hang4", required = false) String startingHang3Hang4);

    @GetMapping(value = CommonServiceApiUrl.SEARCH_BASE_ROAD_ADDRESS_URL)
    List<String> searchBaseRoadAddress(@RequestParam(value = "containingHang2", required = false) String containingHang2,
                                       @RequestParam(value = "startingRoad", required = false) String startingRoad);

    @GetMapping(value = CommonServiceApiUrl.SEARCH_VERSION_URL)
    String getSearchVersion();

}
