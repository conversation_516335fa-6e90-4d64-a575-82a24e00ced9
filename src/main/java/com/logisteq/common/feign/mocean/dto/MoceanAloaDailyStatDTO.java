package com.logisteq.common.feign.mocean.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.logisteq.common.feign.mocean.json.MoceanLocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @created 2021-06-01
 * @project track-service
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MoceanAloaDailyStatDTO {

    // Vin Number
    private String vin;

    // 차량번호
    private String licensePlate;

    // 운행일
    @JsonSerialize(using = MoceanLocalDateSerializer.class)
    private LocalDate criteriaDate;

    // 주행거리, 단위:km
    private Double runningDistance;

    // 주행시간, 단위:분
    private Double runningTime;

    // 기준온도 이탈시간, 단위:분
    private Long abnormalTemperatureTime;

    // 과속시간, 단위:분
    private Long overSpeedTime;

}
