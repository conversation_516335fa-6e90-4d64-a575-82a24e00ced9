package com.logisteq.common.feign.mocean.json;

import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @created 2021-03-17
 * @project track-service
 */
public class MoceanLocalDateTimeSerializer extends LocalDateTimeSerializer {

    public MoceanLocalDateTimeSerializer() {
        super(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }

}