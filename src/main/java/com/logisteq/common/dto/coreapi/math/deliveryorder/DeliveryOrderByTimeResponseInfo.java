package com.logisteq.common.dto.coreapi.math.deliveryorder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeliveryOrderByTimeResponseInfo {
	// private String command = CommandDTO.RES_CLUSTER;

	/**
	 * 결과 개수
	 */
	@JsonProperty("count")
	private Integer count;

	@JsonProperty("route")
	private List<DeliveryOrderResult> deliveryOrders;
}
