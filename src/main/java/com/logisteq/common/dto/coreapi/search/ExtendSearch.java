package com.logisteq.common.dto.coreapi.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExtendSearch extends Search {

	/**
	 * 요청 좌표와 검색결과 위치 사이의 직선거리를 미터(m) 단위로
	 */
	@JsonProperty("distance")
	private Double distance;

	/**
	 * 법정동코드
	 */
	@JsonProperty("bdongcode")
	private String bdongcode;

	/**
	 * 건물 도보 입구점 좌표
	 */
	@JsonProperty("entrance")
	private List<Double> entrance;

	/**
	 * 건물 차량 입구점 좌표
	 */
	@JsonProperty("entrancevehicle")
	private List<Double> entrancevehicle;

	/**
	 * 도로명
	 */
	@JsonProperty("newRoadName")
	private String newRoadName;

	/**
	 * 읍면동명
	 */
	@JsonProperty("eupMyeonDong")
	private String eupMyeonDong;

}
