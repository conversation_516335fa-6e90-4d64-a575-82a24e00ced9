package com.logisteq.common.dto.coreapi.math.deliveryorder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.logisteq.common.dto.PointDTO;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehiclePosInfo extends PointDTO {
	@JsonProperty("start")
	/**
	 * 업무 시작 시간 (Epoch를 seconds로 변환)
	 */
	private Long startTime;

	@Builder(builderMethodName = "VehiclePosInfoBuilder")
	protected VehiclePosInfo(Double x, Double y, Long startTime) {
		super(x, y);
		this.startTime = startTime;
	}
}
