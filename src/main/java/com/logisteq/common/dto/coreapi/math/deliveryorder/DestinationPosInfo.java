package com.logisteq.common.dto.coreapi.math.deliveryorder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import com.logisteq.common.dto.PointDTO;
import com.logisteq.common.dto.coreapi.math.DataPoint;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DestinationPosInfo extends DataPoint  {
	@JsonProperty("reqtimeFr")
	/**
	 * 방문 요청 시간 시작 (Epoch를 seconds로 변환)
	 */
	private Long requestTimeFrom = -1L;


	@JsonProperty("reqtimeTo")
	/**
	 * 방문 요청 종료 시작 (Epoch를 seconds로 변환)
	 */
	private Long requestTimeTo = -1L;

	@JsonProperty("allowTime")
	/**
	 * 예정 허용 시간  (secs)
	 */
	private Long allowTime = 0L;

	@JsonProperty("svctime")
	/**
	 * 서비스 시간 (secs)
	 */
	private Long serviceTime = 0L;

	@JsonProperty("entry")
	protected List<PointDTO> entryList;

	@JsonProperty("postalCode")
	private String postalCode;


	@Builder(builderMethodName = "DestinationPosInfoBuilder")
	protected DestinationPosInfo(Double x, Double y, Long dataId, Long requestTimeFrom, Long requestTimeTo, Long allowTime, Long serviceTime, List<PointDTO> entryList , String postalCode ) {
		super(x, y, dataId);
		this.requestTimeFrom = requestTimeFrom;
		this.requestTimeTo = requestTimeTo;
		this.serviceTime = serviceTime;
		this.allowTime = allowTime;
		this.entryList = entryList;
		this.postalCode = postalCode;
	}
}
