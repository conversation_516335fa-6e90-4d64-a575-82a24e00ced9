package com.logisteq.common.dto.coreapi.route;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MppRequestInfo {
	private static final Integer DEFAULT_DISTANCE_METERS = 2000;
	
	@NotNull
	@Min(-180)
	@Max(180)
	@JsonProperty("dx")
	private Double x;
	
	@NotNull
	@Min(-90)
	@Max(90)
	@JsonProperty("dy")
	private Double y;
	
	@NotNull
	@Min(-1)
	@Max(360)
	@JsonProperty("dir")
	private Integer angle;
	
	@NotNull
	@Min(10)
	@JsonProperty("meter")
	private Integer distance = DEFAULT_DISTANCE_METERS;


	@JsonProperty("subquerymeter")
	private Integer subQueryMeter;

	@JsonProperty("subdepth")
	private Integer subDepth;

	@JsonProperty("submeter")
	private Integer subMeter;

	@Builder
	protected MppRequestInfo(Double x, Double y, Integer angle, Integer distance, Integer subQueryMeter , Integer subDepth, Integer subMeter ) {
		this.x = x;
		this.y = y;
		this.angle = angle;
		this.distance = distance;

		this.subQueryMeter = subQueryMeter;
		this.subDepth = subDepth;
		this.subMeter = subMeter;
	}
}
