package com.logisteq.common.dto.coreapi.link;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.logisteq.common.dto.PointDTO;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetLinkIdxRequestInfo extends PointDTO {

    @JsonProperty("angle")
    private Integer angle;

    @JsonProperty("range")
    private Integer range;

    @Builder(builderMethodName = "GetLinkIdxBuilder")
    protected GetLinkIdxRequestInfo(double x, double y, int angle, int range ){
        super( x,y);
        this.angle = angle;
        this.range = range;
    }

}
