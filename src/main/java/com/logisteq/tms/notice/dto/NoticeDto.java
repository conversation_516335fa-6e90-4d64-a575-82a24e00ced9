package com.logisteq.tms.notice.dto;

import com.logisteq.tms.notice.domain.Notice;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NoticeDto {

    private Long noticeId;
    /**
     * 생성인
     */
    private LocalDateTime createAt;
    /**
     * 시작 일
     */
    private LocalDateTime deployAt;
    /**
     * 종료 일
     */
    private LocalDateTime expireAt;

    /**
     * 작성자 이름
     */
    private String writerName;
    
    /**
     * notice 제목
     */
    private String noticeTitle;

    /**
     * notice 내용
     */
    private String noticeContent;

    /**
     * notice 방식
     */
    private NoticeType noticeType;

    /**
     * 첨부 파일
     */
    private List<String> attachFiles;

    /**
     * 공지대상인 departmentCod 를 모두 저장한다
     */
    private String departmentCodeList;

    public NoticeDto(@NotNull final Notice notice){
        this.noticeId = notice.getNoticeId();
        this.createAt = notice.getCreateAt();
        this.deployAt = notice.getDeployAt();
        this.expireAt = notice.getExpireAt();
        this.noticeTitle = notice.getNoticeTitle();
        this.noticeContent = notice.getNoticeContent();
        this.noticeType = notice.getNoticeType();
        this.attachFiles = notice.getAttachFiles();
        this.departmentCodeList = notice.getDepartmentCodeList();
    }
}
