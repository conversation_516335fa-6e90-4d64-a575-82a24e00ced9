package com.logisteq.tms.notice.repository;

import com.logisteq.tms.notice.domain.Notice;
import com.logisteq.tms.notice.domain.PushAlarm;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface NoticeRepository extends JpaRepository<Notice, Long>, JpaSpecificationExecutor<Notice> {

    List<Notice> findByNoticeIdIn(@NotNull final Set<Long> noticeIds);

    Notice findByNoticeId(@NotNull final Long noticeId);

    List<Notice> findByDeployAtAndPushAlarm(@NotNull LocalDateTime deployAt, @NotNull PushAlarm pushAlarm);
}
