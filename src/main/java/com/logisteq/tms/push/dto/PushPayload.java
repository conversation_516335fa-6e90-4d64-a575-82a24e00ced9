package com.logisteq.tms.push.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Getter @Setter
@AllArgsConstructor @NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PushPayload<T> {
    private Integer type;
    private Long projectId;
    private Long riderId;
    @Builder.Default
    private LocalDateTime timeCreated = LocalDateTime.now();
    private T additionData;

    public String getTimeCreated() {
        return timeCreated.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }
    
    @JsonIgnore
    public String getJsonString() {
    	ObjectMapper objMapper = new ObjectMapper();
		try {
			return objMapper.writeValueAsString(this);
		} catch (JsonProcessingException e) {
			log.error("PushPayload - convert object to string error", e);
			return null;
		}
    }
}