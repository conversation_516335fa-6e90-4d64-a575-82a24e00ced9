package com.logisteq.tms.incident.domain.event;

import com.logisteq.common.util.BeanUtil;
import com.logisteq.tms.incident.domain.Incident;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.PostPersist;

/**
 * <AUTHOR>
 * @created 2021-07-14
 * @project tms-service
 */
@Slf4j
public class IncidentEntityListener {

    @PostPersist
    public void onPostPersist(final Incident incident) {

        //리스너 안에서는 의존성 주입(autowired)이 안되기 때문에 아래와 같이 얻어온다.
        final IncidentEventPublisher incidentEventPublisher = BeanUtil.getBean(IncidentEventPublisher.class);

        incidentEventPublisher.publishIncidentOccurred(incident);
    }

}
