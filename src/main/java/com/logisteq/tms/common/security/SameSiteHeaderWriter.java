package com.logisteq.tms.common.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.web.header.HeaderWriter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collection;

import static org.springframework.http.HttpHeaders.SET_COOKIE;

/**
 * This header writer just adds "SameSite=None;" to the Set-Cookie response header
 */
@Slf4j
class SameSiteHeaderWriter implements HeaderWriter {
    private static final String SAME_SITE_NONE = "SameSite=None";
    private static final String SECURE = "Secure";

    @Override
    public void writeHeaders(HttpServletRequest request, HttpServletResponse response) {

        if (response != null) {
            Collection<String> headers = response.getHeaders(SET_COOKIE);
            for (String header : headers) { // there can be multiple Set-Cookie attributes

                String setCookie = header;
                log.info("SameSiteHeaderWriter setCookie => " + setCookie);

                ArrayList toAdd = new ArrayList<String>();
                toAdd.add(setCookie);

                if (!setCookie.contains(SAME_SITE_NONE)) {
                    toAdd.add(SAME_SITE_NONE);
                }

                if (!setCookie.contains(SECURE)) {
                    toAdd.add(SECURE);
                }
                log.info("writeHeaders setCookie => " + setCookie);
                String addCookies = String.join("; ", toAdd);
                response.setHeader(SET_COOKIE, addCookies);

                log.info("SameSiteHeaderWriter addCookies => " + addCookies);
            }
        }
    }

}