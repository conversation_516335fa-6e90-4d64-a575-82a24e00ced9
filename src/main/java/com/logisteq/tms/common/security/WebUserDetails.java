package com.logisteq.tms.common.security;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.logisteq.tms.user.constant.RoleType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * WARNING!!!
 * 필드 변경 시 WebUserDetailsDeserializer도 함께 변경해야 합니다.
 * 패키지나 파일이름 등이 Http session에 @class라는 필드에서 사용되므로, 패키지나 파일 이름 변경하면 안됩니다.
 * 예) "@class": "com.logisteq.tms.common.security.WebUserDetails"
 */
@Getter
@Setter
@ToString(of = {"id", "name", "organizationId", "organizationCode",})
public class WebUserDetails extends User {

    private static final long serialVersionUID = 244405218856132711L;

    private final Long id;
    private final String name;
    private final Long organizationId;
    private final String organizationName;
    private final String organizationCode;
    private final List<Long> departmentIdList;
    private final String authcode;
    private final boolean readonly;

    public WebUserDetails(final String username,
                          final String password,
                          final boolean enabled,
                          final boolean accountNonExpired,
                          final boolean credentialsNonExpired,
                          final boolean accountNonLocked,
                          final Collection<? extends GrantedAuthority> authorities,
                          final Long id,
                          final String name,
                          final Long organizationId,
                          final String organizationName,
                          final String organizationCode,
                          final List<Long> departmentIdList,
                          final String authcode,
                          final boolean readonly) {

        super(username,
                password,
                enabled,
                accountNonExpired,
                credentialsNonExpired,
                accountNonLocked,
                authorities);

        this.id = id;
        this.name = name;
        this.organizationId = organizationId;
        this.organizationName = organizationName;
        this.organizationCode = organizationCode;
        this.departmentIdList = departmentIdList;
        this.authcode = authcode;
        this.readonly = readonly;
    }

    @JsonIgnore
    public boolean hasAuthority(final RoleType roleType) {
        return Objects.nonNull(this.getAuthorities())
                && Objects.nonNull(roleType)
                && this.getAuthorities().contains(new SimpleGrantedAuthority(roleType.getId()));
    }

    @JsonIgnore
    public boolean hasAuthority(final String roleId) {
        return Objects.nonNull(this.getAuthorities())
                && Objects.nonNull(roleId)
                && this.getAuthorities().contains(new SimpleGrantedAuthority(roleId));
    }

}
