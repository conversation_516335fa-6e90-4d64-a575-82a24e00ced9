package com.logisteq.tms.common.security;

import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

// https://www.baeldung.com/spring-security-extra-login-fields
public class WebUserAuthenticationFilter extends UsernamePasswordAuthenticationFilter {

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException {

        if (!"POST".equals(request.getMethod())) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        }

        final UsernamePasswordAuthenticationToken authRequest = getAuthRequest(request);
        setDetails(request, authRequest);

        return this.getAuthenticationManager().authenticate(authRequest);
    }

    private UsernamePasswordAuthenticationToken getAuthRequest(final HttpServletRequest request) {

        final String username = Optional.ofNullable(obtainUsername(request)).orElse("");
        final String password = Optional.ofNullable(obtainPassword(request)).orElse("");
        final String authcode = Optional.ofNullable(request.getParameter(WebSecurityConstant.SPRING_SECURITY_FORM_AUTH_CODE_KEY)).orElse("");

        final String authenticationToken = StringUtils.joinWith(WebSecurityConstant.SPRING_SECURITY_AUTH_CODE_SEPARATOR,
                username.trim(), authcode);

        return new UsernamePasswordAuthenticationToken(authenticationToken, password);
    }

}
