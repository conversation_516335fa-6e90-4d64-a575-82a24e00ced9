package com.logisteq.tms.common.security;

import com.logisteq.common.exception.CustomException;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.service.OrganizationService;
import com.logisteq.tms.user.service.UserDepartmentService;
import com.logisteq.tms.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Validated
@Service
public class WebUserDetailsService implements UserDetailsService {

    private final UserService userService;
    private final OrganizationService organizationService;
    private final UserDepartmentService userDepartmentService;

    @Autowired
    public WebUserDetailsService(final UserService userService,
                                 final OrganizationService organizationService,
                                 final UserDepartmentService userDepartmentService) {

        this.userService = userService;
        this.organizationService = organizationService;
        this.userDepartmentService = userDepartmentService;
    }

    @Override
    public UserDetails loadUserByUsername(String authenticationToken) throws UsernameNotFoundException {

        final String[] authenticationArray = StringUtils.splitPreserveAllTokens(authenticationToken,
                WebSecurityConstant.SPRING_SECURITY_AUTH_CODE_SEPARATOR);

        final String username = ArrayUtils.get(authenticationArray, 0);
        final String authcode = ArrayUtils.get(authenticationArray, 1);

        if (StringUtils.isBlank(username)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED,
                    WebSecurityConstant.LOGIN_FAIL_REASON_BAD_USERNAME,
                    "아이디를 입력하세요.");
        }

        final User user = userService.getUserByEmail(username)
                .orElseThrow(() -> new UsernameNotFoundException(username + "에 해당하는 사용자 정보가 없습니다."));

        final Organization org = Optional.ofNullable(user.getOrganizationId())
                .map(organizationService::getOrganizationById)
                .orElse(null);

        // 1. 가입승인이 안된 계정 : TODO
        // 2. 비밀번호 초기화 신청 된 계정 : TODO
        // 3. 사용자 역할에 따른 권한 설정 : TODO
        final List<GrantedAuthority> auth = user.getRoles().stream()
                .map(r -> new SimpleGrantedAuthority(r.getId()))
                .collect(Collectors.toList());

        final List<Long> departmentIdList = userDepartmentService.getAuthorizedDepartmentIdListOfUser(user, org, false);

        // 로그인 시 추가로 가져올 정보를 가진 객체
        final WebUserDetails newWebUserDetails = new WebUserDetails(
                user.getEmail(),    // 사용자 이름
                user.getPassword(), // 사용자 비밀번호
                true,
                true,
                true,
                true,
                auth,
                user.getUserId(),
                user.getName(),
                user.getOrganizationId(),
                Optional.ofNullable(org).map(Organization::getOrganizationName).orElse(null),
                Optional.ofNullable(org).map(Organization::getCodeName).orElse(null),
                departmentIdList,
                authcode,
                Boolean.TRUE.equals(user.getReadonly())
        );

        return newWebUserDetails;
    }

}