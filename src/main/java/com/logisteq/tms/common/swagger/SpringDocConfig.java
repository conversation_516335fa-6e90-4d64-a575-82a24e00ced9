package com.logisteq.tms.common.swagger;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.GroupedOpenApi;
import org.springdoc.core.customizers.OpenApiCustomiser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;

import java.util.Map;
import java.util.TreeMap;

@Configuration
public class SpringDocConfig {

    private static final String SECURITY_SCHEME_NAME = "JWT Authentication";
    
    @Value("${server.port:8080}")
    private int serverPort;

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("ALOA API")
                        .description("IoT API Docs"))
                .addServersItem(new Server()
                        .description("Current Server")
                        .url("http://localhost:" + serverPort))
                .addServersItem(new Server()
                        .description("Development")
                        .url("https://aloa-dev.logisteq.com"))
                .addSecurityItem(new SecurityRequirement().addList(SECURITY_SCHEME_NAME))
                .components(new Components()
                        .addSecuritySchemes(SECURITY_SCHEME_NAME,
                                new SecurityScheme()
                                        .name(HttpHeaders.AUTHORIZATION)
                                        .description("A JWT token is required to access this API. JWT token can be obtained by providing correct username and password in the User API.")
                                        .type(SecurityScheme.Type.HTTP)
                                        .in(SecurityScheme.In.HEADER)
                                        .scheme("Bearer")
                                        .bearerFormat("JWT"))
                );
    }

//    @Bean
//    public GroupedOpenApi groupedOpenApi() {
//
//        final String[] packagesToScan = {
//                "com.logisteq.tms.delivery.controller",
//                "com.logisteq.tms.rider.controller",
////                "com.logisteq.tms.openapi.controller",
//                "com.logisteq.tms.product.controller",
//                "com.logisteq.tms.project.controller",
//                "com.logisteq.tms.user.controller",
//                "com.logisteq.tms.vehicle.controller",
//                "com.logisteq.tms.incident.controller",
//                "com.logisteq.tms.app.controller",
//                "com.logisteq.tms.auth.controller",
////                "com.logisteq.tms.external.kep.controller",
//        };
//
//        return GroupedOpenApi.builder()
//                .consumesToMatch("application/json;charset=UTF-8", "application/x-www-form-urlencoded")
//                .producesToMatch("application/json;charset=UTF-8")
//                .group("default")
//                .packagesToScan(packagesToScan)
//                .build();
//    }

    @Bean
    public OpenApiCustomiser sortSchemasAlphabetically() {

        return openApi -> {
            final Map<String, Schema> schemas = openApi.getComponents().getSchemas();
            openApi.getComponents().setSchemas(new TreeMap<>(schemas));
        };
    }

}