package com.logisteq.tms.common.logback;

import ch.qos.logback.classic.PatternLayout;
import com.logisteq.tms.common.constant.CommonConstant;

// logback-spring.xml에서 다음과 같이 사용할 수 있다.
//<layout class="com.logisteq.tms.common.logback.LogbackPatternLayout">
//    <pattern>%user %sessionId</pattern>
//</layout>
// 현재는 이 방식이 아니라, ApiRequestIntercepter에서 MDC에 sessionId를 설정하는 방식을 사용한다.
@Deprecated
public class LogbackPatternLayout extends PatternLayout {

    static {
        PatternLayout.defaultConverterMap.put(CommonConstant.HTTP_USER_ATTR_NAME, LogbackUserConverter.class.getName());
        PatternLayout.defaultConverterMap.put(CommonConstant.SESSION_ID_ATTR_NAME, LogbackSessionIdConverter.class.getName());
    }

}
