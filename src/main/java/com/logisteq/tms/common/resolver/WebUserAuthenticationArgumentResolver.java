package com.logisteq.tms.common.resolver;

import com.logisteq.common.exception.CustomException;
import com.logisteq.tms.common.security.WebUserDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@Slf4j
@Component
public class WebUserAuthenticationArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {

        final Class<?> parameterType = parameter.getParameterType();
        final AuthenticationPrincipal authenticationPrincipalAnnotation = parameter.getParameterAnnotation(AuthenticationPrincipal.class);
        return WebUserDetails.class.isAssignableFrom(parameterType) && authenticationPrincipalAnnotation != null;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter,
                                  ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest,
                                  WebDataBinderFactory binderFactory) throws Exception {

        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (Objects.nonNull(authentication)) {
            final Object principal = authentication.getPrincipal();
            if (principal instanceof WebUserDetails) {
                return principal;
            }
        }

        final HttpServletRequest request = (HttpServletRequest) webRequest.getNativeRequest();
        log.error("사용자 인증 실패 {} {} - {}", request.getMethod(), request.getRequestURI(), parameter.toString());

        throw new CustomException(HttpStatus.UNAUTHORIZED, "로그아웃 상태이니 로그인하세요.", false);
    }

}
