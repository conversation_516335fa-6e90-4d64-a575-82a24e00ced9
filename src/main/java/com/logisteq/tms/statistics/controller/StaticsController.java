package com.logisteq.tms.statistics.controller;

import com.logisteq.tms.statistics.dto.StatisticsRouteItemDTO;
import com.logisteq.tms.statistics.service.StatisticsBasicProcessorService;
import com.logisteq.tms.statistics.utility.StatisticsVehicleConstData;
import com.logisteq.tms.web.constant.WebUrl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping
public class StaticsController {

    private final StatisticsBasicProcessorService statisticService;

    @Autowired
    public StaticsController(final StatisticsBasicProcessorService statisticService) {

        this.statisticService = statisticService;
    }

    /**
     * 수집된 통계를 바탕으로 route와 EnergyGauge 반환함
     * @param riderId  Rider id
     * @return path정보와 energyGauge,velocity 정보가 들어있는 데이터 리스트
     */
    @PostMapping(value = WebUrl.WEB + "/statistics/get-path" )
    public List<StatisticsRouteItemDTO> getEnergyGaugeRoute(@RequestParam final Long riderId , @RequestParam final Long projectId ) {
        return statisticService.getEnergyGaugeRoute(riderId, projectId );
    }


    /**
     * 모든 Rider의 통계를 수집하는 프로세스 실행
     */
    @PostMapping(value = WebUrl.WEB + "/statistics/collect-statics-all" )
    public void collectStatisticsByProjectId( @RequestParam final Long projectId ) {
        statisticService.processStatisticsAllRider(projectId);
        statisticService.removeFailedOrRunningBasicItems(projectId);
    }


    /**
     * 하니의 Rider 통계를 수집하는 프로세스 실행
     */
    @PostMapping(value = WebUrl.WEB + "/statistics/collect-statics" )
    public Long collectStatisticsByRider(@RequestParam final Long riderId , @RequestParam final Long projectId , @RequestParam final String riderType ) {
        statisticService.removeFailedOrRunningByRider(riderId, projectId);
        statisticService.processStatisticsRiderId(riderId, projectId, riderType);
        return riderId;
    }


    /**
     * 하니의 Rider 통계를 수집하는 프로세스 실행. 이전에 모두 삭제하고 실행함. 이것은 테스트 용도이다.
     */
    @PostMapping(value = WebUrl.WEB + "/statistics/collect-statics-test" )
    public Long collectForceBaseStatisticsRider(@RequestParam final Long riderId, @RequestParam final Long projectId , @RequestParam final String riderType ) {
        statisticService.removeAllByRider(riderId, projectId );// 테스트용 모두 지우고 할때
        statisticService.processStatisticsRiderId(riderId, projectId, riderType);
        return riderId;
    }



    /**
     * 하니의 Rider 통계 데이터를 모두 삭제한다.
     */
    @PostMapping(value = WebUrl.WEB + "/statistics/delete-statics" )
    public Long deleteAllByRider(@RequestParam final Long riderId , @RequestParam final Long projectId ) {
        statisticService.removeAllByRider(riderId, projectId );
        return riderId;
    }


    /**
     * 하니의 Rider 통계 데이터를 모두 삭제한다.O
     */
    @PostMapping(value = WebUrl.WEB + "/statistics/delete-statics-all" )
    public void deleteAllByProjectId( @RequestParam final Long projectId ) {
        statisticService.removeAllByProjectId(projectId);
        // TODO: 상현 수석 검토 필요 riderLocationRepository.deleteByProjectId(projectId);
    }

    /**
     * 차량 타입(Trunk,Bike,Pedestrian,EV)별로 vehicleConst값을 불러온다.
     */
    @PostMapping(value = WebUrl.WEB + "/statistics/get-vehicle-info/type" )
    public StatisticsVehicleConstData getVehicleInfoByType( @RequestParam final String riderType   ) {
        return statisticService.getStatisticsVehicleConstDataByRiderType( riderType );
    }

}
