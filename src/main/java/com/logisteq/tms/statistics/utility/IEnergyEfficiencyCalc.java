package com.logisteq.tms.statistics.utility;

import org.locationtech.jts.geom.Coordinate;

import java.time.LocalDateTime;

public interface IEnergyEfficiencyCalc {

    void setStatisticsVehicleConstData( StatisticsVehicleConstData s );

    double getEnergyEfficiency(double velocity);

    void setCoordinates (Coordinate current, Coordinate prev, LocalDateTime time, LocalDateTime prevTime );

    double getEnergyEstimatedEfficiency(double meters, double seconds);//EV_Demo
}
