package com.logisteq.tms.statistics.utility;

/**
 * Convert EnergyConsumtion.cpp/h
 */
public class EnergyConsumptionUtil {

    public static class  FORCE_FACTOR {
        double vehMass; //	1741
        double veh_drag_coef; // 0.36
        double veh_frontial_area; // 2.42
        double rr_coef; // 0.011
        double drive_train_eff;
        double engine_eff;
    };

    public static class  TRACE_DATA {
        double heading;
        double reliability;
        long prev_x;
        long prev_y;
        long prev_z;
        long next_x;
        long next_y;
        long next_z;
        double velocity; // m/s
        double delta_time; // sec;
    };

    public static class  CONS_ENERGY
    {
        double wh_power;
        double wh_force;
    };

    private static double get_road_slope( long x1, long y1, long z1, long x2, long y2, long z2)
    {

        double road_slope = 0.0d;
        // calc real distance in Meter
        long iDistance = (long) Math.sqrt((x2- x1) + (y2 - y1));
        long iHeight = (z2 - z1) ;

        if( iDistance == 0 ) //No divide by 0
            return 0;

        return road_slope =   Math.atan(iHeight /  iDistance);
    }

    private static double get_air_density(int C)
    {
        double veh_ro = 0;
        double f_c = Math.round((C / 10));
        f_c = f_c * 10;
        C = (int)f_c;
        switch (C) {
            case 35: veh_ro = 1.1455; break;
            case 30: veh_ro = 1.1644; break;
            case 25: veh_ro = 1.1839; break;
            case 20: veh_ro = 1.2041; break;
            case 15: veh_ro = 1.2250; break;
            case 10: veh_ro = 1.2466; break;
            case 5: veh_ro = 1.2690; break;
            case 0: veh_ro = 1.2922; break;
            case -5: veh_ro = 1.3163; break;
            case -10: veh_ro = 1.3413; break;
            case -15: veh_ro = 1.3673; break;
            case -20: veh_ro = 1.3943; break;
            case -25: veh_ro = 1.4224; break;
            default:
                veh_ro = 1.2041; break;
        }
        return veh_ro;
    }
    private static double get_rolling_resistance_coef(int typeTireRoad)
    {
        double crr = 0.0;
        switch (typeTireRoad) {
            case 1: crr = 0.002; break; // Bicycle_concrete
            case 2: crr = 0.004; break; // Bicycle_Asphalt
            case 3: crr = 0.01; break;  // Motor Bike
            case 4: crr = 0.015; break;  // Car Tire on Asphalt
            case 5: crr = 0.04; break;  // Truck Tire
            default:
                crr = 0.02; break;
        }
        return crr;
    }
    private static double get_air_drag_coef(int veh_type, int veh_size, int fuel_type )
    {
        if (veh_type < 1 || veh_type > 3)
            return 0;
        if (veh_size < 1 || veh_size > 3)
            return 0;
        if (fuel_type < 1 || fuel_type > 3)
            return 0;
        double drag_coef = 0.0;

        switch (veh_type) {
            case 1:
                if (veh_size == 1) {
                    if (fuel_type == 1) drag_coef = 0.28;
                    else if (fuel_type == 2) drag_coef = 0.3;
                    else if (fuel_type == 3) drag_coef = 0.28;
                }
                else if (veh_size == 2) {
                    if (fuel_type == 1) drag_coef = 0.3;
                    else if (fuel_type == 2) drag_coef = 0.32;
                    else if (fuel_type == 3) drag_coef = 0.3;
                }

                else if (veh_size == 3) {
                    if (fuel_type == 1) drag_coef = 0.32;
                    else if (fuel_type == 2) drag_coef = 0.34;
                    else if (fuel_type == 3) drag_coef = 0.32;

                }

                break;
            case 2 :
                if (veh_size == 1) {
                    if (fuel_type == 1) drag_coef = 0.33;
                    else if (fuel_type == 2) drag_coef = 0.32;
                    else if (fuel_type == 3) drag_coef = 0.33;
                }
                else if (veh_size == 2) {
                    if (fuel_type == 1) drag_coef = 0.34;
                    else if (fuel_type == 2) drag_coef = 0.35;
                    else if (fuel_type == 3) drag_coef = 0.33;
                }
                else if (veh_size == 3) {
                    if (fuel_type == 1) drag_coef = 0.36;
                    else if (fuel_type == 2) drag_coef = 0.38;
                    else if (fuel_type == 3) drag_coef = 0.35;
                }
                break;
            case 3 :
                if (veh_size == 1) {
                    if (fuel_type == 1) drag_coef = 0.4;
                    else if (fuel_type == 2) drag_coef = 0.42;
                    else if (fuel_type == 3) drag_coef = 0.38;
                }
                else if (veh_size == 2) {
                    if (fuel_type == 1) drag_coef = 0.42;
                    else if (fuel_type == 2) drag_coef = 0.45;
                    else if (fuel_type == 3) drag_coef = 0.4;
                }
                else if (veh_size == 3) {
                    if (fuel_type == 1) drag_coef = 0.45;
                    else if (fuel_type == 2) drag_coef = 0.5;
                    else if (fuel_type == 3) drag_coef = 0.45;
                }
                break;

        }
        return drag_coef;
    }
    // Fi
    private static double calc_InertialForce(double kg_VehicleMass, double m_DistDiff, double s_TimeDiff)
    {
        if (s_TimeDiff <= 0)
            return 0;
        return kg_VehicleMass * (m_DistDiff / s_TimeDiff);
    }
    // Fs = mv⋅g⋅sin(as)
    private static double calc_SlopeForce(double kg_VehicleMass, double rad_Slope) {
        return kg_VehicleMass * Math.sin(rad_Slope);
    }
    //Fr = mv ⋅g⋅crr ⋅cos(αs)
    private static double calc_FrictionForce(double kg_VehicleMass, double G, double Crr, double rad_Slope) {
        return kg_VehicleMass * 9.81 * Crr * Math.cos(rad_Slope);
    }
    /*ρ[kg / m3] – air density at 20 °C
    cd[-] – air drag coefficient
    A[m2] – vehicle frontal area
    vv[m/s] – vehicle speed
    */
    private static double calc_AirdragForce(double kg_VehicleMass, double AirDensity, double AirDrag, double m2_FrontialArea, double mps_Velocity) {
        return 0.5 * kg_VehicleMass * AirDensity * AirDrag * m2_FrontialArea * Math.sqrt(mps_Velocity);
    }
    public static boolean set_vehicle_factor(FORCE_FACTOR f_factor, double veh_mass, int veh_type, int veh_size, int fuel_type, int wheel_type, int tire_type, double veh_frontial_area)
    {
        if (fuel_type > 3 && fuel_type < 1)
            return false;
        if (wheel_type > 3 && wheel_type < 1)
            return false;


        f_factor.veh_drag_coef = get_air_drag_coef(veh_type, veh_size, fuel_type);
        f_factor.rr_coef = get_rolling_resistance_coef(tire_type);
        f_factor.vehMass = veh_mass;
        f_factor.veh_frontial_area = veh_frontial_area;


        if (fuel_type == 1)
            f_factor.engine_eff = 0.19;
        else if (fuel_type == 2)
            f_factor.engine_eff = 0.43;
        else if (fuel_type == 3)
            f_factor.engine_eff = 0.10;
        if (wheel_type == 3)
            f_factor.drive_train_eff = 0.75;
        else f_factor.drive_train_eff = 0.8;


        return true;
    }
    private static CONS_ENERGY calc_Power_Consumption(FORCE_FACTOR f_factor, TRACE_DATA t_data, double celcius)
    {
        CONS_ENERGY cons_enrg = new CONS_ENERGY(); //memset(&cons_enrg, 0, sizeof(CONS_ENERGY));

        double Distance = Math.sqrt((t_data.next_x - t_data.prev_x) * (t_data.next_x - t_data.prev_x) + (t_data.next_x - t_data.prev_x) * (t_data.next_x - t_data.prev_x));

        double air_density = get_air_density( (int) celcius);
        double road_slope = get_road_slope(t_data.prev_x, t_data.prev_y, t_data.prev_z, t_data.next_x, t_data.next_y, t_data.next_z);

        double fr = calc_FrictionForce(f_factor.vehMass, 9.81, f_factor.rr_coef, road_slope);
        double fi = calc_InertialForce(f_factor.vehMass, Distance, t_data.delta_time);
        double fa = calc_AirdragForce(f_factor.vehMass, air_density, f_factor.veh_drag_coef, f_factor.veh_frontial_area, t_data.velocity);
        double fs = calc_SlopeForce(f_factor.vehMass, road_slope);

        cons_enrg.wh_force = fi + fr + fa + fs;
        cons_enrg.wh_power = cons_enrg.wh_force * t_data.velocity;
        return cons_enrg;
    }
    private static double calc_Generatable_Power(FORCE_FACTOR f_factor, double mps/*KMH*/, double WHPL)
    {
//        double mps = KMH / 3.6;
        return WHPL * mps * f_factor.drive_train_eff * f_factor.engine_eff;
    }
    public static double calc_Driving_Efficiency(FORCE_FACTOR f_factor, TRACE_DATA t_data, double celcius,  double MPS/*KMH*/,  double WHPL)
    {
        double gen_Power = calc_Generatable_Power(f_factor, MPS/*KMH*/, WHPL);
        CONS_ENERGY cons_enrg = calc_Power_Consumption(f_factor, t_data, celcius);

        if( cons_enrg.wh_power == 0 )
            return 0.0;

        return ( gen_Power / cons_enrg.wh_power ) * 100;
    }
}
