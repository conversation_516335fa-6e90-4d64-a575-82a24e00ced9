package com.logisteq.tms.statistics.dto;

import com.logisteq.tms.statistics.domain.StatisticsCarCharacteristics;
import com.logisteq.tms.statistics.domain.suppl.*;
import lombok.*;


@Deprecated
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class StatisticsCarCharacteristicsDTO {

    private Long id;

    CarType carType;
    CarSize carSize;
    CarFuelType carFuelType;
    CarWheelType carWheelType;
    CarTireType  carTireType;

    /**
     * 차량 에너지 밀도 Energy Density
     */
    double energyDensity;

    /**
     * 차량 무게  Vehicle Mass
     */
    double mass;

    /**
     * 엔진 효율  Engine Efficiency
     */
    double efficiencyEngine;

    /**
     * 구동 효율 DriveTran Efficiency
     */
    double efficiencyDriveTrain;

    /**
     * 타이어 마찰 계수 Rolling Resistance Coefficient
     */
    double rollingResistanceCoefficient ;

    /**
     * 차량 전면 면적  FrontalArea
     */
    double frontalArea ;

    /**
     * 차량 공기 저항  Air Drag Coefficient
     */
    double dragCoefficient ;


    //TODO:  다른 소스 부분에 convertDtoToDomain 도 나중에 DTO로 이동하고 dtoToDomain으로 변경하는게 좋겠다.
    public StatisticsCarCharacteristics dtoToDomain(){
        return StatisticsCarCharacteristics.builder()
                .id(id)
                .carType(carType)
                .carSize(carSize)
                .carFuelType(carFuelType)
                .carWheelType(carWheelType)
                .energyDensity(energyDensity)
                .carTireType(carTireType)
                .mass(mass)
                .efficiencyDriveTrain(efficiencyDriveTrain)
                .efficiencyEngine(efficiencyEngine)
                .rollingResistanceCoefficient(rollingResistanceCoefficient)
                .frontalArea(frontalArea)
                .dragCoefficient(dragCoefficient)
                .build();
    }
}
