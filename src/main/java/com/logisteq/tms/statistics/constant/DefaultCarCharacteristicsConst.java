package com.logisteq.tms.statistics.constant;

import com.logisteq.tms.statistics.domain.StatisticsCarCharacteristics;
import com.logisteq.tms.statistics.domain.suppl.*;
import com.logisteq.tms.statistics.dto.StatisticsCarCharacteristicsDTO;
import com.logisteq.tms.statistics.dto.StatisticsCarCharacteristicsDTO.StatisticsCarCharacteristicsDTOBuilder;

/*
    * 값이 존재 하지 않을때 기본 사용되는 값 생성
 */
@Deprecated
public class DefaultCarCharacteristicsConst {

    public static StatisticsCarCharacteristics getObj(){
        return DefaultCarCharacteristicsConst.getBuilder().build().dtoToDomain();
    }

    public static StatisticsCarCharacteristicsDTO getDTO(){
        return DefaultCarCharacteristicsConst.getBuilder().build();
    }

    public static StatisticsCarCharacteristicsDTOBuilder getBuilder(){
        return StatisticsCarCharacteristicsDTO.builder()
                .carType(CarType.SEDAN)
                .carSize(CarSize.MEDIUM)
                .carFuelType(CarFuelType.GASOLINE)
                .carWheelType(CarWheelType.FRONT)
                .carTireType(CarTireType.CAR_ASPHALT)
                .mass(1908)
                .efficiencyDriveTrain(0.2f)
                .efficiencyEngine(0.8f)
                .rollingResistanceCoefficient(0.015f)
                .energyDensity(8864)
                .frontalArea(2.42f)
                .dragCoefficient(0.36f);

    }

}
