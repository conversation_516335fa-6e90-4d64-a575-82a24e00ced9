package com.logisteq.tms.statistics.domain;


import com.logisteq.tms.statistics.domain.suppl.*;
import com.logisteq.tms.vehicle.domain.VehicleModel;
import lombok.*;

import javax.persistence.*;


/**
 *
 * 더이상 사용되지 않음 - 삭제 예정
 */
@Deprecated
@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class StatisticsCarCharacteristics {
    @Id
    @Column(name="id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column ( nullable= false)
    @Enumerated(EnumType.STRING)
    CarType carType;

    @Column ( nullable= false)
    @Enumerated(EnumType.STRING)
    CarSize carSize;

    @Column ( nullable= false)
    @Enumerated(EnumType.STRING)
    CarFuelType carFuelType;

    @Column ( nullable= false)
    @Enumerated(EnumType.STRING)
    CarWheelType carWheelType;

    @Column ( nullable= false)
    @Enumerated(EnumType.STRING)
    CarTireType carTireType;

    @ManyToOne
    @JoinColumn(name = "vehicle_model_id" )
    private VehicleModel vehicleModel;

    /**
     * 차량 에너지 밀도 Energy Density
     */
    double energyDensity;

    /**
     * 차량 무게  Vehicle Mass
     */
    double mass;

    /**
     * 엔진 효율  Engine Efficiency
     */
    double efficiencyEngine;

    /**
     * 구동 효율 DriveTran Efficiency
     */
    double efficiencyDriveTrain;

    /**
     * 타이어 마찰 계수 Rolling Resistance Coefficient
     */
    double rollingResistanceCoefficient ;

    /**
     * 차량 전면 면적  FrontalArea
     */
    double frontalArea ;

    /**
     * 차량 공기 저항  Air Drag Coefficient
     */
    double dragCoefficient ;

}
