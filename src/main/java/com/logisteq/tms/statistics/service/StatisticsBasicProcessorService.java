package com.logisteq.tms.statistics.service;


import com.logisteq.common.constant.Constant;
import com.logisteq.common.dto.CoordinateDTO;
import com.logisteq.common.dto.coreapi.link.GetLinkIdxRequestInfo;
import com.logisteq.common.dto.coreapi.link.GetLinkVtxRequestInfo;
import com.logisteq.common.dto.coreapi.link.GetLinkVtxResponseInfo;
import com.logisteq.common.dto.track.TrackRiderLocationDTO;
import com.logisteq.common.dto.track.TrackRiderLocationsDTO;
import com.logisteq.common.feign.coreapi.LinkServiceClient;
import com.logisteq.common.util.GeometryUtil;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.repository.RiderRepository;
import com.logisteq.tms.statistics.domain.StatisticsBasic;
import com.logisteq.tms.statistics.domain.StatisticsCarCharacteristics;
import com.logisteq.tms.statistics.domain.StatisticsGenerateRecord;
import com.logisteq.tms.statistics.domain.suppl.BasicStatisticsItem;
import com.logisteq.tms.statistics.domain.suppl.GenerateStatus;
import com.logisteq.tms.statistics.dto.StatisticsRouteItemDTO;
import com.logisteq.tms.statistics.repository.StatisticsBasicRepository;
import com.logisteq.tms.statistics.repository.StatisticsGenerateRecordRepository;
import com.logisteq.tms.statistics.utility.*;
import com.logisteq.tms.vehicle.domain.Vehicle;
import com.logisteq.tms.vehicle.domain.VehicleDriveInfo;
import com.logisteq.tms.vehicle.domain.VehicleModel;
import com.logisteq.tms.vehicle.repository.VehicleDriveInfoRepository;
import com.logisteq.tms.vehicle.repository.VehicleModelRepository;
import com.logisteq.tms.vehicle.repository.VehicleRepository;
import com.logisteq.tms.vehicle.service.VehicleModelService;
import com.logisteq.tms.vehicle.service.VehicleService;
import com.logisteq.tms.vehicle.types.CargoType;
import com.logisteq.tms.vehicle.types.FuelType;
import com.logisteq.tms.vehicle.types.WheelDrvType;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.LineString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import static com.logisteq.tms.statistics.constant.EcoConstant.DEFAULT_STATISTICS_DISTANCE_UNIT;
import static com.logisteq.tms.statistics.constant.EcoConstant.LINK_API_RANGE;

@Slf4j
@Validated
@Service
public class StatisticsBasicProcessorService {

    private final StatisticsGenerateRecordRepository statisticsGenerateRecordRepository;
    private final StatisticsBasicRepository statisticsBasicRepository;
    private final RiderRepository riderRepository;
    private final LinkServiceClient linkServiceClient;
    private final VehicleService vehicleService;
    private final VehicleModelService vehicleModelService;
    private final VehicleDriveInfoRepository vehicleDriveInfoRepository;
    private final VehicleRepository vehicleRepository;
    private final VehicleModelRepository vehicleModelRepository;

    /**
     * 사용되지 않음
     */
    StatisticsCarCharacteristics statisticsCarCharacteristics;

    private int unitDistance = DEFAULT_STATISTICS_DISTANCE_UNIT;

    @Autowired
    public StatisticsBasicProcessorService(final StatisticsGenerateRecordRepository statisticsGenerateRecordRepository,
                                           final StatisticsBasicRepository statisticsBasicRepository,
                                           final RiderRepository riderRepository,
                                           final LinkServiceClient linkServiceClient,
                                           final VehicleService vehicleService,
                                           final VehicleModelService vehicleModelService,
                                           final VehicleDriveInfoRepository vehicleDriveInfoRepository,
                                           final VehicleRepository vehicleRepository,
                                           final VehicleModelRepository vehicleModelRepository) {

        this.statisticsGenerateRecordRepository = statisticsGenerateRecordRepository;
        this.statisticsBasicRepository = statisticsBasicRepository;
        this.riderRepository = riderRepository;
        this.linkServiceClient = linkServiceClient;
        this.vehicleService = vehicleService;
        this.vehicleModelService = vehicleModelService;
        this.vehicleDriveInfoRepository = vehicleDriveInfoRepository;
        this.vehicleRepository = vehicleRepository;
        this.vehicleModelRepository = vehicleModelRepository;
    }

    public void setUnitDistance(int unitDistance) {
        this.unitDistance = unitDistance;
    }


    /**
     * 중간에 실패한 통계 데이터는 삭제 한다. 현재 running중일때 상용 하면 이미 계산 중인 데이터가 삭제 될수도 있으니 이부분은 고려 한다.
     * 현재 통계 수집이 끝나는 경우에만
     * @return
     */
    public boolean checkAndRemoveFailedBasicItems(StatisticsGenerateRecord savedRecord ){

        return true;
    }


    boolean deleteAllByRecordId( StatisticsGenerateRecord record ){
//        statisticsBasicRepository.deleteAllByRecordId(record.getId()); // 이건 exception 던진다. 왜 인지 모르겠다.
        List<StatisticsBasic> findList = statisticsBasicRepository.findByRecordId(record.getId());
        statisticsBasicRepository.deleteAll(findList);
        return true;
    }

    /**
     * Running하고 있는 Record 를 찾아서 이것을 삭제 한다. running중간에 멈췄을 경우에 대한 처리이다.     *
     * @return
     */
    public boolean removeBasicItemRunningRecordByRider(long riderId , long projectId, GenerateStatus status ){
        List<StatisticsGenerateRecord> findList =  statisticsGenerateRecordRepository.findByRiderIdAndProjectIdAndStatus( riderId, projectId, status );
        findList.forEach(record -> {
            deleteAllByRecordId( record );
            statisticsGenerateRecordRepository.delete(record);
        });
        return true;
    }

    public boolean removeFailedOrRunningByRider(long riderId , long projectId ) {
        removeBasicItemRunningRecordByRider( riderId, projectId, GenerateStatus.FAILED );
        removeBasicItemRunningRecordByRider( riderId, projectId, GenerateStatus.RUNNING );
        return true;
    }

    public boolean removeAllByRider(long riderId , long projectId ) {
        removeBasicItemRunningRecordByRider( riderId, projectId, GenerateStatus.DONE );
        removeBasicItemRunningRecordByRider( riderId, projectId, GenerateStatus.FAILED );
        removeBasicItemRunningRecordByRider( riderId, projectId, GenerateStatus.RUNNING );
        return true;
    }


    public boolean removeAllByProjectId( long projectId ) {
        List<StatisticsGenerateRecord> findList =  statisticsGenerateRecordRepository.findByProjectId(  projectId );
        findList.forEach(record -> {
            deleteAllByRecordId( record );
            statisticsGenerateRecordRepository.delete(record);
        });
        return true;
    }

    public boolean removeAll(){
        statisticsBasicRepository.deleteAll();
        statisticsGenerateRecordRepository.deleteAll();
        return true;
    }
    /**
     * Running/Failed 하고 있는 Record 를 찾아서 이것을 삭제 한다. running중간에 멈췄을 경우에 대한 처리이다.     *
     * @return
     */
    public boolean removeFailedOrRunningBasicItems( long projectId){
        List<StatisticsGenerateRecord> findList =  statisticsGenerateRecordRepository.findByStatusAndProjectId(GenerateStatus.RUNNING, projectId );
        findList.forEach(record -> {
            deleteAllByRecordId( record );
            statisticsGenerateRecordRepository.delete(record);
        });
        return true;
    }

    /**
     * Rider Type 정보로 vehicle정보를 얻어 온다
     * @param riderType
     * @return StatisticsVehicleConstData
     */
    public StatisticsVehicleConstData getStatisticsVehicleConstDataByRiderType(String riderType ){
        VehicleDriveInfo findVehicleInfo = getVehicleInfoByRiderType(riderType);
        StatisticsVehicleConstData constData = convertVehicleInfoToConstData(findVehicleInfo);
        return constData;
    }

    /**
     * Rider 정보로 vehicle정보를 얻어 올수 없는 경우에는 RiderType으로 값을 불러온다. 임시 함수이다.
     * @param riderType
     * @return
     */
    private IEnergyEfficiencyCalc getEnergyEfficiencyCalcByRiderType(String riderType ){
        IEnergyEfficiencyCalc energyEfficiencyCalc= null;
        StatisticsVehicleConstData constData = getStatisticsVehicleConstDataByRiderType(riderType);
        return constData != null ? new EnergyEfficiencyCalc( constData ) : null;
    }

    /**
     *  RiderType으로 VehicleInfo를 불러온. 임시 함수이다.
     * @param riderType
     * @return
     */
    private VehicleDriveInfo getVehicleInfoByRiderType(String riderType ){
        AtomicReference<VehicleDriveInfo> findVehicleInfo = new AtomicReference<>();

//        vehicleModelRepository.findOneByModelName(riderType).ifPresent(vehicleModel -> {
//            vehicleRepository.findOneByVehicleModel(vehicleModel).ifPresent(vehicle -> {
//                vehicleDriveInfoRepository.findOneByVehicleId(vehicle.getVehicleId()).ifPresent(findVehicleInfo::set);
//            });
//        });

        VehicleModel vehicleModel = null;
        List<VehicleModel>  modelList = vehicleModelRepository.findByModelNameOrderByCreateAtDesc( riderType );
        if( modelList.size() > 0 ){
            vehicleModel = modelList.get(0);
        }

        if( vehicleModel != null ){
             vehicleRepository.findOneByVehicleModel(vehicleModel).ifPresent(vehicle -> {
                 vehicleDriveInfoRepository.findOneByVehicleId(vehicle.getVehicleId()).ifPresent(findVehicleInfo::set);
             });
        }

        return findVehicleInfo.get() ;
    }

    /**
     * Rider에 맞는 Vehicle을 조사하여 이에 맞는 상수 값이 세팅이 된 Efficiency계산 클래스를 반환한다.
     * @param rider
     * @return  Efficiency 계산 클래스
     */
    private IEnergyEfficiencyCalc getEnergyEfficiencyCalc(Rider rider , String riderType ){

        IEnergyEfficiencyCalc energyEfficiencyCalc = null;

        //TODO: riderId를 통해서 vehicleInfo를 불러온다.
        //임시로    riderType으로 미리 고정된 값을 사용한다.
        energyEfficiencyCalc = getEnergyEfficiencyCalcByRiderType(riderType);

        if( energyEfficiencyCalc == null ){
            StatisticsVehicleConstData data = StatisticsUtil.convertStatisticsCarCharacteristicsToConstData( statisticsCarCharacteristics );
            energyEfficiencyCalc = new EnergyEfficiencyCalc(data);
        }

        return energyEfficiencyCalc;
    }

    /**
     * 해당 Rider의 locations을 수집하여 설정된 unit단위로 item 을 구성하여 DB에 저장한다.
     *
     * @param rider rider
     */
    public boolean processStatisticsRider(Rider rider, long projectId, String riderType ) {
// TODO: 상현 수석 체크 필요
    	
//        //Generation Record를 보고 어디까지 수집이 되었나 검사한다.  저장된 End Location Id부터 불러온다.
//        Long endLid = statisticsGenerateRecordRepository.findFirstByRiderIdAndProjectIdAndStatusOrderByCreatDtDesc( rider.getId(), projectId, GenerateStatus.DONE ).map(StatisticsGenerateRecord::getEndLiId ).orElse(0L);
//        List<RiderLocation> locations = riderLocationRepository.findByRiderIdAndProjectIdAndIdGreaterThanOrderByIdAsc(rider.getId(), projectId, endLid );
//        if (locations.size() == 0)
//            return false;
//
//        StatisticsGenerateRecord savedRecord = statisticsGenerateRecordRepository.save(
//                StatisticsGenerateRecordDTO.builder().creatDt(now()).rider(rider).totalCnt(locations.size()).projectId(projectId)
//                        .startLiId(locations.get(0).getId())
//                        .endLiId(locations.get(locations.size() - 1).getId())
//                        .status(GenerateStatus.RUNNING)
//                        .build().dtoToDomain()
//        );
//
//        try {
//            //Efficiency 계산 클래스 구함. rider
//            IEnergyEfficiencyCalc energyEfficiency = getEnergyEfficiencyCalc(rider, riderType);
//
//            /*통계 데이터 구하기 시작 */
//            List<BasicStatisticsItem> basicItems = convertBasicItemFromLocation( locations, energyEfficiency );
//            processLinkCoordinateValidation( basicItems );//임시 코드
//            List<BasicStatisticsItem> groupingItems = groupingBasicItemsByDistanceUnit(basicItems);
//            /*통계 데이터 구하기 완료 */
//
//            saveGroupingItemsRider(rider, projectId, savedRecord, groupingItems);
//
//
//            savedRecord.setStatus(GenerateStatus.DONE);
//        }catch(Exception e){
//            savedRecord.setStatus(GenerateStatus.FAILED);
//            e.printStackTrace();
//        }finally {
//            savedRecord.setEndDt(now());
//            statisticsGenerateRecordRepository.save(savedRecord);
//        }

        return true;
    }

    /**
     * 해당 Rider의 locations을 수집하여 설정된 unit단위로 item 을 구성하여 DB에 저장한다.
     *
     * @param riderId rider
     */
    public boolean processStatisticsRiderId(long riderId, long projectId, String riderType ) {
        riderRepository.findById(riderId).ifPresent(rider -> processStatisticsRider(rider, projectId, riderType) );
        return true;
    }

    /**
     * 모든 Rider의 locations을 수집하여 설정된 unit단위로 item 을 구성하여 DB에 저장한다.
     */
    public boolean processStatisticsAllRider(Long projectId ) {
        List<Rider> riders = riderRepository.findAll();
        for (Rider rider : riders) {
            processStatisticsRider(rider, projectId, null);
        }
        return true;
    }

    /**
     * Rider의 Basic Item 데이터를 route정보와 EnergyEfficient,Velocity 정보만 리턴한다.
     */
    public List<StatisticsRouteItemDTO> getEnergyGaugeRoute(long riderId, long projectId ) {
        List<StatisticsRouteItemDTO> listRouteItem = new ArrayList<>();
        Optional<Rider> rider = riderRepository.findById(riderId);
        if (rider.isPresent()) {
            List<StatisticsBasic> basicItems = statisticsBasicRepository.findByRiderAndProjectId( rider.get(), projectId );
            for (StatisticsBasic item : basicItems) {
                StatisticsRouteItemDTO routeItem = new StatisticsRouteItemDTO();
                if (item.getRoutes() != null) {
                    routeItem.setRoutes(GeometryUtil.convertRoutesCoordinatesFromLinestring(item.getRoutes()));
                }
                routeItem.setEnergyGauge(item.getEnergyGauge());
                routeItem.setStartTime(item.getStartTime());
                routeItem.setEndTime(item.getEndTime());
                routeItem.setVelocity(item.getVelocity());
                listRouteItem.add(routeItem);
            }
        }
        return listRouteItem;
    }

    /**
     * 두개의 item 을 조합하여 속도,가속도,시간 간격, Eco Gauge를 구하여 item에 저장한다.
     *
     * @param item
     * @param prevItem
     * @return BasicStatisticsItem
     */
    private void makeBasicStaticsItemWithPreviousItem(BasicStatisticsItem item, BasicStatisticsItem prevItem, IEnergyEfficiencyCalc energyEfficiencyCalc ) {
        if( item == null || prevItem == null )
            return;

        prevItem.setEndTime( item.getStartTime() );//endTime은 굳이 저장할 필요 없지만 그냥 한다.

        //이전 값 Location으로  이동한 거리(distance), 지나간 시간(duration)을 구함
        item.setDuration(StatisticsUtil.getInterval(prevItem.getStartTime(), item.getStartTime())); // MilliSeconds
        item.setDistance(StatisticsUtil.getDistanceMeter(prevItem.getCoordinate().getX(), prevItem.getCoordinate().getY(), item.getCoordinate().getX(), item.getCoordinate().getY()));//기본 단위 : Meter

        //속도 , 가속도를 구함.
        double secDuration = item.getDuration() / (double) 1000;
        item.setVelocity(StatisticsUtil.getVelocity(item.getDistance(), secDuration));   // 속도(m/s)  = 간거리 ( distance ) / 시간 (duration) )

        item.setAcceleration(StatisticsUtil.getAcceleration(item.getVelocity(), prevItem.getVelocity(), secDuration)); // 가속도 = ( 현재 속도m/s - 이전 속도m/s ) / 시간(duration)s

        //Eco Gauge를 계산하여 넣음.
        double energyEfficiency = getEnergyEfficiency(item , prevItem, energyEfficiencyCalc );
        item.setEnergyGauge( energyEfficiency ) ;
    }

    double getEnergyEfficiency( BasicStatisticsItem item, BasicStatisticsItem prevItem, IEnergyEfficiencyCalc energyEfficiencyCalc  ){
        energyEfficiencyCalc.setCoordinates(  item.getCoordinate(), prevItem.getCoordinate(), item.getStartTime(), prevItem.getStartTime()   );
        return energyEfficiencyCalc.getEnergyEfficiency( item.getVelocity());
    }



    private boolean isLinkConnected ( List<CoordinateDTO> pnts1, List<CoordinateDTO> pnts2 ) {

        if( pnts1 == null || pnts2 == null )
            return false;

        for( CoordinateDTO p1 : pnts1){
            for( CoordinateDTO p2 : pnts2){
                if( Double.compare( p1.getX() , p2.getX() ) == 0 && Double.compare( p1.getY(), p2.getY() ) == 0 )
                    return true;
            }
        }

        return false;
    }

    private int getHitLinkPointCount(List<Integer> linkHitList){
        return (int) linkHitList.stream().filter( i ->  i > 0  ).count();
    }


    private int getHitTotalCount(List<Integer> linkHitList){
        int totalHitCnt = 0;
        for( Integer i : linkHitList){
            totalHitCnt += i;
        }
        return  totalHitCnt;
    }

    private List<Integer> initIntegerList( int size ){
        List<Integer> arr = new ArrayList<>(size);
        for( int i = 0 ; i < size ;i++){
            arr.add(i,0);
        }
        return arr;
    }


    private void insertLinkCoordinate( List<BasicStatisticsItem> itemList , int linkFirstIndex, int setLinkId , int insertPointIndex ,int direction ) {
        int linkId = setLinkId;
        int prevPointIndex = 0;
        int index = linkFirstIndex;
        do{
            BasicStatisticsItem item = itemList.get(index);
            if( item != null ) {
                    linkId = item.getLinkId();
                    int pointIndex = item.getPointIndex();
                    if ( linkId > 0 && setLinkId == linkId ) {
                        if( direction > 0 && pointIndex > insertPointIndex ){
                            CoordinateDTO p = linkServiceClient.getPointListByLinkId( linkId ).get(insertPointIndex );
                            item.getLinkCoordinateList().add( new Coordinate( p.getX(), p.getY())  );
                            break;
                        }
                    }

            }
            index++;
        }while(setLinkId == linkId && index < itemList.size() );

    }


    private void updateHitPointCount(List<BasicStatisticsItem> itemList , int linkFirstIndex, int setLinkId, int hitPointCount  ){
        int linkId = setLinkId;
        int index = linkFirstIndex;
        do{
            BasicStatisticsItem item = itemList.get(index);
            if( item != null ) {
                    linkId = item.getLinkId();
                    if ( linkId > 0 && setLinkId == linkId) {
                        item.setHitPointCount(hitPointCount);
                    }
            }

            index++;
        }while(setLinkId == linkId && index < itemList.size() );

    }

    private void addLinkCoordinate( List<BasicStatisticsItem> itemList , int linkFirstIndex, int setLinkId ){
        int linkId = setLinkId;
        int prevPointIndex = 0;
        int direction = 0;
        int index = linkFirstIndex;
        int totalPntCnt = 0;
        List<Integer> linkInfoList = null;
        do{
            BasicStatisticsItem item = itemList.get(index);
            if( item != null ) {
                    linkId = item.getLinkId();
                    if (setLinkId == linkId) {
                        totalPntCnt = item.getTotalPointCntLink();
                        if( linkInfoList == null ){
                            linkInfoList = initIntegerList(totalPntCnt);
                        }
                        linkInfoList.set(item.getPointIndex(), 1);
                        direction += ( item.getPointIndex() - prevPointIndex );
                    }
                    prevPointIndex = item.getPointIndex();

            }
            index++;
        }while(setLinkId == linkId && index < itemList.size() );

        if( direction >= 0 ){
            for( int i = 0; i < linkInfoList.size() ; i++ ){
                if( linkInfoList.get(i).equals(0)){
                    insertLinkCoordinate(itemList,linkFirstIndex,setLinkId, i, direction );
                }
            }
        }else {

        }

        updateHitPointCount(itemList, linkFirstIndex, setLinkId, totalPntCnt );

    }


    /**
     * 유효한 좌표를 검사하여 필요없는것은 지우고 빠뜨린 좌표는 넣어준다.
     * @param itemList
     */
    private void processLinkCoordinateValidation(List<BasicStatisticsItem> itemList ){
        for (int i = 1; i < itemList.size(); i++) {
            BasicStatisticsItem item = itemList.get(i);

            if( item.getLinkId() < 0 )
                continue;

            boolean isLinkConnected = item.isConnectedPrevLink() && item.isConnectedNextLink();//앞뒤 링크가 연결되었는지 체크하는 것인데.. 이거 쓰면 선이 이상하게 그려지니 이건 체크하지 않음.

            int linkTotalPointCount = item.getTotalPointCntLink();
            boolean isValidate = false;
            if( item.getHitPointCount()  > linkTotalPointCount/2 ){
                isValidate = true;
            }else if( item.getHitTotalCount() > 5 ){//3 개의 link점이 hit 되어도 유효한 것으로 판단 한다.
                isValidate = true;
            }else if( linkTotalPointCount == 2 && item.getHitTotalCount() > 3 ){
                isValidate = true;
            }

            if( !isValidate ){
                item.setLinkId(-1);
            }
        }

    }



    /**
     * Locations 의 정보를 받아서 RiderLocation 을 가공 하여 통계에 적합한 BasicStatisticsItem 리스트 변환 한다.
     * validate하지 않은 좌표값은 무시하고 저장하지 않는다
     *
     * @param locations 위치 좌표 리스트
     * @return BasicStatisticsItem 변환된 값
     */
    public List<BasicStatisticsItem> convertBasicItemFromLocation(TrackRiderLocationsDTO locations , IEnergyEfficiencyCalc energyEfficiencyCalc) throws  Exception{

        StatisticsValidateCheckUtility validateUtility = new StatisticsValidateCheckUtility();

        List<BasicStatisticsItem> itemList = null;

        BasicStatisticsItem prevItem = null;
        for (TrackRiderLocationDTO location : locations.getLocations()) {

            BasicStatisticsItem item = new BasicStatisticsItem();

            //현재 시간 및 위치 x,y저장
            item.setStartTime(location.getTimestamp());
            item.setCoordinate( new Coordinate( location.getLocation().getX(),location.getLocation().getY(), 0 ));
            item.setAngle( location.getAngle() );

            GetLinkVtxResponseInfo responseInfo = getLinkVtxResponseInfo( item.getCoordinate(), location.getAngle() );
            if( responseInfo != null){
                item.setLinkId( responseInfo.getLinkidx() );
                item.setTotalPointCntLink( responseInfo.getPnts().size() );
            }

            //GPS와 가장 가까운 Link 상의 좌표를 구한다. 매칭된 좌표
            setMatchingLinkCoordinateFromGpsCoordinate(item);

            itemList = validateUtility.getValidateItemList(item);
            prevItem = validateUtility.getLastItemInItemList(itemList);

            makeBasicStaticsItemWithPreviousItem( item, prevItem, energyEfficiencyCalc );

            setExtraLinkInfo(itemList,item);
            itemList.add(item);
        }
        //가장 많이 들어가 있는 List가 유효한 좌표를 가진 리스트이다.
        return validateUtility.getLargestSizeItemList();
    }

    /**
     * groupList아이템의 마지막 위치를 반환한다.
     * @param unifiedItem
     * @return
     */

    private Coordinate getLastCoordinateByUnifiedItem(BasicStatisticsItem unifiedItem){
        Coordinate coord = null;
        LineString routes = unifiedItem.getRoutes();
        if( routes != null && routes.getNumPoints() > 0) {
            coord = routes.getCoordinateN(routes.getNumPoints() - 1);
        }
        return coord;
    }

    /**
     * groupList 평균값을 구한 하나의 Item 으로 만들어서 리턴한다.
     *
     * @param groupList 일정 간격(예 : 100m)안에 있는  item들의 리스트들
     * @param prevGroupItemLastCoordinate 이전 그룹 아이템의 마지막 좌표. input 파라미터와 output 파라미터 동시에 사용된다.
     * @return groupList를 하나로 만든 아이템.
     */
    private BasicStatisticsItem unifyBasicStatisticsGroupItemList(List<BasicStatisticsItem> groupList, Coordinate prevGroupItemLastCoordinate) {
        BasicStatisticsItem unifiedItem = new BasicStatisticsItem();
        int itemSize = groupList.size();
        if (itemSize == 0)
            return unifiedItem;

        BasicStatisticsItem firstItem = groupList.get(0);
        BasicStatisticsItem lastItem = groupList.get(itemSize - 1);
        unifiedItem.setCoordinate(lastItem.getCoordinate());

        double totalDistance = 0;
        double totalVelocity = 0;
        double totalAcceleration = 0;
        double totalEnergyGauge = 0;
        long totalDuration = 0;
        List<Coordinate> linkCoordinateList = new ArrayList<>();
        List<Coordinate> gpsCoordinateList = new ArrayList<>();

        Coordinate prevCoordinate = firstItem.getCoordinate();

        int prevLinkId = -1;
        int linkId = - 1;
        for (BasicStatisticsItem item : groupList) {
            totalDistance += item.getDistance();
            totalVelocity += item.getVelocity();
            totalAcceleration += item.getAcceleration();
            totalDuration += item.getDuration();
            totalEnergyGauge += item.getEnergyGauge();

            //route를 세팅할때는 GPS좌표 (getCoordinate)가 아닌 link상의 Coordinate로 세팅한다.
            Coordinate routeCoordinate = null;
            linkId = item.getLinkId();
            if( linkId > 0) {//Link Id가 존재하면 link 좌표를 넣어준다.
                if( linkId != prevLinkId ) {
                    List<CoordinateDTO> linkPoints = linkServiceClient.getPointListByLinkId(linkId);
                    for (CoordinateDTO p : linkPoints) {
                        linkCoordinateList.add(new Coordinate(p.getX(), p.getY()));
                    }
                }
                prevLinkId = linkId;
            }else {//GPS 좌표를 넣어줌.
                routeCoordinate = item.getCoordinate();
                if ( Double.compare( routeCoordinate.x, prevCoordinate.x ) != 0  || Double.compare(routeCoordinate.y, prevCoordinate.y) !=0 ) {
                    gpsCoordinateList.add(StatisticsUtil.setZtoNaN(routeCoordinate));//Z에는 반드시 NaN이 들어가야 한다. 불러올대 꼬인다.
                }
            }

            if( routeCoordinate != null ) {
                prevCoordinate = routeCoordinate;
            }
        }

        List<Coordinate> selectCoordinate = null;

        if( linkCoordinateList.size() > gpsCoordinateList.size() ){
            selectCoordinate = linkCoordinateList;
        }else{
            selectCoordinate = gpsCoordinateList;
        }

        if( prevGroupItemLastCoordinate != null ) {
            List<Coordinate> newCoordList = AddPrevGroupItemCoordinate(selectCoordinate, prevGroupItemLastCoordinate);
            if( newCoordList !=null ){
                selectCoordinate = newCoordList;
            }
        }

        if ( selectCoordinate.size() > 1 ) {
//            routeCoordinates = StatisticsUtil.simplifyCoordinate(routeCoordinates);
            unifiedItem.setRoutes(GeometryUtil.convertLineStringFromCoordinates(selectCoordinate));
        }else if(linkCoordinateList.size() == 1){
            linkCoordinateList.add(StatisticsUtil.setZtoNaN(prevCoordinate));
            unifiedItem.setRoutes(GeometryUtil.convertLineStringFromCoordinates(selectCoordinate));
        }

        unifiedItem.setDistance(totalDistance);
        if(totalDuration == 0 ){
            totalDuration = StatisticsUtil.getInterval(firstItem.getStartTime(), lastItem.getStartTime()) * 1000 ; // MilliSeconds
        }
        unifiedItem.setDuration(totalDuration);
        unifiedItem.setVelocity(totalVelocity / itemSize);
        unifiedItem.setAcceleration(totalAcceleration / itemSize);
        unifiedItem.setEnergyGauge(totalEnergyGauge / itemSize);
        unifiedItem.setItemCnt(itemSize);
        unifiedItem.setStartTime(firstItem.getStartTime());
        unifiedItem.setEndTime( lastItem.getStartTime());
        unifiedItem.setLinkId(linkId);
        return unifiedItem;
    }


    /**
     *  좌표와 angle 을 입력하여 가장 가까운 link상의 좌표를 얻는다. 만약 실패하면 angle을 변형 시켜서 구한다.
     */
    GetLinkVtxResponseInfo  getLinkVtxResponseInfoUsingVariousAngle( Coordinate coordinate, int angle ) throws Exception {

        GetLinkVtxResponseInfo responseVtxInfo = getLinkVtxResponseInfo( coordinate, angle );

        //retry with angle
        if( responseVtxInfo == null ) {
            int plusAngle = angle, minusAngle = angle;
            for (int i = 0; i < 6; i++) {
                if (i % 2 == 0) {//+ 방향
                    plusAngle += 30;
                    angle = plusAngle % 360;
                } else {//-방향
                    minusAngle -= 30;
                    angle = minusAngle < 0 ?  360 + minusAngle : minusAngle;
                }

                responseVtxInfo = getLinkVtxResponseInfo(coordinate, angle);
                if (responseVtxInfo != null)
                    break;
            }
        }

        return responseVtxInfo;
    }


    /**
     *  좌표와 angle 을 입력하여 가장 가까운 link상의 좌표를 얻는다.
     */
    GetLinkVtxResponseInfo  getLinkVtxResponseInfo( Coordinate coordinate, int angle ) throws Exception {
        GetLinkVtxResponseInfo responseVtxInfo = null;

        GetLinkIdxRequestInfo requestIdxInfo = GetLinkIdxRequestInfo.GetLinkIdxBuilder().x(coordinate.getX()).y(coordinate.getY() ).angle(angle).range(LINK_API_RANGE).build();
        int linkIndex = linkServiceClient.getLinkIdx( Constant.LocationCode.KR, requestIdxInfo );

        if( linkIndex > 0 ){
            GetLinkVtxRequestInfo requestVtxInfo = GetLinkVtxRequestInfo.builder().linkidx( Integer.toString(linkIndex)).build();
            responseVtxInfo = linkServiceClient.getLinkVdx( Constant.LocationCode.KR, true, requestVtxInfo );
        }

        return responseVtxInfo;
    }


    private int prevLinkId = -1;
    private List<Integer> linkHitList = null;

    private void setPreLinkInfo(List<BasicStatisticsItem> itemList, int setLinkId, int hitPointCount, int hitTotalCount, boolean isNextLinkConnected) {
        int linkId = setLinkId;
        int index = itemList.size() - 1;
        boolean isPrevLinkConnected = false;
        if( index < 0 )
                return;
        do {
            BasicStatisticsItem item = itemList.get(index);
            if (item != null) {
                linkId = item.getLinkId();
                if (setLinkId == linkId) {
                    log.debug("[LINK_API] setPreLinkInfo point  link Id : {}, hitCount : {},  isNextLinkConnected : {}", linkId, hitPointCount, isNextLinkConnected);
                    item.setHitPointCount(hitPointCount);
                    item.setHitTotalCount(hitTotalCount);
                    item.setConnectedNextLink(isNextLinkConnected);
                    if (item.isConnectedPrevLink())
                        isPrevLinkConnected = item.isConnectedPrevLink();
                }
            }

            index--;
        } while ((linkId < 0 || (setLinkId == linkId)) && index > 0);

        if (isPrevLinkConnected) {
            setConnectedPrevLinkToPrevItem(itemList, setLinkId, isPrevLinkConnected);
        }
    }

    private void setConnectedPrevLinkToPrevItem( List<BasicStatisticsItem> itemList , int setLinkId, boolean isPrevLinkConnected  ){
        int linkId = setLinkId;
        int index = itemList.size() - 1;
        if( index < 0 )
            return;

        do{
            BasicStatisticsItem item = itemList.get(index);
            if( item != null ) {
                    linkId = item.getLinkId();
                    if (setLinkId == linkId) {
                        log.debug("[LINK_API] setConnectedPrevLinkToPrevItem point  link Id : {}, isPrevLinkConnected : {}",linkId , isPrevLinkConnected);
                        item.setConnectedPrevLink(isPrevLinkConnected);
                    }

            }
            index--;
        }while(  ( linkId < 0 || ( setLinkId == linkId ) ) && index > 0  );
    }

    void setExtraLinkInfo(  List<BasicStatisticsItem> itemList , BasicStatisticsItem item  ){
        int linkId = item.getLinkId();

        if( linkId < 0 )
            return ;

        if( linkId != this.prevLinkId ){
            if( linkHitList != null ){
                int  hitPointCount = getHitLinkPointCount(linkHitList);
                int  hitTotalCount = getHitTotalCount(linkHitList);
                boolean isLinkConnected = isLinkConnected( linkServiceClient.getPointListByLinkId( linkId ), linkServiceClient.getPointListByLinkId( this.prevLinkId ) );
                item.setConnectedPrevLink(isLinkConnected);
                setPreLinkInfo( itemList, prevLinkId, hitPointCount, hitTotalCount, isLinkConnected );
                linkHitList = null;
            }
        }

        if( linkHitList == null) {
            this.linkHitList = initIntegerList( item.getTotalPointCntLink() );
        }

        Integer hit = linkHitList.get(item.getPointIndex());//link된 좌표에 몇번 카운트 되엇는지 체크한다.
        linkHitList.set(item.getPointIndex(), ++hit );

        this.prevLinkId = linkId;

    }


    /**
     *  좌표와 angle 을 입력하여 가장 가까운 link상의 좌표와 link상의 인덱스를 넣어준다.
     */
    void  setMatchingLinkCoordinateFromGpsCoordinate( BasicStatisticsItem item ) throws Exception {
        GetLinkVtxResponseInfo responseVtxInfo = getLinkVtxResponseInfo( item.getCoordinate(), item.getAngle() );

        if( responseVtxInfo != null ){
            item.setPointIndex( getClosestPointIndex ( item.getCoordinate().getX(), item.getCoordinate().getY(), responseVtxInfo.getPnts() ) ) ;
            item.getLinkCoordinateList().add( new Coordinate(responseVtxInfo.getPnts().get(item.getPointIndex()).getX()
                    , responseVtxInfo.getPnts().get(item.getPointIndex()).getY()
                    , responseVtxInfo.getPnts().get(item.getPointIndex()).getZ()) );
        }
    }

    /**
     *  입력된 gps 좌표와 link의 배열 리스트에서 가장 가까운 인덱스를 반환한다.
     */
    int getClosestPointIndex( double gpsX, double gpsY, List<CoordinateDTO> linkPointList){
        int mostCloseIndex = -1;
        double closeDistance = Double.MAX_VALUE;

        if( linkPointList == null | linkPointList.isEmpty()){
            return -1;
        }

        for( int i = 0 ; i < linkPointList.size() ; i++){
                double distance = StatisticsUtil.getDistanceMeter(gpsX, gpsY, linkPointList.get(i).getX(), linkPointList.get(i).getY() );
//                double distance = StatisticsUtil.getDistancePoint(gpsX, gpsY, linkPointList.get(i).getX(), linkPointList.get(i).getY() );
                if( distance < closeDistance ){
                    closeDistance = distance;
                    mostCloseIndex = i;
                }
        }

        return mostCloseIndex;
    }


    /**
     * 이전 좌표 리스트에 이전 마지막 좌표가 존재하지 않으면 이전 아이템 마지막 좌표를 삽입한다.
     * @param routeCoordinates
     * @param prevGroupItemLastCoordinate
     * @return
     */
    private List<Coordinate> AddPrevGroupItemCoordinate(List<Coordinate> routeCoordinates, Coordinate prevGroupItemLastCoordinate ){
        boolean isExist = false;
        List<Coordinate> newCoordList = null;
        for (Coordinate coord : routeCoordinates) {
            if (Double.compare(coord.x, prevGroupItemLastCoordinate.x ) == 0 && Double.compare(coord.y, prevGroupItemLastCoordinate.y ) == 0 ){
                isExist = true;
                break;
            }
        }

        if( !isExist){
            newCoordList = new ArrayList<>();
            newCoordList.add(StatisticsUtil.setZtoNaN(prevGroupItemLastCoordinate));
            newCoordList.addAll(routeCoordinates);
        }

        return newCoordList;
    }

    /**
     * Unit단위로 grouping을 지어서 저장한다
     */
    private List<BasicStatisticsItem> groupingBasicItemsByDistanceUnit( List<BasicStatisticsItem> basicItemList ) {
        List<BasicStatisticsItem> staticItems = new ArrayList<>();
        List<BasicStatisticsItem> groupItemList = new ArrayList<>();
        double accumulateDistance = 0;
        Coordinate prevGroupItemLastCoordinate = basicItemList.get(0).getCoordinate();
        for (BasicStatisticsItem item : basicItemList) {
            accumulateDistance += item.getDistance();

            if ( accumulateDistance < unitDistance  || groupItemList.size() < 2 /* group 아이템의 최소 사이즈는 2개이다.  */ ) { //각 Item의 distance를 합한 값이 unitDistance 를 넘어 갈경우 하나의 Unit Group으로 처리한다. TODO:정확하게 unitDistance 으로 자르는 것은 나중에 고민
                groupItemList.add(item);
            } else { // unitDistance 을 넘어가면 이에 대한 누적된 groupList로 부터 하나의 단위의 baseItem 을 뽑아낸다.
                BasicStatisticsItem unifiedItem = unifyBasicStatisticsGroupItemList(groupItemList, prevGroupItemLastCoordinate  );
                staticItems.add(unifiedItem);

                groupItemList.clear();
                accumulateDistance = 0;
                prevGroupItemLastCoordinate = getLastCoordinateByUnifiedItem(unifiedItem);
                prevLinkId = unifiedItem.getLinkId();
            }
        }

//        if(!groupItemList.isEmpty())
        staticItems.add(unifyBasicStatisticsGroupItemList(groupItemList, prevGroupItemLastCoordinate  ));
        return staticItems;
    }


    private void saveGroupingItemsRider(Rider rider, long projectId, StatisticsGenerateRecord record, List<BasicStatisticsItem> staticItems) {
        if (staticItems != null) {
            staticItems.forEach(item -> {
                StatisticsBasic staticItem = item.convertDomain();
                staticItem.setRider(rider);
                staticItem.setRecord(record);
                staticItem.setProjectId(projectId);
                try {
                    statisticsBasicRepository.save(staticItem);
                }catch (Exception e){
                    e.printStackTrace();
                }

            });
        }
    }


    private Float getTotalVehicleMass( Vehicle vehicle ){

        VehicleModel vehicleModel = vehicle.getVehicleModel();

        float totalMass = vehicleModel.getVehicleMass();

        if( vehicle.getVehicleModel().getCargoType() == CargoType.PASSENGER){
            final float WEIGHT_ONE_PERSON = 70; // 한명 의 몸무게를  70kg으로 가정한다.
            int currentPassenger = vehicleModel.getNumberOfPassengers() - vehicle.getCurrentPassengerCapacity();//총 인원 대비. 이용 가능한 승객수
            float totalWeightPassenger = currentPassenger * WEIGHT_ONE_PERSON;
            if( totalWeightPassenger > 0 )
                totalMass += totalWeightPassenger;
        }else if( vehicle.getVehicleModel().getCargoType() == CargoType.CARGO) {
            float currentCargoWeight = vehicleModel.getMaximumPayload() - vehicle.getCurrentWeightCapacity();
            if( currentCargoWeight > 0 )
                totalMass += currentCargoWeight;
        }

        return totalMass;
    }

    public StatisticsVehicleConstData convertVehicleToConstData( Vehicle vehicle ){
        if( vehicle == null )
            return null;

        VehicleModel vehicleModel = vehicleModelService.getVehicleModel(vehicle.getVehicleModel().getVehicleModelId());

        int carType = 1;
/* TODO: VehicleType이 변경되었음.
        if( vehicleModel.getVehicleType() == VehicleType.SEDANCOUPE ){
            carType = 1;
        }else if( vehicleModel.getVehicleType() == VehicleType.HATCHBACKWAGON ){
            carType = 2;
        }else if (vehicleModel.getVehicleType() == VehicleType.SUVTRUCK ){
            carType = 3;
        }
*/

        int energyDensity = 6864;//TODO: 이건 어디에도 정의가 안되어 있다.
        int carFuelType = 1;
        if( vehicleModel.getFuelType() == FuelType.GASOLINE){
            carFuelType = 1;
        }else if( vehicleModel.getFuelType() == FuelType.DIESEL){
            carFuelType = 2;
        }else if( vehicleModel.getFuelType() == FuelType.FUELCELL){
            carFuelType = 3;
            energyDensity = 9999;
        }

        int carWheelType = 1;
        if( vehicleModel.getWheelDrvType() == WheelDrvType.FWD ){
            carWheelType = 1;
        }else if( vehicleModel.getWheelDrvType() == WheelDrvType.RWD ){
            carWheelType = 2;
        }else if( vehicleModel.getWheelDrvType() == WheelDrvType.AWD || vehicleModel.getWheelDrvType() == WheelDrvType.FOURWD ){
            carWheelType = 3;
        }

        int tireType = 4;
/* TODO: VehicleType이 변경되었음.
        if( vehicleModel.getVehicleType() == VehicleType.SEDANCOUPE ){
            tireType = 4;
        }else if( vehicleModel.getVehicleType() == VehicleType.HATCHBACKWAGON ){
            tireType = 4;
        }else if (vehicleModel.getVehicleType() == VehicleType.SUVTRUCK ){
            tireType = 5;
        }
*/

        Float totalMass = getTotalVehicleMass( vehicle );
        
        return StatisticsVehicleConstData.builder()
                .carType(carType)
                .carSize(1)
                .carFuelType(carFuelType)
                .carWheelType(carWheelType)
                .carTireType(tireType)
                .energyDensity(energyDensity)
                .frontalArea(vehicleModel.getFrontalAreaSQM())                
                .mass(totalMass)//.mass(vehicleModel.getVehicleMass())
                .efficiencyEngine((double)vehicle.getEngineEfficiency())
                .efficiencyDriveTrain((double)vehicle.getDrivetrainEfficiency())
                .rollingResistanceCoefficient((double)vehicle.getRollingResistance())
                .dragCoefficient((double)vehicle.getDragCoefficient())
                .licensePlate(vehicle.getLicensePlate())
                .build();

    }



    public StatisticsVehicleConstData convertVehicleInfoToConstData( VehicleDriveInfo vehicleInfo ){
        if( vehicleInfo == null )
            return null;

        Vehicle vehicle = vehicleService.getVehicle(vehicleInfo.getVehicleId() );
        return convertVehicleToConstData( vehicle );

    }

}
