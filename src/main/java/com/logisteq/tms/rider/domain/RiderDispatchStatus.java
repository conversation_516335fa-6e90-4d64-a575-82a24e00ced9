package com.logisteq.tms.rider.domain;

import com.logisteq.tms.rider.domain.suppl.DispatchStatus;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Builder
@EntityListeners(AuditingEntityListener.class)
@Entity
@Table(name = "rider_dispatch_status", indexes = {@Index(columnList = "riderId"), @Index(columnList = "projectId, riderId")})
public class RiderDispatchStatus {

    @Id
    @Column(name = "rds_id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 기사 아이디
     */
    @Column(nullable = false, updatable = false)
    private Long riderId;

    /**
     * 프로젝트 아이디
     */
    @Column(nullable = false, updatable = false)
    private Long projectId;

    /**
     * 배차상태 (수락, 거부)
     */
    @Convert(converter = DispatchStatus.DispatchStatusConverter.class)
    @Column(columnDefinition = DispatchStatus.COLUMN_DEFINITION)
    private DispatchStatus dispatchStatus;

    /**
     * 생성일시
     */
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createAt;
}
