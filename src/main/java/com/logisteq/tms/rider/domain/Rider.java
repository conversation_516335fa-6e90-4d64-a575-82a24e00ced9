package com.logisteq.tms.rider.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.logisteq.common.converter.PersonalInfoConverter;
import com.logisteq.tms.incident.domain.Incident;
import com.logisteq.tms.rider.domain.suppl.LinkStatus;
import com.logisteq.tms.rider.domain.suppl.WorkAuthority;
import com.logisteq.tms.rider.domain.suppl.WorkStatus;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.Email;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@ToString(of = {"id", "name", "email", "mobile"})
@Entity
@Table(name = "rider")
@SecondaryTables({
	@SecondaryTable(name="rider_info", pkJoinColumns = {		// 변경 가능한 정보는 rider_info라는 테이블에 따로 저장함
			@PrimaryKeyJoinColumn(name="rider_id")})
})
@Getter
@EqualsAndHashCode(of = {"id", "name"})
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class Rider {
	/**
	 * Rider 아이디
	 */
	@Id
	@Column(name = "rider_id", nullable = false)
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	/**
	 * 이름 (성 + 이름)
	 * 
	 */
	@Setter
	@Column(nullable = false, length = 255)
	//@ColumnTransformer(read = "PI_DECRYPT(name)", write = "PI_ENCRYPT(?)")
	@ColumnTransformer(read = "PI_DECRYPT2(name, '${encryption.key}')", write = "PI_ENCRYPT2(?, '${encryption.key}')")
	private String name;

	/**
	 * 핸드폰 번호
	 *
	 * 글자수 제한: 10 ~ 12
	 * 글자 제한: 숫자만 허용
	 */
	@Setter
	@Column(length = 255, table = "rider_info", unique = true)
//	@Size(min=10, max=11)
//	@Convert(converter = PersonalInfoConverter.class)
	//@ColumnTransformer(read = "PI_DECRYPT(mobile)", write = "PI_ENCRYPT(?)") // like 사용하기 위해서 변경함.
	@ColumnTransformer(read = "PI_DECRYPT2(mobile, '${encryption.key}')", write = "PI_ENCRYPT2(?, '${encryption.key}')")
	private String mobile;	
	
	/**
	 * 사용자 이메일
	 */
	@Setter
	@Column(unique = true, length = 255)
	@Email
	@Convert(converter = PersonalInfoConverter.class)
	private String email;
	
	
	/**
	 * 비밀번호
	 * (암호화하여 저장하므로 길이를 60으로 함)
	 */
	@Setter
	@JsonIgnore
	@Column(length = 60)
	private String password;

	// 연결상태 (연결안됨, 입력대기, 연결됨)
	@Setter
	@Column(nullable = false, table = "rider_info")
	private LinkStatus linkStatus = LinkStatus.NOT_CONNECTED;	

	/**
	 * 프로젝트 아이디 목록
	 */
	@ElementCollection
	@CollectionTable(name = "rider_project_ids", joinColumns = @JoinColumn(name = "rider_id"), indexes = {@Index(columnList = "project_id")})
	@Column(name = "project_id")
	private List<Long> projectIds = new ArrayList<Long>();

	/**
	 * rider org 현황 (1:N 양방향)
	 */
	@Setter
	@OneToMany(mappedBy = "rider")
	private List<RiderOrgStatus> orgStatusList = new ArrayList<RiderOrgStatus>();

	/**
	 * 근무 상태 (1:N 양방향)
	 */
	@OneToMany(mappedBy = "rider")
	private List<RiderWorkStatus> workStatusList = new ArrayList<RiderWorkStatus>();
	
	/**
	 * Activity (1:N 양방향)
	 */
	@OneToMany(mappedBy = "rider")
	private List<RiderActivity> activities = new ArrayList<RiderActivity>();
	
	/**
	 * 프로젝트별 기사 속성 (1:N 양방향)
	 */
	@OneToMany(mappedBy = "rider")
	private List<RiderProjectSetting> projectSettings = new ArrayList<RiderProjectSetting>();
	
	
	/**
	 * 사건/사고 상태 (1:N 양방향)
	 */
	@OneToMany(mappedBy = "rider")
	private List<Incident> incidentList = new ArrayList<Incident>();
	
	@CreationTimestamp
	@Column(name = "create_at", updatable = false)
	private LocalDateTime createAt;
	
	@UpdateTimestamp
	@Column(name = "update_at", table = "rider_info")
	private LocalDateTime updateAt;

	@Setter
	@Column(table = "rider_info")
	private String position; //직급...

	@Setter
	@Column(table = "rider_info", length = 20)
	private String employeeNumber;	// 사번

	@Setter
	@Column(table = "rider_info")
	private Integer skillLevel; //숙련도...

	@Setter
	@Column(table = "rider_info") // 자동으로 프로젝트 만들때 포합되어야 되는 라이더
	private Boolean autoCreate = false;

	@Setter
	@Column(table = "rider_info") // 자동으로 만들어지는 프로젝트에 추가될때 소유자인 UserId
	private Long ownerUserId;

	@Setter
	@Column(table = "rider_info") // 가베차를 위한 기사 flag 사용함
	private Boolean isSimulationRider = false;

	@Setter
	@Column(table = "rider_info") // 기사의 배차 번호
	private Long dispatchNumber;

	@Setter
	@Column(table = "rider_info") // 투명_탑 차에 대한 flag
	@ColumnDefault("0") //default 0
	private Boolean transparentTop = false;

	/**
	 * 주민번호 앞자리 (주민번호 앞 6자리 + 성별 1자리)
	 * 
	 */
/*
	@Setter
	@Column(length = 7)
	@Size(min=7, max=7)
	private String socialSecurityNo;
*/

	/**
	 * 근무 상태
	 */
	@Setter
	@Column(table = "rider_info", columnDefinition = "INT(2) NULL DEFAULT NULL comment '0: NONE, 1: 근무시작, 2:점심, 3:휴식, 4:근무종료'")
	private WorkStatus workStatus;

	/**
	 * 업무 권한
	 */
	@Setter
	@Column(table = "rider_info", columnDefinition = WorkAuthority.COLUMN_DEFINITION)
	private WorkAuthority workAuthority;

	/**
	 * 관리자에게 전송하는 메시지
	 */
	@OneToMany(mappedBy = "rider")
	private List<RiderMessage> riderMessage = new ArrayList<>();
	
	/**
	 * 고용 형태 (정규직, 계약직, 외주, 기타)
	 */
	/*
	@Setter
	@Column(table = "rider_info")
	private EmploymentStatus employmentStatus = EmploymentStatus.PERMANENT;
	*/
	
	/**
	 * 운전면허 번호
	 *
	 */
	/*
	@Setter
	@Column(length = 12, table = "rider_info")
	@Size(min=12, max=12)
	private String driverLicenseNo;	
	*/
	
	/**
	 * 운전 면허 종류
	 */
	/*
	@Setter
	@Column(table = "rider_info")
	private DriverLicenseType driverLicenseType = DriverLicenseType.NONE;
	*/
	
	/**
	 * 운전면허 취득일
	 *
	 */
	/*
	@Setter
	@Column(table = "rider_info")
	private LocalDate driverLicenseDate;	
	*/
	
	/**
	 * 숙련도
	 */
	/*
	@Setter
	@Column(table = "rider_info")
	private Double proficiency = 0.0;
	*/

	@Setter
	@OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
	@JoinColumn(name = "rider_recent_setting_id", nullable = true)
	private RiderRecentSetting riderRecentSetting;

	@Builder
	protected Rider(String name, String mobile, String email, String password, Boolean isSimulationRider) {
		this.name = name;
		this.mobile = mobile;
		this.email = email;
		this.password = password;
		this.isSimulationRider = isSimulationRider;
	}
}
