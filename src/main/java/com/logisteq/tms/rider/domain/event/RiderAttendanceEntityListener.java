package com.logisteq.tms.rider.domain.event;

import com.logisteq.tms.rider.domain.RiderAttendance;

import javax.persistence.PrePersist;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @created 2021-02-02
 * @project tms-service
 */
public class RiderAttendanceEntityListener {

    @PrePersist
    public void onPrePersist(final RiderAttendance riderAttendance) {

        final LocalDateTime attendAt = riderAttendance.getAttendAt();
        //assert attendAt != null;

        final LocalDateTime attendRequiredAt = riderAttendance.getAttendRequiredAt();

        if (Objects.isNull(attendRequiredAt) || Objects.isNull(attendAt) || !attendAt.isAfter(attendRequiredAt)) {
            riderAttendance.setIsLate(false);
        } else {
            riderAttendance.setIsLate(true);
        }
    }

}
