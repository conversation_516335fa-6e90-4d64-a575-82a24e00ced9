package com.logisteq.tms.rider.domain;

import com.logisteq.tms.rider.domain.event.RiderAttendanceEntityListener;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Builder
@EntityListeners({AuditingEntityListener.class, RiderAttendanceEntityListener.class})
@Entity
@Table(uniqueConstraints = @UniqueConstraint(columnNames = {"rider_id", "project_id"}))
public class RiderAttendance {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Rider
     */
/*
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "rider_id", nullable = false, updatable = false)
    private Rider rider;
*/

    /**
     * Rider ID
     */
    @Column(name = "rider_id", nullable = false, updatable = false)
    private Long riderId;

    /**
     * Project ID
     */
    @Column(name = "project_id", nullable = false, updatable = false)
    private Long projectId;

    /**
     * 실제 출근 시각 (from App)
     */
    @Column(nullable = false, updatable = false)
    private LocalDateTime attendAt;

    /**
     * 출근 요청 시각 (from workingStartTime of RiderProjectSetting)
     */
    @Column(updatable = false)
    private LocalDateTime attendRequiredAt;

    /**
     * 지각 여부 (출근 요청 시각, 실제 출근 시각 비교)
     */
    @Column(nullable = false, updatable = false)
    private Boolean isLate;

    /**
     * 엔트리 생성 시각
     */
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

}
