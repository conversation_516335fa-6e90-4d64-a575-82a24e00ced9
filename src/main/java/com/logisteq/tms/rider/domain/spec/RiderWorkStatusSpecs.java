package com.logisteq.tms.rider.domain.spec;

import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.domain.RiderWorkStatus;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;

public final class RiderWorkStatusSpecs {
	private static final String KEY_ID 				= "id";
	private static final String KEY_RIDER           = "rider";
	private static final String KEY_PROJECT_ID 		= "projectId";
	private static final String KEY_TIMESTAMP		= "timestamp";
	
	private static final String KEY_CREATE_AT		= "createAt";
	
	public static Specification<RiderWorkStatus> eqId(Long id) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.equal(root.get(KEY_ID), id);
		};
	}
	
	public static Specification<RiderWorkStatus> eqRider(Rider rider) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.equal(root.get(KEY_RIDER), rider);
		};
	}

	public static Specification<RiderWorkStatus> eqProjectId(Long projectId) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.equal(root.get(KEY_PROJECT_ID), projectId);
		};
	}

	public static Specification<RiderWorkStatus> betweenTimestamp(LocalDateTime from, LocalDateTime to) {
		return (root, query, criteriaBuilder) -> {
			if (from != null && to != null) {
				return criteriaBuilder.and(
						criteriaBuilder.greaterThanOrEqualTo(root.get(KEY_TIMESTAMP), from),
						criteriaBuilder.lessThanOrEqualTo(root.get(KEY_TIMESTAMP), to));
			} else if (from != null) {
				return criteriaBuilder.greaterThanOrEqualTo(root.get(KEY_TIMESTAMP), from);
			}
			// (to != null) 
			return criteriaBuilder.lessThanOrEqualTo(root.get(KEY_TIMESTAMP), to); 
		};
	}
	
	public static Specification<RiderWorkStatus> betweenCreateAt(LocalDateTime from, LocalDateTime to) {
		return (root, query, criteriaBuilder) -> {
			if (from != null && to != null) {
				return criteriaBuilder.and(
						criteriaBuilder.greaterThanOrEqualTo(root.get(KEY_CREATE_AT), from),
						criteriaBuilder.lessThanOrEqualTo(root.get(KEY_CREATE_AT), to));
			} else if (from != null) {
				return criteriaBuilder.greaterThanOrEqualTo(root.get(KEY_CREATE_AT), from);
			}
			// (to != null) 
			return criteriaBuilder.lessThanOrEqualTo(root.get(KEY_CREATE_AT), to); 
		};
	}
	
	public static Specification<RiderWorkStatus> composeSpecs(Rider rider, Long projectId,
															  LocalDateTime fromTimestamp, LocalDateTime toTimestamp,
															  LocalDateTime fromCreateAt, LocalDateTime toCreateAt) {
		Specification<RiderWorkStatus> specs = null;
		
		if (rider != null) {
			specs = eqRider(rider);
		}
		
		if (projectId != null) {
			specs = specs == null ? eqProjectId(projectId)
					: specs.and(eqProjectId(projectId));
		}
		
		if (fromTimestamp != null || toTimestamp != null) {
			specs = specs == null ? betweenTimestamp(fromTimestamp, toTimestamp)
					: specs.and(betweenTimestamp(fromTimestamp, toTimestamp));
		}
		
		if (fromCreateAt != null || toCreateAt != null) {
			specs = specs == null ? betweenCreateAt(fromCreateAt, toCreateAt)
					: specs.and(betweenCreateAt(fromCreateAt, toCreateAt));
		}
		return specs;
	}
}