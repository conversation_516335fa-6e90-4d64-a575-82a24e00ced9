package com.logisteq.tms.rider.domain;

import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@AllArgsConstructor
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Builder
@Entity
@Table(name = "rider_work_completion", indexes = {@Index(columnList = "projectId"), @Index(columnList = "rider_id, projectId")})
public class RiderWorkCompletion {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Rider
     */
    @ManyToOne(optional = false)
    @JoinColumn(name = "rider_id", nullable = false, updatable = false)
    private Rider rider;

    /**
     * Project ID
     */
    @Column(nullable = false, updatable = false)
    private Long projectId;

    /**
     * 업무완료 여부
     */
    @Column(nullable = false)
    private Boolean isWorkCompleted;

    /**
     * 생성일시
     */
    @CreationTimestamp
    @Column(name = "create_at", updatable = false)
    private LocalDateTime createAt;
}
