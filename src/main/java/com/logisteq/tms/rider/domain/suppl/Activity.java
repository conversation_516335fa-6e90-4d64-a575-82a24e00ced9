package com.logisteq.tms.rider.domain.suppl;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.stream.Stream;

/**
 * Rider Activity
 * <AUTHOR>
 *
 */
public enum Activity {
	// Activity
	NONE("NONE"),				// 최초 상태
	IN_VEHICLE("IN_VEHICLE"), 		
	ON_BICYCLE("ON_BICYCLE"),
	ON_FOOT("ON_FOOT"),
	STILL("STILL"),	
	UNKNOWN("UNKNOWN"),
	
	// Geofence
	GEOFENCE_ENTER("GF_ENTER"),	
	GEOFENCE_EXIT("GF_EXIT"),
	
	// Delivery
	DELIVERY_COMPLETED("D_COMPLETED"),			
	DELIVERY_FAILED("D_FAILED");
	
	private String shortName;
	
	private Activity(String shortName) {
		this.shortName = shortName;
	}
	
	public String getShortName() {
		return shortName;
	}
	
	public static Activity fromShortName(String shortName) {
		if (shortName == null) {
			return null;
		}
		
        switch (shortName) {
        case "NONE":
        	return Activity.NONE;
        	
        case "IN_VEHICLE":
            return Activity.IN_VEHICLE;
 
        case "ON_BICYCLE":
            return Activity.ON_BICYCLE;
 
        case "ON_FOOT":
            return Activity.ON_FOOT;

        case "STILL":
            return Activity.STILL;
 
        case "UNKNOWN":
            return Activity.UNKNOWN;
 
        case "GF_ENTER":
            return Activity.GEOFENCE_ENTER;

        case "GF_EXIT":
            return Activity.GEOFENCE_EXIT;
            
        case "D_COMPLETED":
        	return Activity.DELIVERY_COMPLETED;
        	
        case "D_FAILED":
        	return Activity.DELIVERY_FAILED;        	
            
        default:
            throw new IllegalArgumentException("ShortName [" + shortName
                    + "] not supported.");
        }
    }

	@Converter(autoApply = true)
	public static class ActivityConverter implements AttributeConverter<Activity, String> {

		@Override
		public String convertToDatabaseColumn(Activity activityStatus) {
			if (activityStatus == null) {
				return null;
			}
			return activityStatus.getShortName();
		}

		@Override
		public Activity convertToEntityAttribute(String shortName) {
			if (shortName == null) {
				return null;
			}

			return Stream.of(Activity.values())
					.filter(a -> a.getShortName().equals(shortName))
					.findFirst()
					.orElseThrow(IllegalArgumentException::new);
		}
	}

}
