package com.logisteq.tms.rider.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logisteq.common.dto.PageableDTO;
import com.logisteq.common.dto.RoutesCoordinate;
import com.logisteq.common.util.CommonUtil;
import com.logisteq.tms.delivery.dto.DeliveryRoutesRequestDTO;
import com.logisteq.tms.rider.domain.RouteByCallapp;
import com.logisteq.tms.rider.domain.RouteByRider;
import com.logisteq.tms.rider.domain.spec.RiderRouteSpecs;
import com.logisteq.tms.rider.dto.RoutesResDTO;
import com.logisteq.tms.rider.repository.RouteByCallappRepository;
import com.logisteq.tms.rider.repository.RouteByRiderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Validated
@Service
public class RiderRouteService {

    private final RouteByRiderRepository routeByRiderRepository;
    private final RouteByCallappRepository routeByCallappRepository;

    @Autowired
    public RiderRouteService(final RouteByRiderRepository routeByRiderRepository,
                             final RouteByCallappRepository routeByCallappRepository) {

        this.routeByRiderRepository = routeByRiderRepository;
        this.routeByCallappRepository = routeByCallappRepository;
    }

    @Transactional
    public void routesSaveByRider(final Long riderId,
                                  final Long projectId,
                                  final List<DeliveryRoutesRequestDTO> routes) {

        final List<RouteByRider> routeByRiderList = new ArrayList<>();

        for (DeliveryRoutesRequestDTO route : routes) {
//			List<RoutesCoordinate> routesList = route.getRoutes();
//			RoutesCoordinate[] routeArray = routesList.toArray( new RoutesCoordinate[routesList.size()] );

            final RouteByRider routeByRider = RouteByRider.builder()
                    .riderId(riderId)
                    .projectId(projectId)
                    .deliveryId(route.getDeliveryId())
                    .distanceToArrival(route.getDistanceToArrival())
                    .eta(route.getEta())
                    .timestamp(route.getTimestamp())
//								.routes( GeometryUtil.createLineStringFromCoordinates( routeArray ) )
                    .routes(voToJson(route.getRoutes()))
//								.routes( route.getRoutes() )
                    .build();
            routeByRiderList.add(routeByRider);
        }

        if (CollectionUtils.isNotEmpty(routeByRiderList)) {
            routeByRiderRepository.saveAll(routeByRiderList);
        }
    }

    private static String voToJson(final List<RoutesCoordinate> routesList) {

        final ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = null;
        try {
            jsonString = objectMapper.writeValueAsString(routesList);
        } catch (JsonProcessingException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        return jsonString;
    }

    public List<RoutesResDTO> getRoutesByRiderId(final Long riderId) {

        final List<RouteByRider> routeList = routeByRiderRepository.findByRiderId(riderId);
        final List<RoutesResDTO> rrDtoList = RoutesResDTO.routeByRiderToDto(routeList);

        return rrDtoList;
    }

    public List<RoutesResDTO> getRoutesBySpec(final Long riderId,
                                              final Long deliveryId,
                                              final Long projectId,
                                              final PageableDTO pageable) {

        final Specification<RouteByRider> spec = RiderRouteSpecs.composeSpecs(riderId, deliveryId, projectId, null, null);

        if (pageable.getSortAsc() != null && pageable.getSortField() != null) {
            if (pageable.getSortAsc()) {
                pageable.setSort(Sort.by(pageable.getSortField()).ascending());
            } else {
                pageable.setSort(Sort.by(pageable.getSortField()).descending());
            }
        }

        final Page<RouteByRider> routeList = routeByRiderRepository.findAll(spec, CommonUtil.getPageable(pageable));
        final List<RoutesResDTO> rrDtoList = RoutesResDTO.routeByRiderToDto(routeList);

//		if (ifProfile("demo") || ifProfile("local-demo") || ifProfile("aws-demo")) {//demo버전일때는 응답으로 내려 줬던 내용들을 삭제 한다.....
        routeByRiderRepository.deleteAll(routeList);
//		}

        return rrDtoList;
    }

    public List<RoutesResDTO> getRoutesAll() {

        final List<RouteByRider> routeList = routeByRiderRepository.findAll();
        final List<RoutesResDTO> rrDtoList = RoutesResDTO.routeByRiderToDto(routeList);

//		if (ifProfile("demo") || ifProfile("local-demo") || ifProfile("aws-demo")) {//demo버전일때는 응답으로 내려 줬던 내용들을 삭제 한다.....
        routeByRiderRepository.deleteAll(routeList);
//		}

        return rrDtoList;
    }

    @Transactional
    public void routeSavebyCallApp(final Long riderId,
                                   final Long projectId,
                                   final String callUserId,
                                   final List<DeliveryRoutesRequestDTO> routes) {

        final List<RouteByCallapp> routeByCallappList = new ArrayList<>();

        for (DeliveryRoutesRequestDTO route : routes) {
//			List<RoutesCoordinate> routesList = route.getRoutes();
//			RoutesCoordinate[] routeArray = routesList.toArray( new RoutesCoordinate[routesList.size()] );

            final RouteByCallapp routeByCallapp = RouteByCallapp.builder()
                    .callUserId(callUserId)
                    .riderId(riderId)
                    .projectId(projectId)
                    .deliveryId(route.getDeliveryId())
                    .distanceToArrival(route.getDistanceToArrival())
                    .eta(route.getEta())
                    .orderNum(route.getOrder())
                    .timestamp(route.getTimestamp())
//														.routes( GeometryUtil.createLineStringFromCoordinates( routeArray ) )
                    .routes(voToJson(route.getRoutes()))
                    .build();
            routeByCallappList.add(routeByCallapp);
        }

        if (CollectionUtils.isNotEmpty(routeByCallappList)) {
            routeByCallappRepository.saveAll(routeByCallappList);
        }
    }

    public List<RoutesResDTO> getRoutesByCallappByRiderId(final Long riderId) {

        final List<RouteByCallapp> routeList = routeByCallappRepository.findByRiderId(riderId);
        final List<RoutesResDTO> rrDtoList = RoutesResDTO.routeByCallappToDto(routeList);

//		if (ifProfile("demo") || ifProfile("local-demo") || ifProfile("aws-demo")) {//demo버전일때는 응답으로 내려 줬던 내용들을 삭제 한다.....
        routeByCallappRepository.deleteAll(routeList);
//		}
        return rrDtoList;
    }

    public List<RoutesResDTO> getRoutesByCallappByCallUserId(final String callUserId) {

        final List<RouteByCallapp> routeList = routeByCallappRepository.findByCallUserId(callUserId);
        final List<RoutesResDTO> rrDtoList = RoutesResDTO.routeByCallappToDto(routeList);

//		if (ifProfile("demo") || ifProfile("local-demo") || ifProfile("aws-demo")) {//demo버전일때는 응답으로 내려 줬던 내용들을 삭제 한다.....
        routeByCallappRepository.deleteAll(routeList);
//		}
        return rrDtoList;
    }

    public List<RoutesResDTO> getRoutesByCallappByRiderIdAndCallUserId(final Long riderId,
                                                                       final String callUserId) {

        final List<RouteByCallapp> routeList = routeByCallappRepository.findByRiderIdAndCallUserId(riderId, callUserId);
        final List<RoutesResDTO> rrDtoList = RoutesResDTO.routeByCallappToDto(routeList);

//		if (ifProfile("demo") || ifProfile("local-demo") || ifProfile("aws-demo")) {//demo버전일때는 응답으로 내려 줬던 내용들을 삭제 한다.....
        routeByCallappRepository.deleteAll(routeList);
//		}
        return rrDtoList;
    }

}
