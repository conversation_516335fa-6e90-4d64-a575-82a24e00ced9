package com.logisteq.tms.rider.service;

import com.logisteq.common.component.PxlComponent;
import com.logisteq.common.component.excel.exception.PxlException;
import com.logisteq.common.exception.CustomException;
import com.logisteq.common.exception.InvalidParameterException;
import com.logisteq.common.exception.ItemNotFoundException;
import com.logisteq.common.feign.track.TrackServiceClient;
import com.logisteq.common.util.CommonUtil;
import com.logisteq.common.util.FormatUtil;
import com.logisteq.tms.common.component.ProfileManager;
import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.DeliveryAllocation;
import com.logisteq.tms.delivery.repository.DeliveryAllocationRepository;
import com.logisteq.tms.delivery.repository.DeliveryRepository;
import com.logisteq.tms.external.joins.constant.DelificateConstant;
import com.logisteq.tms.external.thehyundai.constant.TheHyundaiConstant;
import com.logisteq.tms.file.domain.File;
import com.logisteq.tms.file.domain.suppl.FileCategory;
import com.logisteq.tms.file.domain.suppl.FileType;
import com.logisteq.tms.file.service.FileService;
import com.logisteq.tms.privacy.dto.PrivacyRecordDto;
import com.logisteq.tms.privacy.service.PrivacyRecordService;
import com.logisteq.tms.privacy.suppl.PrivacyDataType;
import com.logisteq.tms.privacy.suppl.PrivacyRecordType;
import com.logisteq.tms.privacy.suppl.PrivacyUsageType;
import com.logisteq.tms.project.constant.ProjectConstant;
import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.domain.suppl.ProjectStatus;
import com.logisteq.tms.project.dto.excel.leftpanel.ProjectRiderSheetDTO;
import com.logisteq.tms.project.dto.excel.leftpanel.ProjectWorkbookDTO;
import com.logisteq.tms.project.dto.web.WebRiderDTO;
import com.logisteq.tms.project.repository.ProjectRepository;
import com.logisteq.tms.project.service.ProjectBasicService;
import com.logisteq.tms.rider.domain.*;
import com.logisteq.tms.rider.domain.spec.RiderSpecs;
import com.logisteq.tms.rider.domain.spec.RiderWorkCompletionSpecs;
import com.logisteq.tms.rider.domain.suppl.DispatchStatus;
import com.logisteq.tms.rider.domain.suppl.LinkStatus;
import com.logisteq.tms.rider.domain.suppl.WorkAuthority;
import com.logisteq.tms.rider.domain.suppl.WorkStatus;
import com.logisteq.tms.rider.repository.*;
import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.constant.UserConstant;
import com.logisteq.tms.user.domain.*;
import com.logisteq.tms.user.repository.dao.GroupProjectRiderDAO;
import com.logisteq.tms.user.service.*;
import com.logisteq.tms.vehicle.domain.Vehicle;
import com.logisteq.tms.vehicle.dto.VehicleDTO;
import com.logisteq.tms.vehicle.dto.VehicleModelDTO;
import com.logisteq.tms.vehicle.service.VehicleService;
import com.logisteq.tms.web.dto.WebRiderVehicleDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.IOException;
import java.text.Normalizer;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@Validated
public class RiderService {

    private final RiderRepository riderRepository;
    private final FileService fileService;
    private final GroupService groupService;
    private final DeliveryAllocationRepository deliveryAllocationRepository;
    private final DeliveryRepository deliveryRepository;
    private final UserService userService;
    private final OrganizationService organizationService;
    private final RiderProjectSettingRepository riderProjectSettingRepository;
    private final VehicleService vehicleService;
    private final RiderRecentSettingService riderRecentSettingService;
    private final PxlComponent excelComponent;
    private final RiderActivityRepository riderActivityRepository;
    private final RiderWorkStatusService riderWorkStatusService;
    private final RiderWorkStatusRepository riderWorkStatusRepository;
    private final PasswordEncoder passwordEncoder;
    private final RiderWorkCompletionRepository riderWorkCompletionRepository;
    private final RiderOrgStatusRepository riderOrgStatusRepository;
    private final ProjectBasicService projectBasicService;
    private final RiderDispatchStatusRepository riderDispatchStatusRepository;
    private final ProfileManager profileManager;
    private final DepartmentService departmentService;
    private final UserDepartmentService userDepartmentService;
    private final RiderDepartmentService riderDepartmentService;
    private final TrackServiceClient trackServiceClient;
    private final PrivacyRecordService privacyRecordService;

    private final ProjectRepository projectRepository;

    @Value("${aloa.storage.upload.excel: true}")
    private boolean storageUploadExcel;

    private static DateTimeFormatter s3DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss-SSS");

    @Autowired
    public RiderService(final RiderRepository riderRepository,
                        final FileService fileService,
                        final GroupService groupService,
                        final DeliveryAllocationRepository deliveryAllocationRepository,
                        final DeliveryRepository deliveryRepository,
                        final UserService userService,
                        final OrganizationService organizationService,
                        final RiderProjectSettingRepository riderProjectSettingRepository,
                        final VehicleService vehicleService,
                        final RiderRecentSettingService riderRecentSettingService,
                        final PxlComponent excelComponent,
                        final RiderActivityRepository riderActivityRepository,
                        final RiderWorkStatusService riderWorkStatusService,
                        final RiderWorkStatusRepository riderWorkStatusRepository,
                        final PasswordEncoder passwordEncoder,
                        final RiderWorkCompletionRepository riderWorkCompletionRepository,
                        final RiderOrgStatusRepository riderOrgStatusRepository,
                        final ProjectBasicService projectBasicService,
                        final RiderDispatchStatusRepository riderDispatchStatusRepository,
                        final ProfileManager profileManager,
                        final DepartmentService departmentService,
                        final UserDepartmentService userDepartmentService,
                        final RiderDepartmentService riderDepartmentService,
                        final TrackServiceClient trackServiceClient,
                        final PrivacyRecordService privacyRecordService,
                        final ProjectRepository projectRepository) {

        this.riderRepository = riderRepository;
        this.fileService = fileService;
        this.groupService = groupService;
        this.deliveryAllocationRepository = deliveryAllocationRepository;
        this.deliveryRepository = deliveryRepository;
        this.userService = userService;
        this.organizationService = organizationService;
        this.riderProjectSettingRepository = riderProjectSettingRepository;
        this.vehicleService = vehicleService;
        this.riderRecentSettingService = riderRecentSettingService;
        this.excelComponent = excelComponent;
        this.riderActivityRepository = riderActivityRepository;
        this.riderWorkStatusService = riderWorkStatusService;
        this.riderWorkStatusRepository = riderWorkStatusRepository;
        this.passwordEncoder = passwordEncoder;
        this.riderWorkCompletionRepository = riderWorkCompletionRepository;
        this.riderOrgStatusRepository = riderOrgStatusRepository;
        this.projectBasicService = projectBasicService;
        this.riderDispatchStatusRepository = riderDispatchStatusRepository;
        this.profileManager = profileManager;
        this.departmentService = departmentService;
        this.userDepartmentService = userDepartmentService;
        this.riderDepartmentService = riderDepartmentService;
        this.trackServiceClient = trackServiceClient;
        this.privacyRecordService = privacyRecordService;
        this.projectRepository = projectRepository;
    }

    public Rider registerRider(final String email,
                               final String password,
                               final String name,
                               final String mobile,
                               final Long orgId,
                               final List<Long> projectIds,
                               final Integer skillLevel,
                               final String position,
                               final String employeeNumber) {

        return registerRider(email, password, name, mobile,orgId,projectIds, skillLevel, position, employeeNumber, null, null, false, null);
    }

    /**
     * Rider 등록
     *
     * @param email
     * @param password
     * @param name
     * @param mobile
     * @param orgId
     * @param projectIds
     * @param skillLevel
     * @param position
     * @param employeeNumber
     * @param userId
     * @param workAuthority
     * @return
     */
    @Transactional
    public Rider registerRider(final String email,
                               final String password,
                               final String name,
                               final String mobile,
                               final Long orgId,
                               final List<Long> projectIds,
                               final Integer skillLevel,
                               final String position,
                               final String employeeNumber,
                               final Long userId,
                               final WorkAuthority workAuthority,
                               final Boolean isSimulationRider,
                               final Long dispatchNumber) {

        final Organization org = organizationService.getOrganizationByOrganizationId(orgId);

        // 중앙일보의 경우 Batch로 실행되므로, 기사전화번호는 같으면 기사명이 다른 경우 Exception처리하지 않고, 기사명을 갱신하도록 함.
        final boolean updateNameIfMobileExist = StringUtils.equals(FormatUtil.stripPhoneNumber(ProjectConstant.SAMPLE_RIDER_MOBILE, false), mobile)
                || StringUtils.equals(org.getCodeName(), DelificateConstant.JOINS_ORG_CODE_NAME)
                || profileManager.isLocalProfiles();

        final Optional<Rider> result = riderRepository.findOne(RiderSpecs.composeSpecs(null, null, mobile, null, null)); // 전체를 찾아야 되기 때문에

        if (result.isPresent()) {
            final String riderName = result.get().getName();

            if (StringUtils.equals(riderName, name)) {
                // 변경사항이 있으면 업데이트하고 리턴, 변경사항이 없으면 기존에 있던 정보를 리턴한다.
                return updateRiderOrNot(null, projectIds, position, employeeNumber, skillLevel, result.get(), orgId, workAuthority);
            } else if (updateNameIfMobileExist) {
                // 기사전화번호가 같으면 기사명 업데이트
                log.info("기사전화번호 {}를 사용하는 기사명을 {}에서 {}로 변경합니다.", mobile, riderName, name);
                return updateRiderOrNot(name, projectIds, position, employeeNumber, skillLevel, result.get(), orgId, workAuthority);
            } else {
                throw new CustomException(HttpStatus.NOT_ACCEPTABLE, "전화번호 (" + mobile + ")를 " + riderName + " 기사가 이미 사용중입니다.", false);
            }
        }

        //가배차 기사가 아닐 경우에만 기사 수 체크함
        if(isSimulationRider == null || !isSimulationRider) {

            final Integer riderCountLimit = org.getRiderCountLimit();
            if (Objects.nonNull(riderCountLimit)) {
                Specification<Rider> specs = Specification.where(null);

//            specs = Objects.requireNonNull(specs).and(RiderSpecs.eqDeleted(false));
//            specs = Objects.requireNonNull(specs).and(RiderSpecs.eqOrganizationId(orgId));

                //org Status list 로 변경함.
                specs = Objects.requireNonNull(specs).and(RiderSpecs.joinWorkOrganizationIdAndIsDeleted(orgId, false));
//            specs = Objects.requireNonNull(specs).and(RiderSpecs.eqworkOrganizationDeleted(false));

                //가배차 기사 검색에서 삭제함.
                specs = Objects.requireNonNull(specs).and(RiderSpecs.isSimulationRiderNull().or(RiderSpecs.isSimulationRiderFalse()));

                final long orgRiderCount = riderRepository.count(specs);

                if (riderCountLimit <= orgRiderCount) {
                    throw new CustomException(HttpStatus.NOT_ACCEPTABLE,
                            org.getOrganizationName() + "은 사용 기사가 초과 되었습니다. (" + orgRiderCount + "/" + riderCountLimit + ")", false);
                }
            }
        }

        try {
            final Rider rider = Rider.builder()
                    .email(email)
                    .name(name)
                    .mobile(mobile)
                    .isSimulationRider(isSimulationRider)
//                    .organizationId(orgId)
//                    .deleted(false)
                    .build();

            if (StringUtils.isNotBlank(password)) {
                rider.setPassword(passwordEncoder.encode(password));
            }
            if (Objects.nonNull(skillLevel)) {
                rider.setSkillLevel(skillLevel);
            }
            if (StringUtils.isNotBlank(position)) {
                rider.setPosition(position);
            }
            if (Objects.nonNull(employeeNumber)) {
                rider.setEmployeeNumber(employeeNumber);
            }
            if (CollectionUtils.isNotEmpty(projectIds)) {
                rider.getProjectIds().addAll(projectIds);
            }
            if (Objects.nonNull(workAuthority)) {
                rider.setWorkAuthority(workAuthority);
            }
            if (Objects.nonNull(dispatchNumber)) {
                rider.setDispatchNumber(dispatchNumber);
            }

            Rider resultRider = riderRepository.save(rider);

            //신규 rider에 대한 org 정보를 저장함.
            riderOrgStatusRepository.save(RiderOrgStatus.builder()
                    .rider(resultRider)
                    .orgId(orgId)
                    .isDeleted(false)
                    .build());

            try {
                final List<RiderOrgStatus> riderOrgStatusList = rider.getOrgStatusList();
                for ( RiderOrgStatus status : riderOrgStatusList) {
                    if(!status.getIsDeleted()) {
                        privacyRecordService.saveRecord(
                                PrivacyRecordDto.builder()
                                        .recordType(PrivacyRecordType.RIDER)
                                        .orgId(status.getOrgId())
                                        .type(PrivacyUsageType.REGISTER)
                                        .dataType(PrivacyDataType.NAME_MOBILE)
                                        .acquisitionChannel("MOBILE")
                                        .func("verifyAuthKey")
                                        .riderId(resultRider.getId())
                                        .build());

                        privacyRecordService.saveRecord(
                                PrivacyRecordDto.builder()
                                        .recordType(PrivacyRecordType.RIDER_INFO)
                                        .type(PrivacyUsageType.COLLECT)
                                        .orgId(status.getOrgId())
                                        .riderId(resultRider.getId())
                                        .dataType(PrivacyDataType.NAME_MOBILE)
                                        .userId(userId)
                                        .func("addRider")
                                        .build());
                    }
                }
            } catch (Exception e) {
                log.error("Error while saving privacy records: ", e);
            }



            return resultRider;

        } catch (DataIntegrityViolationException de) {

            throw new InvalidParameterException("전화번호 (" + mobile + ")를 다른 기사가 이미 사용중입니다.", false);
        } catch (Exception e) {

            log.error("기사 등록 중 에러..." + e.getLocalizedMessage());
            throw e;
        }
    }

    @Transactional
    public Rider updateRiderOrNot(final String name,
                                  final List<Long> projectIds,
                                  final String position,
                                  final String employeeNumber,
                                  final Integer skillLevel,
                                  final Rider updateRider,
                                  final Long orgId,
                                  final WorkAuthority workAuthority) {

        if (StringUtils.isNotBlank(name)) {
            updateRider.setName(name);
        }
        if (CollectionUtils.isNotEmpty(projectIds)) {
            // 프로젝트에 이미 있는 기사를 또 추가되면 그 기사가 여러번 보이게 되므로, 이미 있으면 추가하지 않도록 함.
            final List<Long> riderProjectList = updateRider.getProjectIds();
            projectIds.forEach(projectId -> {
                if (!riderProjectList.contains(projectId)) {
                    riderProjectList.add(projectId);
                }
            });
        }

        final List<RiderOrgStatus> riderOrgStatusList = riderOrgStatusRepository.findByRiderIdAndOrgIdAndIsDeleted(updateRider.getId(), orgId, false);
        if (CollectionUtils.isEmpty(riderOrgStatusList)) {
            riderOrgStatusRepository.save(RiderOrgStatus.builder()
                    .rider(updateRider)
                    .orgId(orgId)
                    .isDeleted(false)
                    .build());
        }

        if (StringUtils.isNotBlank(position)) {
            updateRider.setPosition(position);
        }
        if (StringUtils.isNotBlank(employeeNumber)) {
            updateRider.setEmployeeNumber(employeeNumber);
        }
        if (Objects.nonNull(skillLevel)) {
            updateRider.setSkillLevel(skillLevel);
        }
        if (Objects.nonNull(workAuthority)) {
            updateRider.setWorkAuthority(workAuthority);
        }

        return riderRepository.save(updateRider);
    }

    /**
     * 기사 추가
     *
     * @param userId
     * @param webRiderVehicleDTO
     */
    @Transactional
    public WebRiderDTO addRider(final Long userId,
                                final WebRiderVehicleDTO webRiderVehicleDTO) {

        final String riderName = Optional.ofNullable(webRiderVehicleDTO.getName()).orElse("");
        final String riderMobile = FormatUtil.stripPhoneNumber(webRiderVehicleDTO.getMobile(), true);

        final User user = userService.getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));
        final Organization org = organizationService.getOrganizationByUserId(userId);
        final Long dispatchNumber = webRiderVehicleDTO.getDispatchNumber();
        final Rider rider = this.registerRider(
                null,    // email
                null,    // password
                riderName,    // name
                riderMobile,    // mobile
                org.getId(),
                null,
                webRiderVehicleDTO.getSkillLevel(),
                webRiderVehicleDTO.getPosition(),
                webRiderVehicleDTO.getEmployeeNumber(),
                userId,
                Optional.ofNullable(webRiderVehicleDTO.getWorkAuthority()).orElse(WorkAuthority.DELIVERY),
                webRiderVehicleDTO.getIsSimulationRider(),
                dispatchNumber);

        final String groupName = webRiderVehicleDTO.getGroupName();
        final Long projectId = webRiderVehicleDTO.getProjectId();
        if (Objects.nonNull(groupName) && Objects.nonNull(projectId)) {
            final Group foundGroup = groupService.ifExistGroupThenMappingElseAdd(userId, groupName);

            if (Objects.nonNull(foundGroup)) {
                final List<GroupProjectRider> groupProjectRiderList = foundGroup.getGroupProjectRiderList();
                if (groupProjectRiderList != null /*&& groupProjectRiderList.isEmpty() */) {
                    groupProjectRiderList.add(GroupProjectRider.builder()
                            .projectId(projectId)
                            .riderId(rider.getId())
                            .build());
                    groupService.saveGroup(foundGroup);
                }
            }
        }

        final boolean isTheHyundaiUser = organizationService.isCodeNameOrganization(org, TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME);
        if (isTheHyundaiUser && Objects.nonNull(dispatchNumber) && Objects.nonNull(groupName) && !StringUtils.equals(dispatchNumber.toString(), groupName)) {
            webRiderVehicleDTO.setGroupName(dispatchNumber.toString());
        }

        return saveRiderVehicleAndRecentSetting(user, org, webRiderVehicleDTO, rider);
    }

    @Transactional
    public WebRiderDTO updateRider(final Long userId,
                                   final WebRiderVehicleDTO webRiderVehicleDTO,
                                   final Long riderId) {

        final String riderName = Optional.ofNullable(webRiderVehicleDTO.getName()).orElse("");
        final String riderMobile = FormatUtil.stripPhoneNumber(webRiderVehicleDTO.getMobile(), true);
        final User user = userService.getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));
        final Organization org = organizationService.getOrganizationByUserId(userId);

        // 중앙일보의 경우 Batch로 실행되므로, 기사전화번호는 같으면 기사명이 다른 경우 Exception처리하지 않고, 기사명을 갱신하도록 함.
        //final boolean updateNameIfMobileExist = StringUtils.equals("01012345678", riderMobile)
        //      || (Objects.nonNull(org) && StringUtils.equals(org.getCodeName(), DelificateConstant.JOINS_ORG_CODE_NAME));

        final Rider rider = this.updateRiderById(
                riderId,
                null,    // email
                null,    // password
                riderName,    // name
                riderMobile,    // mobile
                null,
                webRiderVehicleDTO.getSkillLevel(),
                webRiderVehicleDTO.getPosition(),
                webRiderVehicleDTO.getEmployeeNumber(),
                webRiderVehicleDTO.getAutoCreate(),
                Optional.ofNullable(webRiderVehicleDTO.getWorkAuthority()).orElse(WorkAuthority.DELIVERY),
                webRiderVehicleDTO.getDispatchNumber());

        return saveRiderVehicleAndRecentSetting(user, org, webRiderVehicleDTO, rider);
    }

    private WebRiderDTO saveRiderVehicleAndRecentSetting(final User user,
                                                         final Organization organization,
                                                         final WebRiderVehicleDTO webRiderVehicleDTO,
                                                         final Rider rider) {

        final RiderRecentSetting riderRecentSetting = riderRecentSettingService.getRiderRecentSetting(rider);
        Vehicle vehicle = vehicleService.findByLicensePlate(webRiderVehicleDTO.getLicensePlate());

        //차량 정보가 없을 경우
        if (Objects.isNull(vehicle)) {
            if (StringUtils.isBlank(webRiderVehicleDTO.getModelName())) {
                webRiderVehicleDTO.setModelName("포터 2");
            }

            final VehicleModelDTO vehicleModelDTO = VehicleModelDTO.parseFromWebRiderVehicleDTO(webRiderVehicleDTO);
            vehicle = vehicleService.registerVehicleWithVehicleModelDTO(webRiderVehicleDTO.getLicensePlate(), vehicleModelDTO);
        }
        //차량 정보가 있을 경우
        else if (Objects.nonNull(riderRecentSetting)) {
            if (Objects.nonNull(riderRecentSetting.getVehicle().getVehicleModel())) {
                if (StringUtils.isBlank(riderRecentSetting.getVehicle().getVehicleModel().getModelName())) {
                    webRiderVehicleDTO.setModelName("포터 2");
                }
                else {
                    webRiderVehicleDTO.setModelName(riderRecentSetting.getVehicle().getVehicleModel().getModelName());
                }
            } else {
                if (StringUtils.isBlank(webRiderVehicleDTO.getModelName())) {
                    webRiderVehicleDTO.setModelName("포터 2");
                }
            }

            final VehicleModelDTO vehicleModelDTO = VehicleModelDTO.parseFromWebRiderVehicleDTO(webRiderVehicleDTO);
            vehicle = vehicleService.registerVehicleWithVehicleModelDTO(webRiderVehicleDTO.getLicensePlate(), vehicleModelDTO);
        }

        vehicleService.updateVehicle(vehicle.getVehicleId(), VehicleDTO.parseFromVehicle(vehicle));

        List<Department> assignableDepartmentList = null;
        final boolean orgHasDepartment = departmentService.existDepartmentOfOrganization(organization);
        if (orgHasDepartment) {
            if (CollectionUtils.isEmpty(webRiderVehicleDTO.getDepartmentIdList())) {
                assignableDepartmentList = userDepartmentService.getAuthorizedDepartmentListOfUser(user, organization, false);
                if (CollectionUtils.isEmpty(assignableDepartmentList)) {
                    throw new InvalidParameterException("관제자의 부서가 지정되어 있지 않습니다.", false);
                }
            } else {
                final List<Department> authorizedDepartmentList = userDepartmentService.getAuthorizedDepartmentListOfUser(user, organization, true);
                if (CollectionUtils.isEmpty(authorizedDepartmentList)) {
                    throw new InvalidParameterException("관제자의 부서가 지정되어 있지 않습니다.", false);
                }

                final List<Department> departmentList = departmentService.getDepartmentListByDepartmentIdList(organization, webRiderVehicleDTO.getDepartmentIdList());
                assignableDepartmentList = DepartmentService.intersection(authorizedDepartmentList, departmentList);
                if (CollectionUtils.isEmpty(assignableDepartmentList)) {
                    final List<String> departmentNameList = departmentList.stream()
                            .map(Department::getDepartmentName)
                            .collect(Collectors.toList());
                    throw new InvalidParameterException("기사 " + rider.getName() + "의 부서를 " +
                            StringUtils.join(departmentNameList, ",") + "(으)로 지정할 권한이 없습니다.", false);
                }
            }

            riderDepartmentService.assignRiderToDepartmentListOfOrganization(rider, organization, assignableDepartmentList, false);

            // 기사의 근무시작주소가 비어있으면 배정된 부서의 주소로 대체한다.
            if (StringUtils.isBlank(webRiderVehicleDTO.getWorkingStartAddress())) {
                final Department departmentWithAddress = assignableDepartmentList.stream()
                        .filter(d -> StringUtils.isNotBlank(d.getAddressBase()))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(departmentWithAddress)) {
                    webRiderVehicleDTO.setWorkingStartAddress(departmentWithAddress.getAddressBase());
                }
            }
        }

        return WebRiderDTO.of(rider, riderRecentSettingService.saveRiderRecentSetting(rider, vehicle, webRiderVehicleDTO), assignableDepartmentList);
    }

    /**
     * 기사 정보
     *
     * @param riderId
     * @param organizationId
     */
    public WebRiderDTO getWebRider(final Long riderId,
                                   @Nullable final Long organizationId) {

        final Rider rider = this.getRider(riderId);
        final RiderRecentSetting riderRecentSetting = riderRecentSettingService.getRiderRecentSetting(rider);
        final Organization organization = organizationService.getOrganizationById(organizationId);
        final List<Department> assignedDepartmentList = riderDepartmentService.getAssignedDepartmentListOfRider(rider, organization, false);

        final WebRiderDTO webRiderDTO = WebRiderDTO.of(rider, riderRecentSetting, assignedDepartmentList);

        return webRiderDTO;
    }

    /**
     * riders + vehicle + group 저장.
     *
     * @param userId
     * @param organizationId
     * @param excelFile
     * @return
     */
    @Transactional
    public List<WebRiderDTO> saveRidersVehicle(final Long userId,
                                               final Long organizationId,
                                               final MultipartFile excelFile) {

        final String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = LocalDateTime.now().format(s3DateTimeFormatter) + "_riders_drop";
        final String fileExtension = Optional.ofNullable(excelFile.getOriginalFilename())
                .map(FilenameUtils::getExtension)
                .map(s -> StringUtils.prependIfMissing(s, "."))
                .orElse("");
        final String s3TempFileName = fileName + "_" + uuid + fileExtension;
        final String s3FileName = fileName + fileExtension;

        if (storageUploadExcel) {

            CompletableFuture.runAsync(() -> {
                try {
                    final String url = fileService.uploadPrivateFile(excelFile, "organization/temp", s3TempFileName);
                    log.info("기사 엑셀: {} 에 업로드했습니다.", url);
                } catch (Exception e) {
                    log.warn("기사 엑셀: 업로드 실패했습니다. {}: {}", e.getClass().getSimpleName(), e.getMessage());
                }
            });
        }

        final ProjectWorkbookDTO projectWorkbookDTO = importRiderExcel(userId, null, excelFile);
        final List<ProjectRiderSheetDTO> riders = projectWorkbookDTO.getRiders();
        List<WebRiderDTO> webRiderDTOS = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(riders)) {
            final User user = userService.getUser(userId)
                    .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));
            final Organization organization = organizationService.getOrganizationById(user.getOrganizationId());

            webRiderDTOS = riders.stream()
                    .map(projectRiderSheetDTO -> {
                        final List<Long> riderDepartmentIdList = departmentService.getDepartmentListByDepartmentNameList(organization, projectRiderSheetDTO.getDepartmentNameList()).stream()
                                .map(Department::getDepartmentId)
                                .collect(Collectors.toList());
                        final WebRiderVehicleDTO webRiderVehicleDTO = WebRiderVehicleDTO.of(projectRiderSheetDTO, riderDepartmentIdList, organization);
                        final Rider foundRider = this.getRider(FormatUtil.stripPhoneNumber(webRiderVehicleDTO.getMobile(), false)).orElse(null);

                        if (Objects.nonNull(foundRider)) {
                            //기사 전화번호가 있고 rider org에도 있는지 확인함.
                            final Rider orgFoundRider = this.getRidersOrgId(organizationId, FormatUtil.stripPhoneNumber(webRiderVehicleDTO.getMobile(), false), false).orElse(null);

                            if (Objects.nonNull(orgFoundRider)) {
                                final boolean isTheHyundaiOrg = organizationService.isCodeNameOrganization(organization, TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME);
                                if (isTheHyundaiOrg) {
                                    // [현대백화점] 엑셀 파일로 동일 전화번호의 기사 등록시 기존 기사 정보 업데이트
                                    return this.updateRider(userId, webRiderVehicleDTO, foundRider.getId());
                                } else {
                                    throw new CustomException(HttpStatus.BAD_REQUEST, foundRider.getName() + "기사가 전화번호를 이미 사용중입니다.", false);
                                }
                            } else {
                                //기존 rider에 대한 org 정보를 저장함.
                                riderOrgStatusRepository.save(RiderOrgStatus.builder()
                                        .rider(foundRider)
                                        .orgId(organizationId)
                                        .isDeleted(false)
                                        .build());
                                //기존 rider에 대한 정보를 update 시킴
                                return this.updateRider(userId, webRiderVehicleDTO, foundRider.getId());
                            }
                        } else {
                            final WebRiderDTO webRiderDTO = this.addRider(userId, webRiderVehicleDTO);
                            return webRiderDTO;
                        }
                    }).collect(Collectors.toList());
        } else {
            throw new CustomException(HttpStatus.BAD_REQUEST, "기사 추가를 위한 '기사' 시트가 비어있습니다.", false);
        }

        if (storageUploadExcel) {

            CompletableFuture.runAsync(() -> {
                try {
                    final String url = fileService.movePrivateFile("organization/temp", s3TempFileName, "organization/" + organizationId, s3FileName);
                    log.info("기사 엑셀: {} 로 {}을 이동했습니다.", url, s3TempFileName);
                } catch (Exception e) {
                    log.warn("기사 엑셀: {}을 이동 실패했습니다. {}: {}", s3TempFileName, e.getClass().getSimpleName(), e.getMessage());
                }
            });
        }

        return webRiderDTOS;
    }

    private ProjectWorkbookDTO importRiderExcel(final Long userId,
                                                String projectName,
                                                @NotNull final MultipartFile excelFile) {

        try {
            // 프로젝트 이름이 없는 경우 파일명으로 사용
            if (StringUtils.isBlank(projectName)) {
                projectName = Normalizer.normalize(FilenameUtils.getBaseName(excelFile.getOriginalFilename()), Normalizer.Form.NFC).trim();

                if (StringUtils.isBlank(projectName)) {
                    projectName = ProjectConstant.DEFAULT_PROJECT_NAME;
                }
            } else {
                projectName = projectName.trim();
            }

            // 기사 엑셀 파일 읽기
            final ProjectWorkbookDTO projectWorkbookDTO = (ProjectWorkbookDTO) excelComponent.importExcel(projectName, excelFile, ProjectWorkbookDTO.class);
            return projectWorkbookDTO;
        } catch (PxlException e) {
            final String errorMsg = Optional.ofNullable(e.getMessage()).orElseGet(e::toString);
            throw new CustomException(HttpStatus.BAD_REQUEST, errorMsg, false);
        } catch (Exception e) {
            throw new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, e, false);
        }
    }

    /**
     * 프로젝트 아이디 추가
     *
     * @param riderId
     * @param projectId
     * @return
     */
    @Transactional
    public Rider addProjectId(final Long riderId,
                              final Long projectId) {

        final Rider rider = riderRepository.findById(riderId).orElse(null);
        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);
        final List<RiderOrgStatus> riderOrgStatusList = riderOrgStatusRepository.findByRiderIdAndOrgIdAndIsDeleted(riderId, project.getUser().getOrganizationId(),false);

        if (Objects.nonNull(rider) && CollectionUtils.isNotEmpty(riderOrgStatusList)) {
            if (!rider.getProjectIds().contains(projectId)) {
                rider.getProjectIds().add(projectId);
                riderRepository.save(rider);
            }
            return rider;
        }
        throw new InvalidParameterException("해당 프로젝트 아이디 생성에 실패하였습니다");
    }

    /**
     * 프로젝트에서 기사 삭제
     *
     * @param riderId
     * @param projectId
     * @return
     */
    @Transactional
    public Rider deleteRiderFromProject(@NotNull @Positive final Long riderId,
                                        @NotNull @Positive final Long projectId) {

        final Rider rider = riderRepository.findById(riderId).orElse(null);
        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);

        if (ProjectStatus.DONE.equals(project.getStatus())) {
            throw new CustomException(HttpStatus.NOT_ACCEPTABLE, "프로젝트가 이미 종료되었습니다", false);
        }

        final List<RiderOrgStatus> findOrgStatusList = riderOrgStatusRepository.findByRiderIdAndOrgIdAndIsDeleted(riderId, project.getUser().getOrganizationId(), false);

        if (Objects.nonNull(rider) && CollectionUtils.isNotEmpty(findOrgStatusList)) {
            rider.getProjectIds().remove(projectId);

            //기사가 프로젝트에서 삭제될때 projectid null 만드는 작업을 실행시킴
            riderRecentSettingService.updateRiderRecentSettingWithProjectId(rider, null);

            riderProjectSettingRepository.deleteByRiderIdAndProjectId(riderId, projectId);

            final List<Group> groupsOfRider = groupService.getGroupListByProjectIdAndRiderId(projectId, riderId);
            groupsOfRider.forEach(group -> {
                group.getGroupProjectRiderList().remove(GroupProjectRider.builder().projectId(projectId).riderId(riderId).build());
                groupService.saveGroup(group);
            });

            final List<Delivery> deliveries = deliveryRepository.findByProjectIdAndAllocationsRiderId(projectId, riderId);
            deliveries.forEach(delivery -> {
                delivery.setAllocations(new ArrayList<>());
                deliveryRepository.save(delivery);
            });

            final List<DeliveryAllocation> allocations = deliveryAllocationRepository.findByProjectIdAndRiderIdOrderByOrderNumAsc(projectId, riderId);

            deliveryAllocationRepository.deleteAll(allocations);

            return riderRepository.save(rider);
        }
        throw new InvalidParameterException("해당 프로젝트에서 기사 제거에 실패하였습니다");
    }

    /**
     * Rider 상세 정보 조회
     *
     * @param riderId
     * @return
     */
    public Rider getRider(@NotNull final Long riderId) {

        try {
            final Rider rider = riderRepository.findById(riderId).orElse(null);

            if (Objects.nonNull(rider) && Objects.nonNull(rider.getCreateAt())) {
                return rider;
            }
        } catch (Exception e) {
        }
        throw new CustomException(HttpStatus.NOT_FOUND, "기사아이디 " + riderId + "에 해당하는 기사를 찾을 수 없습니다.", false);
    }

    /**
     * Rider 상세 정보 조회
     *
     * @param mobile
     * @return
     */
    public Optional<Rider> getRider(@NotEmpty final String mobile) {

        return riderRepository.findOne(RiderSpecs.eqMobile(mobile));
    }

    /**
     * Rider가 같은 Org에  있는지 확인하는 작업
     *
     * @param organizationId
     * @param mobile
     * @param deleted
     */
    public Optional<Rider> getRidersOrgId(final Long organizationId,
                                          final String mobile,
                                          final Boolean deleted) {

        final Specification<Rider> specs = RiderSpecs.composeSpecs(null, null, mobile, deleted, organizationId);

        return riderRepository.findOne(specs);
    }

    public Optional<Rider> getRiderById(final Long riderId) {

        if (Objects.isNull(riderId)) {
            return Optional.empty();
        }

        return riderRepository.findOne(RiderSpecs.eqId(riderId));
    }

    /**
     * 전체 Rider 정보 조회
     *
     * @param projectId
     * @param deleted
     * @return
     */
    public List<Rider> getRiders(final Long projectId,
                                 final Boolean deleted) {

        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);

        final Specification<Rider> specs = RiderSpecs.composeSpecs(projectId, null, null, deleted, project.getUser().getOrganizationId()); //orgid 확인 해야함.

        final List<Rider> results = specs == null ? riderRepository.findAll(CommonUtil.getDefaultSort())
                : riderRepository.findAll(specs, CommonUtil.getDefaultSort());

        return results;
    }

    /**
     * simulation Rider 정보 조회
     *
     * @param projectId
     * @param deleted
     * @return
     */
    public List<Rider> getSimulationRiders(final Long projectId,
                                 final Boolean deleted) {

        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);

        Specification<Rider> specs = RiderSpecs.composeSpecs(projectId, null, null, deleted, project.getUser().getOrganizationId()); //orgid 확인 해야함.

        specs = Objects.requireNonNull(specs).and(RiderSpecs.isSimulationRiderTrue());

        final List<Rider> results = specs == null ? riderRepository.findAll(CommonUtil.getDefaultSort())
                : riderRepository.findAll(specs, CommonUtil.getDefaultSort());

        return results;
    }


    /**
     * Rider 중에서 프로젝트 참가 중에서 rider_project_setting 에서 project_pushed_at 없는 놈만 찾아야함.
     *
     * @param projectId
     * @param preProject
     * @param deleted
     * @return
     */
    public List<Rider> getTransferRiders(final Long projectId,
                                         final Project preProject,
                                         final Boolean deleted) {

        Project project = null;

        if (preProject == null) {
            project = projectBasicService.getProjectByIdOrThrowException(projectId);
        } else {
            project = preProject;
        }

        final Specification<Rider> specs = RiderSpecs.composeSpecs(projectId, null, null, deleted, project.getUser().getOrganizationId()); //orgid 확인 해야함.

        final List<Rider> results = specs == null ? riderRepository.findAll(CommonUtil.getDefaultSort())
                : riderRepository.findAll(specs, CommonUtil.getDefaultSort());

        final List<Rider> transferResults = results.stream()
                .filter(r -> this.riderTransferProject(r.getId(), projectId))
                .collect(Collectors.toList());

        return transferResults;
    }

    public boolean riderTransferProject(final Long riderId,
                                        final Long projectId) {

        RiderProjectSetting riderProjectSetting = riderProjectSettingRepository.findByRiderIdAndProjectId(riderId, projectId);
        if (riderProjectSetting.getProjectPushedAt() == null) {
            return true;
        } else {
            return false;
        }
    }

    public List<Rider> getRidersISWorkingStatus(final Long projectId,
                                                final Boolean deleted) {

        List<Rider> listRiders = new ArrayList<>();
        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);

        Specification<Rider> specs = RiderSpecs.composeSpecs(projectId, null, null, deleted, project.getUser().getOrganizationId());


        final List<Rider> results = specs == null ? riderRepository.findAll(CommonUtil.getDefaultSort())
                : riderRepository.findAll(specs, CommonUtil.getDefaultSort());

        for (Rider r : results) {

            // 프로젝트의 기사 업무 종료 확인
            final Specification<RiderWorkCompletion> spec = RiderWorkCompletionSpecs.composeSpecs(
                    Objects.nonNull(r.getId()) ? this.getRider(r.getId()) : null, projectId, null, null);

            final long riderWorkCompletionCount = riderWorkCompletionRepository.count(spec);

            // 기사의 최종 근무 상태 확인
            final RiderWorkStatus riderWorkStatus = riderWorkStatusService.getRiderWorkStatus(r.getId(), projectId);

            //기사 최종 근무 상태가 있을 경우에는 근무 상태와 업무 종료를 같이 확인함.
            if (Objects.nonNull(riderWorkStatus)) {
                // 기사의 배차상태 (수락/거절) 확인
                final RiderDispatchStatus riderDispatchStatus = riderDispatchStatusRepository.findTop1ByRiderIdAndProjectIdOrderByCreateAtDesc(r.getId(), projectId);
                if (riderWorkCompletionCount == 0 && (Objects.isNull(riderDispatchStatus) || DispatchStatus.ACCEPTANCE.equals(riderDispatchStatus.getDispatchStatus()))) {
                    listRiders.add(r);
                } else {
                    log.info("[OnDemand] 해당 기사 {} ({})는 근무중이 아니므로 배차에서 제외됩니다 {} ", r.getName(), r.getId(), riderWorkCompletionCount);
                }
            }
            //기사 최종 근무 상태가 없을 경우에는 업무 종료만 확인함.
            else {
                if (riderWorkCompletionCount == 0) {
                    listRiders.add(r);
                } else {
                    log.info("[OnDemand] 해당 기사 {} ({})는 근무중이 아니므로 배차에서 제외됩니다 . RiderWorkCompletion 가 비었습니다", r.getName(), r.getId());
                }
            }
        }
        return listRiders;
    }

    public boolean isRiderInProject(@NotNull @Positive final Long riderId,
                                    @NotNull @Positive final Long projectId) {

        return riderRepository.existsByRiderIdAndProjectId(riderId, projectId);
    }

    public List<GroupProjectRiderDAO> getRidersByProjectIdAndRiderId(final Long projectId,
                                                                     final Long riderId,
                                                                     final Boolean deleted) {

        final Rider r = getRiderById(riderId).orElse(null);

        final List<GroupProjectRiderDAO> groupProjectRiders = new ArrayList<>();
        final List<Group> groups = groupService.getGroupListByProjectIdAndRiderId(projectId, r.getId());
        if (CollectionUtils.isEmpty(groups)) {        // 권역 없음
            groupProjectRiders.add(new GroupProjectRiderDAO(null, null, r.getId()));
        } else {
            groups.forEach(g -> groupProjectRiders.add(new GroupProjectRiderDAO(g.getGroupId(), g.getGroupName(), r.getId())));
        }

        return groupProjectRiders;
    }

    public List<GroupProjectRiderDAO> getRidersByProjectId(final Long projectId,
                                                           final Boolean deleted) {        // redmine-#1138

        final List<Rider> riders = getRiders(projectId, deleted);
        final List<GroupProjectRiderDAO> groupProjectRiders = new ArrayList<>();

        for (Rider r : riders) {
            final List<Group> groups = groupService.getGroupListByProjectIdAndRiderId(projectId, r.getId());
            if (CollectionUtils.isEmpty(groups)) {        // 권역 없음
                groupProjectRiders.add(new GroupProjectRiderDAO(null, null, r.getId()));
            } else {
                groups.forEach(g -> groupProjectRiders.add(new GroupProjectRiderDAO(g.getGroupId(), g.getGroupName(), r.getId())));
            }
        }

        return groupProjectRiders;
    }

    public List<GroupProjectRiderDAO> getSimulationRidersByProjectId(final Long projectId,
                                                           final Boolean deleted) {        // redmine-#1138

        final List<Rider> riders = getSimulationRiders(projectId, deleted);
        final List<GroupProjectRiderDAO> groupProjectRiders = new ArrayList<>();

        for (Rider r : riders) {
            final List<Group> groups = groupService.getGroupListByProjectIdAndRiderId(projectId, r.getId());
            if (CollectionUtils.isEmpty(groups)) {        // 권역 없음
                groupProjectRiders.add(new GroupProjectRiderDAO(null, null, r.getId()));
            } else {
                groups.forEach(g -> groupProjectRiders.add(new GroupProjectRiderDAO(g.getGroupId(), g.getGroupName(), r.getId())));
            }
        }

        return groupProjectRiders;
    }

    /**
     * DEMO 임시로 추가한것 모든 rider정보 불러옴
     * 전체 Rider 정보 조회
     *
     * @return
     */
    public List<Rider> getAllRiders() {

        return riderRepository.findAll();
    }

    /**
     * 전체 Rider 정보 조회 (페이징)
     *
     * @param projectId
     * @param start
     * @param size
     * @param deleted
     * @return
     */
    public Page<Rider> getPageableRiders(final Long projectId,
                                         final Integer start,
                                         final Integer size,
                                         final Boolean deleted) {

        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);

        final Specification<Rider> specs = RiderSpecs.composeSpecs(projectId, null, null, deleted, project.getUser().getOrganizationId()); //orgid 확인 해야함.

        final Page<Rider> results = (specs == null) ? riderRepository.findAll(CommonUtil.getDefaultPageable(start, size))
                : riderRepository.findAll(specs, CommonUtil.getDefaultPageable(start, size));

        if (!results.isEmpty()) {
            return results;
        }

        throw new ItemNotFoundException();
    }

    /**
     * 기사 목록 조회 (기사 관리에 사용)
     *
     * @param userId
     * @param filterDepartmentIdList
     * @param keyword
     * @param inTransit
     * @param workAuthorities
     * @param pageable
     * @return
     */
    public Page<Rider> getRidersForManagement(final Long userId,
                                              final List<Long> filterDepartmentIdList,
                                              final String keyword,
                                              final RoleType userRole,
                                              final Boolean inTransit,
                                              final List<WorkAuthority> workAuthorities,
                                              final Pageable pageable) {

        Specification<Rider> specs = Specification.where(null);

        if (StringUtils.isWhitespace(keyword) && !StringUtils.equals("", keyword)) {
            return Page.empty(pageable);
        }

        if (StringUtils.isNotBlank(keyword)) {
            String keywordMobile = keyword.replace("-", "");
            specs = Objects.requireNonNull(specs).and(RiderSpecs.nameLike(keyword)
                    .or(RiderSpecs.mobileLike(keywordMobile))
                    .or(RiderSpecs.licensePlateLike(keyword))
                    .or(RiderSpecs.employeeNumberLike(keyword)));
        }

        if (Boolean.TRUE.equals(inTransit)) {
            final Set<WorkStatus> workStatus = Stream.of(WorkStatus.START_WORK, WorkStatus.START_WORK_LUNCH, WorkStatus.START_WORK_REST)
                    .collect(Collectors.toCollection(HashSet::new));
            specs = Objects.requireNonNull(specs).and(RiderSpecs.workStatus(workStatus));
        }

        if (CollectionUtils.isNotEmpty(workAuthorities)) {
            specs = Objects.requireNonNull(specs).and(RiderSpecs.inWorkAuthority(workAuthorities));
        }

        //가배차 기능 추가로 rider info의 isSimulationRider flag 추가함.
        specs = Objects.requireNonNull(specs).and(RiderSpecs.isSimulationRiderNull().or(RiderSpecs.isSimulationRiderFalse()));

        final User user = userService.getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));
        final Long orgId = user.getOrganizationId();
        final Organization organization = organizationService.getOrganizationById(orgId);
        final boolean orgHasDepartment = departmentService.existDepartmentOfOrganization(organization);
        final List<Department> authorizedDepartmentList = userDepartmentService.getAuthorizedDepartmentListOfUser(user, organization, true);
        final List<Department> filterDepartmentList = departmentService.getDescendantDepartmentListOfDepartmentIdList(filterDepartmentIdList);

        if (CollectionUtils.isNotEmpty(authorizedDepartmentList)) {
            log.info("getRidersForManagement: 부서아이디 {}에 대한 기사 목록을 조회합니다.",
                    authorizedDepartmentList.stream().map(Department::getDepartmentId).collect(Collectors.toList()));
        }

        specs = specs.and(RiderSpecs.hasAuthority(userRole, user, organization, orgHasDepartment, authorizedDepartmentList, filterDepartmentList));

        return riderRepository.findAll(specs, pageable);
    }

    /**
     * 기사들 삭제 (기사 관리에 사용)
     *
     * @param user
     * @param projectIdList
     * @param riderIdList
     * @return
     */
    @Transactional  //(noRollbackFor = {FeignException.class})
    public List<Rider> deleteRidersForManagement(final User user,
                                                 final Set<Long> projectIdList,
                                                 @NotEmpty final List<Long> riderIdList) {

        final Long orgId = user.getOrganizationId();
        final Long anonymousOrgId = organizationService.getOrganizationByCodeName(UserConstant.ANONYMOUS_ORG_CODE_NAME).getId();

        final List<Rider> riderList = riderRepository.findByIdIn(riderIdList);
        if (CollectionUtils.isEmpty(riderList)) {
            return new ArrayList<>();
        }
        final List<Rider> deletedRiderList = new ArrayList<>();

        riderList.forEach(rider -> {
            final Long riderId = rider.getId();

            //삭제할 기사님이 예정 및 관제중 프로젝트에 있을 경우 기사 삭제 안되게 기능 강화 시킴
            List<Long> projectIds = rider.getProjectIds();

            if(CollectionUtils.isNotEmpty(projectIds)) {
                final String projectNames = projectRepository.findByOrganizationIdAndProjectIds(orgId, projectIds, ProjectStatus.DONE.getEnglish());
                if(ObjectUtils.isNotEmpty(projectNames)){
                    throw new CustomException(HttpStatus.BAD_REQUEST, rider.getName() + " 기사님이 (" + projectNames + ") 포함되어 있어서 삭제 할 수 없습니다.", false);
                }
            }

            //admin으로 로그인 했을경우 모든 기사의 정보를 삭제하게 한다.
            //Track 정보도 삭제 하게 함.
            if (Objects.isNull(orgId)) {
                //모든 기사의 org 정보를 삭제함.
                List<RiderOrgStatus> riderOrgStatus = riderOrgStatusRepository.findByRiderIdAndIsDeleted(riderId, false);
                if (Objects.nonNull(riderOrgStatus)) {
                    riderOrgStatus.forEach(orgStatus -> {
                        orgStatus.setIsDeleted(true);
                        riderOrgStatusRepository.save(orgStatus);
                    });
                } else {
                    log.warn("riderId: {}, orgId: {} 에 해당하는 riderOrgStatus를 찾을 수 없습니다.", riderId, orgId);
                }

                riderDepartmentService.removeRiderFromDepartmentListOfOrganization(rider, null);

                deletedRiderList.add(rider);

                //기사 정보 초기화 시킴
                rider.setMobile(null);
            } else {
                // 관제자가 권한이 있는 부서에서 기사를 삭제한다.
                final Organization organization = organizationService.getOrganizationByOrganizationId(orgId);
                final List<Department> authorizedDepartmentList = userDepartmentService.getAuthorizedDepartmentListOfUser(user, organization, true);
                riderDepartmentService.removeRiderFromDepartmentList(rider, authorizedDepartmentList);

                // 아직 조직의 다른 부서에 소속이 남아있으면 조직에서 삭제하지 않는다.
                final List<Department> assignedDepartmentList = riderDepartmentService.getAssignedDepartmentListOfRider(rider, organization, false);
                if (CollectionUtils.isNotEmpty(assignedDepartmentList)) {
                    return;
                }

                //기사의 org를 삭제하는 기능
                final List<RiderOrgStatus> riderOrgStatusList = riderOrgStatusRepository.findByRiderIdAndOrgIdAndIsDeleted(riderId, orgId, false);
                if (CollectionUtils.isNotEmpty(riderOrgStatusList)) {
                    for (final RiderOrgStatus riderOrgStatus : riderOrgStatusList) {
                        riderOrgStatus.setIsDeleted(true);
                        riderOrgStatusRepository.save(riderOrgStatus);
                    }
                } else {
                    log.warn("riderId: {}, orgId: {} 에 해당하는 riderOrgStatus를 찾을 수 없습니다.", riderId, orgId);
                }

                //riderOrgCount 를 anoymouse org를 제외하고 찾았을 떼 0개 이면 더이상 없다고 생가하고 mobile 삭제하게 해야함.
                Integer riderOrgCount = riderOrgStatusRepository.countByRiderIdAndIsDeletedAndOrgIdNot(riderId, false, anonymousOrgId);

                log.info("남아 있는 org 갯수를 확인함 {}",riderOrgCount);

                //기사의 anonymous 제외하고 0개만 있을 경우 rider에 모든 정보를 삭제해야함.
                if (riderOrgCount == 0) {
                    //만약에 기사 org에 anonymous 있을 경우 삭제 하는 코드 추가함.
                    final List<RiderOrgStatus> riderAnonymousOrgStatusList = riderOrgStatusRepository.findByRiderIdAndOrgIdAndIsDeleted(riderId, anonymousOrgId, false);
                    if (CollectionUtils.isNotEmpty(riderAnonymousOrgStatusList)) {
                        for (final RiderOrgStatus riderAnonymousOrgStatus : riderAnonymousOrgStatusList) {
                            riderAnonymousOrgStatus.setIsDeleted(true);
                            riderOrgStatusRepository.save(riderAnonymousOrgStatus);
                        }
                    } else {
                        log.warn("riderId: {}, anonymousOrgId: {} 에 해당하는 riderOrgStatus를 찾을 수 없습니다.", riderId, anonymousOrgId);
                    }

                    deletedRiderList.add(rider);

                    //모든 rider org가 삭제 되었으므로 기사 정보 초기화 시킴
                    rider.setMobile(null);
                } else {
                    log.info("riderId: {}, 해당 기사가 다른 조직에도 소속돼있어 삭제할 수 없습니다.", riderId);
                }
            }
        });

        riderRepository.saveAll(riderList);

        return deletedRiderList;
    }

    /**
     * Rider 수 조회
     *
     * @param projectId
     * @param deleted
     * @return
     */
    public long getCountOfRiders(final Long projectId,
                                 final Boolean deleted) {

        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);

        final Specification<Rider> specs = RiderSpecs.composeSpecs(projectId, null, null, deleted, project.getUser().getOrganizationId()); //orgid 확인 해야함.

        return specs == null ? riderRepository.count()
                : riderRepository.count(specs);
    }

    /**
     * Rider 삭제 여부 설정
     *
     * @param riderId
     * @param deleted
     */
    @Transactional
    public void setDeleted(final Long riderId,
                           final Boolean deleted) {

        try {
            final Rider rider = riderRepository.findById(riderId).orElse(null);

            if (Objects.nonNull(rider)) {
//                rider.setDeleted(deleted);
                riderRepository.save(rider);
                return;
            }
        } catch (Exception e) {
            log.error("setDeleted() " + e.getMessage());
        }

        throw new InvalidParameterException("해당 기사 삭제에 실패하였습니다");
    }

    /**
     * Rider 삭제
     *
     * @param riderId
     */
    @Transactional
    public void delete(final Long riderId) {

        // TODO: 관련 데이터 모두 삭제
        riderRepository.deleteById(riderId);
    }

    /**
     * Rider 삭제 (by projectId)
     *
     * @param projectId
     */
    @Transactional
    public void deleteAll(final Long projectId) {

        final List<Rider> riders = riderRepository.findAll(RiderSpecs.eqProjectId(projectId));
        for (Rider rider : riders) {
            riderActivityRepository.deleteAll(rider.getActivities());
            riderWorkStatusRepository.deleteAll(rider.getWorkStatusList());
        }

        riderRepository.deleteAll(riders);
    }

    @Transactional
    public Integer deleteByProjectId(@NotNull @Positive final Long projectId) {

        // 기사 자체를 삭제하지는 않는다.
        return riderRepository.deleteRiderProjectIdByProjectId(projectId);
    }

    /**
     * 기사의 프로파일 사진 업로드
     *
     * @param riderId
     * @param image
     * @return
     */
    @Transactional
    public boolean uploadRiderProfileImage(final Long riderId, final MultipartFile image) throws IOException {

        try {
            final Rider rider = getRider(riderId);

            final List<File> fileList = fileService.getFileList(riderId, FileCategory.RIDER_PROFILE, FileType.IMAGE);
            if (CollectionUtils.isEmpty(fileList)) {
                fileService.saveFile(image, FileCategory.RIDER_PROFILE, rider);
            } else {
                fileService.updateFile(image, fileList.get(0), rider);
            }

            return true;
        } catch (IOException e) {
            log.error("uploadProfileImage() :: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 기사의 프로파일 사진 다운로드
     *
     * @param riderId
     * @return
     */
    public Resource downloadRiderProfileImage(final Long riderId) throws IOException {

        try {
            final Rider rider = getRider(riderId);

            final List<File> fileList = fileService.getFileList(rider.getId(), FileCategory.RIDER_PROFILE, FileType.IMAGE);

            if (!CollectionUtils.isEmpty(fileList)) {
                String folderName = fileService.getFolderPath(fileList.get(0), rider);
                return fileService.downloadFile(folderName, fileList.get(0).getSavedName());
            } else {
                return null;
            }
        } catch (IOException e) {
            log.error("downloadProfileImage() :: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 기사의 프로파일 사진 url 주소
     *
     * @param riderId
     * @return
     */
    public String getRiderProfileImageUrl(final Long riderId) {

        final Rider rider = getRider(riderId);

        final List<File> fileList = fileService.getFileList(rider.getId(), FileCategory.RIDER_PROFILE, FileType.IMAGE);

        if (!CollectionUtils.isEmpty(fileList)) {
            return fileService.getFileUrl(fileList.get(0), rider, false);
        } else {
            return null;
        }
    }

    /**
     * 기사의 프로파일 사진 제거
     *
     * @param riderId
     * @return
     */
    @Transactional
    public void deleteRiderProfileImage(final Long riderId) {

        final List<File> fileList = fileService.getFileList(riderId, FileCategory.RIDER_PROFILE, FileType.IMAGE);
        if (CollectionUtils.isEmpty(fileList)) {
            return;
        }

        File profileImageFile = fileList.get(0);
        fileService.deleteFile(profileImageFile, null);
    }

    /**
     * 기사의 링크 상태 변경
     *
     * @param riderId    라이더 아이디
     * @param linkStatus 링크 상태
     */
    @Transactional
    public void updateRiderLinkStatus(@NotNull final Long riderId,
                                      @NotNull final LinkStatus linkStatus) {

        try {
            final Rider rider = getRider(riderId);
            rider.setLinkStatus(linkStatus);
            riderRepository.save(rider);
        } catch (Exception e) {
            log.error("기사 링크 상태 업데이트 실패: {}", e.getMessage());
        }
    }

    /**
     * 기사의 workStatus 변경
     *
     * @param riderId    라이더 아이디
     * @param workStatus 근무상태
     */
    @Transactional
    public void updateRiderWorkStatus(@NotNull final Long riderId,
                                      @NotNull final WorkStatus workStatus) {

        try {
            final Rider rider = getRider(riderId);
            rider.setWorkStatus(workStatus);
            riderRepository.save(rider);
        } catch (Exception e) {
            log.error("기사 근무 상태 업데이트 실패", e);
        }
    }

    /**
     * 기사의 ProjectIds 변경
     *
     * @param rider 라이더 정보
     */
    @Transactional
    public void updateRiderInfo(@NotNull final Rider rider) {

        try {
            riderRepository.save(rider);
        } catch (Exception e) {
            log.error("기사 근무 상태 업데이트 실패", e);
        }
    }

    /**
     * ID가 존재하는 Rider 업데이트
     *
     * @param id
     * @param email
     * @param password
     * @param name
     * @param mobile
     * @param projectIds
     * @param skillLevel
     * @param position
     * @param employeeNumber
     * @param autoCreate
     * @param workAuthority
     * @return
     */
    @Transactional
    public Rider updateRiderById(final Long id,
                                 final String email,
                                 final String password,
                                 final String name,
                                 final String mobile,
                                 final List<Long> projectIds,
                                 final Integer skillLevel,
                                 final String position,
                                 final String employeeNumber,
                                 final Boolean autoCreate,
                                 final WorkAuthority workAuthority,
                                 final Long dispatchNumber) {

        final Rider rider = getRider(id);

        try {
            if (StringUtils.isNotBlank(email)) {
                rider.setEmail(email);
            }
            if (StringUtils.isNotBlank(name)) {
                rider.setName(name);
            }
            if (StringUtils.isNotBlank(mobile)) {
                rider.setMobile(mobile);
            }
            if (StringUtils.isNotBlank(password)) {
                rider.setPassword(passwordEncoder.encode(password));
            }
            if (Objects.nonNull(skillLevel)) {
                rider.setSkillLevel(skillLevel);
            }
            if (StringUtils.isNotBlank(position)) {
                rider.setPosition(position);
            }
            if (StringUtils.isNotBlank(employeeNumber)) {
                rider.setEmployeeNumber(employeeNumber);
            }
            if (CollectionUtils.isNotEmpty(projectIds)) {
                rider.getProjectIds().addAll(projectIds);
            }
            if (Objects.nonNull(autoCreate)) {
                rider.setAutoCreate(autoCreate);
            }
            if (Objects.nonNull(workAuthority)) {
                rider.setWorkAuthority(workAuthority);
            }
            if(Objects.nonNull(dispatchNumber)){
                rider.setDispatchNumber(dispatchNumber);
            }

            return riderRepository.save(rider);
        } catch (Exception e) {
            //log.error("registerRider() " + e.getMessage());
            log.error("rider 업데이트 에러..." + e.getLocalizedMessage());
        }

        throw new InvalidParameterException("기사 정보 업데이트에 실패하였습니다");
    }

    /**
     * 기사 전화 번호와 Org codeName 이용하여 기사가 포함되어 있는지 확인하는 코드
     *
     * @param mobile   (기사 전화 번호)
     * @param codeName (org code name)
     * @return
     */
    public Boolean isCheckingOrgCodeNameInRider(final String mobile,
                                                final String codeName) {

        if (Objects.isNull(codeName)) {
            Rider rider = null;
            rider = riderRepository.findByMobile(mobile);
            if (Objects.isNull(rider)) {
                return false;
            } else {
                return true;
            }
        } else {
            final Organization organization = organizationService.getOrganizationByCodeName(codeName);
            final Optional<Rider> result = riderRepository.findOne(RiderSpecs.composeSpecs(null, null, mobile, false, organization.getId()));

            return result.isPresent();
        }
    }

    @Transactional
    public List<ProjectRiderSheetDTO> getProjectRiderSheetDtoListByDepartment(final Department department) {

        final List<Rider> riderList = riderDepartmentService.getAssignedRiderListOfDepartment(null, department, false);

        final Long orgId = department.getOrganization().getId();
        final List<ProjectRiderSheetDTO> projectRiderSheetDTOList = riderList.stream()
                .filter(r -> {
                    log.info("[getProjectRiderSheetDtoListByDepartment] riderId: {}, orgId: {}", r.getId(), orgId);
                    return riderOrgStatusRepository.existsByRiderIdAndOrgIdAndIsDeleted(r.getId(), orgId, false);
                })
                .map(ProjectRiderSheetDTO::of)
                .collect(Collectors.toList());

        return projectRiderSheetDTOList;
    }

    // 자동 생성된 프로젝트에서 자동 rider flag와 rider의 ownerUserId를 확인하여 프로젝트에 rider를 추가하기 위해서 사용함.
    @Transactional
    public List<ProjectRiderSheetDTO> getRidersByOrgIdAndAutoCreateAndOwnerUserId(final Long orgId,
                                                                                  final Long userId) {

//        List<Rider> riderList = riderRepository.findByOrganizationIdAndAutoCreateAndOwnerUserId(orgId, true, userId);
        final List<Rider> riderList = riderRepository.findByAutoCreateAndOwnerUserId(true, userId);

        if (CollectionUtils.isEmpty(riderList)) {
            return new ArrayList<>();
        } else {
            final List<ProjectRiderSheetDTO> projectRiderSheetDTOList = riderList.stream()
                    .filter(r -> CollectionUtils.isNotEmpty(riderOrgStatusRepository.findByRiderIdAndOrgIdAndIsDeleted(r.getId(), orgId, false)))
                    .map(r -> ProjectRiderSheetDTO.of(r))
                    .collect(Collectors.toList());

            return projectRiderSheetDTOList;
        }
    }

    //아직 안쓰지만 임시로 만들어둠.
    @Transactional
    public void setWorkingTimeRider(Rider rider, LocalTime startWorkingTime, LocalTime endWorkingTime) {
        final RiderRecentSetting riderRecentSetting = riderRecentSettingService.getRiderRecentSetting(rider);
        riderRecentSetting.setWorkingStartTime(startWorkingTime);
        riderRecentSetting.setWorkingEndTime(endWorkingTime);
    }

    //투명탑 기사 찾음
    public Optional<Rider> getRiderTransparentTop(Boolean checked) {
        return riderRepository.findOne(RiderSpecs.eqTransparentTop(checked));
    }

    //투명탑 기사들 찾음
    public Long getCountRidersListTransparentTop(Boolean checked) {
        return riderRepository.count(RiderSpecs.eqTransparentTop(checked));
    }

    /**
     * 기사의 배차상태 저장
     *
     * @param riderId
     * @param projectId
     * @param dispatchStatus
     * @return
     */
    @Transactional
    public RiderDispatchStatus saveRiderDispatchStatus(@NotNull @Positive final Long riderId,
                                                       @NotNull @Positive final Long projectId,
                                                       @NotNull final DispatchStatus dispatchStatus) {

        log.info("[saveRiderDispatchStatus] riderId:{}, projectId:{}, dispatchStatus:{}", riderId, projectId, dispatchStatus.toString());

        return riderDispatchStatusRepository.save(RiderDispatchStatus.builder()
                .riderId(riderId)
                .projectId(projectId)
                .dispatchStatus(dispatchStatus)
                .build()
       );
    }

    /**
     * 기사의 최신 배차상태 조회
     *
     * @param riderId
     * @param projectId
     * @return
     */
    public RiderDispatchStatus getRiderDispatchStatus(@NotNull @Positive final Long riderId,
                                                      @NotNull @Positive final Long projectId) {

        return riderDispatchStatusRepository.findTop1ByRiderIdAndProjectIdOrderByCreateAtDesc(riderId, projectId);
    }

    /**
     * 기사의 배차상태 삭제
     *
     * @param projectId
     * @return
     */
    @Transactional
    public Integer deleteRiderDispatchStatusByProjectId(@NotNull @Positive final Long projectId) {

        return riderDispatchStatusRepository.deleteByProjectId(projectId);
    }

    /**
     * 기사 프로젝트 재 할당을 위한 코드
     */
    @Transactional
    public void deleteByProjectStatus(final Rider rider,
                                      final Long projectId) {

        RiderDispatchStatus dispatchStatus = this.getRiderDispatchStatus(rider.getId(), projectId);
        RiderWorkStatus workStatus = riderWorkStatusRepository.findFirstByRiderIdAndProjectIdOrderByTimestamp(rider.getId(), projectId);
        RiderWorkCompletion riderWorkCompletion = riderWorkCompletionRepository.findByRiderIdAndProjectId(rider.getId(), projectId);

        LocalDateTime timestamp = LocalDateTime.now();

        if (riderWorkCompletion != null) {

            //최신 work completion 값을 삭제함.
            riderWorkCompletionRepository.delete(riderWorkCompletion);

            //최신 값이 배차 거부 일때 배차 대기로 변경함.
            if (dispatchStatus != null && dispatchStatus.equals(DispatchStatus.REJECTION)) {
                riderDispatchStatusRepository.save(RiderDispatchStatus.builder()
                        .riderId(rider.getId())
                        .projectId(projectId)
                        .dispatchStatus(DispatchStatus.ACCEPTANCE).build());
            }
            //최신 work state 값을 변경함
            if (workStatus != null && workStatus.getWorkStatus() != WorkStatus.START_WORK) {
                riderWorkStatusRepository.save(RiderWorkStatus.builder()
                        .rider(rider)
                        .projectId(projectId)
                        .workStatus(WorkStatus.START_WORK)
                        .timestamp(timestamp).build());
            }
        }
    }

    public List<Organization> getOrganizationListOfRider(final Rider rider) {

        final Organization anonymousOrganization = organizationService.getOrganizationByCodeName(UserConstant.ANONYMOUS_ORG_CODE_NAME);

        return riderOrgStatusRepository.findByRiderIdAndIsDeleted(rider.getId(), false).stream()
                .map(riderOrgStatus -> organizationService.getOrganizationById(riderOrgStatus.getOrgId()))
                .filter(org -> Objects.nonNull(org) && !Objects.equals(org, anonymousOrganization))
                .collect(Collectors.toList());
    }

    public List<Rider> getRidersByOrgIdAndAutoCreate(final Long orgId) {

        return riderRepository.findAll(RiderSpecs.joinWorkOrganizationIdAndIsDeleted(orgId, false).and(RiderSpecs.eqAutoCreate(true)));
    }

    public List<Rider> getRidersByOrgIdAndDepartmentListAdnAutoCreate(final Long orgId, final List<Department> authorizedDepartmentList) {

        Specification<Rider> specs = Specification.where(null);

        specs = Objects.requireNonNull(specs).and(RiderSpecs.joinWorkOrganizationIdAndIsDeleted(orgId, false));
        specs = Objects.requireNonNull(specs).and(RiderSpecs.eqAutoCreate(true));

        if(CollectionUtils.isNotEmpty(authorizedDepartmentList))
            specs = Objects.requireNonNull(specs).and(RiderSpecs.inDepartmentList(authorizedDepartmentList));

        return riderRepository.findAll(specs);
    }

    /**
     * 특정 점포명의 배송담당자 ID 목록 조회
     *
     * @param groupName
     * @param fromDt
     * @param toDt
     * @return
     */
    public List<Long> getDriverIdsOfBranch(final String groupName,
                                         final LocalDateTime fromDt,
                                         final LocalDateTime toDt) {

        return riderRepository.findRiderIdsOfBranch(groupName, fromDt, toDt);
    }
}