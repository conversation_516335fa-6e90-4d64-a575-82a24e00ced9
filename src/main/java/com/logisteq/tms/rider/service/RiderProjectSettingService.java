package com.logisteq.tms.rider.service;

import com.logisteq.common.exception.CustomException;
import com.logisteq.common.exception.InvalidParameterException;
import com.logisteq.common.util.CommonUtil;
import com.logisteq.tms.address.service.AddressService;
import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.spec.DeliverySpecs;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.repository.DeliveryRepository;
import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.dto.ProjectsDeliverStatusCountDTO;
import com.logisteq.tms.project.service.ProjectBasicService;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.domain.RiderProjectSetting;
import com.logisteq.tms.rider.domain.RiderRecentSetting;
import com.logisteq.tms.rider.domain.spec.RiderProjectSettingSpecs;
import com.logisteq.tms.rider.dto.DeliveriesReceivedAckRequest;
import com.logisteq.tms.rider.dto.RiderProjectSettingDTO;
import com.logisteq.tms.rider.repository.RiderProjectSettingRepository;
import com.logisteq.tms.vehicle.domain.Vehicle;
import com.logisteq.tms.vehicle.domain.VehicleModel;
import com.logisteq.tms.vehicle.dto.VehicleRouteParamDTO;
import com.logisteq.tms.vehicle.service.VehicleService;
import com.logisteq.tms.vehicle.types.VehicleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Validated
@Service
public class RiderProjectSettingService {

    private final RiderProjectSettingRepository riderProjectSettingRepository;
    private final DeliveryRepository deliveryRepository;
    private final RiderService riderService;
    private final VehicleService vehicleService;
    private final AddressService addressService;
    private final ProjectBasicService projectBasicService;

    private final RiderRecentSettingService riderRecentSettingService;

    @Autowired
    public RiderProjectSettingService(final RiderProjectSettingRepository riderProjectSettingRepository,
                                      final DeliveryRepository deliveryRepository,
                                      final RiderService riderService,
                                      final VehicleService vehicleService,
                                      final AddressService addressService,
                                      final ProjectBasicService projectBasicService,
                                      final RiderRecentSettingService riderRecentSettingService) {

        this.riderProjectSettingRepository = riderProjectSettingRepository;
        this.deliveryRepository = deliveryRepository;
        this.riderService = riderService;
        this.vehicleService = vehicleService;
        this.addressService = addressService;
        this.projectBasicService = projectBasicService;
        this.riderRecentSettingService = riderRecentSettingService;
    }

    /**
     * 프로젝트 설정 저장
     *
     * @param riderId
     * @param projectId
     * @param projectSettingDTO
     * @return
     */
    @Transactional
    public RiderProjectSetting saveProjectSetting(@NotNull final Long riderId,
                                                  @NotNull final Long projectId,
                                                  @NotNull final RiderProjectSettingDTO projectSettingDTO) {

        final Optional<Rider> foundRider = Optional.ofNullable(riderService.getRider(riderId));

        if (foundRider.isPresent()) {
            // redmine-#1138 [ 저장된 값이 있으면 일단 그것을 반환한다.
            // TODO: 기사 프로젝트 설정이 업데이트되는 경우는 나중에 고려해야 한다.
            RiderProjectSetting savedRiderProjectSetting = null;
            try {
                savedRiderProjectSetting = loadProjectSetting(riderId, projectId);
            } catch (Exception e) {
//                log.warn("loadProjectSettings throw exception: {}", e);
            }
            if (Objects.nonNull(savedRiderProjectSetting)) {
                return savedRiderProjectSetting;
            }
            // redmine-#1138 ]

            final RiderProjectSetting projectSetting = RiderProjectSettingDTO.parseFromRiderProjectSettingDTO(projectSettingDTO);
            final Vehicle foundVehicle = Objects.nonNull(projectSettingDTO.getVehicleId()) ? vehicleService.getVehicle(projectSettingDTO.getVehicleId()) : null;

            projectSetting.setRider(foundRider.get());
            projectSetting.setProjectId(projectId);
            projectSetting.setVehicle(foundVehicle);

            if (Objects.isNull(projectSetting.getWorkingStartLocation())
                    && StringUtils.isNotEmpty(projectSetting.getWorkingStartAddress())
                    && Objects.isNull(projectSetting.getWorkingStartLocation())) {

                projectSetting.setWorkingStartLocation(addressService.convertAddressToPoint(projectSetting.getWorkingStartAddress()));
            }

            // 주소는 있는데 좌표가 없는 경우에 채워 준다.
            if (Objects.isNull(projectSetting.getWorkingEndAddress())
                    && StringUtils.isNotEmpty(projectSetting.getWorkingEndAddress())
                    && Objects.isNull(projectSetting.getWorkingEndLocation())) {

                projectSetting.setWorkingEndLocation(addressService.convertAddressToPoint(projectSetting.getWorkingEndAddress()));
            }

            return riderProjectSettingRepository.save(projectSetting);
        }

        throw new InvalidParameterException("프로젝트 세팅 설정에 실패하였습니다");
    }

    @Transactional
    public RiderProjectSetting updateProjectSetting(@NotNull final Long riderId,
                                                    @NotNull final Long projectId,
                                                    @NotNull final RiderProjectSettingDTO projectSettingDTO) {

        final Optional<Rider> foundRider = Optional.ofNullable(riderService.getRider(riderId));

        if (foundRider.isPresent()) {
            // redmine-#1138 [ 저장된 값이 있으면 일단 그것을 반환한다.
            // TODO: 기사 프로젝트 설정이 업데이트되는 경우는 나중에 고려해야 한다.
            RiderProjectSetting savedRiderProjectSetting = null;
            try {
                savedRiderProjectSetting = loadProjectSetting(riderId, projectId);
            } catch (Exception e) {

            }
            if (Objects.isNull(savedRiderProjectSetting)) {
                return null;
            }
            // redmine-#1138 ]

            final Vehicle foundVehicle = Objects.nonNull(projectSettingDTO.getVehicleId()) ? vehicleService.getVehicle(projectSettingDTO.getVehicleId()) : null;
            final RiderProjectSetting projectSetting = RiderProjectSettingDTO.parseFromRiderProjectSettingDTO(projectSettingDTO);

            savedRiderProjectSetting.setRider(foundRider.get());
            savedRiderProjectSetting.setProjectId(projectId);
            savedRiderProjectSetting.setVehicle(foundVehicle);
            savedRiderProjectSetting.setRegionCodeList(projectSetting.getRegionCodeList());

            final String note = projectSettingDTO.getNote();
            if (StringUtils.isNotBlank(note)) {
                savedRiderProjectSetting.setNote(note);
            }

            if (StringUtils.isNotEmpty(projectSetting.getWorkingStartAddress()) && Objects.isNull(projectSetting.getWorkingStartLocation())) {
                final String oldWorkingStartAddress = savedRiderProjectSetting.getWorkingStartAddress();
                if (StringUtils.isEmpty(oldWorkingStartAddress) || !oldWorkingStartAddress.equals(projectSetting.getWorkingStartAddress())) {
                    savedRiderProjectSetting.setWorkingStartAddress(projectSetting.getWorkingStartAddress());
                    savedRiderProjectSetting.setWorkingStartLocation(addressService.convertAddressToPoint(projectSetting.getWorkingStartAddress()));
                }
            }

            if (StringUtils.isNotEmpty(projectSetting.getWorkingEndAddress()) && Objects.isNull(projectSetting.getWorkingEndLocation())) {
                final String oldWorkingEndAddress = savedRiderProjectSetting.getWorkingEndAddress();
                if (StringUtils.isEmpty(oldWorkingEndAddress) || !oldWorkingEndAddress.equals(projectSetting.getWorkingEndAddress())) {
                    savedRiderProjectSetting.setWorkingEndAddress(projectSetting.getWorkingEndAddress());
                    savedRiderProjectSetting.setWorkingEndLocation(addressService.convertAddressToPoint(projectSetting.getWorkingEndAddress()));
                }
            }

            return riderProjectSettingRepository.save(savedRiderProjectSetting);
        }

        throw new InvalidParameterException("프로젝트 세팅 업데이트에 실패하였습니다");
    }

    /**
     * 프로젝트 설정 읽어오기
     *
     * @param riderId
     * @param projectId
     * @return
     */
    public RiderProjectSetting loadProjectSetting(@NotNull final Long riderId,
                                                  @NotNull final Long projectId) {

        final RiderProjectSetting riderProjectSetting = riderProjectSettingRepository.findByRiderIdAndProjectId(riderId, projectId);
        if (Objects.isNull(riderProjectSetting)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "기사 아이디 " + riderId + ", 프로젝트 아이디 " + projectId + "에 해당하는 설정 정보가 없습니다.", false);
        }

        return riderProjectSetting;
    }

    public List<RiderProjectSetting> loadProjectSettings(@NotNull final List<Long> riderIdList,
                                                         @NotNull final Long projectId) {

        final List<RiderProjectSetting> riderProjectSettings = riderProjectSettingRepository.findByRiderIdInAndProjectId(riderIdList, projectId);

        return riderProjectSettings;
    }

    public List<RiderProjectSetting> loadProjectSettings(@NotNull final Long riderId,
                                                         @NotNull final List<Long> projectIdList) {

        final List<RiderProjectSetting> riderProjectSettings = riderProjectSettingRepository.findByRiderIdAndProjectIdIn(riderId, projectIdList);

        return riderProjectSettings;
    }

    /**
     * 프로젝트, 기사에 대한 프로젝트 설정 조회
     *
     * @param projectId
     * @param riderId
     * @return
     */
    public List<RiderProjectSetting> getRiderProjectSettings(final Long projectId,
                                                             final Long riderId,
                                                             final Sort sort) {

        return riderProjectSettingRepository.findAll(
                RiderProjectSettingSpecs.composeSpecs(Optional.ofNullable(riderId)
                                .map(riderService::getRider)
                                .orElse(null),
                        projectId),
                Optional.ofNullable(sort).orElseGet(CommonUtil::getDefaultSort));
    }

    /**
     * 특정 Rider의 마지막 프로젝트 설정 정보 반환
     *
     * @param riderId
     * @return
     */
    public Optional<RiderProjectSetting> getLastProjectSettingByRiderId(@NotNull final Long riderId) {

        return riderProjectSettingRepository.findFirstByRiderIdOrderByCreateAtDesc(riderId);
    }

    @Transactional
    public void setRiderDeliveriesReceived(@NotNull @Positive Long riderId,
                                           @NotNull @Positive Long projectId,
                                           @NotNull DeliveriesReceivedAckRequest request) {

        Optional.ofNullable(riderProjectSettingRepository.findByRiderIdAndProjectId(riderId, projectId))
                .ifPresent(rps -> {
                    rps.setAckTimestamp(request.getAckTimestamp());
                    rps.setAppId(request.getAppId());
                    rps.setAppVersion(request.getAppVersion());
                    rps.setResourceVersion(request.getResourceVersion());
                    rps.setApiVersion(request.getApiVersion());
                    rps.setAndroidVersion(request.getAndroidVersion());
                    rps.setBrandName(request.getBrandName());
                    rps.setModelName(request.getModelName());
                    riderProjectSettingRepository.save(rps);
                });
    }

    /**
     * 특정 Rider의 프로젝트 설정 정보 삭제
     *
     * @param riderId
     * @param projectId
     */
    @Transactional
    public void deleteProjectSetting(@NotNull final Long riderId,
                                     final Long projectId) {

        try {
            if (Objects.nonNull(projectId)) {
                riderProjectSettingRepository.deleteByRiderIdAndProjectId(riderId, projectId);
            } else {
                riderProjectSettingRepository.deleteByRiderId(riderId);
            }
            return;
        } catch (Exception e) {
            log.error("deleteProjectSetting() " + e.getMessage());
        }

        throw new InvalidParameterException("기사의 프로젝트 세팅 삭제에 실패하였습니다 ");
    }

    @Transactional
    public Integer deleteByProjectId(@NotNull @Positive final Long projectId) {

        return riderProjectSettingRepository.deleteByProjectId(projectId);
    }

    @Transactional
    public RiderProjectSetting saveProjectSetting(final RiderProjectSetting rps) {

        return riderProjectSettingRepository.save(rps);
    }

    @Transactional
    public List<RiderProjectSetting> saveProjectSettings(final List<RiderProjectSetting> rpsList) {

        return riderProjectSettingRepository.saveAll(rpsList);
    }

    /**
     * riderId와 projectId를 이용하여 rider와 매핑된 vehicle의 type을 가져온다.
     *
     * @param riderId
     * @param projectId
     * @return
     */
    public VehicleType getVehicleTypeOfProjectRider(@NotNull final Long riderId,
                                                    @NotNull final Long projectId) {

        try {
            final RiderProjectSetting rps = loadProjectSetting(riderId, projectId);
            return rps.getVehicle().getVehicleModel().getVehicleType();
        } catch (Exception e) {
            return VehicleType.TRUCK_CARGO;
        }
    }

    public VehicleRouteParamDTO getVehicleRouteParamOfProjectRider(@NotNull final Long riderId,
                                                                   @NotNull final Long projectId) {

        try {
            final RiderProjectSetting rps = loadProjectSetting(riderId, projectId);
            final VehicleModel vehicleModel = rps.getVehicle().getVehicleModel();
            final VehicleType vehicleType = vehicleModel.getVehicleType();
            final Integer vehicleHeight = Optional.ofNullable(vehicleModel.getOverallHeight())
                    .map(overallHeight -> overallHeight / 10)
                    .orElse(null);
            final Integer vehicleWeight = Optional.ofNullable(vehicleModel.getGrossVehicleWeight())
                    .map(grossVehicleWeight -> (int) Math.ceil(grossVehicleWeight))
                    .orElse(null);

            return VehicleRouteParamDTO.builder()
                    .vehicleType(vehicleType)
                    .vehicleHeight(vehicleHeight)
                    .vehicleWeight(vehicleWeight)
                    .build();
        } catch (Exception e) {
            return VehicleRouteParamDTO.builder()
                    .vehicleType(VehicleType.TRUCK_CARGO)
                    .build();
        }
    }

    public Point getWorkingStartLocationOfProjectRider(@NotNull final Long riderId,
                                                       @NotNull final Long projectId) {

        try {
            final RiderProjectSetting rps = loadProjectSetting(riderId, projectId);
            return rps.getWorkingStartLocation();
        } catch (Exception e) {
            return null;
        }
    }

    public boolean isDeliveryOrderNumFixedByRider(@NotNull final Long riderId,
                                                  @NotNull final Long projectId) {

        final RiderProjectSetting riderProjectSetting = riderProjectSettingRepository.findByRiderIdAndProjectId(riderId, projectId);
        if (Objects.nonNull(riderProjectSetting)) {
            return Boolean.TRUE.equals(riderProjectSetting.getIsOrderNumFixedByRider());
        }

        return false;
    }

    @Transactional
    public void setDeliveryOrderNumFixedByRider(@NotNull final Long riderId,
                                                               @NotNull final Long projectId,
                                                               final boolean isDeliveryOrderNumFixedByRider) {

        final RiderProjectSetting riderProjectSetting = riderProjectSettingRepository.findByRiderIdAndProjectId(riderId, projectId);

        if (Objects.nonNull(riderProjectSetting) && !Boolean.valueOf(isDeliveryOrderNumFixedByRider).equals(riderProjectSetting.getIsOrderNumFixedByRider())) {
            riderProjectSetting.setIsOrderNumFixedByRider(isDeliveryOrderNumFixedByRider);
            riderProjectSettingRepository.save(riderProjectSetting);
            log.info("[setDeliveryOrderNumFixedByRider] riderId: {}, projectId: {}, isOrderNumFixedByRider: {}", riderId, projectId, isDeliveryOrderNumFixedByRider);
        }
    }

    public Long getVehicleSearchProjectSetting(@NotNull final Long vehicleId) {

        RiderProjectSetting riderp = riderProjectSettingRepository.getLastVehicleProjectSettingsList(vehicleId);

        if (Objects.nonNull(riderp)) {
            return riderp.getRider().getId();
        }else{
            throw new CustomException(HttpStatus.NOT_FOUND, "기사 정보를 찾을 수가 없습니다 .", false);
        }
    }

    /**
     * riderId와 preProjectId와 newProjectId를 이용하여 rider에 대한 projectSetting 값을 변경함.
     *
     * @param riderId
     * @param preProjectId
     * @param newProjectId
     * @return
     */
    @Transactional
    public void changedRiderProjectSetting(@NotNull final Long riderId, @NotNull final Long preProjectId, @NotNull final Long newProjectId){
        final List<RiderProjectSetting> rpss = this.getRiderProjectSettings(preProjectId, riderId, null);
        if (CollectionUtils.isNotEmpty(rpss)) {
            RiderProjectSetting rps = rpss.get(0);
            rps.setProjectId(newProjectId);
            this.saveProjectSetting(rps);
        }else{
            throw new CustomException(HttpStatus.NOT_FOUND, "Project Setting 에서 기사 정보를 찾을 수가 없습니다 .", false);
        }

    }

    /**
     * riderId와 날짜를 이용하여 rider의 배송 건수를 확인하기 위해서 구현함.
     * @param riderId
     * @param toDay
     * @return
     */
    public List<ProjectsDeliverStatusCountDTO> getRiderDetailDataProject(@NotNull final Long riderId, @NotNull final LocalDate toDay) {

        Rider rider = riderService.getRider(riderId);

        final LocalDateTime fromDate = toDay.atStartOfDay();
        final LocalDateTime toDate = toDay.plusDays(1L).atStartOfDay();

        Specification<RiderProjectSetting> specs = Specification.where(null);
        specs = Objects.requireNonNull(specs).and(RiderProjectSettingSpecs.eqRider(rider));
        specs = Objects.requireNonNull(specs).and(RiderProjectSettingSpecs.betweenCreateAt(fromDate, toDate));

        final List<RiderProjectSetting> riderProjectSettingList = riderProjectSettingRepository.findAll(specs);

        final List<ProjectsDeliverStatusCountDTO> projectsDeliverCount = riderProjectSettingList.stream().map(riderProjectSetting -> {
            final Long projectId = riderProjectSetting.getProjectId();
            final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);

            final long deliveryCount = this.getCountOfDeliveries(projectId, null, null, null, null, riderId, null);
            final long completeDeliveryCount = this.getCountOfDeliveries(projectId, null, null, DeliveryStatus.completeDeliveryStatusList, null, riderId, null);
            final long failureDeliverCount = this.getCountOfDeliveries(projectId, null, null, DeliveryStatus.failureDeliveryStatusList, null, riderId, null);
            final String riderNote = riderProjectSetting.getRiderNote();

            return ProjectsDeliverStatusCountDTO.parseProjectDeliverStatusCountDTO(project, deliveryCount, completeDeliveryCount, failureDeliverCount, riderNote);

        }).collect(Collectors.toList());

        return projectsDeliverCount.stream()
                .filter(p -> p.getIsDeleted() == false)
                .collect(Collectors.toList());

    }

    /**
     * 배송 목록 갯수 조회
     *
     * @param projectId          프로젝트 아이디
     * @param dateFrom           시작 날짜
     * @param dateTo             종료 날짜
     * @param deliveryStatusList 배송 상태 목록
     * @param userId             관리자 사용자 아이디
     * @param riderId            라이더 아이디
     * @return 배송 목록 갯수
     */
    public long getCountOfDeliveries(final Long projectId,
                                     final LocalDateTime dateFrom,
                                     final LocalDateTime dateTo,
                                     final List<DeliveryStatus> deliveryStatusList,
                                     final Long userId,
                                     final Long riderId,
                                     final List<Long> deliveryIds) {

        Specification<Delivery> specs = DeliverySpecs.composeSpecs(userId, riderId, projectId, dateFrom, dateTo, deliveryStatusList, deliveryIds);
        specs = Objects.requireNonNull(specs).and(DeliverySpecs.eqDeleted(false));
        return deliveryRepository.count(specs);
    }


    @Transactional
    public void addRiderToProject( Long riderId,
                                   Long projectId ) {

        final Rider rider = riderService.getRider(riderId);
        final RiderRecentSetting riderRecentSetting = riderRecentSettingService.updateRiderRecentSettingWithProjectId(rider, projectId);
        final RiderProjectSettingDTO riderProjectSettingDTO = RiderProjectSettingDTO.parseFromRiderRecentSetting(riderId, riderRecentSetting);
        riderService.addProjectId(riderId, projectId);
        saveProjectSetting(riderId, projectId, riderProjectSettingDTO);
    }

    @Transactional
    public void addRiderChangedStartAddressToProject( Long riderId,
                                                      Long projectId,
                                                      String startAddress,
                                                      Point startPos) {

        final Rider rider = riderService.getRider(riderId);
        final RiderRecentSetting riderRecentSetting = riderRecentSettingService.updateRiderRecentSettingWithProjectId(rider, projectId);
        riderRecentSetting.setWorkingStartAddress(startAddress);
        riderRecentSetting.setWorkingStartLocation(startPos);
        final RiderProjectSettingDTO riderProjectSettingDTO = RiderProjectSettingDTO.parseFromRiderRecentSetting(riderId, riderRecentSetting);
        riderService.addProjectId(riderId, projectId);
        saveProjectSetting(riderId, projectId, riderProjectSettingDTO);
    }
}
