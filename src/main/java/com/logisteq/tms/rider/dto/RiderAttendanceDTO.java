package com.logisteq.tms.rider.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.rider.domain.RiderAttendance;
import lombok.*;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.Objects;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Builder
public class RiderAttendanceDTO {

    @NotNull
    @Positive
    private Long riderId;

    @NotNull
    @Positive
    private Long projectId;

    @NotNull
    //@PastOrPresent
    private LocalDateTime attendAt;

    public static RiderAttendanceDTO of(final RiderAttendance riderAttendance) {

        if (Objects.isNull(riderAttendance)) {
            return null;
        }

        return RiderAttendanceDTO.builder()
                .riderId(riderAttendance.getRiderId())
                .projectId(riderAttendance.getProjectId())
                .attendAt(riderAttendance.getAttendAt())
                .build();
    }

}
