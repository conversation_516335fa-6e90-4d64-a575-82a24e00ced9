package com.logisteq.tms.rider.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.rider.domain.RiderWorkStatus;
import com.logisteq.tms.rider.domain.suppl.WorkStatus;
import lombok.*;

import java.time.LocalDateTime;

/**
 * RiDER 근무 상태 DTO
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class RiderWorkStatusDTO {
	/**
	 * 기사 아이디
	 */
	private Long riderId;
	
	/**
	 * 프로젝트 아이디
	 */
	private Long projectId;
	
	/**
	 * 근무 상태
	 */
	private WorkStatus workStatus;
	
	/**
	 * 클라이언트에서 올려준 시각: ms from Epoch
	 */
	private LocalDateTime timestamp;
	
	@Builder
	protected RiderWorkStatusDTO(Long riderId, Long projectId, WorkStatus workStatus, LocalDateTime timestamp) {
		this.riderId = riderId;
		this.projectId = projectId;
		this.workStatus = workStatus;
		this.timestamp = timestamp;
	}
	
	public static RiderWorkStatusDTO parseFromRiderWorkStatus(RiderWorkStatus riderWorkStatus) {
		if (riderWorkStatus == null) {
			return null;
		}
		
		return  RiderWorkStatusDTO.builder()
				.riderId(riderWorkStatus.getRider() == null ? null : riderWorkStatus.getRider().getId())
				.projectId(riderWorkStatus.getProjectId())
				.workStatus(riderWorkStatus.getWorkStatus())
				.timestamp(riderWorkStatus.getTimestamp())
				.build();
	}
	public static RiderWorkStatusDTO parseFromRiderWorkStatusIgnoreDuplicate(RiderWorkStatus riderWorkStatus) {
		if (riderWorkStatus == null) {
			return null;
		}
		
		return  RiderWorkStatusDTO.builder()
//				.riderId(riderWorkStatus.getRider() == null ? null : riderWorkStatus.getRider().getId())
//				.projectId(riderWorkStatus.getProjectId())
				.workStatus(riderWorkStatus.getWorkStatus())
				.timestamp(riderWorkStatus.getTimestamp())
				.build();
	}
}
