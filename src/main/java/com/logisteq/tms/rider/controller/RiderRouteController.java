package com.logisteq.tms.rider.controller;

import com.logisteq.common.dto.PageableDTO;
import com.logisteq.tms.delivery.dto.DeliveryRoutesRequestDTO;
import com.logisteq.tms.rider.constant.ApiUrl;
import com.logisteq.tms.rider.dto.RoutesResDTO;
import com.logisteq.tms.rider.service.RiderRouteService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Tag(name = "RiderRoute")
@Slf4j
@Validated
@RestController
@RequestMapping
public class RiderRouteController {

	private final RiderRouteService riderRouteService;

	@Autowired
	public RiderRouteController(final RiderRouteService riderRouteService) {

		this.riderRouteService = riderRouteService;
	}

	/**
	 * route정보 저장 (기사앱에서 사용)
	 * @param riderId
	 * @param projectId
	 * @param routes
	 */
	@Hidden
	@PostMapping(ApiUrl.RIDER_ROUTES)
	@ResponseStatus(HttpStatus.CREATED)
	public void routeSaveByRider(@PathVariable(required = true) Long riderId,
								 @RequestParam Long projectId,
								 @RequestBody List<DeliveryRoutesRequestDTO> routes) {
		log.info("@~~개수" + routes.size());
		riderRouteService.routesSaveByRider(riderId, projectId, routes);
	}
	
	/**
	 * route정보 조회 - rider별 (기사앱에서 사용)
	 * @param riderId
	 * @param deliveryId
	 * @param projectId
	 * @param pageable
	 * @return
	 */
	@Hidden
	@GetMapping ( ApiUrl.RIDER_ROUTES)
	public List<RoutesResDTO> routeListByRider(@PathVariable Long riderId,
											   @RequestParam(required = false) Long deliveryId,
											   @RequestParam(required = false) Long projectId,
											   final PageableDTO pageable) {
		List<RoutesResDTO> routesList = new ArrayList<>();
		
		routesList = riderRouteService.getRoutesBySpec(riderId, deliveryId, projectId, pageable);
			
//		routesList = riderRouteService.getRoutesByRiderId( riderId );
		
		if (routesList.size() > 0) {
			log.info("디버깅");
		}
		
		return routesList;
	}
	
	
	private boolean isCompleted = false;
	
	/**
	 * route정보 저장 (콜앱에서 사용)
	 * @param riderId
	 * @param projectId
	 * @param routes
	 */
	@Hidden
	@PostMapping(ApiUrl.CALLAPP_RIDER_ROUTES)
	@ResponseStatus(HttpStatus.CREATED)
	public void routeSavebyCallapp(@RequestParam(required = true) String callUserId,
													@RequestParam(required = true) Long riderId,
													@RequestParam Long projectId,
													@RequestBody List<DeliveryRoutesRequestDTO> routes,
													@RequestParam(required = false) Boolean isCompleted) {
		this.isCompleted = isCompleted;
		riderRouteService.routeSavebyCallApp(riderId, projectId, callUserId, routes);
	}
	
	/**
	 * route정보 조회 (콜앱에서 사용)
	 * @param riderId
	 * @return
	 */
	@Hidden
	@GetMapping ( ApiUrl.CALLAPP_RIDER_ROUTES)
	public List<RoutesResDTO> routeListByCallapp( @RequestParam(required = false) Long riderId,
												  @RequestParam(required = false) String callUserId) {
		List<RoutesResDTO> routesList = new ArrayList<>();
		
		if (!this.isCompleted) {
			return routesList;
		}
		
		if ( riderId != null && callUserId == null ) {
			routesList = riderRouteService.getRoutesByCallappByRiderId( riderId );
		} else if ( riderId == null && callUserId != null ) {
			routesList = riderRouteService.getRoutesByCallappByCallUserId( callUserId );
		} else if ( riderId != null && callUserId != null ) {
			routesList = riderRouteService.getRoutesByCallappByRiderIdAndCallUserId( riderId, callUserId );
		} else {
//			throw new InvalidParameterException("riderId와 callUserId 둘중 한개의 값은 있어야 합니다.");
			//route정보 조회  전체
			routesList = riderRouteService.getRoutesAll();
		}

		this.isCompleted = false;
		return routesList;
	}
}
