package com.logisteq.tms.address.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.logisteq.common.dto.CoordinateDTO;
import com.logisteq.common.util.Aes256Util;
import com.logisteq.common.util.GeometryUtil;
import com.logisteq.tms.address.domain.Address;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AddressDTO {

    @Schema(description = "주소 ID")
    private Long id;

    @Schema(description = "기본주소")
    // @NotEmpty(message = "Please provide a base in Address")
    @JsonSerialize(using = Aes256Util.PersonalInfoSerializer.class)
    private String base;

    @Schema(description = "상세주소")
    // @NotEmpty(message = "Please provide a detail in Address")
    @JsonSerialize(using = Aes256Util.PersonalInfoSerializer.class)
    private String detail;

    @Schema(description = "우편번호")
    // @NotEmpty(message = "Please provide a zipCode in Address")
    @JsonSerialize(using = Aes256Util.PersonalInfoSerializer.class)
    private String zipCode;

    @Schema(description = "위치 좌표")
    private CoordinateDTO location;

    @Schema(description = "입구점 좌표")
    private CoordinateDTO entrance;

    @Schema(description = "차량 입구점 좌표")
    private CoordinateDTO entranceVehicle;

    @Schema(description = "폴리곤 정보")
    private List<CoordinateDTO> areaPolygon;

    @Schema(description = "주소검색 API의 응답 결과")
    @JsonIgnore // Json 값에 안보이도록 설정
    private String searchResultBase;

    @Schema(description = "동", example = "상일동")
    private String locality;//동 이름

    @Schema(description = "시군구", example = "종로구")
    private String subAdminArea;

    @Schema(description = "읍면동", example = "종로1가")
    private String eupMyeonDong;

    @JsonIgnore
    public static AddressDTO createDefaultVO(Address entity) {
        return AddressDTO.builder()
                .id(entity.getId())
                .base(entity.getBase())
                .detail(entity.getDetail())
                .searchResultBase(entity.getSearchResultBase())
                .zipCode(entity.getZipCode())
                .locality(entity.getLocality())
                .location(CoordinateDTO.of(entity.getLocation()))
                .entrance(CoordinateDTO.of(entity.getEntrance()))
                .entranceVehicle(CoordinateDTO.of(entity.getEntranceVehicle()))
                .areaPolygon(GeometryUtil.convertPointListFromPolygon(entity.getAreaPolygon()))
                .subAdminArea(entity.getSubAdminArea())
                .eupMyeonDong(entity.getEupMyeonDong())
                .build();
    }

}
