package com.logisteq.tms.address.domain;

import com.logisteq.tms.common.constant.CommonConstant;
import lombok.*;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

@Getter
@Setter
@Builder(access = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Entity
@EntityListeners(AuditingEntityListener.class)
public class AddressException {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(length = CommonConstant.MAX_ADDRESS_BASE)
    private String rooName;

    @Column(length = CommonConstant.MAX_ADDRESS_LEGALDONGCODE)
    private String rooNumber;

    @Column(length = CommonConstant.MAX_ADDRESS_BASE)
    private String jiBunBase;

    @Column(length = CommonConstant.MAX_ADDRESS_DETAIL)
    private String jiBunNumber;

    /*
     * 사용 여부
     */
    @Builder.Default
    @Column
    private Boolean deleted = false;
}
