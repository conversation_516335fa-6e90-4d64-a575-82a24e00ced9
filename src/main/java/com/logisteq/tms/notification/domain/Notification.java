package com.logisteq.tms.notification.domain;

import com.logisteq.common.converter.PersonalInfoConverter;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.domain.suppl.VisitType;
import com.logisteq.tms.notification.types.NotiType;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(indexes = {
        @Index(columnList = "projectId, riderId")
})
@EntityListeners(AuditingEntityListener.class)
public class Notification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long notificationId;

    private Long riderId;

    @Convert(converter = PersonalInfoConverter.class)
    private String riderName; // 기사 이름

    private Long projectId;

    @Convert(converter = NotiType.NotiTypeConverter.class)
    @Column(columnDefinition = NotiType.COLUMN_DEFINITION)
    private NotiType notiType; // noti 타입

    @Builder.Default
    @Column
    private Boolean isRead = false; // 읽음여부

    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createAt;

    @LastModifiedDate
    private LocalDateTime updateAt;

    private String licensePlate; // 차량번호

    @Convert(converter = PersonalInfoConverter.class)
    private String mobile; // 기사 전화번호

    private String detail; // 알림 상세팝업의 메모

    private DeliveryStatus deliveryStatus; // 배송상태

    private String orderId; // 주문번호. customerOrderId가 존재하면 우선 표시하고 없으면 deliveryId로 표시

    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = VisitType.COLUMN_DEFINITION)
    private VisitType visitType; // 업무유형

    private String deliverySubtype; // 배송 하위타입 (공동현관, 경비실, 기타, 주소불명, 배송품 분실, 배송품 파손 등)

    private Long deliveryId;

    @Override
    public String toString() {
        return notiType + " " + riderName + " " + createAt;
    }

}
