package com.logisteq.tms.dispatch.service;


import com.logisteq.common.exception.CustomException;
import com.logisteq.common.exception.InvalidParameterException;
import com.logisteq.common.exception.ItemNotFoundException;
import com.logisteq.common.util.GeometryUtil;
import com.logisteq.tms.address.domain.suppl.DispatchableItem;
import com.logisteq.tms.delivery.constant.DeliveryConstant;
import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.DeliveryAllocation;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.domain.suppl.DeliveryType;
import com.logisteq.tms.delivery.domain.suppl.InspectionStatus;
import com.logisteq.tms.delivery.dto.RouteSectionInfoDTO;
import com.logisteq.tms.delivery.repository.DeliveryAllocationRepository;
import com.logisteq.tms.delivery.repository.DeliveryRepository;
import com.logisteq.tms.delivery.service.*;
import com.logisteq.tms.dispatch.ClusterRule;
import com.logisteq.tms.dispatch.ProjectLoadingMode;
import com.logisteq.tms.dispatch.component.OnDemandManager;
import com.logisteq.tms.dispatch.dto.RiderRequestDTO;
import com.logisteq.tms.product.service.ProductService;
import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.domain.suppl.ProjectAttribute;
import com.logisteq.tms.project.dto.ProjectDelivery;
import com.logisteq.tms.project.dto.ProjectRider;
import com.logisteq.tms.project.service.ProjectBasicService;
import com.logisteq.tms.push.service.PushService;
import com.logisteq.tms.rider.service.RiderProjectSettingService;
import com.logisteq.tms.rider.service.RiderService;
import com.logisteq.tms.rider.service.RiderWorkCompletionService;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.repository.dao.GroupProjectRiderDAO;
import com.logisteq.tms.user.service.OrganizationService;
import com.logisteq.tms.vehicle.dto.VehicleRouteParamDTO;
import com.logisteq.tms.vehicle.types.VehicleType;
import com.logisteq.tms.web.dto.WebRiderIdListDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Dispatch Service
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@Service
public class DispatchService {

    private final ProjectBasicService projectBasicService;
    private final ClusterService clusterService;
    private final DeliveryService deliveryService;
    private final DeliveryStatusHistoryService deliveryStatusHistoryService;
    private final RiderService riderService;
    private final RiderProjectSettingService riderProjectSettingService;
    private final RiderWorkCompletionService riderWorkCompletionService;
    private final DeliveryBasicService deliveryBasicService;
    private final DeliveryRouteService deliveryRouteService;
    private final DeliveryRepository deliveryRepository;
    private final DeliveryAllocationRepository deliveryAllocationRepository;
    private final DeliveryInspectionHistoryService deliveryInspectionHistoryService;
    private final PushService pushService;
    private final ProductService productService;
    private final OnDemandManager onDemandManager;
    private final OrganizationService organizationService;

    public static final Long NOT_FOUND_RIDER_ID    =   0L;

    @Autowired
    public DispatchService(final ProjectBasicService projectBasicService,
                           final ClusterService clusterService,
                           final DeliveryService deliveryService,
                           final DeliveryStatusHistoryService deliveryStatusHistoryService,
                           final RiderService riderService,
                           final RiderProjectSettingService riderProjectSettingService,
                           final RiderWorkCompletionService riderWorkCompletionService,
                           final DeliveryBasicService deliveryBasicService,
                           final DeliveryRouteService deliveryRouteService,
                           final DeliveryRepository deliveryRepository,
                           final DeliveryAllocationRepository deliveryAllocationRepository,
                           final DeliveryInspectionHistoryService deliveryInspectionHistoryService,
                           final PushService pushService,
                           final ProductService productService,
                           final OnDemandManager onDemandManager,
                           final OrganizationService organizationService) {

        this.projectBasicService = projectBasicService;
        this.clusterService = clusterService;
        this.deliveryService = deliveryService;
        this.deliveryStatusHistoryService = deliveryStatusHistoryService;
        this.riderService = riderService;
        this.riderProjectSettingService = riderProjectSettingService;
        this.riderWorkCompletionService = riderWorkCompletionService;
        this.deliveryBasicService = deliveryBasicService;
        this.deliveryRouteService = deliveryRouteService;
        this.deliveryRepository = deliveryRepository;
        this.deliveryAllocationRepository = deliveryAllocationRepository;
        this.deliveryInspectionHistoryService = deliveryInspectionHistoryService;
        this.pushService = pushService;
        this.productService = productService;
        this.onDemandManager = onDemandManager;
        this.organizationService = organizationService;
    }

    /**
     * 특정 프로젝트에 속한 차량, 방문지에 대해서 배송 순서 및 경로 생성
     * 전체 프로젝트 클러스터링과 경로 탐색을 수행한다. (그룹은 이미 만들어져 있어야 동작한다.)
     *
     * @param projectId
     * @param routeOption
     * @param userId
     */
    // Redmine #1640 Transaction으로 처리하지 않아야 Multi-thread transaction에서 변경된 DB값을 읽을 수 있다.
    //@Transactional
    public void dispatchByProjectId(final Long projectId,
                                    final Integer routeOption,
                                    final Long userId) {

        projectBasicService.setRouteOption(projectId, routeOption);

        this.pushService.sendUpdateDialogMessageToWeb( userId, projectId, "배송지 정보를 수집중입니다.");
        final List<Delivery> deliveries = deliveryService.getAllDeliveries(projectId, null, null,
                null,//Arrays.asList(DeliveryStatus.WAITING, DeliveryStatus.READY),
                null, null);
        final List<ProjectDelivery> projectDeliveries = deliveryBasicService.convertDeliveriesToProjectDeliveries(projectId, deliveries);

        // redmine-#1138
        this.pushService.sendUpdateDialogMessageToWeb( userId, projectId, "기사 정보를 수집중입니다.");
        final List<GroupProjectRiderDAO> groupProjectRiderDAOList = riderService.getRidersByProjectId(projectId, false);
        final List<ProjectRider> projectRiders = clusterService.convertRidersToProjectRiders(projectId, groupProjectRiderDAOList);

        this.pushService.sendUpdateDialogMessageToWeb( userId, projectId, "경로탐색을 수행합니다.");
        dispatchByRidersDeliveries(projectId, userId, projectRiders, projectDeliveries, false, false);

        projectBasicService.updateProjectAttribute(projectId,
                ProjectAttribute.builder()
                        .isFirstRoutingDone(true)
                        .isRouteEnabled(false)
                        .isSendingRiderEnabled(true)
                        .isRouteExecute(true)
                        .isClusterDone(true)//경로 탐색이 되었다는 것은 clusterDone 이 true 로 되어야 한다
                        .build()
        );
    }

    /**
     * 특정 프로젝트에 속하고 특정 기사 리스트에 대해 차량, 방문지에 대해서 배송 순서 및 경로 생성
     * 특정 아이디 리스트만 경로 탐색을 수행한다. 만약 아이디 리스트가 없으면 모든 기사에 대해 수행한다 (redmine-#1138 다시 풀음)
     *
     * @param projectId
     * @param riderIdList
     * @param routeOption
     * @param withClustering
     * @param onDemandMode
     */
    // Redmine #1640 Transaction으로 처리하지 않아야 Multi-thread transaction에서 변경된 DB값을 읽을 수 있다.
    //@Transactional
    public void dispatchByRiders(final Long projectId,
                                 final List<Long> riderIdList,
                                 final Integer routeOption,
                                 final boolean withClustering,
                                 final boolean onDemandMode) {

        if (CollectionUtils.isEmpty(riderIdList)) {
            this.dispatchByProjectId(projectId, routeOption, null);
            return;
        }

        projectBasicService.setRouteOption(projectId, routeOption);

        //기사의 리스트를 하나 하나씩 요청하여 경로 탐색을 수행한다
        for (final Long riderId : riderIdList) {
            if (Objects.isNull(riderId) || !riderService.isRiderInProject(riderId, projectId)) {
                continue;
            }

            final List<Delivery> riderDeliveries = deliveryService.getAllDeliveries(projectId, null, null,
                    null,//Arrays.asList(DeliveryStatus.WAITING, DeliveryStatus.READY),
                    null, riderId);

            List<ProjectDelivery> projectDeliveries = new ArrayList<>();

            if (onDemandMode) {
                projectDeliveries = deliveryBasicService.convertDeliveriesToProjectWithSetOrderByAllocationOrder(riderDeliveries);
            } else {
                projectDeliveries = deliveryBasicService.convertDeliveriesToProjectDeliveries(projectId, riderDeliveries);
            }

            final List<GroupProjectRiderDAO> groupProjectRiderDAO = riderService.getRidersByProjectIdAndRiderId(projectId, riderId, false);
            final List<ProjectRider> oneProjectRider = clusterService.convertRidersToProjectRiders(projectId, groupProjectRiderDAO);

            if (CollectionUtils.isNotEmpty(projectDeliveries)) {
                dispatchByRidersDeliveries(projectId, null, oneProjectRider, projectDeliveries, withClustering, onDemandMode);
            }
        }

        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);
        final boolean isClusterDone = Boolean.TRUE.equals(project.getIsClusterDone()) || withClustering;

        projectBasicService.updateProjectAttribute(projectId,
                ProjectAttribute.builder()
                        .isClusterDone(Boolean.valueOf(isClusterDone))
                        .isFirstRoutingDone(true)
                        .isRouteEnabled(false)
                        .isSendingRiderEnabled(true)
                        .build()
        );
    }

    /**
     * 특정 아이디 리스트만 경로 탐색을 수행한다. 만약 아이디 리스트가 없으면 모든 기사에 대해 수행한다
     */
    @Deprecated
    // Redmine #1640 Transaction으로 처리하지 않아야 Multi-thread transaction에서 변경된 DB값을 읽을 수 있다.
    //@Transactional
    public void dispatchByProject(final Long projectId) {

// redmine-#1138		List<Rider> riders  = riderService.getRiders( projectId, false)
//					.stream().filter( r -> ( riderIdsDTO == null || riderIdsDTO.getRiderIds() == null)|| riderIdsDTO.getRiderIds().contains( r.getId())).collect(Collectors.toList());
//
//		//기사의 리스트를 하나 하나씩 요청하여 경로 탐색을 수행한다
//		for (Rider rider : riders) {
//			List<Delivery> riderDeliveries = deliveryService.getAllDeliveries(projectId, null, null,
//					null,//Arrays.asList(DeliveryStatus.WAITING, DeliveryStatus.READY),
//					null, rider.getId());
//
//			List<ProjectDelivery> projectDeliveries = deliveryBasicService.convertDeliveriesToProjectDeliveries( projectId, riderDeliveries );
//			List<ProjectRider> oneProjectRider = clusterService.convertRidersToProjectRiders( projectId, Arrays.asList( rider)) ;
//
//			dispatchProject(projectId, oneProjectRider, projectDeliveries);
//		}

        final List<GroupProjectRiderDAO> groupProjectRiderList = riderService.getRidersByProjectId(projectId, false);
        for (GroupProjectRiderDAO groupProjectRider : groupProjectRiderList) {
            final List<Delivery> riderDeliveries = deliveryService.getAllDeliveries(projectId, null, null,
                    Arrays.asList(DeliveryStatus.WAITING, DeliveryStatus.READY, DeliveryStatus.GOING, DeliveryStatus.SERVICING),
                    null, groupProjectRider.getRiderId());

            final List<ProjectDelivery> projectDeliveries = deliveryBasicService.convertDeliveriesToProjectDeliveries(projectId, riderDeliveries);

            // 동적으로 목적지가 추가된 이후에 호출될시에 추가된 목적지만 groupName이 있는 경우가 있다. 그러므로 이때는 모두 null로 설정한다. => 불필요한 코드 같아서 삭제함.
            //long hasGroupNameDeliveryCount = projectDeliveries.stream().filter(r -> r.getGroupName() != null).count();
            //if (hasGroupNameDeliveryCount != projectDeliveries.size()) {//일부분에 null이 존재할때
            //projectDeliveries.forEach(delivery -> delivery.setGroupName(null));
            //}

            final List<ProjectRider> oneProjectRider = clusterService.convertRidersToProjectRiders(projectId, Arrays.asList(groupProjectRider));

            dispatchByRidersDeliveries(projectId, null, oneProjectRider, projectDeliveries, false , false);
        }

        projectBasicService.updateProjectAttribute(projectId,
                ProjectAttribute.builder()
                        .isClusterDone(true)
                        .isFirstRoutingDone(true)
                        .isRouteEnabled(false)
                        .isSendingRiderEnabled(true)
                        .build()
        );
    }

    /**
     * 해당 배송지의 order값이 1부터 연속적인 순서를 가지고 있는지 체크한다.
     * 예를 들어 5개의 배송지일때 [1,2,3,4,5 ] [ 4,3,2,1,5 ] [2,3,4,5,6] => true   ,  [1,2,3,4,6][1,1,2,3,4] => false
     * @return
     */
    private boolean hasSequentialDeliveryOrders(List<ProjectDelivery> projectDeliveries) {

        projectDeliveries.sort(Comparator.comparingLong(ProjectDelivery::getOrder));

        return IntStream.range(0, projectDeliveries.size() - 1)
                .mapToLong( operand -> Optional.ofNullable( projectDeliveries.get(operand + 1).getOrder()).orElse(0)
                        - Optional.ofNullable( projectDeliveries.get(operand).getOrder()).orElse(0))
//                .allMatch(value -> value == 1);
                  .allMatch(value -> value > 0); // 중간에 배송지를 삭제할 경우 꼭 1씩 증가하지 않는다.  순차적으로 증가하면 순차적인 배송 순서를 가지고 있는것으로 판단한다
    }

    /**
     * 해당 프로젝트안의 배차및 경로 탐색 수행
     *
     * @param projectId
     * @param userId
     * @param projectRiders     null 일때는 프로젝트의 모든 기사가 선택된다
     * @param projectDeliveries null 일때는 프로젝트의 모든 목적지가 선택된다
     * @param withClustering
     * @param onDemandMode
     */
    @Transactional
    public void dispatchByRidersDeliveries(@NotNull final Long projectId,
                                           final Long userId,
                                           final List<ProjectRider> projectRiders,
                                           final List<ProjectDelivery> projectDeliveries,
                                           final boolean withClustering,
                                           final boolean onDemandMode) {

        if (CollectionUtils.isEmpty(projectRiders)) {
            throw new InvalidParameterException("해당 배송지에 배차할 기사가 존재하지 않습니다");
        }

        if (CollectionUtils.isEmpty(projectDeliveries)) {
            throw new InvalidParameterException("배송 목록이 비어 있습니다.");
        }

        ProjectLoadingMode loadingMode;
        if (onDemandMode && projectBasicService.isRoutingDone(projectId)) {
            if (this.hasSequentialDeliveryOrders(projectDeliveries)) {//OnDemand 모드이고 순서가 정의되어 있다면 현재 설정된 순서를 유지하면서 경로 탐색 한다
                loadingMode = ProjectLoadingMode.GROUP_ORDER;
                log.info( "[OnDemand] 온디맨드 상태에서 현재 배송상태를 유지하고 경로탐색을 합니다 ");
            } else {  //다수의 배송지를 이동하거나 다른 기사의 배송지를 받을 경우 새롭게 경로가 짜도록 한다 . 이때 order는 순차적으로 들어오지 않으므로 순차적인 Order를 체크한다.
                loadingMode = ProjectLoadingMode.AUTO;
                log.warn( "[OnDemand] 온디맨드 상태에서 다시 배송지를 탐색합니다!!!!!! 기존의 배송 순서는 무시되고 새로운 배송 순서를 가지게 됩니다. ");
            }
        } else {
            loadingMode = ClusterService.getProjectClusteringType(projectRiders, projectDeliveries);
        }

        final ClusterRule clusterRule = projectBasicService.getClusterRule(projectId);

        DispatchableItem.setDispatchableItemListWithNoGroupTempName(projectRiders);
        DispatchableItem.setDispatchableItemListWithNoGroupTempName(projectDeliveries);

        final List<RiderRequestDTO> riderRequestDtoList = clusterService.getRidersOfCluster(projectId, projectRiders);

        if (loadingMode == ProjectLoadingMode.GROUP_ORDER && clusterRule == ClusterRule.NORMAL) {
            // 주어진 그룹 정보와 순서를 이용하여 배차 수행
            if (withClustering) {//클러스터링 같이 수행
                dispatchByGivenGroupsAndOrders(projectId, riderRequestDtoList, projectDeliveries);
            }

            if (DeliveryConstant.USE_MULTI_THREADS_WHEN_GROUP_ORDER_ROUTING) {
                deliveryRouteService.performRoutingByProjectRiders(projectId, riderRequestDtoList, loadingMode, clusterRule, userId);
            }
        } else /* if (loadingMode == ProjectLoadingMode.AUTO || loadingMode == ProjectLoadingMode.GROUP) */ {
            if (withClustering) {//클러스터링 같이 수행
                dispatchByGivenGroups(projectId, riderRequestDtoList, projectDeliveries);
            } else {
                riderRequestDtoList.forEach(riderRequestDTO -> riderRequestDTO.setGroupName(null));
            }

            deliveryRouteService.performRoutingByProjectRiders(projectId, riderRequestDtoList, loadingMode, clusterRule, userId);
        }
    }

    /**
     * 주어진 그룹과 순서를 이용하여 배차
     *
     * @param projectId
     * @param riderRequestDtoList
     * @param projectDeliveries
     */
    @Transactional
    public void dispatchByGivenGroupsAndOrders(@NotNull final Long projectId,
                                               @NotEmpty final List<RiderRequestDTO> riderRequestDtoList,
                                               @NotEmpty final List<ProjectDelivery> projectDeliveries) {

        final Map<String, List<RiderRequestDTO>> riderRequestDtoGroups = ClusterService.createRiderRequestDtoGroups(riderRequestDtoList);
        final Map<String, List<ProjectDelivery>> deliveryGroups = ClusterService.createProjectDeliveryGroups(projectDeliveries);

        final Set<String> riderGroupSet = riderRequestDtoGroups.keySet();
        final Set<String> deliveryGroupSet = deliveryGroups.keySet();
        final Set<String> danglingDeliveryGroups = SetUtils.difference(deliveryGroupSet, riderGroupSet);

        if (CollectionUtils.isNotEmpty(danglingDeliveryGroups)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "권역 " + danglingDeliveryGroups + "에 배정된 기사가 없습니다.", false);
        }

        /* 배정된 방문지가 없는 기사가 있어도 동작 가능해서 주석처리합니다.
        final Set<String> danglingRiderGroups = SetUtils.difference(riderGroupSet, deliveryGroupSet);

        if (CollectionUtils.isNotEmpty(danglingRiderGroups)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "권역 " + danglingRiderGroups + "에 배정된 방문지가 없습니다.", false);
        }
        */

        deliveryGroups.forEach((groupName, projectDeliveryList) ->
                assignEqually(projectId, riderRequestDtoGroups.get(groupName), projectDeliveryList));

        if (!DeliveryConstant.USE_MULTI_THREADS_WHEN_GROUP_ORDER_ROUTING) {
            final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);
            riderRequestDtoList.forEach(r -> {
                deliveryRouteService.calcRiderRouteAppPlanEtdEta(project, r.getRiderId(), r.getWorkStartDateTime());
            });
        }
    }

    /**
     * 주어진 그룹을 이용하여 배차
     * 그룹 내의 순서는 자동으로 수행
     *
     * @param projectId
     * @param riderRequestDtoList
     * @param projectDeliveries
     */
    @Transactional
    public void dispatchByGivenGroups(@NotNull final Long projectId,
                                      @NotEmpty final List<RiderRequestDTO> riderRequestDtoList,
                                      @NotEmpty final List<ProjectDelivery> projectDeliveries) {

        final Map<String, List<RiderRequestDTO>> riderRequestDtoGroups = ClusterService.createRiderRequestDtoGroups(riderRequestDtoList);
        final Map<String, List<ProjectDelivery>> deliveryGroups = ClusterService.createProjectDeliveryGroups(projectDeliveries);

        final Set<String> riderGroupSet = riderRequestDtoGroups.keySet();
        final Set<String> deliveryGroupSet = deliveryGroups.keySet();
        final Set<String> danglingDeliveryGroups = SetUtils.difference(deliveryGroupSet, riderGroupSet);

        if (CollectionUtils.isNotEmpty(danglingDeliveryGroups)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "권역 " + danglingDeliveryGroups + "에 배정된 기사가 없습니다.", false);
        }

        /* 배정된 방문지가 없는 기사가 있어도 동작 가능해서 주석처리합니다.
        final Set<String> danglingRiderGroups = SetUtils.difference(riderGroupSet, deliveryGroupSet);

        if (CollectionUtils.isNotEmpty(danglingRiderGroups)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "권역 " + danglingRiderGroups + "에 배정된 방문지가 없습니다.", false);
        }
        */

        deliveryGroups.forEach((groupName, projectDeliveryList) ->
                clusterService.performClustering(projectId, riderRequestDtoGroups.get(groupName), projectDeliveryList));
    }

    @Transactional
    public void assignEqually(@NotNull final Long projectId,
                              @NotEmpty final List<RiderRequestDTO> riderRequestDtoList,
                              @NotEmpty final List<ProjectDelivery> projectDeliveries) {

        final boolean existHub = projectDeliveries.stream()
                .anyMatch(projectDelivery -> DeliveryType.HUB.equals(projectDelivery.getDelivery().getType()));

        final boolean existIntermediateDestination = projectDeliveries.stream()
                .anyMatch(projectDelivery -> DeliveryType.INTERMEDIATE_DESTINATION.equals(projectDelivery.getDelivery().getType()));

        final boolean existFinalDestination = projectDeliveries.stream()
                .anyMatch(projectDelivery -> DeliveryType.FINAL_DESTINATION.equals(projectDelivery.getDelivery().getType()));

        if (existHub && existIntermediateDestination && existFinalDestination) {
            throw new InvalidParameterException("그룹 안에 물류센터와 거점이 같이 존재합니다.");
        }

        // Firstmile은 hubCount > 0 && intermediateDestinationCount > 0 일 때
        // Lastmile은 intermediateDestinationCount > 0 && finalDestinationCount > 0 일 때
        // PickupPlace가 존재한다고 판단한다.
        final boolean hasPickupPlace = (existHub && existIntermediateDestination) || (existIntermediateDestination && existFinalDestination);
        final Delivery pickupPlace = hasPickupPlace ? projectDeliveries.get(0).getDelivery() : null;
        final Integer routeOption = projectBasicService.getRouteOption(projectId);

        // PickupPlace 처리
        if (hasPickupPlace) {
            riderRequestDtoList.forEach(riderRequestDto -> {
                allocatePickupPlaceManually(projectId, pickupPlace, riderRequestDto);
            });
        }

        int riderIndex = 0;
        final int riderLimit = Math.max(projectDeliveries.size() / riderRequestDtoList.size(), 1);
        int currentLimit = 0;

        // 일반 방문지 처리
        for (int destIndex = hasPickupPlace ? 1 : 0; destIndex < projectDeliveries.size(); destIndex++) {

            if (riderIndex == riderRequestDtoList.size()) {
                // 다하고 남은 방문지를 첫번째 기사에 배분
                riderIndex = 0;
                currentLimit = 0;
            }

            if (currentLimit < riderLimit) {
                final RiderRequestDTO riderRequestDto = riderRequestDtoList.get(riderIndex);
                final ProjectDelivery projectDelivery = projectDeliveries.get(destIndex);
                final Delivery delivery = projectDelivery.getDelivery();
                final DeliveryAllocation foundDeliveryAllocation = delivery.getAllocations().stream()
                        .filter(da -> da.getRiderId().equals(riderRequestDto.getRiderId()))
                        .findFirst()
                        .orElse(null);

                if (Objects.nonNull(foundDeliveryAllocation)) {

                    if (!DeliveryConstant.USE_MULTI_THREADS_WHEN_GROUP_ORDER_ROUTING) {
                        final VehicleRouteParamDTO vehicleRouteParamDTO = riderProjectSettingService.getVehicleRouteParamOfProjectRider(riderRequestDto.getRiderId(), projectId);
                        final VehicleType vehicleType = vehicleRouteParamDTO.getVehicleType();

                        final Point startPoint = currentLimit == 0
                                ? hasPickupPlace ? pickupPlace.getDetail().getDestinationAddress().getLocationByVehicleType(vehicleType)
                                : riderRequestDto.getCurrentLocation() // 첫번째 방문지는 PickupPlace 혹은 기사 위치가 출발점
                                : projectDeliveries.get(destIndex - 1).getDelivery().getDetail().getDestinationAddress().getLocationByVehicleType(vehicleType); // 그 외는 이전 방문지 위치가 출발점

                        final RouteSectionInfoDTO routeSectionInfo = deliveryRouteService.requestRoutingP2P(startPoint,
                                projectDelivery.getDelivery().getDetail().getDestinationAddress().getLocationByVehicleType(vehicleType),
                                routeOption,
                                vehicleRouteParamDTO);

                        final Double estimatedMeters = routeSectionInfo.getEstimatedMeters();
                        final Long estimatedSeconds = routeSectionInfo.getEstimatedSeconds();
                        final LineString routePath = GeometryUtil.convertLineStringFromPointList(routeSectionInfo.getCoordinateList());
                        final Long staySeconds = Optional.ofNullable(delivery.getDetail().getEstimatedStayTime()).orElse(0L) * 60L;

                        deliveryRouteService.updateInitialRoutes(foundDeliveryAllocation, estimatedMeters, estimatedSeconds, routePath, staySeconds);
                    }

                    foundDeliveryAllocation.setOrderNum(projectDelivery.getOrder());
                    deliveryAllocationRepository.save(foundDeliveryAllocation);
                }

                currentLimit++;
            } else {
                riderIndex++;
                currentLimit = 0;
            }
        }
    }

    /**
     * 엑셀에 지정된 PickupPlace의 DeliveryAllocate 저장
     *
     * @param projectId
     * @param pickupPlace
     * @param riderRequestDto
     */
    @Transactional
    public void allocatePickupPlaceManually(@NotNull final Long projectId,
                                            @NotNull final Delivery pickupPlace,
                                            @NotNull final RiderRequestDTO riderRequestDto) {

        final DeliveryAllocation deliveryAllocation = deliveryService.setDeliveryAllocation(pickupPlace, pickupPlace.getProjectId(), riderRequestDto.getRiderId(), true);
        deliveryAllocation.setOrderNum(1);

        if (!DeliveryConstant.USE_MULTI_THREADS_WHEN_GROUP_ORDER_ROUTING) {

            final Integer routeOption = projectBasicService.getRouteOption(projectId);
            final VehicleRouteParamDTO vehicleRouteParamDTO = riderProjectSettingService.getVehicleRouteParamOfProjectRider(riderRequestDto.getRiderId(), projectId);
            final VehicleType vehicleType = vehicleRouteParamDTO.getVehicleType();

            final RouteSectionInfoDTO routeSectionInfo = deliveryRouteService.requestRoutingP2P(riderRequestDto.getCurrentLocation(),
                    pickupPlace.getDetail().getDestinationAddress().getLocationByVehicleType(vehicleType),
                    routeOption,
                    vehicleRouteParamDTO);

            deliveryRouteService.updateInitialRoutes(deliveryAllocation,
                    routeSectionInfo.getEstimatedMeters(),
                    routeSectionInfo.getEstimatedSeconds(),
                    routeSectionInfo.getRoutePath(),
                    Optional.ofNullable(pickupPlace.getDetail().getEstimatedStayTime()).orElse(0L) * 60L);
        }

        final Delivery savedDelivery = deliveryRepository.save(pickupPlace);
        final DeliveryAllocation savedDeliveryAllocation = savedDelivery.getAllocations().stream()
                .filter(da -> da.getRiderId().equals(riderRequestDto.getRiderId()))
                .findFirst()
                .orElseThrow(() -> new ItemNotFoundException("기사의 배차정보가 저장되지 않았습니다."));

        deliveryStatusHistoryService.insertDeliveryStatusHistory(savedDeliveryAllocation, DeliveryStatus.READY, false);

        final InspectionStatus inspectionStatus = deliveryService.setDefaultInspectionStatus(savedDeliveryAllocation.getDelivery());
        deliveryInspectionHistoryService.saveDeliveryInspectionHistory(savedDeliveryAllocation, inspectionStatus);

        productService.handleOrderItemReception(savedDeliveryAllocation.getDelivery().getDeliveryProducts(), savedDeliveryAllocation.getProjectId(), savedDeliveryAllocation.getRiderId());
    }

    private void initDeliveryAllocation(Delivery delivery) {

        if (CollectionUtils.isNotEmpty(delivery.getAllocations())) {
            final InspectionStatus inspectionStatus = deliveryService.setDefaultInspectionStatus(delivery);
            DeliveryAllocation savedAllocation = delivery.getAllocations().get(0);

            if (Objects.nonNull(savedAllocation) && Objects.isNull(savedAllocation.getStatusHistories())) {
                deliveryStatusHistoryService.insertDeliveryStatusHistory(savedAllocation, DeliveryStatus.READY, false);
                deliveryInspectionHistoryService.saveDeliveryInspectionHistory(savedAllocation, inspectionStatus);
                productService.handleOrderItemReception(savedAllocation.getDelivery().getDeliveryProducts(), savedAllocation.getProjectId(), savedAllocation.getRiderId());
            }
        }
    }

    /**
     * 배송지 추가가 되고 순서가 바뀌지 않도록 경로 탐색 수행
     * @param projectId
     * @param newDelivery
     * @param riderId
     */
    public void performOnDemandRoutingByAddingDelivery(Long projectId,
                                                       Delivery newDelivery,
                                                       Long riderId) {

        final Project project = projectBasicService.getProjectById(projectId);
        onDemandManager.reArrangeDeliveryOrderByAddingDelivery(projectId, riderId, newDelivery);

        final WebRiderIdListDTO webRiderIdListDTO = WebRiderIdListDTO.builder()
                .projectId(projectId)
                .riderIds(Arrays.asList(riderId))
                .build();

        this.dispatchByRiders(projectId, webRiderIdListDTO.getRiderIds(), project.getRouteOption(), false, true);
    }

    /**
     * 배차가 완료된 상태에서 추가된 배송지 리스트에 한해서 dispatch(배차,경로탐색) 을 수행한다
     * @param projectId
     * @param deliveries
     * @param riderId   기사 지정(null이면 미지정)
     * @param isAutoRouting
     */
    @Transactional
    public  List<Long>  dispatchAddDeliveries(Long projectId,
                                              List<Delivery> deliveries,
                                              Long riderId,
                                              boolean isAutoRouting) {

        List<Long> riderIdList = new ArrayList<>();
        Project project = projectBasicService.getProjectById(projectId);

        if (Boolean.TRUE.equals(project.getIsClusterDone())) {//클러스터링 완료 되었으면 다시 배차를 수행한다.
            if (Objects.isNull(riderId)) {
                deliveries.forEach(delivery -> {
                    try {
                        Long findProperRiderId = onDemandManager.performClusterOnDemandAddDelivery(projectId, delivery);

                        //분할 배송 모드 진입
                        if (Objects.nonNull(findProperRiderId)) {
                            riderIdList.add(findProperRiderId);
                        } else {
                            Pair<Long,Long> findPairProperRiderIds =  onDemandManager.performClusterOnDemandAddSplitDelivery(projectId, delivery);
                            if (Objects.nonNull(findPairProperRiderIds)) {
                                riderIdList.add(findPairProperRiderIds.getFirst());
                                riderIdList.add(findPairProperRiderIds.getSecond());
                            } else {
                                riderIdList.add( DispatchService.NOT_FOUND_RIDER_ID);
                            }
                        }

                    } catch (Exception e) { //배차 기사가 존재하지 않을 경우 추가된 배송지를 삭제한다
                        log.info("[OnDemand] 배차중 오류 발생 또는 배차기사를 찾을수 없습니다. 배차 실패 처리 합니다. {} {} " , e.getCause(), e.getMessage());
                        riderIdList.add( DispatchService.NOT_FOUND_RIDER_ID);
                    }
                });
            } else {//이미 기사가 지정되어 온 경우
                clusterService.getClustersByRidersDeliveries(projectId, riderId, deliveries, null);
                deliveries.forEach(delivery -> riderIdList.add(riderId));
            }

            //경로 탐색 단계 일댸는 다시 경로 탐색을 수행한다
            if (projectBasicService.isRoutingDone(projectId) && isAutoRouting) {
                List<Long> distinctRiderIds =  new ArrayList<> (new HashSet<>( riderIdList.stream().filter( id-> id > 0).collect(Collectors.toList()))) ;//중복된 아이디 제거
                if (distinctRiderIds.size() > 0) {
                    final WebRiderIdListDTO webRiderIdListDTO = WebRiderIdListDTO.builder().projectId(projectId).riderIds(distinctRiderIds).build();
                    this.dispatchByRiders(projectId, webRiderIdListDTO.getRiderIds(), project.getRouteOption(), false, true);
                }
            }
        }

        return riderIdList;
    }

    public void dispatchByRiderId(Long projectId,
                                  Long riderId,
                                  Boolean isPerformCluster) {

        this.dispatchByRiderIds(projectId, Arrays.asList(riderId), isPerformCluster);
    }

    /**
     * 삭제된 배송지 리스트에 한해서 dispatch(배차,경로탐색,기사전송) 을 수행한다
     *
     * @param projectId
     * @param riderIds
     */
    public void dispatchByRiderIds(Long projectId,
                                   List<Long> riderIds,
                                   Boolean withClustering) {

        Project project = projectBasicService.getProjectById(projectId);
        boolean isRoutingDone = Boolean.TRUE.equals(project.getIsFirstRoutingDone());

        if (Boolean.TRUE.equals(project.getIsClusterDone())) {//클러스터링 완료 되었으면 다시 배차를 수행한다.
            if (CollectionUtils.isEmpty(riderIds)) {
                if (withClustering) {
                    clusterService.getClustersByRiders(projectId, null);
                } else {
                    // #1872, #1988 방문지 삭제 후 경로 관련 오동작 수정 (이거 막아서 문제 생기면 임의로 수정하지 말고 알려주세요)
                    //clusterService.deleteAllRoutePlanByDeliveries(projectId, null);
                }
            } else {
                riderIds.forEach(riderId -> {
                    if (withClustering) {
                        clusterService.getClustersByRiders(projectId, riderId);
                    } else {
                        // #1872, #1988 방문지 삭제 후 경로 관련 오동작 수정 (이거 막아서 문제 생기면 임의로 수정하지 말고 알려주세요)
                        //clusterService.deleteAllRoutePlanByDeliveries(projectId, riderId);
                    }
                });
            }

            if (isRoutingDone && CollectionUtils.isNotEmpty(riderIds)) {//OnDemand 상태에서는 경로 탐색을 수행한후 자동으로 push를 내려준다
                // 기사의 경로를 다시 재탐색하고 업데이트 Push 메시지를 보낸다
                final WebRiderIdListDTO webRiderIdListDTO = WebRiderIdListDTO.builder().projectId(projectId).riderIds(riderIds).build();
                this.dispatchByRiders(projectId, webRiderIdListDTO.getRiderIds(), project.getRouteOption(), withClustering , false);
            }
        }
    }

    /**
     * 관제중( 또는 경로탐색 이후) 변경 사항이 배송지 추가 등 변경 사항이 발견되었을 경우 수행
     * @param projectId
     */
//    @Deprecated
//    public void dispatchProjectOnDemand(Long projectId) {
//        Project project = projectBasicService.getProjectById(projectId);
//        final List<Delivery> riderDeliveries = deliveryService.getAllDeliveries(projectId, null, null,  null, null, null);
//
//        //배차 되지 않은 배송지를 찾는다
//        List<Delivery> unAllocatedDeliveries = riderDeliveries.stream().filter( delivery -> delivery.getAllocations() == null || delivery.getAllocations().size() == 0).collect(Collectors.toList());
//        if (unAllocatedDeliveries.size() > 0) {
//            this.dispatchAddDeliveries(projectId, unAllocatedDeliveries, null);
//        }
//    }

    public void sendUpdateDeliveriesArrivedToRiderAppIfNeed(Long projectId,
                                                            Collection<Long> riderIds) {

        if (projectBasicService.isProjectAnonymous(projectId)) { //기사앱에 Push 메시지 보내기
            return;
        }

        if (projectBasicService.isDispatchToRidersDone(projectId)) {
            final Organization organization = organizationService.getOrganizationByProjectId(projectId);
            final String codeName = Optional.ofNullable(organization).map(Organization::getCodeName).orElse(StringUtils.EMPTY);

            riderIds.forEach(riderId -> {
                if (!riderWorkCompletionService.isRiderWorkCompletedInProject(riderId, projectId)) {
                    pushService.sendUpdateDeliveriesArrivedToRiderApp(riderId, projectId, codeName);
                }
            });
            projectBasicService.updateProjectAttribute(projectId, ProjectAttribute.builder().isSendingRiderEnabled(false).build()); // 기사에게 전송 버튼 비활성화
        }
    }

    public void getOnDemandClustersAndDispatchByRidersWithUserId(final Long projectId,
                                                                 final Long userId) {

        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);
        List<Delivery> addedDeliveries = clusterService.getOnDemandClustersByRidersWithUserId(projectId, userId);

        if (addedDeliveries.size() > 0) {
            List<Long> riderIds = addedDeliveries.stream()
                    .map(delivery -> deliveryBasicService.getDeliveryAllocation(delivery.getId())
                            .map(deliveryAllocation -> deliveryAllocation.getRiderId())
                            .orElse(null))
                    .collect(Collectors.toList());

            this.dispatchByRiders(projectId, riderIds, project.getRouteOption(), false, true);
        }
    }

    /**
     * 순서고정 여부에 따라 프로젝트내 특정 기사의 경로생성
     *
     * @param projectId
     * @param riderId
     * @param isFixedOrder
     */
    public void routeByProjectRider(final Long projectId,
                                    final Long riderId,
                                    final Boolean isFixedOrder) {

        // 기사의 배차정보가 있는지 확인
        List<DeliveryAllocation> riderDeliveryAllocationList = deliveryBasicService.getRiderDeliveryAllocations(riderId, projectId);
        if (CollectionUtils.isEmpty(riderDeliveryAllocationList)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "기사의 배차정보가 없습니다.", false);
        }

        final Integer routeOption = projectBasicService.getRouteOption(projectId);
        final long nullOrderCount = riderDeliveryAllocationList.stream()
                .filter(rda -> Objects.isNull(rda.getOrderNum()))
                .count();

        log.info("[routeByProjectRider] projectId: {}, riderId: {}, isFixedOrder: {}, allocationListSize: {}, nullOrderCount: {}", projectId, riderId, isFixedOrder, riderDeliveryAllocationList.size(), nullOrderCount);

        if ((long) riderDeliveryAllocationList.size() == nullOrderCount) {
            // 모든 배송순서가 없으면 최적화 경로생성
            this.dispatchByRiders(projectId, Arrays.asList(riderId), routeOption, false, false);
        } else {
            // 배송순서가 하나라도 있다면 기존 순서에 추가 배차순으로 덧붙여 경로생성
            if (Objects.nonNull(isFixedOrder) && Boolean.TRUE.equals(isFixedOrder)) {
                // 가장 큰 order_num 뒤에 delivery_allocation.id 오름차순으로 배송순서를 하나씩 증가하도록 처리
                int maxOrder = riderDeliveryAllocationList.stream()
                        .filter(da -> Objects.nonNull(da.getOrderNum()))
                        .mapToInt(DeliveryAllocation::getOrderNum)
                        .max()
                        .orElse(0);

                List<DeliveryAllocation> nullOrderAllocations = riderDeliveryAllocationList.stream()
                        .filter(da -> Objects.isNull(da.getOrderNum()))
                        .sorted(Comparator.comparingLong(DeliveryAllocation::getId))
                        .collect(Collectors.toList());

                for (DeliveryAllocation da : nullOrderAllocations) {
                    da.setOrderNum(++maxOrder);
                }
                deliveryBasicService.saveDeliveryAllocations(riderDeliveryAllocationList);

                List<DeliveryAllocation> results = riderDeliveryAllocationList.stream()
                        .sorted(Comparator.comparing(DeliveryAllocation::getOrderNum))
                        .collect(Collectors.toList());
                results.forEach(r -> log.info("[routeByProjectRider] orderNum: {}, allocationId: {}, deliveryId: {}, customerOrderId: {}", r.getOrderNum(), r.getId(), r.getDelivery().getId(), r.getDelivery().getDetail().getCustomerOrderId()));

                this.dispatchByRiders(projectId, Arrays.asList(riderId), routeOption, false, true);
            }
        }
    }

}
