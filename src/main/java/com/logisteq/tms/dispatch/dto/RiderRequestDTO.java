package com.logisteq.tms.dispatch.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.logisteq.tms.delivery.domain.suppl.DeliveryType;
import com.logisteq.tms.vehicle.types.MilesType;
import lombok.*;
import org.locationtech.jts.geom.Point;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiderRequestDTO {

    private Long riderId;

    // 프로젝트가 관제 중이고 기사의 마지막 위치가 있다면 그걸 사용하고, 그렇지 않으면 근무시작주소
    private Point currentLocation;

    // 근무시작주소 (현재 설정하는 코드는 있으나 이를 활용하는 코드는 없다.)
    @Deprecated
    private Point workStartLocation;

    // 근무시작시간 (현재 설정하는 코드가 없다.)
    // (NULL이 아니면 기사의 경로탐색 시 RouteAppPlan ETA를 계산하기 위한 기준 시간으로 활용된다.)
    @Deprecated
    private LocalDateTime workStartDateTime;

    private MilesType milesType;

    private String groupName;

    @JsonIgnore
    public DeliveryType getDeliveryType() {

        if (milesType == MilesType.FIRSTMILE) {
            return DeliveryType.INTERMEDIATE_DESTINATION;
        } else {
            return DeliveryType.FINAL_DESTINATION;
        }
    }

}
