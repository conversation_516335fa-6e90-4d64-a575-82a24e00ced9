package com.logisteq.tms.auth.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

@Slf4j
@Validated
@Service
public class Aes256Service {

	@Value("${service-auth-secret: null}")
	private String serviceSecret;
	private SecretKeySpec keySpec = null;
	private String iv = null;
	
	@PostConstruct
	public void init() {
		this.iv = this.serviceSecret.substring(0, 16);
		
		byte[] keyBytes = new byte[16];
		byte[] b = this.serviceSecret.getBytes(StandardCharsets.UTF_8);
		int len = b.length;
		if (len > keyBytes.length) {
			len = keyBytes.length;
		}
		System.arraycopy(b, 0, keyBytes, 0, len);
		this.keySpec = new SecretKeySpec(keyBytes, "AES");
	}

	public String encrypt(String data) {
		try {
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
			cipher.init(Cipher.ENCRYPT_MODE, keySpec, new IvParameterSpec(iv.getBytes()));
			byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
			String enStr = Base64.encodeBase64String(encrypted);
			return enStr;
		} catch (InvalidKeyException | NoSuchAlgorithmException | NoSuchPaddingException | InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException e) {
			log.error("인증키 생성 실패", e);
			return null;
		}
	}

	public String decrypt(String encryptionData) {
		try {
			Cipher c = Cipher.getInstance("AES/CBC/PKCS5Padding");
			c.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(iv.getBytes()));
			byte[] byteStr = Base64.decodeBase64(encryptionData.getBytes());
			return new String(c.doFinal(byteStr), StandardCharsets.UTF_8);
		} catch (NoSuchAlgorithmException | InvalidKeyException | NoSuchPaddingException | IllegalBlockSizeException | BadPaddingException | InvalidAlgorithmParameterException e) {
			log.error("인증키 생성 실패", e);
			return null;
		}
	}

}
