package com.logisteq.tms.auth.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.logisteq.common.dto.auth.OauthToken;
import com.logisteq.common.exception.CustomException;
import com.logisteq.common.feign.auth.AuthServiceClient;
import com.logisteq.common.sms.service.SmsService;
import com.logisteq.tms.app.domain.AppBinaryVersion;
import com.logisteq.tms.app.service.AppService;
import com.logisteq.tms.auth.domain.AuthKey;
import com.logisteq.tms.auth.dto.AuthKeyData;
import com.logisteq.tms.auth.dto.AuthProjectDTO;
import com.logisteq.tms.auth.dto.AuthSendData;
import com.logisteq.tms.auth.service.AuthKeyService;
import com.logisteq.tms.common.component.ProfileManager;
import com.logisteq.tms.privacy.dto.PrivacyRecordDto;
import com.logisteq.tms.privacy.service.PrivacyRecordService;
import com.logisteq.tms.privacy.suppl.PrivacyDataType;
import com.logisteq.tms.privacy.suppl.PrivacyRecordType;
import com.logisteq.tms.privacy.suppl.PrivacyUsageType;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.domain.RiderOrgStatus;
import com.logisteq.tms.rider.domain.suppl.LinkStatus;
import com.logisteq.tms.rider.service.RiderService;
import com.logisteq.tms.user.constant.UserType;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.service.UserService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 인증키 컨트롤러 V2
 */
@Tag(name = "AuthKeyV2")
@Slf4j
@Validated
@RestController
@RequestMapping("/api/auth/v2")
public class AuthKeyControllerV2 {

    private final SmsService smsService;
    private final AuthKeyService authKeyService;
    private final AuthServiceClient authServiceClient;
    private final RiderService riderService;
    private final AppService appService;
    private final ProfileManager profileManager;
    private final UserService userService;
    private final PrivacyRecordService privacyRecordService;

    @Value("${spring.security.oauth2-client.app.client-id}")
    private String appClientId;

    @Value("${spring.security.oauth2-client.app.client-secret}")
    private String appClientSecret;

    @Autowired
    public AuthKeyControllerV2(final SmsService smsService,
                               final AuthKeyService authKeyService,
                               final AuthServiceClient authServiceClient,
                               final RiderService riderService,
                               final AppService appService,
                               final ProfileManager profileManager,
                               final UserService userService,
                               final PrivacyRecordService privacyRecordService) {

        this.smsService = smsService;
        this.authKeyService = authKeyService;
        this.riderService = riderService;
        this.authServiceClient = authServiceClient;
        this.appService = appService;
        this.profileManager = profileManager;
        this.userService = userService;
        this.privacyRecordService = privacyRecordService;
    }

    /**
     * 현대 POC 특정 rider 전화번호 4개는 인증 절차 없이 바로 사용 가능하게 기능 구현
     *
     * @param userType
     * @param appId
     * @param withAppUrl
     * @param codeName
     * @param sendData
     * @return
     */
    @Hidden
    @PostMapping("key/pass/verification")
    public Map<String, Object> passVerifyAuthKey(@RequestParam(defaultValue = "RIDER", required = false) UserType userType,
                                                 @RequestParam(defaultValue = "1", required = false) Long appId,
                                                 @RequestParam(defaultValue = "false", required = false) Boolean withAppUrl,
                                                 @RequestParam(required = false) @Nullable String codeName,
                                                 @RequestBody @Valid AuthSendData sendData) {

        // 유저 아이디 조회
        final String mobile = sendData.getMobile();
        if (riderService.isCheckingOrgCodeNameInRider(mobile, codeName)) {
            Long userId = getUserId(UserType.RIDER, mobile);

            // 최신 앱 버전 정보 조회 TODO : Android 앱 강제로 설정
            AppBinaryVersion appBinaryVersion = null;
            if (withAppUrl) {
                try {
                    final List<AppBinaryVersion> appBinaryVersions = appService.getRecentAppVersion(appId).getBinaryVersions();
                    if (CollectionUtils.isNotEmpty(appBinaryVersions)) {
                        appBinaryVersion = appBinaryVersions.get(0);
                    }
                } catch (Exception e) {
                    log.error("최신 앱 정보 조회 오류", e);
                }
            }

            // 기사 링크 연결 상태로 변경
            if (UserType.RIDER.equals(userType)) {
                riderService.updateRiderLinkStatus(userId, LinkStatus.CONNECTED);
            }

            OauthToken token = null;
            try {
                // http://localhost:5903/oauth/token
                // username 은 :oauth:@{UserType}@{userId}@{authKey}
                // password 는 발급한 인증키를 사용한다.
                final String oauthUsername = ":oauth:@" + userType.name() + "@" + userId + "@" + mobile;
                final String oauthPassword = mobile;
                token = getAppAccessToken(oauthUsername, oauthPassword);
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 응답 데이터 생성
            Map<String, Object> result = new HashMap<>();
            result.put("userId", String.valueOf(userId));
            result.put("token", token);

            return result;
        } else {
            throw new CustomException(HttpStatus.UNAUTHORIZED, mobile + "번호는 사용 할수 없습니다.", false);
        }
    }

    /**
     * 인증키 발송
     *
     * @param userType
     * @param appId
     * @param withAppUrl
     * @param sendData   데이터
     */
    @Operation(summary = "인증키 발송", description = "인증키를 발송한다.")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "OK")
    })
    @PostMapping(value = "key")
    public void sendAuthKey(@RequestParam(defaultValue = "RIDER", required = false) UserType userType,
                            @RequestParam(defaultValue = "1", required = false) Long appId,
                            @RequestParam(defaultValue = "false", required = false) Boolean withAppUrl,
                            @RequestBody @Valid AuthSendData sendData) {

        // 유저 아이디 조회
        final String mobile = sendData.getMobile();
        final Long userId = getUserId(userType, mobile);

        // 최신 앱 버전 정보 조회 TODO : Android 앱 강제로 설정
        AppBinaryVersion appBinaryVersion = null;
        if (withAppUrl) {
            try {
                final List<AppBinaryVersion> appBinaryVersions = appService.getRecentAppVersion(appId).getBinaryVersions();
                if (CollectionUtils.isNotEmpty(appBinaryVersions)) {
                    appBinaryVersion = appBinaryVersions.get(0);
                }
            } catch (Exception e) {
                log.error("최신 앱 정보 조회 오류", e);
            }
        }

        // 문자 전송
        boolean isSuccess = authKeyService.sendAndSaveAuthKey(userId, userType, sendData.getMobile(), appBinaryVersion);
        if (isSuccess) {
            // 라이더 링크 상태 변경
            if (UserType.RIDER.equals(userType)) {
                riderService.updateRiderLinkStatus(userId, LinkStatus.KEY_SENT);
            }
        }
    }

    /**
     * 인증키 검증
     *
     * @param userType
     * @param authKeyData 검증 데이터
     * @return 사용자 아이디
     */
    @Operation(summary = "인증키 검증", description = "인증키를 검증한다.")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "OK"),
            @ApiResponse(responseCode = "400", description = "Bad Request"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Not Found")
    })
    @PostMapping("key/verification")
    public Map<String, Object> verifyAuthKey(@RequestParam(defaultValue = "RIDER", required = false) UserType userType,
                                             @RequestBody @Valid AuthKeyData authKeyData) {

        // 유저 아이디 조회
        Long userId = 0L;
        if (UserType.RIDER.equals(userType)) {
            userId = getUserId(userType, authKeyData.getMobile());
        } else if (UserType.WEB.equals(userType)) {
            final User user = userService.getUserByEmail(authKeyData.getEmail()).orElse(null);
            if (Objects.nonNull(user)) {
                userId = user.getUserId();
            }
        }

        // 인증번호 유효성 검사
        final String authKey = authKeyData.getAuthKey();
        final String mobile = authKeyData.getMobile();
        authKeyService.validateAuthKey(userId, userType, authKey, mobile);

        // 기사 링크 연결 상태로 변경
        if (UserType.RIDER.equals(userType)) {
            riderService.updateRiderLinkStatus(userId, LinkStatus.CONNECTED);
        }

        OauthToken token = null;
        if (UserType.RIDER.equals(userType)) {
            try {
                // http://localhost:5903/oauth/token
                // username 은 :oauth:@{UserType}@{userId}@{authKey}
                // password 는 발급한 인증키를 사용한다.
                final String oauthUsername = ":oauth:@" + userType.name() + "@" + userId + "@" + authKey;
                final String oauthPassword = authKey;
                token = getAppAccessToken(oauthUsername, oauthPassword);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 응답 데이터 생성
        Map<String, Object> result = new HashMap<>();
        result.put("userId", String.valueOf(userId));
        result.put("token", token);

        if (UserType.RIDER.equals(userType)) {
            // 기사의 업무권한을 응답에 추가 (예: "workAuthority":"DELIVERY")
            final Rider rider = riderService.getRiderById(userId).orElse(null);
            if (Objects.nonNull(rider)) {
                final String workAuthority = Optional.ofNullable(rider.getWorkAuthority()).map(wa -> wa.getEnglish().get(0)).orElse(null);
                result.put("workAuthority", workAuthority);
            }
        }

        return result;
    }

    /**
     * App OAuth Token 발급
     *
     * @param username
     * @param password
     * @return
     */
    private OauthToken getAppAccessToken(final String username,
                                         final String password) throws JsonProcessingException {

        // NEW (auth-service와 통신 시 Feign 사용)
        {
            // 서버에서 발급받은 clientId:clientSecret를 base64 Encoding하여 해더에 설정한다.
            String base64Credentials = Base64.getEncoder().encodeToString((appClientId + ":" + appClientSecret).getBytes());

            Map<String, String> formData = new HashMap<>();
            formData.put("grant_type", "password");
            formData.put("scope", "read write");
            formData.put("username", username);
            formData.put("password", password);

            return authServiceClient.postOauthToken("Basic " + base64Credentials, formData);
        }

        // OLD (auth-service와 통신 시 HttpClient 사용)
//        {
//            HttpClient httpClient = HttpClientBuilder.create().build();
//
//            // Profile 에 따른 api-gateway 포트를 가져와서 endpoint를 설정한다.
//            ProfileManager.ProfileServerEndPoint profileServerEndPoint = profileManager.getCurrentProfileServerPort();
//            String tokenEndpoint = profileServerEndPoint.getApiGateway() + "/oauth/token";
//            HttpPost httpPost = new HttpPost(tokenEndpoint);
//
//            // 서버에서 발급받은 clientId:clientSecret를 base64 Encoding하여 해더에 설정한다.
//            String base64Credentials = Base64.getEncoder().encodeToString((appClientId + ":" + appClientSecret).getBytes());
//            httpPost.addHeader("Authorization", "Basic " + base64Credentials);
//
//            String grant_type = "password";
//            String scope = "read write";
//            httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded");
//            StringEntity input;
//            try {
//                input = new StringEntity("grant_type=" + grant_type +
//                        "&scope=" + scope + "&username=" + username + "&password=" + password);
//                httpPost.setEntity(input);
//            } catch (UnsupportedEncodingException e) {
//                e.printStackTrace();
//            }
//
//            // Http 요청 수행
//            HttpResponse response = null;
//            String result = null;
//            try {
//                response = httpClient.execute(httpPost);
//                result = IOUtils.toString(response.getEntity().getContent(), "UTF-8");
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//
//            //log.info("result : {}", result);
//
//            ObjectMapper om = new ObjectMapper();
//            return om.readValue(result, OauthToken.class);
//        }
    }

    /**
     * 인증키 해지
     *
     * @param userId
     * @param userType
     */
    @Hidden
    @PostMapping("key/cancellation")
    public void cancelAuthKey(@RequestParam Long userId,
                              @RequestParam(defaultValue = "RIDER", required = false) UserType userType) {

        // 기사 링크 연결 상태로 변경
        if (UserType.RIDER.equals(userType)) {
            riderService.updateRiderLinkStatus(userId, LinkStatus.NOT_CONNECTED);
        }

        Rider rider = riderService.getRider( userId );
        if( rider != null ) {
            try{
                final List<RiderOrgStatus> riderOrgStatusList = rider.getOrgStatusList();
                for (RiderOrgStatus status : riderOrgStatusList) {
                    if (!status.getIsDeleted()) {
                        Optional.ofNullable(status.getOrgId()).ifPresent(orgId -> {
                            privacyRecordService.saveRecord(
                                    PrivacyRecordDto.builder()
                                            .recordType(PrivacyRecordType.RIDER)
                                            .orgId(orgId)
                                            .type(PrivacyUsageType.DEACTIVATE)
                                            .dataType(PrivacyDataType.NAME_MOBILE)
                                            .acquisitionChannel("MOBILE")
                                            .func("cancelAuthKey")
                                            .riderId(userId)
                                            .build());
                        });
                    }
                }
            } catch (Exception e) {
                log.error("Error occurred while saving privacy records: ", e);
            }
        }
    }

    /**
     * 프로젝트의 기사들에게 일괄 인증키 전송 (라이더 전용)
     *
     * @param authProjectDto 프로젝트 데이터
     * @param appId
     * @param skipSentRider  강제 전송
     */
    @Hidden
    @PostMapping(value = "keys", params = "userType=RIDER")
    public void sendAuthKeysToRiderInProject(@RequestBody @Valid final AuthProjectDTO authProjectDto,
                                             @RequestParam(defaultValue = "1", required = false) final Long appId,
                                             @RequestParam(required = false, defaultValue = "false") final Boolean skipSentRider) {

        // 프로젝트에 할당된 기사 목록 조회
        final List<Rider> riders = riderService.getRiders(authProjectDto.getProjectId(), false);
        if (CollectionUtils.isEmpty(riders)) {
            return;
        }

        // 기사들에게 인증키 발송
        riders.forEach(rider -> {
            final Long riderId = rider.getId();

            if (skipSentRider) {
                // 인증키 발송 여부 확인
                final List<AuthKey> authKeyList = authKeyService.getAuthKeyList(riderId, UserType.RIDER);
                final AuthKey authKey = authKeyList.stream()
                        .filter(key -> key.getExpiredAt().isAfter(LocalDateTime.now()))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(authKey)) {
                    log.info("이미 인증키 발송 되었습니다. userId: {}", authKey.getUserId());
                    return;
                }
            }

            // 앱 바이너리 버전 조회
            final AppBinaryVersion appBinaryVersion = appService.getRecentAppVersion(appId).getBinaryVersions().get(0);

            // 모바일 번호
            final String riderMobile = rider.getMobile();

            // 문자 전송
            boolean isSuccess = authKeyService.sendAndSaveAuthKey(rider.getId(), UserType.RIDER, riderMobile, appBinaryVersion);
            if (isSuccess) {
                // 라이더 링크 상태 변경
                riderService.updateRiderLinkStatus(riderId, LinkStatus.KEY_SENT);
            }
        });
    }

//    /**
//     * 인증 메시지 텍스트 조합
//     *
//     * @param appBinaryVersion 앱 바이너리 버전 객체
//     * @param authNumber 인증번호
//     * @return 조합된 메시지
//     */
//    private String combineAuthMessage(AppBinaryVersion appBinaryVersion, String authNumber) {
//        String message = "";
//
//        if (appBinaryVersion != null && StringUtils.isNotBlank(appBinaryVersion.getFileDownloadUrl())) {
//            message += ("APP DOWNLOAD [" + appBinaryVersion.getFileDownloadUrl() + "]");
//        }
//
//        if (StringUtils.isNotBlank(authNumber)) {
//            if (StringUtils.isNotBlank(message)) {
//                message += "\r\n";
//            }
//            message += "인증번호[" + authNumber + "]";
//        }
//
//        return message;
//    }
//
//    /**
//     * 문자 전송
//     *
//     * @param mobile 사용자 모바일 번호
//     * @param appBinaryVersion 앱 바이너리 버전
//     * @param authNumber 인증번호
//     * @return 성공 여부
//     */
//    public boolean sendSmsMessage(String mobile, AppBinaryVersion appBinaryVersion, String authNumber) {
//
//        // local profile 인 경우 전송 루틴 스킵
////        if (profileManager.isLocalProfiles()) {
////            return true;
////        }
//
//        // 문자 전송
//        try {
//            smsService.sendSmsMessage(SmsMessage.builder()
//                    .from(AuthConstant.AUTH_MSG_SENDER_MOBILE_NUMBER)
//                    .to(mobile)
//                    //.subject(AuthConstant.AUTH_SMS_MESSAGE_SUBJECT)
//                    .text(combineAuthMessage(appBinaryVersion, authNumber))
//                    .build());
//
//            return true;
//        } catch (Exception e) {
//            log.error("인증키 발송 오류", e);
//            return false;
//        }
//    }

    /**
     * 사용자 아이디 조회
     *
     * @param userType
     * @param mobile
     * @return
     */
    private Long getUserId(final UserType userType,
                           final String mobile) {

        Long userId = null;

        switch (userType) {
            case RIDER:
                final Rider rider = riderService.getRider(mobile).orElseThrow(() -> new CustomException(HttpStatus.BAD_REQUEST, mobile + " 전화번호를 갖는 기사를 찾을 수 없습니다.", false));
                userId = rider.getId();
                break;
            // TODO :
            default:
                throw new CustomException(HttpStatus.BAD_REQUEST, "Unsupported user type");
        }

        return userId;
    }

}
