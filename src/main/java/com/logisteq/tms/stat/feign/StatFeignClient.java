package com.logisteq.tms.stat.feign;

import com.logisteq.tms.stat.constant.StatConst;
import com.logisteq.tms.stat.constant.StatUrl;
import com.logisteq.tms.stat.domain.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.Positive;
import java.time.LocalDate;
import java.time.Year;
import java.time.YearMonth;
import java.util.List;
import java.util.Set;

/**
 * 통계 Feign Client
 *
 * <AUTHOR>
 */
@Validated
@FeignClient(name = "tms-stat-service",
        url = "${feign.client.config.tms-stat-service.url:}",
        configuration = StatFeignClientConfig.class
)
public interface StatFeignClient {

    /**
     * 기사의 전체 배송 통계 조회
     *
     * @param pageable
     * @param riderId
     * @param likeRiderName
     * @param userIdList
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_RIDER_TOTAL_DELIVERY_API)
    Page<StatRiderTotalDeliveryResponseDTO> getStatRiderTotalDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.RIDER_ID_PARAM_NAME, required = false) @Positive final Long riderId,
            @RequestParam(name = StatConst.RIDER_NAME_LIKE_PARAM_NAME, required = false) final String likeRiderName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 기사의 년간 배송 통계 조회
     *
     * @param pageable
     * @param riderIdList
     * @param likeRiderName
     * @param userIdList
     * @param year
     * @param fromYear
     * @param toYear
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_RIDER_YEARLY_DELIVERY_API)
    List<Page<StatRiderYearlyDeliveryResponseDTO>> getStatRidersYearlyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.RIDER_ID_LIST_PARAM_NAME, required = false) final List<Long> riderIdList,
            @RequestParam(name = StatConst.RIDER_NAME_LIKE_PARAM_NAME, required = false) final String likeRiderName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.YEAR_PARAM_NAME, required = false) final Year year,
            @RequestParam(name = StatConst.FROM_YEAR_PARAM_NAME, required = false) final Year fromYear,
            @RequestParam(name = StatConst.TO_YEAR_PARAM_NAME, required = false) final Year toYear,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 기사의 월간 배송 통계 조회
     *
     * @param pageable
     * @param riderIdList
     * @param likeRiderName
     * @param userIdList
     * @param yearMonth
     * @param fromYearMonth
     * @param toYearMonth
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_RIDER_MONTHLY_DELIVERY_API)
    List<Page<StatRiderMonthlyDeliveryResponseDTO>> getStatRidersMonthlyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.RIDER_ID_LIST_PARAM_NAME, required = false) final List<Long> riderIdList,
            @RequestParam(name = StatConst.RIDER_NAME_LIKE_PARAM_NAME, required = false) final String likeRiderName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.YEARMONTH_PARAM_NAME, required = false) final YearMonth yearMonth,
            @RequestParam(name = StatConst.FROM_YEARMONTH_PARAM_NAME, required = false) final YearMonth fromYearMonth,
            @RequestParam(name = StatConst.TO_YEARMONTH_PARAM_NAME, required = false) final YearMonth toYearMonth,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 기사의 주간 배송 통계 조회
     *
     * @param pageable
     * @param riderIdList
     * @param likeRiderName
     * @param userIdList
     * @param week
     * @param fromWeek
     * @param toWeek
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_RIDER_WEEKLY_DELIVERY_API)
    List <Page<StatRiderWeeklyDeliveryResponseDTO>> getStatRidersWeeklyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.RIDER_ID_LIST_PARAM_NAME, required = false) final List<Long> riderIdList,
            @RequestParam(name = StatConst.RIDER_NAME_LIKE_PARAM_NAME, required = false) final String likeRiderName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.WEEK_PARAM_NAME, required = false) final LocalDate week,
            @RequestParam(name = StatConst.FROM_WEEK_PARAM_NAME, required = false) final LocalDate fromWeek,
            @RequestParam(name = StatConst.TO_WEEK_PARAM_NAME, required = false) final LocalDate toWeek,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 기사의 일간 배송 통계 조회
     *
     * @param pageable
     * @param riderIdList
     * @param likeRiderName
     * @param userIdList
     * @param date
     * @param fromDate
     * @param toDate
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_RIDER_DAILY_DELIVERY_API)
    List<Page<StatRiderDailyDeliveryResponseDTO>> getStatRidersDailyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.RIDER_ID_LIST_PARAM_NAME, required = false) final List<Long> riderIdList,
            @RequestParam(name = StatConst.RIDER_NAME_LIKE_PARAM_NAME, required = false) final String likeRiderName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.DATE_PARAM_NAME, required = false) final LocalDate date,
            @RequestParam(name = StatConst.FROM_DATE_PARAM_NAME, required = false) final LocalDate fromDate,
            @RequestParam(name = StatConst.TO_DATE_PARAM_NAME, required = false) final LocalDate toDate,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 기사의 일간/프로젝트별 배송 통계 통계 조회
     *
     * @param pageable
     * @param riderId
     * @param likeRiderName
     * @param projectId
     * @param likeProjectName
     * @param userIdList
     * @param date
     * @param fromDate
     * @param toDate
     * @return
     */
    @GetMapping(StatUrl.STAT_RIDER_DAILY_PROJECT_DELIVERY_API)
    Page<StatRiderDailyProjectDeliveryResponseDTO> getStatRiderDailyProjectDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.RIDER_ID_PARAM_NAME, required = false) @Positive final Long riderId,
            @RequestParam(name = StatConst.RIDER_NAME_LIKE_PARAM_NAME, required = false) final String likeRiderName,
            @RequestParam(name = StatConst.PROJECT_ID_PARAM_NAME, required = false) @Positive final Long projectId,
            @RequestParam(name = StatConst.PROJECT_NAME_LIKE_PARAM_NAME, required = false) final String likeProjectName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.DATE_PARAM_NAME, required = false) final LocalDate date,
            @RequestParam(name = StatConst.FROM_DATE_PARAM_NAME, required = false) final LocalDate fromDate,
            @RequestParam(name = StatConst.TO_DATE_PARAM_NAME, required = false) final LocalDate toDate);

    /**
     * 프로젝트별 배송 통계 조회
     *
     * @param pageable
     * @param projectId
     * @param likeProjectName
     * @param userIdList
     * @param date
     * @param fromDate
     * @param toDate
     * @return
     */
    @GetMapping(StatUrl.STAT_PROJECT_DELIVERY_API)
    Page<StatProjectDeliveryResponseDTO> getStatProjectDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.PROJECT_ID_PARAM_NAME, required = false) @Positive final Long projectId,
            @RequestParam(name = StatConst.PROJECT_NAME_LIKE_PARAM_NAME, required = false) final String likeProjectName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.DATE_PARAM_NAME, required = false) final LocalDate date,
            @RequestParam(name = StatConst.FROM_DATE_PARAM_NAME, required = false) final LocalDate fromDate,
            @RequestParam(name = StatConst.TO_DATE_PARAM_NAME, required = false) final LocalDate toDate);

    /**
     * 일반 전체 배송 통계 조회
     *
     * @param pageable
     * @param userIdList
     * @return
     */
    @GetMapping(StatUrl.STAT_GENERAL_TOTAL_DELIVERY_API)
    Page<StatGeneralTotalDeliveryResponseDTO> getStatGeneralTotalDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList);

    /**
     * 일반 년간 배송 통계 조회
     *
     * @param pageable
     * @param userIdList
     * @param year
     * @param fromYear
     * @param toYear
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_GENERAL_YEARLY_DELIVERY_API)
    Page<StatGeneralYearlyDeliveryResponseDTO> getStatGeneralYearlyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.YEAR_PARAM_NAME, required = false) final Year year,
            @RequestParam(name = StatConst.FROM_YEAR_PARAM_NAME, required = false) final Year fromYear,
            @RequestParam(name = StatConst.TO_YEAR_PARAM_NAME, required = false) final Year toYear,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 일반 월간 배송 통계 조회
     *
     * @param pageable
     * @param userIdList
     * @param yearMonth
     * @param fromYearMonth
     * @param toYearMonth
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_GENERAL_MONTHLY_DELIVERY_API)
    Page<StatGeneralMonthlyDeliveryResponseDTO> getStatGeneralMonthlyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.YEARMONTH_PARAM_NAME, required = false) final YearMonth yearMonth,
            @RequestParam(name = StatConst.FROM_YEARMONTH_PARAM_NAME, required = false) final YearMonth fromYearMonth,
            @RequestParam(name = StatConst.TO_YEARMONTH_PARAM_NAME, required = false) final YearMonth toYearMonth,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 일반 주간 배송 통계 조회
     *
     * @param pageable
     * @param userIdList
     * @param week
     * @param fromDate
     * @param toDate
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_GENERAL_WEEKLY_DELIVERY_API)
    Page<StatGeneralWeeklyDeliveryResponseDTO> getStatGeneralWeeklyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.WEEK_PARAM_NAME, required = false) final LocalDate week,
            @RequestParam(name = StatConst.FROM_WEEK_PARAM_NAME, required = false) final LocalDate fromDate,
            @RequestParam(name = StatConst.TO_WEEK_PARAM_NAME, required = false) final LocalDate toDate,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 일반 일간 배송 통계 조회
     *
     * @param pageable
     * @param userIdList
     * @param date
     * @param fromDate
     * @param toDate
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_GENERAL_DAILY_DELIVERY_API)
    Page<StatGeneralDailyDeliveryResponseDTO> getStatGeneralDailyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.DATE_PARAM_NAME, required = false) final LocalDate date,
            @RequestParam(name = StatConst.FROM_DATE_PARAM_NAME, required = false) final LocalDate fromDate,
            @RequestParam(name = StatConst.TO_DATE_PARAM_NAME, required = false) final LocalDate toDate,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 권역의 전체 배송 통계 조회
     *
     * @param pageable
     * @param groupName
     * @param likeGroupName
     * @param userIdList
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_GROUP_NAME_TOTAL_DELIVERY_API)
    Page<StatGroupNameTotalDeliveryResponseDTO> getStatGroupNameTotalDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.GROUP_NAME_COLUMN_NAME, required = false) final String groupName,
            @RequestParam(name = StatConst.GROUP_NAME_LIKE_PARAM_NAME, required = false) final String likeGroupName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);


    /**
     * 권역의 년간 배송 통계 조회
     *
     * @param pageable
     * @param groupNameList
     * @param likeGroupName
     * @param userIdList
     * @param year
     * @param fromYear
     * @param toYear
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_GROUP_NAME_YEARLY_DELIVERY_API)
    List<Page<StatGroupNameYearlyDeliveryResponseDTO>> getStatGroupNamesYearlyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.GROUP_NAME_LIST_PARAM_NAME, required = false) final List<String> groupNameList,
            @RequestParam(name = StatConst.GROUP_NAME_LIKE_PARAM_NAME, required = false) final String likeGroupName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.YEAR_PARAM_NAME, required = false) final Year year,
            @RequestParam(name = StatConst.FROM_YEAR_PARAM_NAME, required = false) final Year fromYear,
            @RequestParam(name = StatConst.TO_YEAR_PARAM_NAME, required = false) final Year toYear,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 권역의 월간 배송 통계 조회
     *
     * @param pageable
     * @param groupNameList
     * @param likeGroupName
     * @param userIdList
     * @param yearMonth
     * @param fromYearMonth
     * @param toYearMonth
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_GROUP_NAME_MONTHLY_DELIVERY_API)
    List<Page<StatGroupNameMonthlyDeliveryResponseDTO>> getStatGroupNamesMonthlyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.GROUP_NAME_LIST_PARAM_NAME, required = false) final List<String> groupNameList,
            @RequestParam(name = StatConst.GROUP_NAME_LIKE_PARAM_NAME, required = false) final String likeGroupName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.YEARMONTH_PARAM_NAME, required = false) final YearMonth yearMonth,
            @RequestParam(name = StatConst.FROM_YEARMONTH_PARAM_NAME, required = false) final YearMonth fromYearMonth,
            @RequestParam(name = StatConst.TO_YEARMONTH_PARAM_NAME, required = false) final YearMonth toYearMonth,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 권역의 주간 배송 통계 조회
     *
     * @param pageable
     * @param groupNameList
     * @param likeGroupName
     * @param userIdList
     * @param week
     * @param fromWeek
     * @param toWeek
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_GROUP_NAME_WEEKLY_DELIVERY_API)
    List <Page<StatGroupNameWeeklyDeliveryResponseDTO>> getStatGroupNamesWeeklyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.GROUP_NAME_LIST_PARAM_NAME, required = false) final List<String> groupNameList,
            @RequestParam(name = StatConst.GROUP_NAME_LIKE_PARAM_NAME, required = false) final String likeGroupName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.WEEK_PARAM_NAME, required = false) final LocalDate week,
            @RequestParam(name = StatConst.FROM_WEEK_PARAM_NAME, required = false) final LocalDate fromWeek,
            @RequestParam(name = StatConst.TO_WEEK_PARAM_NAME, required = false) final LocalDate toWeek,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 권역의 일간 배송 통계 조회
     *
     * @param pageable
     * @param groupNameList
     * @param likeGroupName
     * @param userIdList
     * @param date
     * @param fromDate
     * @param toDate
     * @param groupBy
     * @return
     */
    @GetMapping(StatUrl.STAT_GROUP_NAME_DAILY_DELIVERY_API)
    List<Page<StatGroupNameDailyDeliveryResponseDTO>> getStatGroupNamesDailyDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.GROUP_NAME_LIST_PARAM_NAME, required = false) final List<String> groupNameList,
            @RequestParam(name = StatConst.GROUP_NAME_LIKE_PARAM_NAME, required = false) final String likeGroupName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.DATE_PARAM_NAME, required = false) final LocalDate date,
            @RequestParam(name = StatConst.FROM_DATE_PARAM_NAME, required = false) final LocalDate fromDate,
            @RequestParam(name = StatConst.TO_DATE_PARAM_NAME, required = false) final LocalDate toDate,
            @RequestParam(name = StatConst.GROUP_BY_PARAM_NAME, required = false) final Boolean groupBy);

    /**
     * 권역의 일간/프로젝트별 배송 통계 통계 조회
     *
     * @param pageable
     * @param groupName
     * @param likeGroupName
     * @param projectId
     * @param likeProjectName
     * @param userIdList
     * @param date
     * @param fromDate
     * @param toDate
     * @return
     */
    @GetMapping(StatUrl.STAT_GROUP_NAME_DAILY_PROJECT_DELIVERY_API)
    Page<StatGroupNameDailyProjectDeliveryResponseDTO> getStatGroupNameDailyProjectDeliveries(
            final Pageable pageable,
            @RequestParam(name = StatConst.GROUP_NAME_COLUMN_NAME, required = false) final String groupName,
            @RequestParam(name = StatConst.GROUP_NAME_LIKE_PARAM_NAME, required = false) final String likeGroupName,
            @RequestParam(name = StatConst.PROJECT_ID_PARAM_NAME, required = false) @Positive final Long projectId,
            @RequestParam(name = StatConst.PROJECT_NAME_LIKE_PARAM_NAME, required = false) final String likeProjectName,
            @RequestParam(name = StatConst.USER_ID_LIST_PARAM_NAME, required = false) final Set<Long> userIdList,
            @RequestParam(name = StatConst.DATE_PARAM_NAME, required = false) final LocalDate date,
            @RequestParam(name = StatConst.FROM_DATE_PARAM_NAME, required = false) final LocalDate fromDate,
            @RequestParam(name = StatConst.TO_DATE_PARAM_NAME, required = false) final LocalDate toDate);

}
