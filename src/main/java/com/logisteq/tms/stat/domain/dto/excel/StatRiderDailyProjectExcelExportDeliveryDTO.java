package com.logisteq.tms.stat.domain.dto.excel;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.common.component.excel.annotation.PxlColumn;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;

@Getter
@Setter
@AllArgsConstructor
@RequiredArgsConstructor
@Builder
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)

public class StatRiderDailyProjectExcelExportDeliveryDTO {
    @NotNull
    @PxlColumn(name = "수행날짜", pattern = "yyyy-MM-dd")
    private LocalDate date;

    @NotBlank
    @PxlColumn(name = "프로젝트명")
    private String projectName;

  //  @NotNull
    @PxlColumn(name = "근무시간", pattern = "HH:mm:ss")
    private LocalTime workingTime;

  //  @NotNull
    @PxlColumn(name = "근무종료", pattern = "HH:mm:ss")
    private LocalTime endOfWorkTime;

 //   @NotNull
    @PxlColumn(name = "총 근무시간", pattern = "HH:mm:ss")
    private LocalTime totalWorkingTime;

 //   @NotNull
    @PxlColumn(name = "총 휴식시간", pattern = "HH:mm:ss")
    private LocalTime totalRestTime;

    @NotNull
    @PxlColumn(name = "총 주행거리(km)")
    private Double workingMeters;

    @NotNull
    @PxlColumn(name = "총 수행건수")
    private Long numOfAllDeliveries;

    @NotNull
    @PxlColumn(name = "완료")
    private Long numOfCompletedDeliveries;

    @NotNull
    @PxlColumn(name = "실패")
    private Long numOfFailedDeliveries;

    @NotNull
    @PxlColumn(name = "지연")
    private Long numOfDelayDeliveries;

    //@NotNull
    @PxlColumn(name = "관리자에게 전달사항")
    private String message;

    public static StatRiderDailyProjectExcelExportDeliveryDTO parseFromStatDelivery(
            @NotBlank final LocalDate dt,
            final String prjName,
            final LocalTime wTime,
            final LocalTime eTime,
            final LocalTime tWTime,
            final LocalTime tRTime,
            final Double wMeters,
            final Long numOfADeliveries,
            final Long numOfCDeliveries,
            final Long numOfFDeliveries,
            final Long numOfDDeliveries,
            final String msg) {
        return StatRiderDailyProjectExcelExportDeliveryDTO.builder()
                .date(dt)
                .projectName(prjName)
                .workingTime(wTime)
                .endOfWorkTime(eTime)
                .totalWorkingTime(tWTime)
                .totalRestTime(tRTime)
                .workingMeters(Math.round(wMeters / 1000. * 10000) / 10000.)
                .numOfAllDeliveries(numOfADeliveries)
                .numOfCompletedDeliveries(numOfCDeliveries)
                .numOfFailedDeliveries(numOfFDeliveries)
                .numOfDelayDeliveries(numOfDDeliveries)
                .message(msg)
                .build();
    }
}
