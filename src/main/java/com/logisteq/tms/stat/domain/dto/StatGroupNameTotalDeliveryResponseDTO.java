package com.logisteq.tms.stat.domain.dto;

import lombok.*;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import java.time.LocalDateTime;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ToString
public class StatGroupNameTotalDeliveryResponseDTO {

    /**
     * 권역명
     */
    @NonNull
    @NotNull
    private String groupName;

    /**
     * 업데이트날짜
     */
    @NonNull
    @NotNull
    private LocalDateTime updatedAt;

    /**
     * 할당된 프로젝트 수
     */
    @NonNull
    @NotNull
    @Positive
    private Long numOfProjects;

    /**
     * 근무 거리
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Double workingMeters;

    /**
     * 근무 시간
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long workingSeconds;

    /**
     * 할당된 모든 배송 건수
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfAllDeliveries;

    /**
     * 배차 대기 중인 배송 건수
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfWaitingDeliveries;

    /**
     * 배송 전인 배송 건수
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfReadyDeliveries;

    /**
     * 배송 중인 배송 건수
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfGoingDeliveries;

    /**
     * 서비스 중인 배송 건수
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfServicingDeliveries;

    /**
     * 거절된 배송 건수
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfRejectedDeliveries;

    /**
     * 실패한 배송 건수
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfFailedDeliveries;

    /**
     * 완료된 배송 건수
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfCompletedDeliveries;

    /**
     * 주문 금액 총 값
     */
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long totalOrderAmountDeliveries;
}
