package com.logisteq.tms.user.service;

import com.logisteq.tms.user.domain.Hub;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.repository.HubRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Slf4j
@Validated
@Service
public class HubService {

    private final HubRepository hubRepository;

    @Autowired
    public HubService(final HubRepository hubRepository) {

        this.hubRepository = hubRepository;
    }

    /**
     * Hub 목록 저장
     *
     * @param hubList
     * @return
     */
    @Transactional
    public List<Hub> saveHubList(final List<Hub> hubList) {

        return hubRepository.saveAll(hubList);
    }

    /**
     * Hub 저장
     *
     * @param hub
     * @return
     */
    @Transactional
    public Hub saveHub(final Hub hub) {

        return hubRepository.save(hub);
    }

    /**
     * 조직의 Hub 목록 조회
     *
     * @param organization
     * @return
     */
    public List<Hub> getHubListByOrganization(final Organization organization) {

        return hubRepository.findHubListByOrganizationAndActive(organization, true);
    }

    /**
     * 조직과 Hub명으로 Hub 목록 조회
     *
     * @param organization
     * @param hubName
     * @return
     */
    public List<Hub> getHubListByOrganizationAndHubName(final Organization organization,
                                                        final String hubName) {

        return hubRepository.findHubListByOrganizationAndHubNameAndActive(organization, hubName, true);
    }

}
