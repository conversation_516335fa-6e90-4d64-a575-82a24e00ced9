package com.logisteq.tms.user.repository.specs;

import com.logisteq.tms.user.domain.UserAccessHistory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.Optional;

public class UserAccessHistorySpecs {
    private static final String KEY_DATE_TIME = "dateTime";
    private static final String KEY_API_URL = "apiUrl";
    private static final String KEY_RIDER_ID = "riderId";
    private static final String KEY_USER_ID = "userId";
    private static final String KEY_LOG = "/web/log";
    private static final String KEY_BO_LOG = "/bo/";
    private static final String KEY_PROJECT = "/web/project/";
    private static final String KEY_USER_ADD = "/api/bo/userAdd";
    private static final String KEY_USER_DELETE = "/api/bo/userView";
    private static final String KEY_POST = "POST";
    private static final String KEY_LOCATION_API = "/web/tracks/rider/last-locations";

    public static Specification<UserAccessHistory> userAccessHistoryApiUrlLike(String apiUrl) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.like(root.get(KEY_API_URL), "%" + apiUrl + "%");
        };
    }

    public static Specification<UserAccessHistory> userAccessHistoryAdmin() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get(KEY_USER_ID), 1);
        };
    }

//    public static Specification<UserAccessHistory> userAccessHistoryApiLog() {
//        return (root, query, criteriaBuilder) -> {
//            return criteriaBuilder.and(
//                    criteriaBuilder.like(root.get(KEY_API_URL), "%" + KEY_LOG + "%"),
//                    criteriaBuilder.like(root.get(KEY_API_URL), "%" + KEY_BO_LOG + "%")
//            );
//        };
//    }

    public static Specification<UserAccessHistory> userAccessHistoryApiLog() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.like(root.get(KEY_API_URL), "%" + KEY_LOG + "%");
        };
    }

    public static Specification<UserAccessHistory> userAccessHistoryApiBoLog() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.like(root.get(KEY_API_URL), "%" + KEY_BO_LOG + "%");
        };
    }

    public static Specification<UserAccessHistory> userAccessHistoryApiProject() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.like(root.get(KEY_API_URL), "%" + KEY_PROJECT + "%");
        };
    }

    public static Specification<UserAccessHistory> userAccessHistoryApiUserAdd() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.like(root.get(KEY_API_URL), "%" + KEY_USER_ADD + "%");
        };
    }

    public static Specification<UserAccessHistory> userAccessHistoryApiUserDeleted() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.and(
                    criteriaBuilder.like(root.get(KEY_API_URL), KEY_USER_DELETE),
                    criteriaBuilder.like(root.get(KEY_API_URL), KEY_POST));
        };
    }

    public static Specification<UserAccessHistory> userAccessHistoryBetweenCreate(LocalDateTime fromDate, LocalDateTime toDate) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.and(
                    criteriaBuilder.greaterThanOrEqualTo(root.get(KEY_DATE_TIME), fromDate),
                    criteriaBuilder.lessThanOrEqualTo(root.get(KEY_DATE_TIME), toDate));
        };
    }

    public static Specification<UserAccessHistory> userInNotNullId() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.and(
                    criteriaBuilder.isNotNull(root.get(KEY_RIDER_ID)),
                    criteriaBuilder.isNotNull(root.get(KEY_USER_ID))
            );
        };
    }

    public static Specification<UserAccessHistory> userAccessHistoryApiGetLocation() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.like(root.get(KEY_API_URL), "%" + KEY_LOCATION_API + "%");
        };
    }

    public static Specification<UserAccessHistory> adminComposeSpecs(String apiUrl, LocalDateTime fromDate, LocalDateTime toDate) {

        Specification<UserAccessHistory> specs = (root, query, criteriaBuilder) -> {
            return null;
        };

        if (StringUtils.isNotBlank(apiUrl)) {
            specs = specs.and(userAccessHistoryApiUrlLike(apiUrl));
        }

        if (fromDate != null && toDate != null) {
            if (!fromDate.equals(toDate)) {
                specs = Optional.ofNullable(specs)
                        .map(sp -> sp.and(userAccessHistoryBetweenCreate(fromDate, toDate)))
                        .orElseGet(() -> userAccessHistoryBetweenCreate(fromDate, toDate));
            }
        }

        specs = specs.and(userAccessHistoryAdmin());
        specs = specs.and(userAccessHistoryApiLog().or(userAccessHistoryApiBoLog()));

        return specs;
    }

    public static Specification<UserAccessHistory> otherComposeSpecs(LocalDateTime fromDate, LocalDateTime toDate) {

        Specification<UserAccessHistory> specs = (root, query, criteriaBuilder) -> {
            return null;
        };

        if (fromDate != null && toDate != null) {
            if (!fromDate.equals(toDate)) {
                specs = Optional.ofNullable(specs)
                        .map(sp -> sp.and(userAccessHistoryBetweenCreate(fromDate, toDate)))
                        .orElseGet(() -> userAccessHistoryBetweenCreate(fromDate, toDate));
            }
        }

        specs = specs.and(userInNotNullId());
        specs = specs.and(userAccessHistoryApiGetLocation());

        return specs;
    }
}
