package com.logisteq.tms.user.repository;

import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.RiderDepartment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

public interface RiderDepartmentRepository extends JpaRepository<RiderDepartment, Long> {

    List<RiderDepartment> findByRiderAndDepartment(final Rider rider, final Department department);

    List<RiderDepartment> findByDepartmentIn(final Collection<Department> departmentList);

    List<RiderDepartment> findByRider(final Rider rider);

    List<RiderDepartment> findByRiderAndDepartment_Organization(final Rider rider, final Organization organization);

    RiderDepartment findTop1ByRiderOrderByUpdatedAtDesc(final Rider rider);

    long countByRiderAndDepartment(final Rider rider, final Department department);

    boolean existsByRiderAndDepartment(final Rider rider, final Department department);

    @Transactional
    void deleteByRiderAndDepartment(final Rider rider, final Department department);

    @Transactional
    void deleteByRiderAndDepartmentIn(final Rider rider, final List<Department> departmentList);

    @Query(nativeQuery = false,
            value = "SELECT DISTINCT r " +
                    "FROM RiderDepartment rd " +
                    "INNER JOIN Rider r ON rd.rider = r " +
                    "WHERE rd.department in :departmentList")
    Page<Rider> findRiderPageByDepartmentIn(@Param("departmentList") final Collection<Department> departmentList, final Pageable pageable);

}
