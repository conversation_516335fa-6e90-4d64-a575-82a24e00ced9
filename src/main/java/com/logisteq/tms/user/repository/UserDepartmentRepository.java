package com.logisteq.tms.user.repository;

import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.domain.UserDepartment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

public interface UserDepartmentRepository extends JpaRepository<UserDepartment, Long> {

    List<UserDepartment> findByUserAndDepartment(final User user, final Department department);

    List<UserDepartment> findByDepartmentIn(final Collection<Department> departmentList);

    List<UserDepartment> findByUser(final User user);

    List<UserDepartment> findByUserAndDepartment_Organization(final User user, final Organization organization);

    long countByUserAndDepartment(final User user, final Department department);

    boolean existsByUserAndDepartment(final User user, final Department department);

    @Transactional
    void deleteByUserAndDepartment(final User user, final Department department);

    @Query(nativeQuery = false,
            value = "SELECT DISTINCT u " +
                    "FROM UserDepartment ud " +
                    "INNER JOIN User u ON ud.user = u " +
                    "WHERE ud.department in :departmentList AND u.deleteAt IS NULL")
    Page<User> findUserPageByDepartmentIn(@Param("departmentList") final Collection<Department> departmentList, final Pageable pageable);

}
