package com.logisteq.tms.user.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum RoleType {

    ROLE_ADMIN("ROLE_ADMIN"),
    ROLE_ORG_ADMIN("ROLE_ORG_ADMIN"),
    ROLE_COMMON("ROLE_COMMON"),
    ROLE_ANONYMOUS("ROLE_ANONYMOUS"),
    ;

    private final String id;

    private static final Map<String, RoleType> idToEnum = Stream
            .of(values())
            .collect(Collectors.toMap(e -> e.id, e -> e));

    public static RoleType fromId(final String roleId) {
        final RoleType value = idToEnum.get(roleId);
        return value;
    }

    public boolean equals(final String roleId) {
        return Objects.nonNull(roleId) && this.id.equals(roleId);
    }

    /**
     * 이거 대신 getId() 사용하세요.
     *
     * @return
     */
    @Deprecated
    @Override
    public String toString() {
        return id;
    }

}
