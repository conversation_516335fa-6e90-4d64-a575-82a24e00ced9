package com.logisteq.tms.user.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpMethod;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 사용자 접근 기록
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@IdClass(UserAccessHistoryPrimaryId.class)
@Entity
@Table(indexes = {
        @Index(columnList = "riderId"),
        @Index(columnList = "userId, riderId"),
        @Index(columnList = "dateTime"),
})
public class UserAccessHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seq")
    private Long aiSeq;     // auto increment

    // Random Session ID
    @Column(nullable = true)
    private String sessionId;

    // 접속 IP
    @Column(nullable = false)
    private String ip;

    // 관제자 ID
    @Column
    private Long userId;

    // AuthClient ID
    @Column
    private String clientId;

    // 기사 ID
    @Column
    private Long riderId;

    // 접속 일시
    @Id
    @Column(columnDefinition = "DATETIME(3)", nullable = false)
    private LocalDateTime dateTime;

    // 접속 API Method
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, columnDefinition = "enum('GET','HEAD','POST','PUT','PATCH','DELETE','OPTIONS','TRACE')")
    private HttpMethod apiMethod;

    // 접속 API URL
    @Column(nullable = false, columnDefinition = "TEXT")
    private String apiUrl;

}
