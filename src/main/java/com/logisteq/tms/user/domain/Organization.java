package com.logisteq.tms.user.domain;

import com.logisteq.tms.user.dto.OrganizationDTO;
import com.logisteq.tms.user.dto.OrganizationEditDTO;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Getter @Setter @ToString(of = {"id", "organizationName"})
@AllArgsConstructor @NoArgsConstructor
@Builder
public class Organization {
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	private String organizationName;//조직이름, 사명
	
	private String organizationOwner;//조직대표이름
	
	private String telephone;//조직 전번

	// FK
	private Long addressId;
	
	@NotEmpty(message = "codeName는 필수값 입니다.")
	@Column(nullable = false, length = 10, unique = true)
	private String codeName; //각 고객사별 관리를 위한 코드 예:joins,  lightning

	private Integer riderCountLimit; // 조직에서 최대로 유지하는 rider수
	private Integer userCountLimit; // 조직에서 최대로 유지하는 user(관제자)수

	@CreationTimestamp
	@Column(nullable = false, updatable = false)
	private LocalDateTime createAt;
	
	@UpdateTimestamp
	@Column(nullable = true, updatable = true)
	private LocalDateTime updateAt;

	@Column(nullable = true, updatable = true)
	private LocalDate usageStartDate;

	@Column(nullable = true, updatable = true)
	private LocalDate usageEndDate;

	@Column(nullable = false, updatable = true, columnDefinition = "integer default 0")
	private Integer preservationDate;

	@Column(nullable = true, updatable = true) //admin user ID를 가지고 있어어함.
	private Long userId;

	@Column(nullable = true, updatable = true) // 2-factor 로그인 인증 필요한 조직인지 여부
	private Boolean isAuthcodeLoginRequired;

	@Column(nullable = true, updatable = true) //FMSService organization 확인을 위한 flag
	private Boolean isFmsService;

	@Column(nullable = true, updatable = true) //자동 생성 프로젝트 organization 확인을 위한 flag
	private Boolean isAutoScheduleProject;

	@Column(nullable = true, updatable = true) // 몇 레벨의 부서로 프로젝트를 자동 생성할지 설정한다. (0이나 null이면 자동 생성하지 않음.)
	private Integer autoScheduleDepartmentProjectLevel;

	@Column
	private Boolean isReturnDestEnabled; // 회차 기능 활성화 여부를 위한 flag

	@Column
	private Boolean isUsePredefinedGrouping; //엑셀에 정의된 배차 정보대로 그룹이 설정 되는지에 대한 사용 여부 ( 푸디스트 이용 )

	@Column
	private Boolean isCallbackToGlovisDev; // 카카오 알림톡 Callback을 Glovis 개발계 또는 운영계중 어디로 호출할지에 대한 flag (true: dev, false: prod)

	public static Organization parseFromDTO ( OrganizationDTO dto) {
		Organization org = Organization.builder()
									.organizationName(dto.getOrganizationName())
									.organizationOwner(dto.getOrganizationOwner())
									.telephone(dto.getTelephone())
									.addressId(dto.getAddressId())
									.codeName(dto.getCodeName())
									.riderCountLimit(dto.getRiderCountLimit())
									.userCountLimit(dto.getUserCountLimit())
									.usageStartDate(dto.getUsageStartDate())
									.usageEndDate(dto.getUsageEndDate())
									.userId(dto.getUserId())
									.preservationDate(dto.getPreservationDate())
									.isFmsService(dto.getIsFmsService())
									.isReturnDestEnabled(dto.getIsReturnDestEnabled())
									.isUsePredefinedGrouping(dto.getIsUsePredefinedGrouping())
									.build();
		return org;
	}

	public static Organization parseNewFromDTO (OrganizationEditDTO dto) {
		Organization org = Organization.builder()
						.codeName(dto.getCodeName())
						.organizationName(dto.getOrganizationName())
						.organizationOwner(dto.getOrganizationOwner())
						.telephone(dto.getTelephone())
						.userCountLimit(dto.getUserCountLimit())
						.riderCountLimit(dto.getRiderCountLimit())
						.preservationDate(dto.getPreservationDate())
						.usageStartDate(dto.getUsageStartDate())
						.usageEndDate(dto.getUsageEndDate())
						.isAuthcodeLoginRequired(dto.getIsAuthcodeLoginRequired())
						.build();
		return org;
	}

}
