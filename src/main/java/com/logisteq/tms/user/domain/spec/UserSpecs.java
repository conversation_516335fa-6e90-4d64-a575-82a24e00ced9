package com.logisteq.tms.user.domain.spec;

import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.domain.UserDepartment;
import com.logisteq.tms.user.service.DepartmentService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.util.List;
import java.util.Objects;

public final class UserSpecs {
    private static final String KEY_NAME = "name";
    private static final String KEY_PHONE_NUMBER = "phoneNumber";
    private static final String KEY_ORGANIZATION_ID = "organizationId";
    private static final String KEY_DELETE_AT = "deleteAt";
    private static final String KEY_USER_DEPARTMENT_LIST = "userDepartmentList";
    private static final String KEY_USER = "user";
    private static final String KEY_DEPARTMENT = "department";

    public static Specification<User> eqUser(User user) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root, user);
        };
    }

    public static Specification<User> nameLike(String name) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.like(root.get(KEY_NAME), "%" + name + "%");
        };
    }

    public static Specification<User> phoneNumberLike(String phoneNumber) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.like(root.get(KEY_PHONE_NUMBER), "%" + phoneNumber + "%");
        };
    }

    public static Specification<User> eqOrganizationId(final Long organizationId) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get(KEY_ORGANIZATION_ID), organizationId);
        };
    }

    public static Specification<User> isDeleted(final boolean deleted) {
        return (root, query, criteriaBuilder) -> {
            if (deleted) {
                return root.get(KEY_DELETE_AT).isNotNull();
            } else {
                return root.get(KEY_DELETE_AT).isNull();
            }
        };
    }

    public static Specification<User> inDepartmentList(final List<Department> departmentList) {

// Join 방식은 사용자가 소속된 부서가 여러개일때 중복된 사용자정보가 반환되므로, Subquery 방식을 사용했다.

//        return (root, query, criteriaBuilder) -> {
//
//            final Join<User, UserDepartment> departmentJoin = root.join(KEY_USER_DEPARTMENT_LIST, JoinType.INNER);
//            departmentJoin.on(departmentJoin.get(KEY_DEPARTMENT).in(departmentList));
//
//            return criteriaBuilder.conjunction();
//        };

        return (root, query, criteriaBuilder) -> {

            final Subquery<UserDepartment> subquery = query.subquery(UserDepartment.class);
            final Root<UserDepartment> subqueryRoot = subquery.from(UserDepartment.class);
            final Predicate riderPredicate = criteriaBuilder.equal(subqueryRoot.get(KEY_USER), root);
            final Predicate departmentPredicate = subqueryRoot.get(KEY_DEPARTMENT).in(departmentList);

            subquery.select(subqueryRoot).where(riderPredicate, departmentPredicate);

            return criteriaBuilder.exists(subquery);
        };
    }

    public static Specification<User> hasAuthority(final RoleType userRole,
                                                   final User user,
                                                   final Organization organization,
                                                   final boolean orgHasDepartment,
                                                   final List<Department> authorizedDepartmentList,
                                                   final List<Department> filterDepartmentList) {

        if (RoleType.ROLE_ADMIN.equals(userRole)) {
            return (root, query, criteriaBuilder) -> criteriaBuilder.conjunction();
        } else if (RoleType.ROLE_ORG_ADMIN.equals(userRole) || RoleType.ROLE_COMMON.equals(userRole)) {
            if (Objects.isNull(organization)) {
                // 소속조직이 없는 관제자인 경우
                // -> 권한이 없으므로 자기자신만 조회되도록 함
                return eqUser(user);
            } else if (orgHasDepartment) {
                // 소속조직이 있고 부서가 있는 조직이라면 부서에 따른 권한 조건 추가
                if (CollectionUtils.isNotEmpty(authorizedDepartmentList)) {
                    // 소속조직과 소속부서가 모두 있는 관제자인 경우
                    // -> 관제자의 부서들과 사용자의 부서들 간의 공통부서가 있는 조건 필요
                    if (CollectionUtils.isNotEmpty(filterDepartmentList)) {
                        final Specification<User> spec1 = eqOrganizationId(organization.getId());
                        final Specification<User> spec2 = inDepartmentList(DepartmentService.intersection(authorizedDepartmentList, filterDepartmentList));
                        return spec1.and(spec2);
                    } else {
                        final Specification<User> spec1 = eqOrganizationId(organization.getId());
                        final Specification<User> spec2 = inDepartmentList(authorizedDepartmentList);
                        return spec1.and(spec2);
                    }
                } else {
                    // 소속조직은 있으나 소속부서가 없는 관제자인 경우
                    // -> 권한이 없으므로 자기자신만 조회되도록 함
                    return eqUser(user);
                }
            } else {
                // 소속조직이 있고 부서가 없는 조직인 경우
                return eqOrganizationId(organization.getId());
            }
        } else {
            return eqUser(user);
        }
    }

}
