package com.logisteq.tms.user.controller;

import com.logisteq.common.exception.CustomException;
import com.logisteq.common.exception.InvalidParameterException;
import com.logisteq.common.util.PrivacyUtil;
import com.logisteq.tms.auth.service.AuthKeyService;
import com.logisteq.tms.common.security.WebUserDetails;
import com.logisteq.tms.delivery.dto.PageRequestDTO;
import com.logisteq.tms.file.dto.FileDTO;
import com.logisteq.tms.privacy.dto.PrivacyRecordDto;
import com.logisteq.tms.privacy.service.PrivacyRecordService;
import com.logisteq.tms.privacy.suppl.PrivacyRecordType;
import com.logisteq.tms.privacy.suppl.PrivacyUsageType;
import com.logisteq.tms.project.dto.ProjectDTO;
import com.logisteq.tms.project.service.ProjectBasicService;
import com.logisteq.tms.project.service.ProjectService;
import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.constant.UserType;
import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.Role;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.dto.*;
import com.logisteq.tms.user.service.*;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Tag(name = "User")
@Validated
@RestController
@RequestMapping("/api/users")
public class UserController {

    private final PasswordEncoder passwordEncoder;
    private final UserService userService;
    private final RoleService roleService;
    private final ProjectBasicService projectBasicService;
    private final ProjectService projectService;
    private final AuthKeyService authKeyService;
    private final OrganizationService organizationService;
    private final DepartmentService departmentService;
    private final UserDepartmentService userDepartmentService;
    private final PrivacyRecordService privacyRecordService;

    @Autowired
    public UserController(final PasswordEncoder passwordEncoder,
                          final UserService userService,
                          final RoleService roleService,
                          final ProjectBasicService projectBasicService,
                          final ProjectService projectService,
                          final AuthKeyService authKeyService,
                          final OrganizationService organizationService,
                          final DepartmentService departmentService,
                          final UserDepartmentService userDepartmentService,
                          final PrivacyRecordService privacyRecordService) {

        this.passwordEncoder = passwordEncoder;
        this.userService = userService;
        this.roleService = roleService;
        this.projectBasicService = projectBasicService;
        this.projectService = projectService;
        this.authKeyService = authKeyService;
        this.organizationService = organizationService;
        this.departmentService = departmentService;
        this.userDepartmentService = userDepartmentService;
        this.privacyRecordService = privacyRecordService;
    }

    /**
     * COMMON 사용자 등록
     *
     * @param userDTO
     * @return
     */
    @Hidden
    @Transactional
    @PostMapping
    public Long registerCommonUser(@AuthenticationPrincipal WebUserDetails userInfo,
                                   @RequestBody final UserDTO userDTO) {

        if (userInfo.isReadonly()) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "쓰기 권한이 없습니다.", false);
        }

        if (userDTO.getOrganizationId() == null) {
            throw new InvalidParameterException("organizationId 데이터가 없습니다.");
        }

        final Long authUserId = userInfo.getId();
        final Long authUserOrgId = userInfo.getOrganizationId();
        final User authUser = userService.getUser(authUserId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, authUserId + " 사용자 정보가 없습니다.", false));
        final RoleType authUserRoleType = userService.getUserRoleByUser(authUser);
        final Long userOrgId = userDTO.getOrganizationId();
        final String userEmail = userDTO.getEmail();
        final List<Long> departmentIdList = userDTO.getDepartmentIdList();

        if (!RoleType.ROLE_ADMIN.equals(authUserRoleType) && !userOrgId.equals(authUserOrgId)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "소속 조직에만 사용자 추가할 수 있습니다.", false);
        }

        final List<User> users = userService.getUserByOrganizationRoleCommon(userOrgId);
        final OrganizationDTO orgDto = organizationService.getOrganizationDTOByOrganizationId(userOrgId);
        if (orgDto.getUserCountLimit() != null && users.size() >= orgDto.getUserCountLimit()) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "사용자 수가 Max 입니다..", false);
        }

        final Role role = roleService.getRoleByRoleType(RoleType.ROLE_COMMON)
                .orElseThrow(() -> new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, "ROLE_COMMON 을 찾을 수 없습니다."));

        final Optional<User> foundUser = userService.getUserByEmail(userEmail);
        if (foundUser.isPresent()) {
            throw new CustomException(HttpStatus.NOT_FOUND, userEmail + " 메일이 존재하고 있습니다.", false);
        }

        final User newUser = userService.saveUser(userEmail,
                userDTO.getName() != null ? userDTO.getName() : userEmail,
                userDTO.getPassword(),
                userDTO.getPhoneNumber(),
                userOrgId,
                Collections.singletonList(role),
                userDTO.getReadonly());

        final Organization organization = organizationService.getOrganizationById(userOrgId);
        final boolean orgHasDepartment = departmentService.existDepartmentOfOrganization(organization);
        if (orgHasDepartment) {
            if (CollectionUtils.isEmpty(departmentIdList)) {
                throw new InvalidParameterException("사용자의 부서가 지정되어 있지 않습니다.", false);
            }

            final List<Department> departmentList = departmentService.getDepartmentListByDepartmentIdList(organization, departmentIdList);

            if (RoleType.ROLE_ADMIN.equals(authUserRoleType)) {
                userDepartmentService.assignUserToDepartmentListOfOrganization(newUser, organization, departmentList, true);
            } else {
                final List<Department> authorizedDepartmentList = userDepartmentService.getAuthorizedDepartmentListOfUser(authUser, organization, true);
                if (CollectionUtils.isEmpty(authorizedDepartmentList)) {
                    throw new InvalidParameterException("관제자의 부서가 지정되어 있지 않습니다.", false);
                }

                final List<Department> assignableDepartmentList = DepartmentService.intersection(authorizedDepartmentList, departmentList);
                if (CollectionUtils.isEmpty(assignableDepartmentList)) {
                    final List<String> departmentNameList = departmentList.stream()
                            .map(Department::getDepartmentName)
                            .collect(Collectors.toList());
                    throw new InvalidParameterException("사용자 " + userEmail + "의 부서를 " +
                            StringUtils.join(departmentNameList, ",") + "(으)로 지정할 권한이 없습니다.", false);
                }

                userDepartmentService.assignUserToDepartmentListOfOrganization(newUser, organization, assignableDepartmentList, true);
            }
        }


        try {
            privacyRecordService.saveRecord(
                    PrivacyRecordDto.builder()
                            .recordType(PrivacyRecordType.USER)
                            .type(PrivacyUsageType.REGISTER)
                            .orgId(Optional.ofNullable(organization).map(Organization::getId).orElse(null))
                            .userId(newUser.getUserId())
                            .runUserId(userInfo.getId())
                            .build());
        } catch (Exception e) {
            log.error("Failed to save privacy record: {}", e.getMessage(), e);
        }

        return newUser.getUserId();
    }

    /**
     * Org admin userDto 등록
     *
     * @param userDTO
     * @return
     */
    @Hidden
    @Transactional
    @PostMapping("/api/users/org_admin")
    public Long registerOrgAdminUser(@AuthenticationPrincipal WebUserDetails userInfo,
                                     @RequestBody final UserDTO userDTO) {

        if (userInfo.isReadonly()) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "쓰기 권한이 없습니다.", false);
        }

        if (userDTO.getOrganizationId() == null) {
            throw new InvalidParameterException("organizationId 데이터가 없습니다.");
        }

        final Role role = roleService.getRoleByRoleType(RoleType.ROLE_ORG_ADMIN)
                .orElseThrow(() -> new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, "ROLE_ORG_ADMIN 을 찾을 수 없습니다."));

        //저장되어 있는 user 가 이미 저장되어 있는 user가 있는지 확인함.
        final Optional<User> foundUser = userService.getUserByEmail(userDTO.getEmail());
        if (foundUser.isPresent()) {
            throw new CustomException(HttpStatus.NOT_FOUND, userDTO.getEmail() + " 메일이 존재하고 있습니다.", false);
        }

        final User newUser = userService.saveUser(userDTO.getEmail(),
                userDTO.getName() != null ? userDTO.getName() : userDTO.getEmail(),
                userDTO.getPassword(),
                userDTO.getPhoneNumber(),
                userDTO.getOrganizationId(),
                Collections.singletonList(role),
                userDTO.getReadonly());

        final Organization organization = organizationService.getOrganizationById(newUser.getOrganizationId());
        final List<Department> departmentList = departmentService.getRootDepartmentListOfOrganization(organization);
        if (Objects.nonNull(organization) && CollectionUtils.isNotEmpty(departmentList)) {
            userDepartmentService.assignUserToDepartmentListOfOrganization(newUser, organization, departmentList, true);
        }

        try {
            privacyRecordService.saveRecord(
                    PrivacyRecordDto.builder()
                            .recordType(PrivacyRecordType.USER)
                            .userId(newUser.getUserId())
                            .type(PrivacyUsageType.REGISTER)
                            .runUserId(userInfo.getId())
                            .func("registerOrgAdminUser")
                            .build());
        } catch (Exception e) {
            log.error("Failed to save privacy record: {}", e.getMessage(), e);
        }

        return newUser.getUserId();
    }

    /**
     * 사용자 목록 조회 API (사용자 관리에 사용)
     *
     * @param userInfo
     * @param departmentIdList
     * @param keyword
     * @param pageable
     * @return
     */
    @Hidden
    @GetMapping(params = {"type=manage"})
    public Page<UserManagementDTO> getUsersForManagement(
            @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
            @RequestParam(required = false) final List<Long> departmentIdList,
            @RequestParam(required = false) final String keyword,
            @PageableDefault(size = 10, sort = "createAt", direction = Sort.Direction.DESC) final Pageable pageable) {

        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        final Long authUserId = userInfo.getId();
        final User user = userService.getUser(authUserId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, authUserId + " 사용자 정보가 없습니다.", false));
        final RoleType userRole = userService.getUserRoleByUser(user);
        final List<Department> visibleFamilyDepartmentList = userDepartmentService.getVisibleFamilyDepartmentListOfUser(user, null);
        final Page<User> users = userService.getUsersForManagement(authUserId, departmentIdList, keyword, userRole, pageable);

        final Page<UserManagementDTO> userManagementDTOs = users
                .map(u -> {
                    final Organization organization = organizationService.getOrganizationById(u.getOrganizationId());
                    final List<Department> authorizedDepartmentList = userDepartmentService.getAuthorizedDepartmentListOfUser(u, organization, false);
                    final List<Department> visibleAuthorizedDepartmentList = DepartmentService.intersection(visibleFamilyDepartmentList, authorizedDepartmentList);

                    return UserManagementDTO.of(u, organization, visibleAuthorizedDepartmentList);
                });

        return userManagementDTOs;
    }

    @Hidden
    @DeleteMapping("{userIdList}")
    public void deleteUser(@AuthenticationPrincipal @Parameter(hidden = true) final WebUserDetails authUser,
                           @PathVariable @NotEmpty final Set<Long> userIdList) {

        if (authUser.isReadonly()) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "쓰기 권한이 없습니다.", false);
        }

        userIdList.forEach(userService::deleteUser);

        try {
            Optional.ofNullable(organizationService.getOrganizationByCodeName(authUser.getOrganizationCode()))
                    .ifPresent(organization -> {
                        privacyRecordService.saveRecordArray(userIdList.stream()
                                .map(userId -> {
                                    User user = userService.getUserById(userId);
                                    return PrivacyRecordDto.builder()
                                            .recordType(PrivacyRecordType.USER)
                                            .orgId(Optional.ofNullable(organization).map(Organization::getId).orElse(null))
                                            .type(PrivacyUsageType.WITHDRAW)
                                            .userId(Optional.ofNullable(user).map(User::getUserId).orElse(null))
                                            .runUserId(authUser.getId())
                                            .func("deleteUser")
                                            .build();
                                })
                                .collect(Collectors.toList())
                        );
                    });
        } catch (Exception e) {
            log.error("Error occurred while saving privacy records: ", e);
        }
    }

    @Hidden
    @GetMapping
    public UserDTO getUserInfo(@RequestParam final String email) {

        final User user = userService.getUserByEmail(email)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, email + "에 해당하는 사용자 정보가 없습니다.", false));

        final Organization organization = organizationService.getOrganizationById(user.getOrganizationId());
        final List<Department> departmentList = userDepartmentService.getAuthorizedDepartmentListOfUser(user, organization, false);

        return new UserDTO(user, departmentList);
    }

    /**
     * 로그인 실패 횟수 조회
     *
     * @param email
     * @return
     */
    @Hidden
    @GetMapping("/login-failed-count")
    public Integer getLoginFailedCount(final String email) {

        return userService.getLoginFailedCount(email);
    }

    /**
     * 계정 잠금 일시 조회
     *
     * @param email
     * @return
     */
    @Hidden
    @GetMapping("/account-locked-at")
    public LocalDateTime getAccountLockedAt(final String email) {

        return userService.getAccountLockedAt(email);
    }

    /**
     * 가입 사용자 중복 체크
     */
    @Hidden
    @PostMapping("/check-duplicate")
    public boolean checkUserDuplicate(@RequestParam final String email) {

        final Optional<User> optUser = userService.getUserByEmail(email);

        return optUser.isPresent();
    }

    /**
     * 사용자 이름 변경
     *
     * @param authUser
     * @param userChangeNameDTO
     */
    @Hidden
    @PutMapping("/change-name")
    public void changeName(@AuthenticationPrincipal final WebUserDetails authUser,
                           @Valid @RequestBody final UserChangeNameDTO userChangeNameDTO) {

        final String email = userChangeNameDTO.getEmail();
        final User user = userService.getUserByEmail(email).orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, email + " 사용자는 존재하지 않습니다.", false));

        userService.changeName(authUser.getId(), userChangeNameDTO.getName());

        try {
            Optional.ofNullable(organizationService.getOrganizationByCodeName(authUser.getOrganizationCode()))
                .ifPresent(organization -> {
                    privacyRecordService.saveRecord(
                            PrivacyRecordDto.builder()
                                    .recordType(PrivacyRecordType.USER)
                                    .orgId(organization.getId())
                                    .userId(user.getUserId())
                                    .type(PrivacyUsageType.CHANGE)
                                    .func("changeName")
                                    .extra(userChangeNameDTO.getName())
                                    .kind("NAME")
                                    .runUserId(authUser.getId())
                                    .build());
                });
        } catch (Exception e) {
            log.error("Error occurred while saving privacy records: ", e);
        }
    }

    @Hidden
    @PutMapping("/change-pw")
    public void changePassword(@AuthenticationPrincipal final WebUserDetails authUser,
                               @Valid @RequestBody final UserChangePasswordDTO userChangePasswordDTO) {

        final String email = userChangePasswordDTO.getEmail();
        final User user = userService.getUserByEmail(email).orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, email + " 사용자는 존재하지 않습니다.", false));

        userService.changePassword(user.getUserId(), userChangePasswordDTO.getOrgPassword(), userChangePasswordDTO.getNewPassword());


        if (authUser != null) {
            try {
                Optional.ofNullable(organizationService.getOrganizationByCodeName(authUser.getOrganizationCode()))
                        .ifPresent(organization -> {
                            privacyRecordService.saveRecord(
                                    PrivacyRecordDto.builder()
                                            .recordType(PrivacyRecordType.USER)
                                            .type(PrivacyUsageType.CHANGE)
                                            .orgId(organization.getId())
                                            .userId(user.getUserId())
                                            .runUserId(user.getUserId())
                                            .kind("PASSWORD")
                                            .extra("[NEW-PASSWORD]")
                                            .func("changePassword")
                                            .runUserId(authUser.getId())
                                            .build());
                        });
            } catch (Exception e) {
                log.error("Error occurred while saving privacy records: ", e);
            }
        }
    }

    /**
     * 사용자 전화번호 변경
     *
     * @param authUser
     * @param userChangePhoneNumberDTO
     */
    @Hidden
    @PutMapping("/change-phone-number")
    public void changePhoneNumber(@AuthenticationPrincipal final WebUserDetails authUser,
                                  @Valid @RequestBody final UserChangePhoneNumberDTO userChangePhoneNumberDTO) {

        final String email = userChangePhoneNumberDTO.getEmail();
        final User user = userService.getUserByEmail(email).orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, email + " 사용자는 존재하지 않습니다.", false));

        final String newPhoneNumber = userChangePhoneNumberDTO.getPhoneNumber();
        if (newPhoneNumber.equals(user.getPhoneNumber())) {
            throw new CustomException(HttpStatus.BAD_REQUEST, userChangePhoneNumberDTO.getPhoneNumber() + "는 현재 사용중인 전화번호 입니다.", false);
        }

        authKeyService.validateAuthKey(user.getUserId(), UserType.WEB, userChangePhoneNumberDTO.getAuthKey(), userChangePhoneNumberDTO.getPhoneNumber());

        userService.changePhoneNumber(user.getUserId(), userChangePhoneNumberDTO.getPhoneNumber());

        if (authUser != null ) {
            try {
                Optional.ofNullable(organizationService.getOrganizationByCodeName(authUser.getOrganizationCode()))
                        .ifPresent(organization -> {
                            privacyRecordService.saveRecord(
                                    PrivacyRecordDto.builder()
                                            .recordType(PrivacyRecordType.USER)
                                            .type(PrivacyUsageType.CHANGE)
                                            .orgId(organization.getId())
                                            .userId(user.getUserId())
                                            .runUserId(user.getUserId())
                                            .func("changePhoneNumber")
                                            .kind("PHONE")
                                            .extra(PrivacyUtil.maskingTelephone(userChangePhoneNumberDTO.getPhoneNumber()))
                                            .runUserId(authUser.getId())
                                            .build());
                        });
            } catch (Exception e) {
                log.error("Error occurred while saving privacy records: ", e);
            }

        }
    }

    @Hidden
    @PostMapping("/send-auth-key")
    public void sendUserAuthKey(@AuthenticationPrincipal @NotNull final WebUserDetails authUser,
                                @RequestBody final UserPhoneNumberDTO userPhoneNumberDTO) {

        final User user = userService.getUser(authUser.getId())
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "사용자 정보가 없습니다.", false));
        final String phoneNumber = Optional.ofNullable(userPhoneNumberDTO)
                .map(UserPhoneNumberDTO::getPhoneNumber)
                .orElseGet(user::getPhoneNumber);

        if (StringUtils.isBlank(phoneNumber)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "전화번호가 필요합니다.", false);
        }

        authKeyService.sendAndSaveAuthKey(authUser.getId(), UserType.WEB, phoneNumber, null);
    }

    /**
     * 사용자 프로파일 사진 업로드
     *
     * @param authUser
     * @param profileImgFile
     * @return
     */
    @Hidden
    @PostMapping("/profile-img")
    public FileDTO uploadProfileImage(@AuthenticationPrincipal final WebUserDetails authUser,
                                      final MultipartFile profileImgFile) {

        if (authUser == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        final FileDTO userProfileImgFile = userService.uploadProfileImage(authUser.getId(), profileImgFile);
        return userProfileImgFile;
    }

    /**
     * 사용자 프로파일 이미지 제거
     *
     * @param authUser
     */
    @Hidden
    @DeleteMapping("profile-img")
    public void deleteProfileImage(@AuthenticationPrincipal final WebUserDetails authUser) {

        userService.deleteProfileImage(authUser.getId());
    }

    /**
     * 계정 잠금 해지
     *
     * @param authUser
     * @param userDTO
     */
    @Hidden
    @PutMapping("/account/unlock")
    public void unlockAccount(@AuthenticationPrincipal final WebUserDetails authUser,
                              @RequestBody final UserDTO userDTO) {

        // TODO : 관리자 ROLE 인 경우에만 가능해야 한다.
        // OAuth Access Token 발급시 관리자 clientId/secretKey 를 사용하여 발급받은 사용자만 가능

        userService.unlockAccount(userDTO.getUserId());
    }

    /**
     * 프로젝트 목록 조회
     *
     * @param authUser
     * @param pageable
     * @param userId
     * @return
     */
    @Hidden
    @GetMapping("{userId}/projects")
    public List<ProjectDTO> getProjects(@AuthenticationPrincipal final WebUserDetails authUser,
                                        final PageRequestDTO pageable,
                                        @PathVariable final Long userId) {

        if (authUser == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        if (authUser.getId().longValue() != userId.longValue()) {
            throw new CustomException(HttpStatus.FORBIDDEN, "No permission");
        }

        if (pageable.getSize() < 1) {
            pageable.setSize(100); // FIXME size 값이 없으면 기본값 100으로 설정
        }

        return projectBasicService.getProjectsByUserId(authUser.getId(), pageable.of(null))
                .stream()
                .map(ProjectDTO::parseFromProject)
                .collect(Collectors.toList());
    }

    @Hidden
    @PostMapping("{userId}/projects")
    public ProjectDTO createNewProject(@PathVariable final Long userId,
                                       @AuthenticationPrincipal final WebUserDetails authUser,
                                       @RequestBody final ProjectDTO dto) {

        if (authUser == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        if (authUser.getId().longValue() != userId.longValue()) {
            throw new CustomException(HttpStatus.FORBIDDEN, "No permission");
        }

        return ProjectDTO.parseFromProject(projectService.saveProject(userId, dto));
    }

}