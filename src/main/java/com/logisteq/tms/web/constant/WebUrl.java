package com.logisteq.tms.web.constant;

import com.logisteq.tms.delivery.constant.DeliveryConstant;
import com.logisteq.tms.external.hmg.constant.HmgConstant;

/**
 * API URL  클래스
 */
public final class WebUrl {
    private WebUrl() {
        throw new AssertionError();
    }

    public static class Params {
        public static final String PROJECT_ID = "projectId";            // 프로젝트 아이디
        public static final String START = "start";                // 시작 페이지: 0 ~
        public static final String SIZE = "size";                // 페이지 당 목록 크기 : 1 ~ 50
        public static final String FROM = "from";                // 시작 시각
        public static final String TO = "to";                    // 마지막 시각
        public static final String CODE = "code";
        public static final String X_POS = "x";
        public static final String Y_POS = "y";
        public static final String KEYWORD = "keyword";
    }

    public static final String WEB = "/web";

    public static final String WEB_VERSION = WEB + "/version";
    public static final String WEB_ROUTE_VERSION = WEB + "/route/version";
    public static final String WEB_SEARCH_VERSION = WEB + "/search/version";
    public static final String SESSION_ATTR = WEB + "/session-attr";

    public static final String ANONYMOUS = WEB + "/anonymous";
    public static final String LOGIN = WEB + "/login";
    public static final String LOGOUT = WEB + "/logout";

    public static final String USER_AUTH = WEB + "/auth-user";
    public static final String USERS = WEB + "/users";
    public static final String USER_LIST = WEB + "/users/{userIdList}";
    public static final String USERS_EMAIL = WEB + "/users/email";
    public static final String USER_LOGIN_FAILED_COUNT = WEB + "/user/login-failed-count";
    public static final String USER_ACCOUNT_LOCKED_AT = WEB + "/user/account-locked-at";
    public static final String USER_SEND_AUTH_KEY = WEB + "/user/send-auth-key";
    public static final String USER_CHECK_CHECK_USER_DUPLICATE = WEB + "/user/check-duplicate";
    public static final String USER_CHANGE_NAME = WEB + "/user/change-name";
    public static final String USER_CHANGE_PASSWORD = WEB + "/user/change-pw";
    public static final String USER_CHANGE_PHONE_NUMBER = WEB + "/user/change-phone-number";
    public static final String USER_PROFILE_IMG = WEB + "/user/profile-img";
    public static final String USER_PROJECT = USERS + "/{userId}/projects";
    public static final String USER_VALIDATE = USERS + "/validate";

    public static final String DEPARTMENTS = WEB + "/departments";
    public static final String DEPARTMENTS_INFO_BY_ID = DEPARTMENTS + "/id/{departmentIdList}";
    public static final String DEPARTMENTS_INFO_BY_CODE = DEPARTMENTS + "/code/{departmentCodeList}";
    public static final String DEPARTMENTS_PARENT = DEPARTMENTS + "/parent";
    public static final String DEPARTMENTS_CHILD = DEPARTMENTS + "/child";
    public static final String DEPARTMENTS_FAMILY = DEPARTMENTS + "/family";
    public static final String DEPARTMENTS_VISIBLE = DEPARTMENTS + "/visible";
    public static final String DEPARTMENTS_VISIBLE_PARENT = DEPARTMENTS + "/visible/parent";
    public static final String DEPARTMENTS_VISIBLE_CHILD = DEPARTMENTS + "/visible/child";
    public static final String DEPARTMENTS_VISIBLE_FAMILY = DEPARTMENTS + "/visible/family";
    public static final String DEPARTMENTS_AUTHORIZED = DEPARTMENTS + "/authorized";

    // 익명사용자 전용
    public static final String ANONYMOUS_USER_REG = USERS + "/anonymous";

    public static final String PROJECT = WEB + "/projects";
    public static final String PROJECT_EXCEL = PROJECT + "/excel";

    public static final String PROJECT_EXCEL_BY_PERIOD = PROJECT + "/excel-by-period";
    public static final String PROJECT_HAND_WRITTEN_EXCEL = PROJECT + "/hand-written-excel";
    public static final String PROJECT_EXCEL_DELIVERY_PRODUCT_PER_RIDER = PROJECT_EXCEL + "/delivery-product-per-rider";
    public static final String PROJECT_EXCEL_DELIVERY_RIDER_PER_PRODUCT = PROJECT_EXCEL + "/delivery-rider-per-product";

    public static final String PROJECT_COPY_PROJECT = PROJECT + "/copy";
    public static final String PROJECT_TRANSFER_PROJECT = PROJECT + "/transfer";
    public static final String UPDATE_PROJECT_NAME = PROJECT + "/projectname" + "/{projectId}" ;

    public static final String PROJECT_CSV = PROJECT + "/csv";
    public static final String PROJECT_INFO = PROJECT + "/{projectId}";
    public static final String PROJECT_EXCEL_FROM_DESTINATION_LIST_PANEL = PROJECT_INFO + "/excel/destination-list-panel";
    public static final String PROJECT_EXCEL_FROM_LIST_PANEL = PROJECT_INFO + "/excel/project-excel";
    public static final String PROJECT_EXCEL_JOINS_INVOICE = PROJECT_INFO + "/excel/joins-invoice";

    public static final String PROJECT_RIDER_INFO = PROJECT_INFO + "/riders";
    public static final String PROJECT_ROUTE = PROJECT_INFO + "/route";
    public static final String PROJECT_CLUSTER = PROJECT_INFO + "/cluster";
    public static final String PROJECT_CLUSTER_RIDER = PROJECT_INFO + "/cluster-rider";
    public static final String PROJECT_PUSH_RIDER = PROJECT_INFO + "/push-rider";
    public static final String PROJECT_TERMINATE = PROJECT_INFO + "/terminate";
    public static final String PROJECT_EMPTY_PROJECT = PROJECT + "/empty";
    public static final String PROJECT_ADD = PROJECT + "/add";
    public static final String PROJECT_FILES = PROJECT_INFO + "/files";
    public static final String PROJECT_FILES_ZIP_DOWNLOAD = PROJECT_FILES + "/zip-download";
    public static final String PROJECT_DELIVERY_CHANGED =  PROJECT + "/delivery-changed";

    public static final String PROJECT_AUTO_ADD_RIDER=  PROJECT + "/auto-add-rider";
    public static final String PROJECT_SIMULATION_RIDERS_AUTO_CHANGED_DISPATCH_RIDERS=  PROJECT + "/simulation-riders-auto-changed-dispatch-riders";

    public static final String PROJECT_SEND_DISPATCH_TO_OMS =  PROJECT + "/send-dispatch-to-oms";

    public static final String PROJECT_OF_DEPARTMENT = PROJECT + "/department-projects";
    public static final String PROJECT_OF_DEPARTMENT_RECENT = PROJECT_OF_DEPARTMENT + "/recent";

    public static final String PROJECT_RIDER_ROUTE = PROJECT + "rider-route";

    public static final String DELIVERIES = WEB + "/deliveries";
    public static final String DELIVERY = WEB + "/delivery";
    public static final String DELIVERY_STATUS = DELIVERY + "/{deliveryId}/status";
    public static final String DELIVERY_FORCE_STATUS = DELIVERY + "/{deliveryId}/status-force";
    public static final String DELIVERIES_FORCE_STATUS = DELIVERIES + "/status-force";
    public static final String D_DELIVERY_URL = WEB + "/delete-delivery";
    public static final String GET_DELIVERY_ROUTE_PATH = WEB + "/deliveries/route";
    public static final String GET_DELIVERY_ROUTE_ETA = WEB + "/deliveries/routeeta";
    public static final String DELIVERY_CHANGE_CLUSTER = DELIVERY + DeliveryConstant.UrlConst.CLUSTER;
    public static final String DELIVERIES_CHANGE_CLUSTER = DELIVERIES + DeliveryConstant.UrlConst.CLUSTER;
    public static final String DELIVERIES_REPORT = DELIVERIES + DeliveryConstant.UrlConst.REPORT;
    public static final String DELIVERY_INFO = DELIVERIES +"/{deliveryId}";
    public static final String DELIVERY_VOC_SAVE = DELIVERY_INFO + DeliveryConstant.UrlConst.VOC;
    public static final String DELIVERY_VOC_GET_MESSAGE_LIST = DELIVERIES + DeliveryConstant.UrlConst.VOC_LIST;
    public static final String DELIVERY_SET_INVOICE_PRINT_COUNT = DELIVERIES + DeliveryConstant.UrlConst.INVOICE_PRINT_COUNT;
    public static final String DELIVERY_GET_INVOICE_INFO = DELIVERIES + DeliveryConstant.UrlConst.INVOICE_INFO;

    public static final String DELIVERIES_FILE_ZIP_DOWNLOAD = DELIVERIES + "/{deliveryId}" + DeliveryConstant.UrlConst.FILE + DeliveryConstant.UrlConst.ZIP_DOWNLOAD;
    public static final String DELIVERIES_EXCEL = DELIVERIES + "/excel";
    public static final String DELIVERIES_PAST_EXCEL = DELIVERIES + "/past-excel";
    public static final String DELIVERIES_PAST = DELIVERIES + "/past";
    public static final String DELIVERIES_FILE = DELIVERIES + "/{deliveryId}/file";
    public static final String DELIVERY_FILE = DELIVERY_INFO + DeliveryConstant.UrlConst.FILE;

    public static final String DELIVERIES_TMS_REPORT = DELIVERIES + DeliveryConstant.UrlConst.TMS_REPORT;

    public static final String DELIVERY_CS_SAVE_HISTORY = DELIVERY + "/cs-save-history";

    public static final String PROJECTS_DELIVERIES = DELIVERIES + "/projects-deliveries";
    public static final String WAREHOUSE = DELIVERIES + "/warehouse";

    public static final String DELIVERIES_CANCEL = DELIVERIES + "/cancel";

    public static final String RIDER = WEB + "/rider";
    public static final String WEB_GET_RIDER = RIDER + "/{riderId}";

    public static final String WEB_GET_MOBILE_RIDER = RIDER + "/find-mobile";
    public static final String RIDER_PUSH_MESSAGE = RIDER + "/push-message";
    public static final String RIDER_REQUEST_CHANGE_DELIVERY_STATUS = RIDER + "/request-change-delivery-status";

    public static final String RIDERS = WEB + "/riders";
    public static final String D_RIDERS = WEB + "/delete-riders";
    public static final String RIDER_PROFILE_IMAGE_URL = RIDERS + "/{riderId}/profile-image-url";
    public static final String RIDER_PROJECT_SETTING = RIDERS + "/{riderId}/project-setting";
    public static final String RIDERS_DETAIL_DATA_PROJECT = RIDERS + "/{riderId}/get-data-project";
    public static final String RIDERS_PROJECT_SETTING_NOTE = RIDERS + "/put-project-setting-rider-note";
    public static final String RIDERS_VEHICLES = WEB + "/riders/vehicles";
    public static final String RIDER_CURRENT_LOCATIONS = RIDERS + "/current-locations";
    public static final String GET_RIDER = RIDERS + "/{riderId}";
    public static final String RIDER_ADD_PROJECT = RIDERS + "/{riderId}/add";
    public static final String RIDERS_ADD_TO_PROJECT = RIDERS + "/addproject";
    public static final String RIDER_GET_ORG_MAX_COUNT = RIDERS + "/orgridermaxcount";
    public static final String RIDERS_EXCEL_ADD = RIDERS + "/excel";
    public static final String RIDERS_SWITCH_CLUSTERING = RIDERS + "/switch-cluster";
    public static final String RIDER_VEICLE_NUMBER_GET_RIDER = RIDERS + "/vehicleNumberList"; //기사 번호로 기사 이름 찾기 위해 사용함
    public static final String RIDER_GET_GROUP_NAME = RIDERS + "/groupname";

    public static final String TRACKS = WEB + "/tracks";
    public static final String TRACKS_CURRENT_LOCATIONS = TRACKS + "/current-locations";
    public static final String TRACKS_RIDER_LOCATIONS = TRACKS + "/rider/{riderId}/locations";
    public static final String TRACKS_RIDER_TIMESTAMP = TRACKS + "/timestamp";
    public static final String TRACKS_RIDER_LAST_LOCATIONS = TRACKS + "/rider/last-locations";
    public static final String TRACKS_MOCEAN = TRACKS + "/mocean";
    public static final String TRACKS_MOCEAN_VIN_INFO = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.VIN_INFO;
    public static final String TRACKS_MOCEAN_VIN_INFOS = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.VIN_INFOS;
    public static final String TRACKS_MOCEAN_LAST_INFO = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.LAST_INFO;
    public static final String TRACKS_MOCEAN_TRAJECTORY = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.TRAJECTORY;
    public static final String TRACKS_MOCEAN_LAST_VEHICLE_STATUS = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.LAST_VEHICLE_STATUS;
    public static final String TRACKS_MOCEAN_LAST_VEHICLE_STATUSES = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.LAST_VEHICLE_STATUSES;
    public static final String TRACKS_MOCEAN_VEHICLE_STATUS = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.VEHICLE_STATUS;
    public static final String TRACKS_MOCEAN_VEHICLE_STATUSES = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.VEHICLE_STATUSES;
    public static final String TRACKS_MOCEAN_TEMPERATURES = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.TEMPERATURES;
    public static final String TRACKS_MOCEAN_TEMPERATURES_BOUNDS = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.TEMPERATURES_BOUNDS;
    public static final String TRACKS_MOCEAN_DAILY_STAT = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.DAILY_STAT;
    public static final String TRACKS_MOCEAN_WEEKLY_STAT = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.WEEKLY_STAT;
    public static final String TRACKS_MOCEAN_MONTHLY_STAT = TRACKS_MOCEAN + HmgConstant.MoceanApiUrl.MONTHLY_STAT;

    public static final String GROUPS = WEB + "/groups";
    public static final String VEHICLEMODELS = WEB + "/vehicleModel";
    public static final String VEHICLE_ITEM = WEB + "/vehicle/{vehicleId}";

    public static final String NOTIFICATIONS = WEB + "/notifications";
    public static final String NOTIFICATIONS_COUNT = NOTIFICATIONS + "/count";
    public static final String NOTIFICATIONS_DELETE_DESTINATION = NOTIFICATIONS + "/delete-destination";

    public static final String SEARCH = WEB + "/search";
    public static final String SMART_SEARCH = WEB + "/smart-search";
    public static final String EXTEND_SEARCH = WEB + "/extend-search";
    public static final String SEARCH_COORD = WEB + "/search-coord";
    public static final String EXTEND_SEARCH_COORD = WEB + "/extend-search-coord";
    public static final String SEARCH_ENTRANCE = WEB + "/search-entrance";
    public static final String ROUTE = WEB + "/route";
    public static final String ROUTE_TIME_RESERVATION = WEB + "/route-reservation";
    public static final String MPP = WEB + "/mpp";
    public static final String CLUSTER = WEB + "/cluster";
    public static final String CLUSTER_TIME_RESERVATION = WEB + "/cluster-reservation";
    public static final String RANGE_PROJECTION = WEB + "/range-projection";

    public static final String PRODUCT = WEB + "/product";
    public static final String PRODUCT_PROJECT = PRODUCT + "/project";
    public static final String PRODUCT_OVERALL_STATUS = PRODUCT + "/overall-status";

    public static final String DEMO = WEB + "/demo";
    public static final String DEMO_STATUS = DEMO + "/status";
    public static final String DEMO_PROJECT = DEMO + "/project";
    public static final String DEMO_DELIVERY_URL = DEMO + "/delivery";
    public static final String DEMO_DELIVERIES = DEMO + "/deliveries";
    public static final String DEMO_GET_DELIVERY_ROUTE_PATH = DEMO + "/deliveries/route";
    public static final String EV_STATION = WEB + "/evstation";
    public static final String FIND_NEAR_EV_STATION = EV_STATION + "/find-near";

    public static final String PUSH = WEB + "/push";

    public static final String CHECK_RIDER_MOBILE_EXISTS = WEB + "/check-rider-mobile-exists";
    public static final String CHECK_LICENSE_PLATE_EXISTS = WEB + "/check-license-plate-exists";

    public static final String CHECK_RIDER_WORKING_TIME = WEB + "/check-rider-working-time";

    public static final String RIDER_PROJECT_INFO = WEB + "/rider-project-info";

    public static final String WEB_ENUM = WEB + "/enum";
    public static final String ENUM_DELIVERY_TYPES = WEB_ENUM + "/delivery-types";
    public static final String ENUM_VISIT_TYPES = WEB_ENUM + "/visit-types";
    public static final String ENUM_DELIVERY_STATUS = WEB_ENUM + "/delivery-status";
    public static final String ENUM_DELIVERY_COMPLETED_TYPES = WEB_ENUM + "/delivery-completed-types";
    public static final String ENUM_DELIVERY_FAILURE_TYPES = WEB_ENUM + "/delivery-failure-types";
    public static final String ENUM_CARGO_SUB_TYPES = WEB_ENUM + "/cargo-sub-types";
    public static final String ENUM_CARGO_TYPES = WEB_ENUM + "/cargo-types";
    public static final String ENUM_FUEL_TYPES = WEB_ENUM + "/fuel-types";
    public static final String ENUM_MILES_TYPES = WEB_ENUM + "/miles-types";
    public static final String ENUM_VEHICLE_SIZE_TYPES = WEB_ENUM + "/vehicle-size-types";
    public static final String ENUM_VEHICLE_TYPES = WEB_ENUM + "/vehicle-types";
    public static final String ENUM_WHEEL_DRIVE_TYPES = WEB_ENUM + "/wheel-drive-types";
    public static final String ENUM_PRODUCT_SIZE_TYPES = WEB_ENUM + "/product-size-types";
    public static final String ENUM_WORK_AUTHORITIES = WEB_ENUM + "/work-authorities";

    // 글로비스 전용 Web URL
    @Deprecated
    public static final String GLOVIS_DELIVERY_CODES_VER1 = WEB + "/glovis/delivery-codes-ver1";
    public static final String GLOVIS_DELIVERY_CODES_VER2 = WEB + "/glovis/delivery-codes-ver2";
    public static final String GLOVIS_DELIVERY_CODES = WEB + "/glovis/delivery-codes";

    //글로비스 배송상태정보 조회용
    public static final String GLOVIS_DELIVERY_STATUS = WEB + "/glovis/delivery-status";

    //notice
    public static final String NOTICE = WEB + "/notice";

    //backoffice
    public static final String BO = "/bo";
    public static final String WEB_BO_COMPANY_INFO_LIST = WEB + BO + "/company-info-list";

    public static final String WEB_BO_COMPANY_LIST = WEB + BO + "/company-list";
    public static final String WEB_BO_COMPANY_USER_LIST = WEB + BO + "/company-user-list";

    public static final String WEB_BO_DEPARTMENTS_FAMILY = WEB + BO + "/departments/family";

    public static final String WEB_BO_RIDER_LOCATION_LIST = WEB + BO + "/rider-location-list";
    public static final String WEB_BO_DELIVERY_INFO_ACCESS_LIST = WEB + BO + "/delivery-info-access-list";
    public static final String WEB_BO_RIDER_INFO_ACCESS_LIST = WEB + BO + "/rider-info-access-list";
    public static final String WEB_BO_RIDER_ACCESS_LIST = WEB + BO + "/rider-access-list";
    public static final String WEB_BO_USER_ACCESS_LIST = WEB + BO + "/user-access-list";
    public static final String WEB_BO_ORGANIZATION_DELETED = WEB + BO + "/organization-deleted";
    public static final String WEB_BO_ORGANIZATION_ADD = WEB + BO + "/organization-add";
    public static final String WEB_BO_ORGANIZATION_MODIFY = WEB + BO + "/organization-modify";
    public static final String WEB_BO_ORGANIZATION_USE_CASE_MODIFY = WEB + BO + "/organization-use-case-modify";
//    public static final String WEB_BO_ORGANIZATION_ADMIN_USER_MODIFY = WEB + BO + "/organization-admin-user-modify"; // 삭제 예정

    public static final String WEB_BO_USER_EDIT = WEB + BO + "/user-edit";
    public static final String WEB_BO_USER_PASSWORD = WEB + BO + "/user-password";
    public static final String WEB_BO_USER_DELETED = WEB + BO + "/user-deleted";
    public static final String WEB_BO_USER_ADD = WEB + BO + "/user-add";

    // Report(Dashboard...)
    public static final String REPORT = WEB + "/report";
    public static final String REPORT_DELIVERY = REPORT + "/delivery";
    public static final String REPORT_DELIVERY_DAILY = REPORT_DELIVERY + "/daily";
    public static final String REPORT_DELIVERY_DAILY_COUNT = REPORT_DELIVERY_DAILY + "/count";
    public static final String REPORT_DELIVERY_WEEKLY = REPORT_DELIVERY + "/weekly";
    public static final String REPORT_DELIVERY_WEEKLY_COUNT = REPORT_DELIVERY_WEEKLY + "/count";
    public static final String REPORT_DELIVERY_MONTHLY = REPORT_DELIVERY + "/monthly";
    public static final String REPORT_DELIVERY_MONTHLY_COUNT = REPORT_DELIVERY_MONTHLY + "/count";
    public static final String REPORT_DELIVERY_YEARLY = REPORT_DELIVERY + "/yearly";
    public static final String REPORT_DELIVERY_YEARLY_COUNT = REPORT_DELIVERY_YEARLY + "/count";

    /*
    가배차 기사 추가
    */
    public static final String SIMULATION_RIDERS_ADD_PROJECT = RIDERS + "/simulation-riders-add-project";
    public static final String SIMULATION_RIDER_TO_DISPATCH_RIDER_PROJECT = RIDERS + "/simulation-rider-to-dispatch-rider-project";
    public static final String PROJECT_EXCEL_EXPORT_SIMULATION_RIDERS = PROJECT_INFO + "/excel/project-simulation-riders-export-excel";
    public static final String PROJECT_SIMULATION_RIDER_TO_DISPATCH_RIDER_CHANGED_EXCEL = PROJECT + "/simulation-rider-to-dispatch-rider-changed-excel";
}