package com.logisteq.tms.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@AllArgsConstructor
@Builder
@Schema(description = "웹 기사 경로 데이터 전송 객체")
public class WebRiderRouteDTO {

    @Schema(description = "프로젝트 ID", example = "23182")
    @NotNull(message = "프로젝트 ID가 없습니다.")
    final Long projectId;

    @Schema(description = "기사 ID", example = "6631")
    @NotNull(message = "기사 ID가 없습니다.")
    final Long riderId;

    @Schema(description = "순서고정 여부", example = "true")
    @NotNull(message = "순서고정 여부가 없습니다.")
    final Boolean isFixedOrder;

}
