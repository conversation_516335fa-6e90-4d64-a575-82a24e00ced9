package com.logisteq.tms.web.dto.demo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "데모 상태 정보 DTO")
public class DemoStatusDTO {

	@Schema(description = "실행 중 여부", example = "false")
	private Boolean isRunning = false;

	public DemoStatusDTO(Boolean isRunning) {
		this.isRunning = isRunning;
	}

	public static DemoStatusDTO parseFromResult(Boolean result) {
		return new DemoStatusDTO(result);
	}
}
