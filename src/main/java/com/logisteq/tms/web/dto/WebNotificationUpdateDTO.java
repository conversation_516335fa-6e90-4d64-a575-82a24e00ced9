package com.logisteq.tms.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Getter @Setter
@AllArgsConstructor @NoArgsConstructor
@Builder
@Schema(description = "웹 알림 업데이트 데이터 전송 객체")
public class WebNotificationUpdateDTO {
	@Schema(description = "업데이트할 알림 ID 목록", example = "[1, 2, 3]")
	List<Long> notificationIdList;
	
	@Schema(description = "프로젝트 ID", example = "1")
	Long projectId;
}
