package com.logisteq.tms.web.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter @Setter
@AllArgsConstructor @NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "웹 응답 데이터 전송 객체")
public class WebResponseDTO {
	@Schema(description = "성공 여부", example = "true")
	Boolean isSuccess;
	
	@Schema(description = "응답 메시지", example = "요청이 성공적으로 처리되었습니다.")
	String message;
	
	@Schema(description = "응답 데이터 (타입은 요청에 따라 다름)")
	Object data;
}
