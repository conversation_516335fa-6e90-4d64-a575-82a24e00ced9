package com.logisteq.tms.web.service;

import com.logisteq.tms.common.component.TransformerColumnKeyLoader;
import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.domain.suppl.DeliveryType;
import com.logisteq.tms.delivery.dto.DeliveryDTO;
import com.logisteq.tms.delivery.dto.DeliveryInvoiceInfoDTO;
import com.logisteq.tms.delivery.repository.DeliveryRepository;
import com.logisteq.tms.delivery.repository.dao.DeliveryInvoiceInfoDAO;
import com.logisteq.tms.delivery.service.DeliveryBasicService;
import com.logisteq.tms.delivery.service.DeliveryService;
import com.logisteq.tms.product.dto.OrderItemDTO;
import com.logisteq.tms.product.service.ProductService;
import com.logisteq.tms.project.dto.web.WebDestinationDTO;
import com.logisteq.tms.project.service.ProjectService;
import com.logisteq.tms.web.dto.WebDeliveryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Validated
@Service
public class WebDeliveryService {

    private final ProjectService projectService;
    private final DeliveryService deliveryService;
    private final DeliveryBasicService deliveryBasicService;
    private final DeliveryRepository deliveryRepository;
    private final ProductService productService;
    private final TransformerColumnKeyLoader transformerColumnKeyLoader;

    @Autowired
    public WebDeliveryService(final ProjectService projectService,
                              final DeliveryService deliveryService,
                              final DeliveryBasicService deliveryBasicService,
                              final DeliveryRepository deliveryRepository,
                              final ProductService productService,
                              final TransformerColumnKeyLoader transformerColumnKeyLoader) {

        this.projectService = projectService;
        this.deliveryService = deliveryService;
        this.deliveryBasicService = deliveryBasicService;
        this.deliveryRepository = deliveryRepository;
        this.productService = productService;
        this.transformerColumnKeyLoader = transformerColumnKeyLoader;
    }

    @Transactional
    public Delivery saveDelivery(final Long userId,
                                 final WebDeliveryDTO webDeliveryDTO) {

        if (Objects.isNull(webDeliveryDTO.getDeliveryType())) {
            webDeliveryDTO.setDeliveryType(DeliveryType.FINAL_DESTINATION);
        }

        final DeliveryDTO deliveryDto = DeliveryDTO.of(webDeliveryDTO, this.productService);
        final Delivery delivery = deliveryService.getDeliveryByDeliverDTO(userId, webDeliveryDTO.getProjectId(), deliveryDto, null, null);
        final List<OrderItemDTO> orderItemList = deliveryDto.getOrderItemList();

        deliveryService.setDeliveryProductsByOrderItem(orderItemList, delivery);
        deliveryBasicService.saveDelivery(delivery);

        return delivery;
    }

    @Transactional
    public WebDestinationDTO updateDelivery(final Long userId, final WebDeliveryDTO dto) {

        final DeliveryDTO deliveryDTO = DeliveryDTO.of(dto, this.productService);
        deliveryDTO.setProjectId(dto.getProjectId());
        deliveryDTO.setDeliveryId(dto.getDeliveryId());
        deliveryDTO.setUserDefinedOrderNum(dto.getUserDefinedOrderNum());
        if (Objects.nonNull(dto.getDeliveryStatus())) {
            deliveryDTO.setDeliveryStatus(DeliveryStatus.fromKeywordString(dto.getDeliveryStatus()));
        }

        final Delivery delivery = deliveryService.updateDelivery(deliveryDTO, userId, null);

        return projectService.makeWebDestinationDTO(delivery, Boolean.FALSE);
    }

    /**
     * 송장 출력 정보 조회
     *
     * @param projectId
     * @param riderIdList
     * @param deliveryIdList
     * @param includeSimulationRider
     * @return
     */
    public List<List<DeliveryInvoiceInfoDTO>> getDeliveryInvoiceInfoListOfProject(@NotNull @Positive final Long projectId,
                                                                                  final Collection<Long> riderIdList,
                                                                                  final Collection<Long> deliveryIdList,
                                                                                  final boolean includeSimulationRider) {

        final String secretKey = transformerColumnKeyLoader.getSecretKey();

        final List<DeliveryInvoiceInfoDAO> deliveryInvoiceInfoDAOList = deliveryRepository.findDeliveryInvoiceInfoListByProjectId(projectId,
                riderIdList,
                deliveryIdList,
                secretKey);

        // 필요하다면 가배차 기사 필터링
        final List<DeliveryInvoiceInfoDAO> filteredDeliveryInvoiceInfoDAOList;
        if (includeSimulationRider) {
            filteredDeliveryInvoiceInfoDAOList = deliveryInvoiceInfoDAOList;
        } else {
            filteredDeliveryInvoiceInfoDAOList = deliveryInvoiceInfoDAOList.stream()
                    .filter(dao -> !Boolean.TRUE.equals(dao.getIsSimulationRider()))
                    .collect(Collectors.toList());
        }

        // DAO -> DTO
        final List<DeliveryInvoiceInfoDTO> deliveryInvoiceInfoDTOList = DeliveryInvoiceInfoDTO.of(filteredDeliveryInvoiceInfoDAOList);

        // 기사별 그룹핑
        final Map<Pair<Long, String>, List<DeliveryInvoiceInfoDTO>> deliveryInvoiceInfoDTOListMap = deliveryInvoiceInfoDTOList.stream()
                .collect(Collectors.groupingBy(dto -> Pair.of(dto.getRiderId(), dto.getRiderName())));

        // 기사명으로 정렬하여 2차원 리스트 생성
        final List<List<DeliveryInvoiceInfoDTO>> deliveryInvoiceInfoDTOListList = deliveryInvoiceInfoDTOListMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(Comparator.comparing(Pair::getRight)))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        return deliveryInvoiceInfoDTOListList;
    }

}