package com.logisteq.tms.web.controller;

import com.logisteq.common.exception.CustomException;
import com.logisteq.common.util.FormatUtil;
import com.logisteq.tms.common.component.AuthorityManager;
import com.logisteq.tms.common.security.WebUserDetails;
import com.logisteq.tms.delivery.domain.suppl.DeliveryCompletedType;
import com.logisteq.tms.delivery.domain.suppl.DeliveryFailureType;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.dto.DeliveryRidersChangeDTO;
import com.logisteq.tms.external.thehyundai.constant.TheHyundaiConstant;
import com.logisteq.tms.external.thehyundai.constant.ToHomeConstant;
import com.logisteq.tms.privacy.dto.PrivacyRecordDto;
import com.logisteq.tms.privacy.service.PrivacyRecordService;
import com.logisteq.tms.privacy.suppl.PrivacyDataType;
import com.logisteq.tms.privacy.suppl.PrivacyRecordType;
import com.logisteq.tms.privacy.suppl.PrivacyUsageType;
import com.logisteq.tms.project.dto.ProjectsDeliverStatusCountDTO;
import com.logisteq.tms.project.dto.web.WebRiderDTO;
import com.logisteq.tms.project.service.ProjectService;
import com.logisteq.tms.push.service.PushService;
import com.logisteq.tms.rider.constant.ApiUrl;
import com.logisteq.tms.rider.controller.RiderController;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.domain.RiderOrgStatus;
import com.logisteq.tms.rider.domain.suppl.WorkAuthority;
import com.logisteq.tms.rider.dto.RiderDTO;
import com.logisteq.tms.rider.dto.RiderManagementDTO;
import com.logisteq.tms.rider.dto.RiderProjectSettingDTO;
import com.logisteq.tms.rider.dto.SimulationRiderDTO;
import com.logisteq.tms.rider.service.RiderService;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.service.DepartmentService;
import com.logisteq.tms.user.service.OrganizationService;
import com.logisteq.tms.user.service.RiderDepartmentService;
import com.logisteq.tms.vehicle.controller.VehicleModelController;
import com.logisteq.tms.vehicle.domain.Vehicle;
import com.logisteq.tms.vehicle.dto.VehicleDTO;
import com.logisteq.tms.vehicle.dto.VehicleModelDTO;
import com.logisteq.tms.vehicle.dto.VehicleModelListDTO;
import com.logisteq.tms.vehicle.service.VehicleService;
import com.logisteq.tms.web.constant.WebConstant;
import com.logisteq.tms.web.constant.WebUrl;
import com.logisteq.tms.web.dto.*;
import com.logisteq.tms.web.service.WebRiderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springdoc.api.annotations.ParameterObject;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

@Tag(name = "WebRider", description = "기사 관리 API")
@Slf4j
@Validated
@RestController
public class WebRiderController {

    private final RiderController riderController;
    private final RiderService riderService;
    private final VehicleModelController vehicleModelController;
    private final VehicleService vehicleService;
    private final WebRiderService webRiderService;
    private final OrganizationService organizationService;
    private final RiderDepartmentService riderDepartmentService;
    private final PushService pushService;
    private final AuthorityManager authorityManager;
    private final ProjectService projectService;
    private final PrivacyRecordService privacyRecordService;

    @Autowired
    public WebRiderController(final RiderController riderController,
                              final RiderService riderService,
                              final VehicleModelController vehicleModelController,
                              final VehicleService vehicleService,
                              final WebRiderService webRiderService,
                              final OrganizationService organizationService,
                              final RiderDepartmentService riderDepartmentService,
                              final PushService pushService,
                              final AuthorityManager authorityManager,
                              final ProjectService projectService,
                              final PrivacyRecordService privacyRecordService) {

        this.riderController = riderController;
        this.riderService = riderService;
        this.vehicleModelController = vehicleModelController;
        this.vehicleService = vehicleService;
        this.webRiderService = webRiderService;
        this.organizationService = organizationService;
        this.riderDepartmentService = riderDepartmentService;
        this.pushService = pushService;
        this.authorityManager = authorityManager;
        this.projectService = projectService;
        this.privacyRecordService = privacyRecordService;
    }

    /**
     * 기사 목록 조회 API (with Paging)
     *
     * @param userInfo
     * @param departmentIdList
     * @param keyword
     * @param inTransit
     * @param workAuthorities
     * @param pageable
     * @return
     */
    @Operation(
        summary = "기사 목록 조회 (페이징)",
        description = "기사 목록을 페이징하여 조회합니다.",
        parameters = {
            @Parameter(name = "type", description = "조회 타입 (고정값)", required = true, example = "page",
                      in = ParameterIn.QUERY)
        }
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "400", description = "잘못된 요청 데이터")
    })
    @GetMapping(value = WebUrl.RIDERS, params = {"type=page"})
    public Page<RiderDTO> getRidersWithPaging(
            @Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
            @Parameter(description = "부서 ID 목록") @RequestParam(required = false) final List<Long> departmentIdList,
            @Parameter(description = "검색 키워드") @RequestParam(required = false) final String keyword,
            @Parameter(description = "이동 중 여부") @RequestParam(required = false) final Boolean inTransit,
            @Parameter(description = "작업 권한 목록") @RequestParam(required = false) final List<WorkAuthority> workAuthorities,
            @ParameterObject @PageableDefault(size = 10, sort = "id", direction = Sort.Direction.ASC) final Pageable pageable) {

        if (authorityManager.isAnonymousUser(userInfo)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "이미 로그아웃되었습니다. 다시 로그인 해주세요.", false);
        }

        return riderController.getRidersWithPaging(userInfo, departmentIdList, keyword, inTransit, workAuthorities, pageable);
    }

    /**
     * 기사 목록 조회 API (기사 관리에 사용)
     *
     * @param userInfo
     * @param departmentIdList
     * @param keyword
     * @param inTransit
     * @param workAuthorities
     * @param pageable
     * @return
     */
    @Operation(
        summary = "기사 관리 목록 조회", 
        description = "기사 관리를 위한 기사 목록을 조회합니다.",
        parameters = {
            @Parameter(name = "type", description = "조회 타입 (고정값)", required = true, example = "manage", 
                      in = ParameterIn.QUERY)
        }
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자")
    })
    @GetMapping(value = WebUrl.RIDERS, params = {"type=manage"})
    public Page<RiderManagementDTO> getRidersForManagement(
            @Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
            @Parameter(description = "부서 ID 목록") @RequestParam(required = false) final List<Long> departmentIdList,
            @Parameter(description = "검색 키워드") @RequestParam(required = false) final String keyword,
            @Parameter(description = "이동 중 여부") @RequestParam(required = false) final Boolean inTransit,
            @Parameter(description = "작업 권한 목록") @RequestParam(required = false) final List<WorkAuthority> workAuthorities,
            @ParameterObject @PageableDefault(size = 10, sort = "id", direction = Sort.Direction.DESC) final Pageable pageable) {

        if (authorityManager.isAnonymousUser(userInfo)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "이미 로그아웃되었습니다. 다시 로그인 해주세요.", false);
        }

        return riderController.getRidersForManagement(userInfo, departmentIdList, keyword, inTransit, workAuthorities, pageable);
    }

    /**
     * 기사들 삭제 API (기사 관리에 사용)
     *
     * @param userInfo
     * @param riderIdList
     */
    @Operation(
        summary = "기사 삭제",
        description = "기사 관리에서 선택한 기사들을 삭제합니다.",
        parameters = {
            @Parameter(name = "type", description = "조회 타입 (고정값)", required = true, example = "manage",
                      in = ParameterIn.QUERY)
        }
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "삭제 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @DeleteMapping(value = WebUrl.RIDERS, params = {"type=manage"})
    public void deleteRidersForManagement(
            @Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
            @Parameter(description = "삭제할 기사 ID 목록") @RequestParam @NotEmpty final List<Long> riderIdList) {

        if (authorityManager.isAnonymousUser(userInfo)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "이미 로그아웃되었습니다. 다시 로그인 해주세요.", false);
        }

        if (!authorityManager.hasAuthorityByRiderIds(userInfo, riderIdList)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 기사를 삭제할 권한이 없습니다.", false);
        }

        riderController.deleteRidersForManagement(userInfo, riderIdList);

        try {
            for (Long riderId : riderIdList) {
                final Rider rider = riderService.getRider(riderId);
                if( rider !=null ) {
                    final List<RiderOrgStatus> riderOrgStatusList = rider.getOrgStatusList();
                    for (RiderOrgStatus status : riderOrgStatusList) {
                        privacyRecordService.saveRecord(
                                PrivacyRecordDto.builder()
                                        .recordType(PrivacyRecordType.RIDER)
                                        .orgId(status.getOrgId())
                                        .type(PrivacyUsageType.WITHDRAW)
                                        .runUserId(userInfo.getId())
                                        .dataType(PrivacyDataType.NAME_MOBILE)
                                        .func("deleteRidersForManagement")
                                        .riderId(riderId)
                                        .build());

                    }
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while saving privacy records: ", e);
        }

    }

    /**
     * 웹에서 rider와 관련 vehicle정보 +group정보 등을 저장
     *
     * @param userInfo
     * @param isRiderAdd
     * @param webRiderVehicleDTO
     * @return
     */
    @Operation(summary = "기사 및 차량 정보 등록", description = "웹에서 기사와 관련된 차량 정보 및 그룹 정보를 저장합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "등록 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PostMapping(value = WebUrl.RIDERS_VEHICLES, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public WebRiderDTO registerRiderVehicle(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                            @Parameter(description = "기사 추가 여부") @RequestParam(required = false) final Boolean isRiderAdd,
                                            @Parameter(description = "기사 및 차량 정보") @RequestBody WebRiderVehicleDTO webRiderVehicleDTO) {

        final Long projectId = webRiderVehicleDTO.getProjectId();

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }

        final Long authUserId = userInfo.getId();
        final Long authUserOrgId = userInfo.getOrganizationId();
        final Organization authUserOrganization = organizationService.getOrganizationById(authUserOrgId);

        ////////////////////////////////
        // isRiderAdd 에 대한 정보를 변경이 필요한지 확인 작업 기능
        // orgID로 확인해야함.
        // 추가 할때만 아래 코드 확인해야함.(기사 추가시 전화 번호를 이용하여 rider 확인 후 org가 있는지 확인함.)
        if (Boolean.TRUE.equals(isRiderAdd)) {
            final Rider foundRider = riderService.getRider(FormatUtil.stripPhoneNumber(webRiderVehicleDTO.getMobile(), false))
                    .orElse(null);

            if (Objects.nonNull(foundRider)) {
                webRiderVehicleDTO.setRiderId(foundRider.getId());
            }
        }

        final Long riderId = webRiderVehicleDTO.getRiderId();
        final Rider rider = riderService.getRiderById(riderId).orElse(null);
        List<Long> oldDepartmentIdList = null;
        if (Objects.nonNull(rider) && Objects.nonNull(authUserOrganization)) {
            oldDepartmentIdList = riderDepartmentService.getAssignedDepartmentIdListOfRider(rider, authUserOrganization, false);
        }

        final boolean isTheHyundaiUser = organizationService.isCodeNameOrganization(authUserOrganization, TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME);
        final boolean isToHomeUser = organizationService.isCodeNameOrganization(authUserOrganization, ToHomeConstant.TOHOME_ORG_CODE_NAME);
        final Long dispatchNumber = webRiderVehicleDTO.getDispatchNumber();
        final String groupName = webRiderVehicleDTO.getGroupName();
        if ((isTheHyundaiUser || isToHomeUser) && Objects.nonNull(dispatchNumber) && Objects.nonNull(groupName)) {
            webRiderVehicleDTO.setGroupName(dispatchNumber.toString());
        }

        final WebRiderDTO webRiderDTO = webRiderService.saveRidersAndVehicle(authUserId, authUserOrgId, webRiderVehicleDTO, isRiderAdd);

        if (Objects.nonNull(oldDepartmentIdList)) {
            final List<Long> newDepartmentIdList = riderDepartmentService.getAssignedDepartmentIdListOfRider(rider, authUserOrganization, false);

            if (!DepartmentService.isUnorderedEqual(oldDepartmentIdList, newDepartmentIdList)) {
                pushService.sendRiderDepartmentsChangedToRiderApp(riderId, authUserOrgId, newDepartmentIdList);
            }
        }

        return webRiderDTO;
    }

    /**
     * project에 포함된 rider삭제....
     *
     * @param dto
     */
    @PostMapping(WebUrl.D_RIDERS)
    public void deleteRidersFromProject(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                        @RequestBody WebRiderIdListDTO dto) {

        final Long projectId = dto.getProjectId();

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }
        if (!authorityManager.hasAuthorityByRiderIds(userInfo, dto.getRiderIds())) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 기사를 삭제할 권한이 없습니다.", false);
        }

        webRiderService.deleteRidersFromProject(dto.getRiderIds(), dto.getProjectId());
    }

    /**
     * web에서 rider정보 불러옴
     *
     * @param riderId
     */
    @GetMapping(WebUrl.WEB_GET_RIDER)
    public WebRiderDTO getRider(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                @PathVariable(required = true) Long riderId) {

        if (Objects.isNull(userInfo)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        if (!authorityManager.hasAuthorityByRiderId(userInfo, riderId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 기사를 정보를 가져올 권한이 없습니다.", false);
        }

        return riderService.getWebRider(riderId, userInfo.getOrganizationId());
    }

    /**
     * web에서 rider정보 불러옴
     *
     * @param riderId
     */
    @GetMapping(WebUrl.GET_RIDER)
    public WebRiderDTO getProjectRider(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                       @PathVariable(required = true) Long riderId,
                                       @RequestParam(required = false) Long projectId) {

        if (!authorityManager.hasAuthorityByRiderId(userInfo, riderId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 기사를 조회할  권한이 없습니다.", false);
        }

        return riderController.getProjectRider(userInfo, riderId, projectId);
    }

    /**
     * web에서 rider정보 불러옴
     *
     * @param vehicleNumberList
     */
    @GetMapping(WebUrl.RIDER_VEICLE_NUMBER_GET_RIDER)
    public List<String> getFMSRidersName(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                         @RequestParam(required = true) List<String> vehicleNumberList) {

        return riderController.getFMSRidersName(userInfo, vehicleNumberList);
    }


    /**
     * web에서 rider를 프로젝트에 추가함
     *
     * @param riderId
     */
    @GetMapping(WebUrl.RIDER_ADD_PROJECT)
    public void addRiderToProject(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                  @PathVariable(required = true) Long riderId,
                                  @RequestParam(required = true) Long projectId) {

        if (!authorityManager.hasAuthorityByRiderId(userInfo, riderId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 기사를 추가할 권한이 없습니다.", false);
        }

        riderController.addRiderToProject(userInfo, riderId, projectId);
    }

    /**
     * web에서 rider를 프로젝트에 추가함
     *
     * @param riderIdList
     * @param projectId
     */
    @GetMapping(value = WebUrl.RIDERS_ADD_TO_PROJECT)
    public void addRidersForManagement(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                       @RequestParam @NotEmpty final List<Long> riderIdList,
                                       @RequestParam(required = false) final Long projectId) {

        if (!authorityManager.hasAuthorityByRiderIds(userInfo, riderIdList)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 기사를 조회할  추가할 권한이 없습니다.", false);
        }

        riderController.addRidersToProject(userInfo, riderIdList, projectId);
    }

    /**
     * web에서 org max rider 확인이 필요함.
     */
    @GetMapping(WebUrl.RIDER_GET_ORG_MAX_COUNT)
    public Integer getOrgRiderMaxCount(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo) {

        return riderController.getOrgRiderMaxCount(userInfo);
    }

    /**
     * 기사 프로파일 url 주소
     *
     * @param riderId
     * @return
     * @throws IOException
     */
    @GetMapping(WebUrl.RIDER_PROFILE_IMAGE_URL)
    public String getRiderProfileImageUrl(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                          @PathVariable(required = true) final Long riderId) {

        if (!authorityManager.hasAuthorityByRiderId(userInfo, riderId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 기사를 조회할  권한이 없습니다.", false);
        }

        return riderController.getRiderProfileImageUrl(riderId);
    }

    @GetMapping(WebUrl.RIDERS_DETAIL_DATA_PROJECT)
    public List<ProjectsDeliverStatusCountDTO> getRiderDetailDataProject(
            @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
            @PathVariable(required = true) final Long riderId,
            @RequestParam(value = ApiUrl.Params.TO, required = false) @DateTimeFormat(pattern = WebConstant.REQ_DATE_FORMAT) LocalDate toDay) {

        if (!authorityManager.hasAuthorityByRiderId(userInfo, riderId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 기사를 조회할  권한이 없습니다.", false);
        }

        return riderController.getRiderDetailDataProject(riderId, toDay);
    }

    @PostMapping(WebUrl.RIDER_PUSH_MESSAGE)
    public void sendPushGeneralMessageToRider(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                              @RequestBody @Valid final WebRiderPushGeneralMessageDTO webRiderPushGeneralMessageDTO) {

        if (authorityManager.isAnonymousUser(userInfo)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "이미 로그아웃되었습니다. 다시 로그인 해주세요.", false);
        }

        final Long riderId = webRiderPushGeneralMessageDTO.getRiderId();
        final Long projectId = webRiderPushGeneralMessageDTO.getProjectId();
        final String message = webRiderPushGeneralMessageDTO.getMessage();

        if (!authorityManager.hasAuthorityByRiderId(userInfo, riderId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "기사에 대한 권한이 없습니다.", false);
        }

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "프로젝트에 대한 권한이 없습니다.", false);
        }

        pushService.sendGeneralMessageToRiderApp(riderId, projectId, message);
    }

    @PostMapping(WebUrl.RIDER_REQUEST_CHANGE_DELIVERY_STATUS)
    public void requestChangeDeliveryStatusToRider(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                   @RequestBody @Valid final WebRiderPushRequestChangeDeliveryStatusDTO webRiderPushRequestChangeDeliveryStatusDTO) {

        if (authorityManager.isAnonymousUser(userInfo)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "이미 로그아웃되었습니다. 다시 로그인 해주세요.", false);
        }

        final Long riderId = webRiderPushRequestChangeDeliveryStatusDTO.getRiderId();
        final Long projectId = webRiderPushRequestChangeDeliveryStatusDTO.getProjectId();
        final Long deliveryId = webRiderPushRequestChangeDeliveryStatusDTO.getDeliveryId();
        final DeliveryStatus deliveryStatus = webRiderPushRequestChangeDeliveryStatusDTO.getDeliveryStatus();
        final DeliveryCompletedType deliveryCompletedType = webRiderPushRequestChangeDeliveryStatusDTO.getDeliveryCompletedType();
        final DeliveryFailureType deliveryFailureType = webRiderPushRequestChangeDeliveryStatusDTO.getDeliveryFailureType();
        final String message = webRiderPushRequestChangeDeliveryStatusDTO.getMessage();

        if (!authorityManager.hasAuthorityByRiderId(userInfo, riderId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "기사에 대한 권한이 없습니다.", false);
        }

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "프로젝트에 대한 권한이 없습니다.", false);
        }

        if (DeliveryStatus.COMPLETED.equals(deliveryStatus)) {
            if (Objects.isNull(deliveryCompletedType)) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "배송완료인 경우 배송완료타입을 지정하세요.", false);
            }
        } else if (DeliveryStatus.FAILURE.equals(deliveryStatus)) {
            if (Objects.isNull(deliveryFailureType)) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "배송실패인 경우 배송실패타입을 지정하세요.", false);
            }
        } else {
            throw new CustomException(HttpStatus.BAD_REQUEST, "배송완료나 배송실패로 지정하세요.", false);
        }

        pushService.sendRequestChangeDeliveryStatusToRiderApp(riderId,
                projectId,
                deliveryId,
                deliveryStatus,
                deliveryCompletedType,
                deliveryFailureType,
                message);
    }

    @Transactional
    @PutMapping(value = WebUrl.RIDERS_PROJECT_SETTING_NOTE)
    public void putProjectSettingRiderNote(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                           @RequestParam Long riderId,
                                           @RequestParam Long projectId,
                                           @RequestParam String riderNote) {

        if (!authorityManager.hasAuthorityByRiderId(userInfo, riderId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 기사를 조회할  권한이 없습니다.", false);
        }

        riderController.putProjectSettingRiderNote(riderId, projectId, riderNote);
    }

    /**
     * 특정 기사의 프로젝트별 설정 저장
     *
     * @param riderId
     * @param riderProjectSettingDTO
     */
    @PostMapping(WebUrl.RIDER_PROJECT_SETTING)
    @ResponseStatus(HttpStatus.OK)
    public void saveProjectSetting(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                   @RequestHeader(required = true) Long projectId,
                                   @PathVariable(required = true) Long riderId,
                                   @RequestBody(required = true) RiderProjectSettingDTO riderProjectSettingDTO) {

        if (!authorityManager.hasAuthorityByRiderId(userInfo, riderId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 기사 정보를 수정할 권한이 없습니다.", false);
        }

        riderController.saveProjectSetting(projectId, riderId, riderProjectSettingDTO);
    }

    /**
     * 특정 기사의 프로젝트별 설정 읽어오기
     *
     * @param riderId
     * @param projectId
     * @return
     */
    @GetMapping(WebUrl.RIDER_PROJECT_SETTING)
    public RiderProjectSettingDTO loadProjectSetting(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                     @RequestHeader(required = true) @Positive final Long projectId,
                                                     @PathVariable(required = true) @Positive final Long riderId) {

        return riderController.loadProjectSetting(projectId, riderId);
    }

    /**
     * 엑셀 파일로 기사 추가
     *
     * @param userInfo
     * @param excelFile
     * @return
     * @throws Exception
     */
    @PostMapping(value = WebUrl.RIDERS_EXCEL_ADD, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public List<WebRiderDTO> addRidersFromExcel(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                @RequestParam("excelFile") final @NotNull MultipartFile excelFile) throws Exception {

        if (Objects.isNull(userInfo)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        final List<WebRiderDTO> webRiderDTOS = riderService.saveRidersVehicle(userInfo.getId(), userInfo.getOrganizationId(), excelFile);

        return webRiderDTOS;
    }

    @GetMapping(value = WebUrl.VEHICLEMODELS)
    public VehicleModelListDTO getVehicleModelList(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                   @ModelAttribute VehicleModelDTO vehicleModelDTO,
                                                   @PageableDefault(size = 100/*10*/, sort = "vehicleModelId", direction = Sort.Direction.DESC) Pageable pageable) {

        return vehicleModelController.getVehicleModelList(vehicleModelDTO, pageable);
    }

    /**
     * Vehicle 단일 조회
     *
     * @param vehicleId
     * @return
     */
    @GetMapping(value = WebUrl.VEHICLE_ITEM)
    public VehicleDTO getVehicle(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                 @PathVariable(required = true) Long vehicleId) {

        return VehicleDTO.parseFromVehicle(vehicleService.getVehicle(vehicleId));
    }

    /**
     * 기사의 전화번호가 존재하는지 체크 (있으면 data값이 true)
     *
     * @param mobile
     * @return
     */
    @GetMapping(WebUrl.CHECK_RIDER_MOBILE_EXISTS)
    public WebResponseDTO checkRiderMobileExists(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                 @RequestParam String mobile) {

//        final Rider rider = riderService.getRider(FormatUtil.stripPhoneNumber(mobile, true)).orElse(null);
        //이전 org가 rider 있는지 확인 하는것
        final Rider rider = riderService.getRidersOrgId(userInfo.getOrganizationId(), FormatUtil.stripPhoneNumber(mobile, true), false).orElse(null);

        return WebResponseDTO.builder()
                .isSuccess(true)
                .message("성공")
                .data(Objects.nonNull(rider))
                .build();
    }

    /**
     * 차량 번호가 존재하는지 체크 (있으면 data값이 true)
     *
     * @param licensePlate
     * @return
     */
    @GetMapping(WebUrl.CHECK_LICENSE_PLATE_EXISTS)
    public WebResponseDTO checkLicensePlateExists(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                  @RequestParam String licensePlate) {

        final Vehicle vehicle = vehicleService.findByLicensePlate(licensePlate);

        return WebResponseDTO.builder()
                .isSuccess(true)
                .message("성공")
                .data(Objects.nonNull(vehicle))
                .build();
    }

    @GetMapping(WebUrl.CHECK_RIDER_WORKING_TIME)
    public WebResponseDTO checkRiderWorkingTime(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                @RequestParam Long projectId) {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "인증되지 않는 사용자 입니다.", false);
        }

        return WebResponseDTO.builder()
                .isSuccess(true)
                .message("성공")
                .data(projectService.checkRiderWorkingTime(projectId))
                .build();
    }

    @GetMapping(WebUrl.RIDER_GET_GROUP_NAME)
    public String getGroupNameByRiderId(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                        @RequestParam Long riderId) {

        return projectService.getRecentRiderGroupName(riderId);
    }

    @PostMapping(WebUrl.SIMULATION_RIDERS_ADD_PROJECT)
    @ResponseStatus(HttpStatus.OK)
    public void saveSimulationRidersAddProject(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                               @RequestBody @Valid @NotNull final SimulationRiderDTO simulationRiderDTO) {

        webRiderService.simulationRidersAddProject(userInfo, simulationRiderDTO);
    }

    @PostMapping(WebUrl.SIMULATION_RIDER_TO_DISPATCH_RIDER_PROJECT)
    @ResponseStatus(HttpStatus.OK)
    public void simulationRiderChangedProject(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                              @RequestBody @Valid @NotNull final DeliveryRidersChangeDTO dto) {

        if (!authorityManager.hasAuthorityByRiderId(userInfo, dto.getRiderId1())) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지를 권한이 없습니다.", false);
        }
        if (!authorityManager.hasAuthorityByRiderId(userInfo, dto.getRiderId2())) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지를 권한이 없습니다.", false);
        }

        webRiderService.simulationRiderChangedDispatchRiderProject(userInfo, dto);
    }

    /**
     * web에서 전화 번호를 이용하여 rider정보 불러옴
     *
     * @param mobile
     */
    @GetMapping(WebUrl.WEB_GET_MOBILE_RIDER)
    public WebRiderDTO getFindMobileRider(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                          @RequestParam(required = true) String mobile) {

        if (Objects.isNull(userInfo)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        Rider rider = riderService.getRider(FormatUtil.stripPhoneNumber(mobile, false)).orElse(null);
        if (Objects.nonNull(rider))
            return riderService.getWebRider(rider.getId(), userInfo.getOrganizationId());
        else
            throw new CustomException(HttpStatus.BAD_REQUEST, "기사를 찾을 수가 없습니다. <br> 출발 주소를 직접 입력하세요", false);
    }

}
