package com.logisteq.tms.web.controller;

import com.logisteq.common.exception.CustomException;
import com.logisteq.common.exception.ItemNotFoundException;
import com.logisteq.tms.common.component.AuthorityManager;
import com.logisteq.tms.common.security.WebUserDetails;
import com.logisteq.tms.delivery.controller.DeliveryController;
import com.logisteq.tms.delivery.controller.DeliveryExcelController;
import com.logisteq.tms.delivery.controller.DeliveryRouteController;
import com.logisteq.tms.delivery.controller.DemoDeliveryController;
import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.DeliveryAllocation;
import com.logisteq.tms.delivery.domain.DeliveryGlovisStatusHistory;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.domain.suppl.GlovisDeliveryStatus;
import com.logisteq.tms.delivery.domain.suppl.VOCStatus;
import com.logisteq.tms.delivery.dto.*;
import com.logisteq.tms.delivery.dto.web.WebProjectDeliveryDTO;
import com.logisteq.tms.delivery.service.DeliveryBasicService;
import com.logisteq.tms.delivery.service.DeliveryCsHistoryService;
import com.logisteq.tms.delivery.service.DeliveryService;
import com.logisteq.tms.dispatch.service.DispatchService;
import com.logisteq.tms.external.glovis.constant.GlovisConstant;
import com.logisteq.tms.external.glovis.dto.*;
import com.logisteq.tms.external.glovis.service.GlovisDeliveryCodeService;
import com.logisteq.tms.external.glovis.service.GlovisService;
import com.logisteq.tms.external.thehyundai.constant.TheHyundaiConstant;
import com.logisteq.tms.file.domain.suppl.FileCategory;
import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.dto.web.WebDestinationDTO;
import com.logisteq.tms.project.service.ProjectBasicService;
import com.logisteq.tms.project.service.ProjectService;
import com.logisteq.tms.push.service.PushService;
import com.logisteq.tms.stat.constant.StatConst;
import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.constant.UserConstant;
import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.service.DepartmentService;
import com.logisteq.tms.user.service.OrganizationService;
import com.logisteq.tms.user.service.UserService;
import com.logisteq.tms.web.constant.WebConstant;
import com.logisteq.tms.web.constant.WebUrl;
import com.logisteq.tms.web.dto.WebDeliveryDTO;
import com.logisteq.tms.web.dto.WebDeliveryDeleteDTO;
import com.logisteq.tms.web.dto.WebInvoiceInfoRequestDTO;
import com.logisteq.tms.web.dto.WebInvoicePrintCountDTO;
import com.logisteq.tms.web.service.WebDeliveryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Tag(name = "WebDelivery", description = "웹 배송 관리 API - 배송지 생성, 조회, 상태 변경, 파일 관리 및 배송 리포트 기능을 제공합니다.")
@Slf4j
@Validated
@RestController
public class WebDeliveryController {

    private final DeliveryController deliveryController;
    private final DeliveryRouteController deliveryRouteController;
    private final DeliveryExcelController deliveryExcelController;
    private final DemoDeliveryController demoDeliveryController;
    private final WebDeliveryService webDeliveryService;
    private final UserService userService;
    private final ProjectBasicService projectBasicService;
    private final ProjectService projectService;
    private final PushService pushService;
    private final DeliveryService deliveryService;
    private final DeliveryBasicService deliveryBasicService;
    private final OrganizationService organizationService;
    private final AuthorityManager authorityManager;
    private final DispatchService dispatchService;
    private final GlovisDeliveryCodeService glovisDeliveryCodeService;
    private final GlovisService glovisService;
    private final DeliveryCsHistoryService deliveryCsHistoryService;
    private final DepartmentService departmentService;

    @Autowired
    public WebDeliveryController(final DeliveryController deliveryController,
                                 final DeliveryRouteController deliveryRouteController,
                                 final DeliveryExcelController deliveryExcelController,
                                 final DemoDeliveryController demoDeliveryController,
                                 final WebDeliveryService webDeliveryService,
                                 final UserService userService,
                                 final ProjectBasicService projectBasicService,
                                 final ProjectService projectService,
                                 final PushService pushService,
                                 final DeliveryService deliveryService,
                                 final DeliveryBasicService deliveryBasicService,
                                 final OrganizationService organizationService,
                                 final AuthorityManager authorityManager,
                                 final DispatchService dispatchService,
                                 final GlovisDeliveryCodeService glovisDeliveryCodeService,
                                 final GlovisService glovisService,
                                 final DeliveryCsHistoryService deliveryCsHistoryService,
                                 final DepartmentService departmentService) {

        this.deliveryController = deliveryController;
        this.deliveryRouteController = deliveryRouteController;
        this.deliveryExcelController = deliveryExcelController;
        this.demoDeliveryController = demoDeliveryController;
        this.webDeliveryService = webDeliveryService;
        this.userService = userService;
        this.projectBasicService = projectBasicService;
        this.projectService = projectService;
        this.pushService = pushService;
        this.deliveryService = deliveryService;
        this.deliveryBasicService = deliveryBasicService;
        this.organizationService = organizationService;
        this.authorityManager = authorityManager;
        this.dispatchService = dispatchService;
        this.glovisDeliveryCodeService = glovisDeliveryCodeService;
        this.glovisService = glovisService;
        this.deliveryCsHistoryService = deliveryCsHistoryService;
        this.departmentService = departmentService;
    }

    /**
     * 두 기사의 클러스터링을 바꾼다
     *
     * @param userInfo
     * @param dto
     * @return
     */
    @Operation(summary = "기사간 클러스터링 교환", description = "두 기사 간의 클러스터링을 교환합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "클러스터링 교환 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PutMapping(WebUrl.RIDERS_SWITCH_CLUSTERING)
    public void switchRiderClustering(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                      @Parameter(description = "기사 클러스터링 교환 정보") @RequestBody DeliveryRidersChangeDTO dto) {

        if (!authorityManager.hasAuthorityByRiderId(userInfo, dto.getRiderId1())) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지를 권한이 없습니다.", false);
        }
        if (!authorityManager.hasAuthorityByRiderId(userInfo, dto.getRiderId2())) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지를 권한이 없습니다.", false);
        }

        deliveryController.switchRiderClustering(dto.getProjectId(), dto.getRiderId1(), dto.getRiderId2());
    }

    /**
     * 배송 삭제 (단일 혹은 리스트)
     *
     * @param userInfo
     * @param dto
     */
    @Operation(summary = "배송 삭제", description = "단일 또는 여러 배송을 삭제합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "삭제 성공"),
        @ApiResponse(responseCode = "400", description = "잘못된 요청 데이터"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PostMapping(WebUrl.D_DELIVERY_URL)
    public void deleteDeliveries(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                 @Parameter(description = "삭제할 배송 정보") @RequestBody @Valid WebDeliveryDeleteDTO dto) {

        final Long authUserId = userInfo.getId();
        final List<Long> deliveries = dto.getDeliveries();
        final Long projectId = dto.getProjectId();

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }
        if (!authorityManager.hasAuthorityByDeliveryIds(userInfo, dto.getDeliveries())) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지를 권한이 없습니다.", false);
        }

        //삭제 대신에  deleted = true로 수정
//		deliveryService.deleteDeliveries(authUserId, projectId, deliveries);
//		deliveryService.setDeleteDeliveries( projectId, deliveries );

        // 배송지가 삭제되면 경로 생성 버튼은 활성화, 기사에게 전송은 비활성화 되어야 한다.
//		projectService.changeProjectAttributeToNeedToUpdateRider(projectId);

        List<Long> updateRiderId = deliveryService.deleteDeliveryIdsInfo(deliveries, authUserId);
        dispatchService.dispatchByRiderIds(projectId, updateRiderId, false); //삭제 할때는 클러스터링을 수행할 필요가 없음
    }

    /**
     * 신규 배송 목록 조회
     *
     * @return
     * @throws InterruptedException
     */
    @Operation(
        summary = "데모용 배송지 조회",
        description = "데모용 배송지 목록을 조회합니다.",
        parameters = {
            @Parameter(name = "type", description = "조회 타입 (고정값)", required = true, example = "demo",
                      in = ParameterIn.QUERY)
        }
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "400", description = "잘못된 요청 데이터"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.DEMO_DELIVERIES, params = {"type=demo"})
    public List<DeliveryDTO> getNewDeliveries(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                              @Parameter(description = "프로젝트 ID", example = "1") @RequestParam @Min(1) Long projectId,
                                              @Parameter(description = "호출 사용자 ID", example = "1") @RequestParam @Min(1) Long callUserId) {

        if (!authorityManager.hasAuthorityByUserId(userInfo, callUserId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "인증되지 않는 사용자 입니다.", false);
        }

        return demoDeliveryController.getNewDeliveries(projectId, callUserId);
    }

    /**
     * 신규 배송 정보 저장 [mobile-demo]
     *
     * @return
     */
    @Operation(
        summary = "데모용 배송 정보 저장",
        description = "데모용 신규 배송 정보를 저장합니다.",
        parameters = {
            @Parameter(name = "type", description = "조회 타입 (고정값)", required = true, example = "demo",
                      in = ParameterIn.QUERY)
        }
    )
    @ApiResponses({
        @ApiResponse(responseCode = "201", description = "생성 성공"),
        @ApiResponse(responseCode = "400", description = "잘못된 요청 데이터"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PostMapping(value = WebUrl.DEMO_DELIVERIES, params = {"type=demo"}, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)/* Demo 수정 */
    public List<DeliveryDTO> saveDemoDeliveries(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                @Parameter(description = "사용자 ID", example = "1") @RequestParam @Min(1) Long userId,
                                                @Parameter(description = "프로젝트 ID", example = "1") @RequestParam @Min(1) Long projectId,
                                                @Parameter(description = "저장할 배송 정보 목록") @Valid @RequestBody List<DeliveryDTO> reqDeliveries) {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "인증되지 않는 사용자 입니다.", false);
        }

        return demoDeliveryController.saveDeliveries(userId, projectId, reqDeliveries);
    }

//	@PostMapping(value = WebUrl.DEMO_DELIVERIES, params = {"type=demo"}, consumes = MediaType.APPLICATION_JSON_VALUE)
//	@ResponseStatus(HttpStatus.CREATED)
//	public DeliveryDTO saveDemoDelivery(@RequestParam @Min(1) Long callUserId, @RequestParam Long projectId
//			, @RequestParam Long serviceId/*TODO 임시로 이걸 admin user id로 사용하는데 나중에는 serviceID로 adminUserID를 알수 있어야 한다.*/, @RequestBody DeliveryDTO reqDelivery) {
//		return demoDeliveryController.saveDemoDelivery(callUserId, projectId, serviceId, reqDelivery);
//	}

    /**
     * 배송 목록 삭제
     *
     * @param userInfo
     * @param projectId
     */
    @Operation(summary = "배송 목록 삭제", description = "프로젝트에 속한 배송 목록을 삭제합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "삭제 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @DeleteMapping(value = WebUrl.DEMO_DELIVERIES)
    public void deleteDeliveries(@Parameter(hidden = true) @AuthenticationPrincipal WebUserDetails userInfo,
                                 @Parameter(description = "프로젝트 ID", example = "1") @RequestParam Long projectId) {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "인증되지 않는 사용자 입니다.", false);
        }

        deliveryController.deleteDeliveries(userInfo, projectId);
    }

    @Operation(summary = "배송 상태 조회", description = "특정 배송의 현재 상태를 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음"),
        @ApiResponse(responseCode = "404", description = "배송을 찾을 수 없음")
    })
    @GetMapping(WebUrl.DELIVERY_STATUS)
    public DeliveryStatusDTO getDeliveryStatus(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                               @Parameter(description = "배송 ID", example = "1") @PathVariable Long deliveryId,
                                               @Parameter(description = "기사 ID", example = "1") @RequestParam Long riderId) {

        DeliveryStatusDTO riderDeliveryAllocation = null;
        try {
//			riderDeliveryAllocation = deliveryController.getDeliveryFailureOrCompletedHistory(deliveryId, riderId );
            riderDeliveryAllocation = deliveryService.getDeliveryStatus(deliveryId, riderId, false);
        } catch (ItemNotFoundException e) {

        }
        return riderDeliveryAllocation;
    }

    /**
     * 강제 배송 완료/실패  변경 API
     *
     * @param deliveryId
     * @param riderId
     * @param deliveryStatusDTO
     */
    @Operation(summary = "배송 상태 강제 변경", description = "배송 상태를 강제로 완료 또는 실패로 변경합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "변경 성공"),
        @ApiResponse(responseCode = "400", description = "잘못된 요청 데이터"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @Transactional
    @PutMapping(value = WebUrl.DELIVERY_FORCE_STATUS)
    public void updateForceDeliveryStatus(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                          @Parameter(description = "배송 ID", example = "1") @PathVariable @Min(1) Long deliveryId,
                                          @Parameter(description = "기사 ID", example = "1") @RequestParam Long riderId,
                                          @Parameter(description = "변경할 배송 상태 정보") @Valid @RequestBody DeliveryStatusDTO deliveryStatusDTO) {

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, deliveryId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

//		this.deliveryController.updateDeliveryStatus( deliveryId, riderId, deliveryStatusDTO );
        this.deliveryService.updateForceDeliveryStatus(deliveryId, riderId, deliveryStatusDTO);
    }

    /**
     * 강제 배송지들 완료/실패  변경 API
     *
     * @param customerCode
     * @param deliveryIds       배송지들 아이디
     * @param customerOrderIds
     * @param deliveryStatusDTO 배송 상태 정보
     */
    @Operation(summary = "다중 배송 상태 강제 변경", description = "여러 배송의 상태를 강제로 완료 또는 실패로 변경합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "변경 성공"),
        @ApiResponse(responseCode = "400", description = "잘못된 요청 데이터"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @Transactional
    @PutMapping(value = WebUrl.DELIVERIES_FORCE_STATUS)
    public void updateForceDeliveriesStatus(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                            @Parameter(description = "고객 코드") @RequestParam(required = false) String customerCode,
                                            @Parameter(description = "배송 ID 목록") @RequestParam(required = false) List<Long> deliveryIds,
                                            @Parameter(description = "고객 주문 ID 목록") @RequestParam(required = false) List<String> customerOrderIds,
                                            @Parameter(description = "변경할 배송 상태 정보") @Valid @RequestBody DeliveryStatusDTO deliveryStatusDTO) {

        if (!authorityManager.hasAuthorityByDeliveryIds(userInfo, deliveryIds)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        deliveryController.updateForceDeliveriesStatus(customerCode, deliveryIds, customerOrderIds, deliveryStatusDTO);
    }

    /**
     * 배송 상태 변경 API - Demo에서 사용함
     *
     * @param deliveryId
     * @param riderId
     * @param deliveryStatusDTO
     */
    @Operation(summary = "배송 상태 업데이트", description = "특정 배송의 상태를 업데이트합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "업데이트 성공"),
        @ApiResponse(responseCode = "400", description = "잘못된 요청 데이터"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음"),
        @ApiResponse(responseCode = "404", description = "배송을 찾을 수 없음")
    })
    @Transactional
    @PutMapping(value = WebUrl.DELIVERY_STATUS)
    public void updateDeliveryStatus(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                     @Parameter(description = "배송 ID", example = "1") @PathVariable @Min(1) Long deliveryId,
                                     @Parameter(description = "기사 ID", example = "1") @RequestParam Long riderId,
                                     @Parameter(description = "배송 상태 정보") @Valid @RequestBody DeliveryStatusDTO deliveryStatusDTO) {

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, deliveryId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        this.deliveryController.updateDeliveryStatus(deliveryId, riderId, deliveryStatusDTO);
    }

    /**
     * 단일 배송 정보 저장.
     *
     * @param userInfo
     * @param dto
     * @return
     */
    @Operation(summary = "배송지 생성", description = "새로운 배송지를 생성합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "생성 성공"),
        @ApiResponse(responseCode = "400", description = "잘못된 요청 데이터"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PostMapping(WebUrl.DELIVERY)
    public WebDestinationDTO saveDelivery(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                          @Parameter(description = "배송 정보") @RequestBody WebDeliveryDTO dto) {

        final Long projectId = dto.getProjectId();

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, dto.getDeliveryId())) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        final Long authUserId = userInfo.getId();
        WebDestinationDTO webDestinationDTO;

        if (dto.getDeliveryId() != null) {
            webDestinationDTO = webDeliveryService.updateDelivery(authUserId, dto);
        } else {
            final Delivery delivery = webDeliveryService.saveDelivery(authUserId, dto);
            final List<Long> updatedRiderIds = dispatchService.dispatchAddDeliveries(dto.getProjectId(), Arrays.asList(delivery), dto.getRiderId(), true);
            webDestinationDTO = projectService.makeWebDestinationDTO(delivery, Boolean.FALSE);
            if (updatedRiderIds.size() > 0) {
                webDestinationDTO.setRiderId(updatedRiderIds.get(0));
            }
        }

        // 다른 사용자들에 대한 status 변경을 위해서 사용함.
        final Project project = projectBasicService.getProjectByIdOrThrowException(dto.getProjectId());

        pushService.sendReloadProjectMessageToWeb(userInfo.getId(), project.getId(), "프로젝트 상태가 변경되었습니다.");

        return webDestinationDTO;
    }

    /**
     * 배송 목록 조회 API (with Paging)
     *
     * @param userId
     * @param pageable
     * @param dateFrom
     * @param dateTo
     * @return
     */
    @Operation(
        summary = "배송 목록 조회 (페이징)",
        description = "배송 목록을 페이징하여 조회합니다. 사용자, 기사, 프로젝트, 날짜 범위, 상태로 필터링 가능합니다.",
        parameters = {
            @Parameter(name = "type", description = "조회 타입 (고정값)", required = true, example = "page",
                      in = ParameterIn.QUERY)
        }
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.DELIVERIES, params = {"type=page"})
    public Page<DeliveryDTO> getDeliveriesWithPaging(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                     @Parameter(description = "사용자 ID") @RequestParam(required = false) @Min(1) Long userId,
                                                     @Parameter(description = "기사 ID") @RequestParam(required = false) @Min(1) Long riderId,
                                                     @Parameter(description = "프로젝트 ID") @RequestParam(required = false) Long projectId,
                                                     @Parameter(description = "페이지 요청 정보") final PageRequestDTO pageable,
                                                     @Parameter(description = "시작 날짜시간") @RequestParam(required = false) @DateTimeFormat(pattern = WebConstant.REQ_DATE_TIME_FORMAT) LocalDateTime dateFrom,
                                                     @Parameter(description = "종료 날짜시간") @RequestParam(required = false) @DateTimeFormat(pattern = WebConstant.REQ_DATE_TIME_FORMAT) LocalDateTime dateTo,
                                                     @Parameter(description = "배송 상태 목록") @RequestParam(required = false) List<DeliveryStatus> deliveryStatusList, 
                                                     @Parameter(description = "조회 타입", example = "page") @RequestParam("type") String type,
                                                     @Parameter(description = "특정 배송 ID 목록") @RequestParam(required = false) List<Long> deliveryIds) {

        if (!authorityManager.hasAuthorityByUserId(userInfo, userId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "인증되지 않는 사용자 입니다.", false);
        }

        if (!authorityManager.hasAuthorityByDeliveryIds(userInfo, deliveryIds)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        return deliveryController.getDeliveriesWithPaging(userId, riderId, projectId, pageable, dateFrom, dateTo, deliveryStatusList, type, deliveryIds);
    }

    /**
     * 배송 상세 조회 API
     */
    @Operation(summary = "배송 상세 정보 조회", description = "특정 배송의 상세 정보를 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음"),
        @ApiResponse(responseCode = "404", description = "배송을 찾을 수 없음")
    })
    @GetMapping(WebUrl.DELIVERY_INFO)
    public DeliveryDTO getDeliveryInfo(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                       @Parameter(description = "배송 ID", example = "1") @PathVariable @Min(1) Long deliveryId,
                                       @Parameter(description = "기사 ID (선택 사항)") @RequestParam(required = false) Long riderId) {

        return deliveryController.getDeliveryInfo(deliveryId, riderId);
    }

    /**
     * 방문지 목록 조회 API (방문지 관리에 사용)
     *
     * @param userInfo
     * @param deliveryId
     * @return
     */
    @Operation(
        summary = "배송지 관리용 정보 조회",
        description = "관리 페이지에서 사용할 특정 배송지의 상세 정보를 조회합니다.",
        parameters = {
            @Parameter(name = "type", description = "조회 타입 (고정값)", required = true, example = "manage",
                      in = ParameterIn.QUERY)
        }
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음"),
        @ApiResponse(responseCode = "404", description = "배송을 찾을 수 없음")
    })
    @GetMapping(value = WebUrl.DELIVERY_INFO, params = {"type=manage"})
    public DeliveryManagementDeliveryInfoDTO getDeliveryForManagement(
            @Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
            @Parameter(description = "배송 ID", example = "1") @PathVariable @Positive final Long deliveryId) {

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, deliveryId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        return deliveryController.getDeliveryForManagement(userInfo, deliveryId);
    }

    /**
     * 방문지 목록 조회 API (방문지 관리에 사용)
     */
    @Operation(
        summary = "관리용 배송 목록 조회",
        description = "관리 페이지에서 사용할 배송 목록을 슬라이스하여 조회합니다. 부서, 키워드, 위치, 날짜로 필터링 가능합니다.",
        parameters = {
            @Parameter(name = "type", description = "조회 타입 (고정값)", required = true, example = "manage",
                      in = ParameterIn.QUERY)
        }
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.DELIVERIES, params = {"type=manage"})
    public Slice<DeliveryManagementDTO> getDeliveriesForManagement(
            @Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
            @Parameter(description = "부서 ID 목록") @RequestParam(required = false) final List<Long> departmentIdList,
            @Parameter(description = "검색 키워드 (수취인, 연락처, 주소 등)") @RequestParam(required = false) final String keyword,
            @Parameter(description = "위치 검색 키워드") @RequestParam(required = false) final String locationKeyword,
            @Parameter(description = "시작 날짜시간") @RequestParam(required = false) @DateTimeFormat(pattern = WebConstant.REQ_DATE_TIME_FORMAT) LocalDateTime fromDate,
            @Parameter(description = "종료 날짜시간") @RequestParam(required = false) @DateTimeFormat(pattern = WebConstant.REQ_DATE_TIME_FORMAT) LocalDateTime toDate,
            @Parameter(description = "페이지 요청 정보") @PageableDefault(size = 10, sort = "create_at", direction = Sort.Direction.DESC) final Pageable pageable) {

        if (authorityManager.isAnonymousUser(userInfo)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "이미 로그아웃되었습니다. 다시 로그인 해주세요.", false);
        }

        return deliveryController.getDeliveriesForManagement(userInfo, departmentIdList, keyword, locationKeyword, fromDate, toDate, pageable);
    }

    /**
     * 배송지(VOC) 목록 조회 API (배송지(VOC) 관리에 사용)
     */
    @Operation(
        summary = "조직별 배송 관리 목록 조회",
        description = "특정 프로젝트 또는 부서에 대한 배송 목록을 슬라이스하여 조회합니다. VOC 상태, 키워드, 날짜로 필터링 가능합니다.",
        parameters = {
            @Parameter(name = "type", description = "조회 타입 (고정값)", required = true, example = "delivery_manage",
                      in = ParameterIn.QUERY)
        }
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.DELIVERIES, params = {"type=delivery_manage"})
    public Slice<DeliveryManagermentOrganizationIdDTO> getDeliveriesForManagement(
            @Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
            @Parameter(description = "프로젝트 ID") @RequestParam(required = false) final Long projectId,
            @Parameter(description = "부서 ID 목록") @RequestParam(required = false) final List<Long> departmentIdList,
            @Parameter(description = "검색 키워드") @RequestParam(required = false) final String keyword,
            @Parameter(description = "VOC 상태") @RequestParam(required = false) final VOCStatus vocStatus,
            @Parameter(description = "검색 값 (송장번호 등)") @RequestParam(required = false) final Long searchValue,
            @Parameter(description = "시작 날짜시간") @RequestParam(required = false) @DateTimeFormat(pattern = WebConstant.REQ_DATE_TIME_FORMAT) LocalDateTime fromDate,
            @Parameter(description = "종료 날짜시간") @RequestParam(required = false) @DateTimeFormat(pattern = WebConstant.REQ_DATE_TIME_FORMAT) LocalDateTime toDate,
            @Parameter(description = "페이지 요청 정보") @PageableDefault(size = 10, sort = "create_at", direction = Sort.Direction.DESC) final Pageable pageable) {

        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        return deliveryController.getDeliveriesForManagement(userInfo, projectId, departmentIdList, keyword, vocStatus, searchValue, fromDate, toDate, pageable);
    }

    /**
     * 방문지들 삭제 API (방문지 관리에 사용)
     *
     * @param userInfo
     * @param deliveryIdList
     */
    @Operation(
        summary = "관리용 배송 목록 삭제",
        description = "관리 페이지에서 선택한 배송 목록을 삭제합니다.",
        parameters = {
            @Parameter(name = "type", description = "조회 타입 (고정값)", required = true, example = "manage",
                      in = ParameterIn.QUERY)
        }
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "삭제 성공"),
        @ApiResponse(responseCode = "400", description = "잘못된 요청 (ID 목록이 비어있음)"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @DeleteMapping(value = WebUrl.DELIVERIES, params = {"type=manage"})
    public void deleteDeliveriesForManagement(
            @Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
            @Parameter(description = "삭제할 배송 ID 목록") @RequestParam @NotEmpty final List<Long> deliveryIdList) {

        if (!authorityManager.hasAuthorityByDeliveryIds(userInfo, deliveryIdList)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        deliveryController.deleteDeliveriesForManagement(userInfo, deliveryIdList);
    }

    @Operation(summary = "배송 VOC 메시지 추가", description = "배송에 대한 고객의 소리(VOC) 메시지를 추가합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "VOC 추가 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PostMapping(value = WebUrl.DELIVERY_VOC_SAVE)
    public void addDeliveryVOCMessage(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                      @Parameter(description = "배송 ID") @PathVariable(required = false) final Long deliveryId,
                                      @Parameter(description = "VOC 상태") @RequestParam(required = false) final VOCStatus vocStatus,
                                      @Parameter(description = "VOC 메시지") @RequestParam(required = false) final String vocMessage) {
        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, deliveryId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        deliveryController.addDeliveryVOCMessage(userInfo, deliveryId, null, vocStatus, vocMessage);
    }

    @Operation(summary = "배송 VOC 메시지 목록 조회", description = "특정 배송에 대한 VOC 메시지 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.DELIVERY_VOC_GET_MESSAGE_LIST)
    public List<DeliveryVocMessageDTO> getDeliveryVOCMessageList(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                                 @Parameter(description = "배송 ID", example = "1") @RequestParam(required = true) final Long deliveryId) {

        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, deliveryId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        return deliveryController.getDeliveryVOCMessageList(deliveryId);
    }

    /**
     * 배송 송장 Print Count 증가
     *
     * @param userInfo
     * @param deliveryList
     */
    @Operation(summary = "송장 출력 횟수 증가", description = "배송 송장의 출력 횟수를 증가시킵니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "업데이트 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PutMapping(value = WebUrl.DELIVERY_SET_INVOICE_PRINT_COUNT)
    public void increaseInvoicePrintCountOfDeliveries(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                      @Parameter(description = "출력 횟수를 증가시킬 배송 목록") @Valid @RequestBody(required = true) final WebInvoicePrintCountDTO deliveryList) {

        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        if (!authorityManager.hasAuthorityByDeliveryIds(userInfo, deliveryList.getDeliveryIds())) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        deliveryController.increaseInvoicePrintCountOfDeliveries(deliveryList);
    }

    /**
     * 특정 프로젝트의 배송 목록 조회 API (Feat. Reporting)
     *
     * @param pageable
     * @param projectId
     * @return
     * <AUTHOR>
     */
    @Operation(summary = "배송 할당 리포트 조회", description = "특정 프로젝트의 배송 할당 리포트를 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음"),
        @ApiResponse(responseCode = "404", description = "프로젝트를 찾을 수 없음")
    })
    @GetMapping(value = WebUrl.DELIVERIES_REPORT)
    public Page<DeliveryAllocationReportDTO> getDeliveryAllocationReport(
            @Parameter(hidden = true) @AuthenticationPrincipal final WebUserDetails userInfo,
            @Parameter(description = "페이지 정보") @PageableDefault(size = StatConst.PAGEABLE_SIZE_DEFAULT) final Pageable pageable,
            @Parameter(description = "프로젝트 ID", example = "1") @RequestParam(name = "projectId") @NotNull @Positive final Long projectId) {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "인증되지 않는 사용자 입니다.", false);
        }

        final Project project = Optional.ofNullable(projectBasicService.getProjectById(projectId)).orElseThrow(ItemNotFoundException::new);
        final Long authUserId = userInfo.getId();
        final User user = userService.getUser(authUserId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, authUserId + " 사용자 정보가 없습니다.", false));
        final RoleType roleType = userService.getUserRoleByUser(user);

        final Set<Long> userIdList;
        if (RoleType.ROLE_ADMIN.equals(roleType)) {
            userIdList = null;
        } else if (RoleType.ROLE_ORG_ADMIN.equals(roleType) || RoleType.ROLE_COMMON.equals(roleType)) {
            final Long orgId = user.getOrganizationId();
            final List<User> userList = userService.getUserByOrganization(orgId);
            userIdList = userList.stream().map(User::getUserId).collect(Collectors.toSet());
        } else {
            userIdList = Stream.of(authUserId).collect(Collectors.toSet());
        }

        if (!CollectionUtils.isEmpty(userIdList)) {
            final Long projectUserId = project.getUser().getUserId();
            if (!userIdList.contains(projectUserId)) {
                throw new SecurityException();
            }
        }

        return deliveryController.getDeliveryAllocationReport(pageable, projectId);
    }

    /**
     * 특정 배송지를  기사에게  클러스터링 변경한다
     *
     * @param userInfo
     * @param deliveryId
     * @param deliveryRiderOrderDto
     * @return
     * <AUTHOR>
     */
    @Operation(summary = "배송 클러스터 변경", description = "특정 배송지를 다른 기사에게 클러스터링 변경합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "클러스터 변경 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PutMapping(value = WebUrl.DELIVERY_CHANGE_CLUSTER)
    public void changeDeliveryCluster(@Parameter(hidden = true) @AuthenticationPrincipal final WebUserDetails userInfo,
                                      @Parameter(description = "배송 ID", example = "1") @RequestParam(name = "deliveryId") @NotNull @Positive final Long deliveryId,
                                      @Parameter(description = "기사 주문 정보") @Valid @RequestBody DeliveryRiderOrderDTO deliveryRiderOrderDto) {

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, deliveryId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        deliveryController.changeClusterDeliveryToRider(deliveryId, deliveryRiderOrderDto);
    }

    /**
     * 특정 배송지 리스트를  모두  기사에게  클러스터링 변경한다
     *
     * @param userInfo
     * @param deliveryRiderOrderDto
     * @return
     * <AUTHOR>
     */
    @Operation(summary = "다중 배송 클러스터 변경", description = "여러 배송지를 모두 다른 기사에게 클러스터링 변경합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "클러스터 변경 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PutMapping(value = WebUrl.DELIVERIES_CHANGE_CLUSTER)
    public void changeDeliveriesCluster(@Parameter(hidden = true) @AuthenticationPrincipal final WebUserDetails userInfo,
                                        @Parameter(description = "배송 ID 목록") @RequestParam(required = false) List<Long> deliveryIds,
                                        @Parameter(description = "기사 주문 정보") @Valid @RequestBody DeliveryRiderOrderDTO deliveryRiderOrderDto) {

        if (!authorityManager.hasAuthorityByDeliveryIds(userInfo, deliveryIds)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        deliveryController.changeClusterDeliveriesToRider(deliveryIds, null, deliveryRiderOrderDto, userInfo);
    }

    /**
     * 특정 배송정보의 전체 첨부 파일을 다운로드 함
     *
     * @param userInfo
     * @param response
     * @param deliveryId
     * @param projectId
     * @param riderId
     */
    @Operation(summary = "배송 첨부파일 다운로드", description = "특정 배송의 전체 첨부 파일을 ZIP 형태로 다운로드합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "다운로드 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.DELIVERIES_FILE_ZIP_DOWNLOAD)
    public void downloadDeliveryAttachedPhotos(@Parameter(hidden = true) @AuthenticationPrincipal final WebUserDetails userInfo,
                                               @Parameter(hidden = true) @NotNull final HttpServletResponse response,
                                               @Parameter(description = "배송 ID", example = "1") @PathVariable @Positive final Long deliveryId,
                                               @Parameter(description = "프로젝트 ID", example = "1") @RequestParam @Positive final Long projectId,
                                               @Parameter(description = "기사 ID", example = "1") @RequestParam @Positive final Long riderId) {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "인증되지 않는 사용자 입니다.", false);
        }

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, deliveryId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        deliveryController.downloadDeliveryAttachedPhotos(response, deliveryId, projectId, riderId);
    }

    /**
     * 배송 목록 경로 선형 조회
     *
     * @param riderId
     * @param projectId
     * @return
     */
    @Operation(summary = "배송 경로 조회", description = "배송 목록의 경로를 선형으로 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = {WebUrl.GET_DELIVERY_ROUTE_PATH, WebUrl.DEMO_GET_DELIVERY_ROUTE_PATH})
    public List<DeliveryRoutesResponseDTO> getDeliveryRoutePath(@Parameter(description = "기사 ID", example = "1") @RequestParam Long riderId,
                                                                @Parameter(description = "프로젝트 ID", example = "1") @RequestParam Long projectId) {

        return deliveryRouteController.getDeliveryRoutePath(riderId, projectId);
    }

    /**
     * 배송 목록 ETA 조회
     *
     * @param riderId
     * @param projectId
     * @return
     */
    @Operation(summary = "배송 ETA 조회", description = "배송 목록의 예상 도착시간(ETA)을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.GET_DELIVERY_ROUTE_ETA)
    public List<DeliveryRoutesETAResponseDTO> getDeliveryRouteETA(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                                  @Parameter(description = "기사 ID", example = "1") @RequestParam Long riderId,
                                                                  @Parameter(description = "프로젝트 ID", example = "1") @RequestParam Long projectId) {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "인증되지 않는 사용자 입니다.", false);
        }

        return deliveryRouteController.getDeliveryRouteETA(riderId, projectId);
    }

    /**
     * 방문지 주문내역 엑셀파일 생성
     *
     * @param deliveryId
     */
    @Operation(summary = "배송 주문내역 엑셀 생성", description = "방문지 주문내역을 엑셀 파일로 생성합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 생성 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자")
    })
    @GetMapping(value = WebUrl.DELIVERIES_EXCEL, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void createExcelFromDelivery(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                        @Parameter(description = "배송 ID", example = "1") @RequestParam final Long deliveryId,
                                        @Parameter(hidden = true) final HttpServletResponse response) throws Exception {

        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        deliveryExcelController.createExcelFromDelivery(deliveryId, userInfo.getId(), response);
    }

    /**
     * 방문지별 과거 주문내역 엑셀파일 생성
     *
     * @param deliveryId
     */
    @Operation(summary = "과거 주문내역 엑셀 생성", description = "방문지별 과거 주문내역을 엑셀 파일로 생성합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 생성 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자")
    })
    @GetMapping(value = WebUrl.DELIVERIES_PAST_EXCEL, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void createExcelFromPastDelivery(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                            @Parameter(description = "배송 ID", example = "1") @RequestParam final Long deliveryId,
                                            @Parameter(hidden = true) final HttpServletResponse response) throws Exception {

        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        deliveryExcelController.createExcelFromPastDelivery(deliveryId, userInfo.getId(), response);
    }

    /**
     * 방문지별 과거 주문내역 조회
     *
     * @param deliveryId
     */
    @Operation(summary = "과거 주문내역 조회", description = "방문지별 과거 주문내역을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.DELIVERIES_PAST)
    public List<DeliveryPastOrderDTO> getPastDelivery(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                      @Parameter(description = "배송 ID", example = "1") @RequestParam final Long deliveryId) throws Exception {

        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, deliveryId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        return deliveryController.getPastDelivery(deliveryId, userInfo.getId());
    }

    /**
     * 배송지 이미지 업로드
     *
     * @param userInfo
     * @param deliveryId
     * @param riderId
     * @param projectId
     * @param fileCategory
     * @param prefix
     * @param imagesFile
     */
    @Operation(summary = "배송 이미지 업로드", description = "배송지에 관련된 이미지 파일을 업로드합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "업로드 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PostMapping(value = WebUrl.DELIVERIES_FILE)
    public void putDeliveryImageUpload(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                       @Parameter(description = "배송 ID", example = "1") @PathVariable final Long deliveryId,
                                       @Parameter(description = "기사 ID", example = "1") @RequestParam final Long riderId,
                                       @Parameter(description = "프로젝트 ID", example = "1") @RequestParam final Long projectId,
                                       @Parameter(description = "파일 카테고리") @RequestParam final FileCategory fileCategory,
                                       @Parameter(description = "파일명 접두사") @RequestParam(required = false) String  prefix,
                                       @Parameter(description = "업로드할 이미지 파일 목록") @RequestParam("files") final @NotNull List<MultipartFile> imagesFile) {

        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "로그인되지 않았습니다.", false);
        }

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, deliveryId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        deliveryController.uploadFile(deliveryId, riderId, projectId, fileCategory, prefix, imagesFile );
    }

    /**
     * 배송지 업로드 파일 조회
     *
     * @param userInfo
     * @param deliveryId
     * @return
     */
    @Operation(summary = "배송 업로드 파일 조회", description = "배송지에 업로드된 파일 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.DELIVERY_FILE)
    public List<String> getUploadFile(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                      @Parameter(description = "배송 ID", example = "1") @PathVariable final Long deliveryId) {

        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "로그인되지 않았습니다.", false);
        }

        if (!authorityManager.hasAuthorityByDeliveryId(userInfo, deliveryId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 배송지 정보에 접근할 권한이 없습니다.", false);
        }

        List<DeliveryAllocation> allocations = deliveryBasicService.getDeliveryAllocations(deliveryId);

        return deliveryService.getDeliveryCompletedFilesURLList(allocations.get(0), false);
    }

    /**
     * 송장 출력 정보 조회
     *
     * @param userInfo
     * @param webInvoiceInfoRequestDTO
     * @return
     */
    @Operation(summary = "배송 송장 정보 조회", description = "프로젝트의 배송 송장 정보 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PostMapping(WebUrl.DELIVERY_GET_INVOICE_INFO)
    public List<List<DeliveryInvoiceInfoDTO>> getDeliveryInvoiceInfoListOfProject(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                                                  @RequestBody @Valid final WebInvoiceInfoRequestDTO webInvoiceInfoRequestDTO) {

        final Long projectId = webInvoiceInfoRequestDTO.getProjectId();

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "인증되지 않는 사용자 입니다.", false);
        }

        return webDeliveryService.getDeliveryInvoiceInfoListOfProject(projectId,
                webInvoiceInfoRequestDTO.getRiderIdList(),
                webInvoiceInfoRequestDTO.getDeliveryIdList(),
                Boolean.TRUE.equals(webInvoiceInfoRequestDTO.getIncludeSimulationRider()));
    }

    /**
     * 권역 정보 엑셀을 등록할수 있는 권한 체크
     *
     * @param userInfo
     * @return
     */
    private Boolean enableRegisterUpdateDeliveryCode(final WebUserDetails userInfo) {

        Boolean enabled = false;

        final String organizationCode = userInfo.getOrganizationCode();
        if (GlovisConstant.GLOVIS_ORG_CODE_NAME.equals(organizationCode) || UserConstant.LOGISTEQ_ORG_CODE_NAME.equals(organizationCode)) {
            enabled = true;
        } else {
            final Boolean isUsePredefinedGrouping = Optional.ofNullable(organizationService.getOrganizationById(userInfo.getOrganizationId())).map(Organization::getIsUsePredefinedGrouping).orElse(false);
            if (!Boolean.TRUE.equals(isUsePredefinedGrouping)) {
                enabled = true;
            }
        }
        return enabled;
    }

    /**
     * 글로비스 운송장 배송 분류코드를 통째로 갱신한다.
     *
     * @param userInfo
     * @param excelFile
     */
    @Deprecated
    @PutMapping(value = {WebUrl.GLOVIS_DELIVERY_CODES_VER1}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public void updateDeliveryCodesVer1(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                        @RequestParam("excelFile") @NotNull final MultipartFile excelFile,
                                        @RequestParam(required = false) final Integer headerRowIndex,
                                        @RequestParam(required = false) final Integer firstDataRowIndex,
                                        @RequestParam(required = false) final Integer lastDataRowIndex,
                                        @RequestParam(required = false) final Integer firstDataColumnIndex,
                                        @RequestParam(required = false) final Integer lastDataColumnIndex) {

        if (Boolean.FALSE.equals(enableRegisterUpdateDeliveryCode(userInfo))) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "권한이 없습니다.", false);
        }

        glovisDeliveryCodeService.updateDeliveryCodesVer1(excelFile, headerRowIndex, firstDataRowIndex, lastDataRowIndex, firstDataColumnIndex, lastDataColumnIndex);
    }

    /**
     * 글로비스 우편번호 배송권역 테이블을 통째로 갱신한다.
     *
     * @param userInfo
     * @param excelFile
     */
    @Operation(summary = "글로비스 배송 코드 업데이트 V2", description = "글로비스 우편번호 배송권역 테이블을 엑셀 파일로 갱신합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "업데이트 성공"),
        @ApiResponse(responseCode = "401", description = "인증되지 않은 사용자")
    })
    @PutMapping(value = {WebUrl.GLOVIS_DELIVERY_CODES_VER2, WebUrl.GLOVIS_DELIVERY_CODES}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Integer updateDeliveryCodesVer2(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                           @RequestParam("excelFile") @NotNull final MultipartFile excelFile,
                                           @RequestParam(required = false) final Integer headerRowIndex,
                                           @RequestParam(required = false) final Integer firstDataRowIndex,
                                           @RequestParam(required = false) final Integer lastDataRowIndex,
                                           @RequestParam(required = false) final Integer firstDataColumnIndex,
                                           @RequestParam(required = false) final Integer lastDataColumnIndex) {

//        if (Boolean.FALSE.equals(enableRegisterUpdateDeliveryCode(userInfo))) {
//            throw new CustomException(HttpStatus.UNAUTHORIZED, "권한이 없습니다.", false);
//        }
        String organizationCode = userInfo.getOrganizationCode();
        return glovisDeliveryCodeService.updateDeliveryCodesVer2(excelFile, headerRowIndex, firstDataRowIndex, lastDataRowIndex, firstDataColumnIndex, lastDataColumnIndex, organizationCode);
    }

    @GetMapping(value = {WebUrl.GLOVIS_DELIVERY_CODES_VER2, WebUrl.GLOVIS_DELIVERY_CODES})
    public List<GlovisDeliveryCodeDTOVer2> getCompanyCodeDeliveryCodesVer2(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo) {

        if (Objects.isNull(userInfo)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        return glovisDeliveryCodeService.getCompanyCodeDeliveryCodesVer2(userInfo.getOrganizationCode());
    }

    //GlovisController::postDeliveryStatusInfo    중복 코드 존재 - 나중에 GlovisService쪽으로 분리 필요.
    @GetMapping(WebUrl.GLOVIS_DELIVERY_STATUS)
    public GlovisResponseDTO getGlovisDeliveryStatus(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                     @RequestParam final String invoiceNumber) {

        final Delivery delivery = deliveryService.getDeliveryByProjectIdAndUserIdAndCustomerOrderId(null, userInfo.getId(), invoiceNumber);

        if (delivery == null) return null;

        List<GlovisDeliveryStatusDetailDTO> statusList = new ArrayList<>();
        String unDliveryCode = "";
        String unDeliveryCodeName = "";
        String unDeliveryCodeDetail = "";
        final Long deliveryId = delivery.getId();
        final List<DeliveryGlovisStatusHistory> deliveryGlovisStatusHistories = glovisService.getDeliveryStatusHistories(deliveryId);

        for (final DeliveryGlovisStatusHistory history : deliveryGlovisStatusHistories) {
            final LocalDateTime statusUpdateDt = history.getStatusUpdateDt();
            LocalDate date = null;
            LocalTime time = null;
            if (Objects.nonNull(statusUpdateDt)) {
                date = statusUpdateDt.toLocalDate();
                time = statusUpdateDt.toLocalTime();
            }

            statusList.add(GlovisDeliveryStatusDetailDTO.builder()
                    .status(history.getStatus())
                    .statusName(history.getStatusName())
                    .date(date)
                    .time(time)
                    .branchName(history.getBranchName())
                    .build());

            // 미배송 data 설정
            if (GlovisDeliveryStatus.UNDELIVERY.getCode().equals(history.getStatus())) {
                unDliveryCode = history.getUnDeliveryCode();
                unDeliveryCodeName = history.getUnDeliveryCodeName();
                unDeliveryCodeDetail = history.getUnDeliveryCodeDetail();
            }
        }

        final Object data = GlovisDeliveryStatusRespDTO.builder()
                .invoiceNumber(invoiceNumber)
                .statusList(statusList)
                .unDliveryCode(unDliveryCode)
                .unDeliveryCodeName(unDeliveryCodeName)
                .unDeliveryCodeDetail(unDeliveryCodeDetail)
                .build();

        return GlovisResponseDTO.of(ResponseResultDTO.of(ResultStatus.SUCCESS), data);
    }

    /**
     * [푸디스트] 배송리포트 생성
     * 프로젝트별, 배송담당별, 일일 배송실적
     *
     * @param userInfo
     * @param reqDTO
     * @param response
     * @throws Exception
     */
    @PostMapping(value = WebUrl.DELIVERIES_TMS_REPORT, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void createDeliveryReport(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                     @RequestBody @Valid final TmsReportReqDTO reqDTO,
                                     @NotNull final HttpServletResponse response) throws Exception {

        if (StringUtils.isBlank(reqDTO.getEmail())) {
            // Web API로 배송리포트 생성 요청시 이메일 입력을 필수로 한다.
            throw new CustomException(HttpStatus.BAD_REQUEST, "이메일을 입력해주세요.", false);
        }

        deliveryExcelController.createDeliveryReport(userInfo.getId(), reqDTO, response);
    }

    /**
     * 관제 웹에서 관제자가 화물 완료 승인을 위한 API
     * @param deliveryCsDto
     * @param userInfo
     */
    @PutMapping(value = WebUrl.DELIVERY_CS_SAVE_HISTORY)
    public void saveDeliveryCsHistory(@Valid @RequestBody DeliveryCsHistoryDTO deliveryCsDto,
                                      @AuthenticationPrincipal @NotNull WebUserDetails userInfo) {
        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        deliveryCsHistoryService.saveForceDeliveryCsHistory(deliveryCsDto, userInfo.getName(), userInfo.getId());
    }

    /**
     * [현대백화점] 여러 프로젝트의 배송정보 조회
     *
     * @param userInfo
     * @param fromDate
     * @param toDate
     * @param warehouseCode
     * @return
     */
    @GetMapping(WebUrl.PROJECTS_DELIVERIES)
    public List<WebProjectDeliveryDTO> getMultipleProjectDeliveries(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                                    @RequestParam @DateTimeFormat(pattern = WebConstant.REQ_DATE_FORMAT) final LocalDate fromDate,
                                                                    @RequestParam @DateTimeFormat(pattern = WebConstant.REQ_DATE_FORMAT) final LocalDate toDate,
                                                                    @RequestParam @NotBlank final String warehouseCode) {

        if (!TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME.equals(userInfo.getOrganizationCode())) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증된 사용자가 아닙니다.", false);
        }

        final List<Long> departmentIdList = userInfo.getDepartmentIdList();
        if (CollectionUtils.isEmpty(departmentIdList)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "사용자의 사업장 정보가 없습니다.", false);
        }

        if (Objects.nonNull(fromDate) && Objects.isNull(toDate)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "종료일이 없습니다.", false);
        }

        if (Objects.isNull(fromDate) && Objects.nonNull(toDate)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "시작일이 없습니다.", false);
        }

        if (Objects.nonNull(fromDate) && Objects.nonNull(toDate) && toDate.isBefore(fromDate)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "시작일이 종료일보다큽니다.", false);
        }

        return deliveryService.getMultipleProjectDeliveries(fromDate, toDate, warehouseCode);
    }

    /**
     * 사용자의 센터코드 목록 조회
     *
     * @param userInfo
     * @return
     */
    @GetMapping(WebUrl.WAREHOUSE)
    public List<String> getWarehouseCodeListOfUser(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo) {

        final List<Long> departmentIdList = userInfo.getDepartmentIdList();
        if (CollectionUtils.isEmpty(departmentIdList)) {
            return Collections.emptyList();
        }

        List<String> warehouseCodeList = new ArrayList<>();
        final Organization organization = organizationService.getOrganizationByUserId(userInfo.getId());
        final List<Department> departmentList = departmentService.getDepartmentListByDepartmentIdList(organization, departmentIdList);

        for (final Department d : departmentList) {
            final Integer departmentLevel = d.getDepartmentLevel();
            if (departmentLevel > 2) { // 이관구분별 부서는 미허용
                continue;
            }
            if (departmentLevel == 1) {
                final List<Department> childDepartmentList = departmentService.getChildDepartmentListOfDepartment(d);
                if (CollectionUtils.isNotEmpty(childDepartmentList)) {
                    for (final Department cd : childDepartmentList) {
                        if (cd.getDepartmentLevel() == 2) { // 물류센터 또는 점
                            warehouseCodeList.add(cd.getDepartmentName());
                        }
                    }
                } else {
                    warehouseCodeList.add(d.getDepartmentName());
                }
            } else {
                warehouseCodeList.add(d.getDepartmentName());
            }
        }

        return warehouseCodeList;
    }

    /**
     * [올리브영] 배달취소
     *
     * @param userInfo
     * @param deliveryCancellationDTO
     */
    @PostMapping(value = WebUrl.DELIVERIES_CANCEL)
    public void cancelDelivery(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                               @RequestBody @Valid final DeliveryCancellationDTO deliveryCancellationDTO) {

        if (Objects.isNull(userInfo)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않은 사용자입니다.", false);
        }

        deliveryController.cancelDelivery(userInfo.getId(), deliveryCancellationDTO);
    }

}
