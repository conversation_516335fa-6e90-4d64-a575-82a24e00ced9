package com.logisteq.tms.external.thehyundai.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logisteq.tms.common.component.ProfileManager;
import com.logisteq.tms.common.constant.CommonConstant;
import com.logisteq.tms.delivery.domain.*;
import com.logisteq.tms.delivery.domain.event.DeliveryCompletedEvent;
import com.logisteq.tms.delivery.domain.event.DeliveryFailureEvent;
import com.logisteq.tms.delivery.domain.event.TrunkLineEvent;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.service.DeliveryBasicService;
import com.logisteq.tms.delivery.service.DeliveryService;
import com.logisteq.tms.external.thehyundai.constant.TheHyundaiConstant;
import com.logisteq.tms.external.thehyundai.dto.TheHyundaiDeliveryStatusCallbackDTO;
import com.logisteq.tms.external.thehyundai.dto.TheHyundaiReqDTO;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.service.RiderService;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.service.OrganizationService;
import com.logisteq.tms.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.NotNull;
import java.net.URI;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Validated
@Service
public class TheHyundaiCallbackService {

    private final ProfileManager profileManager;
    private final UserService userService;
    private final DeliveryBasicService deliveryBasicService;
    private final DeliveryService deliveryService;
    private final OrganizationService organizationService;
    private final RiderService riderService;
    private final ObjectMapper objectMapper;

    private final boolean theHyundaiEventListenerEnabled;
    private final String deliveryStatusCallbackUrl;

    @Autowired
    public TheHyundaiCallbackService(final ProfileManager profileManager,
                                     final UserService userService,
                                     final DeliveryBasicService deliveryBasicService,
                                     final DeliveryService deliveryService,
                                     final OrganizationService organizationService,
                                     final RiderService riderService,
                                     final ObjectMapper objectMapper,
                                     @Value("${event-listener.thehyundai.enabled: true}") final boolean theHyundaiEventListenerEnabled) {

        this.profileManager = profileManager;
        this.userService = userService;
        this.deliveryBasicService = deliveryBasicService;
        this.deliveryService = deliveryService;
        this.organizationService = organizationService;
        this.riderService = riderService;
        this.objectMapper = objectMapper;

        if (this.profileManager.isProdProfiles()) {
            this.deliveryStatusCallbackUrl = TheHyundaiConstant.TheHyundaiApiUrl.THEHYUNDAI_API_SERVER_PROD + TheHyundaiConstant.TheHyundaiApiUrl.DELIVERY_STATUS_CALLBACK;
        } else {
            this.deliveryStatusCallbackUrl = TheHyundaiConstant.TheHyundaiApiUrl.THEHYUNDAI_API_SERVER_DEV + TheHyundaiConstant.TheHyundaiApiUrl.DELIVERY_STATUS_CALLBACK;
        }

        this.theHyundaiEventListenerEnabled = theHyundaiEventListenerEnabled;
        if (theHyundaiEventListenerEnabled) {
            log.info("theHyundaiEventListenerEnabled is enabled.");
        } else {
            log.warn("theHyundaiEventListenerEnabled is not enabled.");
        }
    }

    /**
     * 배송상태 Callback
     *
     * @param callbackDTOList
     */
    @Async
    @Transactional(readOnly = true)
    public void postDeliveryStatusCallback(final List<TheHyundaiDeliveryStatusCallbackDTO> callbackDTOList) {

        if (CollectionUtils.isEmpty(callbackDTOList)) {
            log.warn(CommonConstant.CALLBACK_MARKER, "[postDeliveryStatusCallback] 요청 데이터가 없습니다.");
            return;
        }

        if (StringUtils.isBlank(this.deliveryStatusCallbackUrl)) {
            log.warn(CommonConstant.CALLBACK_MARKER, "[postDeliveryStatusCallback] Callback URL이 없습니다.");
            return;
        }

        final HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectionRequestTimeout(TheHyundaiConstant.HTTP_CONNECT_TIMEOUT);
        factory.setConnectTimeout(TheHyundaiConstant.HTTP_CONNECT_TIMEOUT);
        factory.setReadTimeout(TheHyundaiConstant.HTTP_READ_TIMEOUT);

        final RestTemplate restTemplate = new RestTemplate(factory);
        restTemplate.setErrorHandler(new DefaultResponseErrorHandler() {
            @Override
            public boolean hasError(HttpStatus statusCode) {
                return false;
            }
        });

        final URI uri = URI.create(this.deliveryStatusCallbackUrl);

        final HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        final TheHyundaiReqDTO reqDTO = TheHyundaiReqDTO.of(callbackDTOList);

        try {
            String reqBody = objectMapper.writeValueAsString(reqDTO);
            log.info(CommonConstant.CALLBACK_MARKER, "[postDeliveryStatusCallback] reqBody: {}", reqBody);
        } catch (JsonProcessingException e) {
            log.error(CommonConstant.CALLBACK_MARKER, "[postDeliveryStatusCallback] Req JsonProcessingException: " + e.getMessage());
        } catch (Exception e) {
            log.error(CommonConstant.CALLBACK_MARKER, "[postDeliveryStatusCallback] Req Exception: " + e.getMessage());
        }

        final HttpEntity<TheHyundaiReqDTO> requestEntity = new HttpEntity<>(reqDTO, httpHeaders);

        final ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, String.class);
        final HttpStatus httpStatus = responseEntity.getStatusCode();
        final String responseBody = responseEntity.getBody();

        if (!httpStatus.is2xxSuccessful()) {
            callbackDTOList.forEach(callbackDTO -> {
                final String slipNo = callbackDTO.getSlipNo();
                final String deliveryStatus = callbackDTO.getDeliveryStatus();
                final String statusType = callbackDTO.getStatusType();
                log.error(CommonConstant.CALLBACK_MARKER, "[postDeliveryStatusCallback] slipNo: {}, deliveryStatus: {}, statusType: {}에 대해 Callback 오류 발생 {}: {}", slipNo, deliveryStatus, statusType, httpStatus, responseBody);
            });
            // TODO: httpStatus가 2xx가 아닐 경우 Callback 재호출 ?
        } else {
            callbackDTOList.forEach(callbackDTO -> {
                final String slipNo = callbackDTO.getSlipNo();
                final String deliveryStatus = callbackDTO.getDeliveryStatus();
                final String statusType = callbackDTO.getStatusType();
                log.info(CommonConstant.CALLBACK_MARKER, "[postDeliveryStatusCallback] slipNo: {}, deliveryStatus: {}, statusType: {}에 대해 Callback 완료 {}: {}", slipNo, deliveryStatus, statusType, httpStatus, responseBody);
            });
        }
    }

    /**
     * 간선 상/하차 이벤트 리스너
     *
     * @param trunkLineEvent
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT,
            condition = "@environment.getProperty('event-listener.thehyundai.enabled')")
    @Transactional(readOnly = true)
    public void onTrunkLineChanged(@NotNull final TrunkLineEvent trunkLineEvent) {

        final Organization organization = trunkLineEvent.getOrganization();
        final Long adminUserId = userService.getAdminUserIdOfOrganization(organization);
        if (Objects.isNull(adminUserId)) {
            log.warn("TheHyundaiCallbackService::[onTrunkLineChanged] 조직 {}에 대한 조직 관리자를 찾을 수 없습니다.", organization.getOrganizationName());
            return;
        }
        if (!StringUtils.equals(TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME, organization.getCodeName())) {
            log.warn("[TheHyundaiCallbackService::onTrunkLineChanged] userId: {}는 현대백화점 관제자가 아닙니다.", adminUserId);
            return;
        }

        final TheHyundaiDeliveryStatusCallbackDTO callbackDTO = TheHyundaiDeliveryStatusCallbackDTO.of(trunkLineEvent);
        postDeliveryStatusCallback(Arrays.asList(callbackDTO));
    }

//    /**
//     * 배송출발 이벤트 리스너
//     *
//     * @param deliveryGoingEvent
//     */
//    @Async
//    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT,
//            condition = "@environment.getProperty('event-listener.thehyundai.enabled')")
//    @Transactional(readOnly = true)
//    public void onDeliveryGoing(@NotNull final DeliveryGoingEvent deliveryGoingEvent) {
//
//        final DeliveryStatus deliveryStatus = deliveryGoingEvent.getDeliveryStatus();
//        if (!DeliveryStatus.GOING.equals(deliveryStatus)) {
//            log.warn("[TheHyundaiCallbackService::onDeliveryGoing] 잘못된 배송상태 {}가 전달되었습니다.", deliveryStatus);
//            return;
//        }
//
//        final Long userId = deliveryGoingEvent.getUserId();
//        if (!organizationService.isCodeNameUser(userId, TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME)) {
//            log.warn("[TheHyundaiCallbackService::onDeliveryGoing] userId: {}는 현대백화점 관제자가 아닙니다.", userId);
//            return;
//        }
//
//        final Long deliveryId = deliveryGoingEvent.getDeliveryId();
//        final Delivery delivery = deliveryBasicService.getDeliveryInfo(deliveryId);
//        if (Objects.isNull(delivery)) {
//            log.warn("[TheHyundaiCallbackService::onDeliveryGoing] deliveryId: {} 배송이 존재하지 않습니다.", deliveryId);
//            return;
//        }
//
//        final DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocation(deliveryId).orElse(null);
//        if (Objects.isNull(deliveryAllocation)) {
//            log.warn("[TheHyundaiCallbackService::onDeliveryGoing] deliveryId: {} 배송할당이 존재하지 않습니다.", deliveryId);
//            return;
//        }
//
//        final Long riderId = deliveryAllocation.getRiderId();
//        final Rider rider = riderService.getRider(riderId);
//        final List<DeliveryAllocation> riderDeliveryAllocationList = deliveryBasicService.getRiderDeliveryAllocations(riderId, deliveryAllocation.getProjectId());
//        final long remainedDeliveryCount = riderDeliveryAllocationList.stream()
//                .filter(da -> !DeliveryStatus.isDeliveryEnded(da.getStatus()))
//                .count();
//        final DeliveryDetail deliveryDetail = delivery.getDetail();
//        final Receiver receiver = deliveryDetail.getReceiver();
//        final String recvHp = Optional.ofNullable(receiver).map(Receiver::getReceiverRealPhoneNumber).orElse("");
//        final String sendMsg = createSendMessage(receiver, deliveryDetail.getSenderInfo(), delivery.getDeliveryProducts(), deliveryGoingEvent.getEstimatedArrivalDateTime());
//        final TheHyundaiDeliveryStatusCallbackDTO callbackDTO = TheHyundaiDeliveryStatusCallbackDTO.of(deliveryGoingEvent, rider, remainedDeliveryCount, recvHp, sendMsg);
//
//        postDeliveryStatusCallback(Arrays.asList(callbackDTO));
//    }

    /**
     * 배송완료 이벤트 리스너
     *
     * @param deliveryCompletedEvent
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT,
            condition = "@environment.getProperty('event-listener.thehyundai.enabled')")
    @Transactional(readOnly = true)
    public void onDeliveryCompleted(@NotNull final DeliveryCompletedEvent deliveryCompletedEvent) {

        final DeliveryStatus deliveryStatus = deliveryCompletedEvent.getDeliveryStatus();
        if (!DeliveryStatus.COMPLETED.equals(deliveryStatus)) {
            log.warn("[TheHyundaiCallbackService::onDeliveryCompleted] 잘못된 배송상태 {}가 전달되었습니다.", deliveryStatus);
            return;
        }

        final Long userId = deliveryCompletedEvent.getUserId();
        if (!organizationService.isCodeNameUser(userId, TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME)) {
            log.warn("[TheHyundaiCallbackService::onDeliveryCompleted] userId: {}는 현대백화점 관제자가 아닙니다.", userId);
            return;
        }

        final Long deliveryId = deliveryCompletedEvent.getDeliveryId();
        final Delivery delivery = deliveryBasicService.getDeliveryInfo(deliveryId);
        if (Objects.isNull(delivery)) {
            log.warn("[TheHyundaiCallbackService::onDeliveryCompleted] deliveryId: {} 배송이 존재하지 않습니다.", deliveryId);
            return;
        }

        final DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocation(deliveryId).orElse(null);
        if (Objects.isNull(deliveryAllocation)) {
            log.warn("[TheHyundaiCallbackService::onDeliveryCompleted] deliveryId: {} 배송할당이 존재하지 않습니다.", deliveryId);
            return;
        }

        final Rider rider = riderService.getRider(deliveryAllocation.getRiderId());
        final List<String> fileUrlList = deliveryService.getDeliveryCompletedFilesURLList(deliveryAllocation, true);
        final String fileUrl = CollectionUtils.isNotEmpty(fileUrlList) ? fileUrlList.get(fileUrlList.size() - 1) : "";
        final TheHyundaiDeliveryStatusCallbackDTO callbackDTO = TheHyundaiDeliveryStatusCallbackDTO.of(deliveryCompletedEvent, rider, fileUrl);

        postDeliveryStatusCallback(Arrays.asList(callbackDTO));
    }

    /**
     * 배송실패 이벤트 리스너
     *
     * @param deliveryFailureEvent
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT,
            condition = "@environment.getProperty('event-listener.thehyundai.enabled')")
    @Transactional(readOnly = true)
    public void onDeliveryFailed(@NotNull final DeliveryFailureEvent deliveryFailureEvent) {

        final DeliveryStatus deliveryStatus = deliveryFailureEvent.getDeliveryStatus();
        if (!DeliveryStatus.FAILURE.equals(deliveryStatus)) {
            log.warn("[TheHyundaiCallbackService::onDeliveryFailed] 잘못된 배송상태 {}가 전달되었습니다.", deliveryStatus);
            return;
        }

        final Long userId = deliveryFailureEvent.getUserId();
        if (!organizationService.isCodeNameUser(userId, TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME)) {
            log.warn("[TheHyundaiCallbackService::onDeliveryFailed] userId: {}는 현대백화점 관제자가 아닙니다.", userId);
            return;
        }

        final Long deliveryId = deliveryFailureEvent.getDeliveryId();
        final Delivery delivery = deliveryBasicService.getDeliveryInfo(deliveryId);
        if (Objects.isNull(delivery)) {
            log.warn("[TheHyundaiCallbackService::onDeliveryFailed] deliveryId: {} 배송이 존재하지 않습니다.", deliveryId);
            return;
        }

        final DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocation(deliveryId).orElse(null);
        if (Objects.isNull(deliveryAllocation)) {
            log.warn("[TheHyundaiCallbackService::onDeliveryFailed] deliveryId: {} 배송할당이 존재하지 않습니다.", deliveryId);
            return;
        }

        final Rider rider = riderService.getRider(deliveryAllocation.getRiderId());
        final TheHyundaiDeliveryStatusCallbackDTO callbackDTO = TheHyundaiDeliveryStatusCallbackDTO.of(deliveryFailureEvent, rider);

        postDeliveryStatusCallback(Arrays.asList(callbackDTO));
    }

    /**
     * 배송출발 알림톡 메시지 생성
     *
     * @param receiver
     * @param senderInfo
     * @param deliveryProductList
     * @param estimatedArrivalDt
     * @return
     */
    public String createSendMessage(final Receiver receiver,
                                    final SenderInfo senderInfo,
                                    final List<DeliveryProduct> deliveryProductList,
                                    final LocalDateTime estimatedArrivalDt) {

        String receiverName = Optional.ofNullable(receiver).map(Receiver::getReceiverOwner).orElse("");
        if (StringUtils.isBlank(receiverName) && Objects.nonNull(receiver)) {
            receiverName = receiver.getReceiverName();
        }

        String senderName = "";
        if (Objects.nonNull(senderInfo)) {
            senderName = Optional.ofNullable(senderInfo.getSenderName()).orElse("");
            final String senderCompanyName = senderInfo.getSenderCompanyName();
            if (StringUtils.isNotBlank(senderCompanyName)) {
                senderName += "(" + senderCompanyName + ")";
            }
        }

        String productName = "";
        if (CollectionUtils.isNotEmpty(deliveryProductList)) {
            productName = deliveryProductList.get(0).getItemName();
        }

//        final String estimatedTimeOfArrival = Optional.ofNullable(estimatedArrivalDt)
//                .map(t -> {
//                    final String fromTime = t.toLocalTime().format(DateTimeFormatter.ofPattern("HH"));
//                    final String toTime = t.toLocalTime().plusHours(2).format(DateTimeFormatter.ofPattern("HH"));
//                    return fromTime + "시~" + toTime + "시";
//                })
//                .orElse("");

        StringBuilder sb = new StringBuilder();
        sb.append("[현대백화점 배송안내]\n\n");
        sb.append(receiverName);
        sb.append(" 님\n\n");
        sb.append(senderName);
        sb.append(" 님께서 보내신 소중한 상품이 오늘 배송 예정입니다.\n\n");
        sb.append("● 상품명 : ");
        sb.append(productName);
//        sb.append("\n");
//        sb.append("● 배송예정시간 : ");
//        sb.append(estimatedTimeOfArrival);
//        sb.append("\n\n");
//        sb.append("※배송 예정시간은 배송 상황에 따라 변경될 수 있습니다.");

        return sb.toString();
    }

}
