package com.logisteq.tms.external.thehyundai.dto;

import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.DeliveryDetail;
import com.logisteq.tms.delivery.domain.event.DeliveryCompletedEvent;
import com.logisteq.tms.delivery.domain.event.DeliveryFailureEvent;
import com.logisteq.tms.delivery.domain.event.TrunkLineEvent;
import com.logisteq.tms.delivery.domain.suppl.DeliveryCategory;
import com.logisteq.tms.delivery.domain.suppl.DeliveryCompletedType;
import com.logisteq.tms.delivery.domain.suppl.DeliveryFailureType;
import com.logisteq.tms.external.etc.utils.CustomerUtil;
import com.logisteq.tms.external.thehyundai.constant.TheHyundaiConstant;
import com.logisteq.tms.rider.domain.Rider;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Getter
@Setter
@Builder
public class TheHyundaiDeliveryStatusCallbackDTO {

    // TMS의 관리 ID (= deliveryId를 문자열로 전달)
    @NotNull(message = "orderId is not null.")
    private String orderId;

    // 현대백화점 주문번호
    @NotNull(message = "slipNo is not null.")
    private String slipNo;

    // 배송상태 대구분
    // 01: 배송출발, 02: 배송완료, 03: 배송실패, 04: 간선상차, 05: 간선하차
    @NotNull(message = "deliveryStatus is not null.")
    private String deliveryStatus;

    // 배송상태 중구분
    @NotNull(message = "statusType is not null.")
    private String statusType;

    // 배송상태 소구분
    private String remark;

    // 변경일자
    @NotNull(message = "changeDt is not null.")
    private String changeDt;

    // 변경시각
    @NotNull(message = "changeDs is not null.")
    private String changeDs;

    // 기사명
    @NotNull(message = "driverNm is not null.")
    private String driverNm;

    // 기사연락처
    @NotNull(message = "driverHp is not null.")
    private String driverHp;

    // 수신인 실연락처
    private String recvHp;

    // 알림톡 내용
    private String sendMsg;

    /**
     * 간선 상/하차시 Callback 요청 데이터 생성
     *
     * @param trunkLineEvent
     * @return
     */
    public static TheHyundaiDeliveryStatusCallbackDTO of(@NotNull final TrunkLineEvent trunkLineEvent) {

        return TheHyundaiDeliveryStatusCallbackDTO.builder()
                .orderId("")
                .slipNo(trunkLineEvent.getSlipNo())
                .deliveryStatus(trunkLineEvent.getDeliveryStatus())
                .statusType(trunkLineEvent.getStatusType())
                .remark("")
                .changeDt(trunkLineEvent.getChangeDt())
                .changeDs(trunkLineEvent.getChangeDs())
                .driverNm(trunkLineEvent.getDriverNm())
                .driverHp(trunkLineEvent.getDriverHp())
                .recvHp("")
                .sendMsg("")
                .build();
    }

    /**
     * 배송출발시 Callback 요청 데이터 생성
     *
     * @param delivery
     * @param rider
     * @param orderNum
     * @param recvHp
     * @param sendMsg
     * @param estimatedArrivalDt
     * @param goingDt
     * @return
     */
    public static TheHyundaiDeliveryStatusCallbackDTO of(@NotNull final Delivery delivery,
                                                         final Rider rider,
                                                         final Integer orderNum,
                                                         final String recvHp,
                                                         final String sendMsg,
                                                         final LocalDateTime estimatedArrivalDt,
                                                         final LocalDateTime goingDt) {

        final DeliveryDetail deliveryDetail = delivery.getDetail();
        final String orderId = delivery.getId().toString();
        final String slipNo = CustomerUtil.extractOrganizationCodeAndUniqueCode(deliveryDetail.getCustomerOrderId()).getRight();
        final String eta = Optional.ofNullable(estimatedArrivalDt).map(t -> t.toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm"))).orElse("");
        final String changeDt = Optional.ofNullable(goingDt).map(t -> t.format(DateTimeFormatter.ofPattern("yyyyMMdd"))).orElse("");
        final String changeDs = Optional.ofNullable(goingDt).map(t -> t.toLocalTime().format(DateTimeFormatter.ofPattern("HHmmss"))).orElse("");
        final DeliveryCategory deliveryCategory = deliveryDetail.getDeliveryCategory();
        String deliveryStatus = TheHyundaiConstant.TheHyundaiDeliveryStatus.DEPARTURED;
        if (deliveryCategory.isShortDistance()) {
            deliveryStatus = TheHyundaiConstant.TheHyundaiDeliveryStatus.SD_DEPARTURED;
        }

        return TheHyundaiDeliveryStatusCallbackDTO.builder()
                .orderId(orderId)
                .slipNo(slipNo)
                .deliveryStatus(deliveryStatus)
                .statusType(eta)
                .remark(String.valueOf(orderNum))
                .changeDt(changeDt)
                .changeDs(changeDs)
                .driverNm(Optional.ofNullable(rider).map(Rider::getName).orElse(""))
                .driverHp(Optional.ofNullable(rider).map(Rider::getMobile).orElse(""))
                .recvHp(StringUtils.isNotBlank(recvHp) ? recvHp : "")
                .sendMsg(sendMsg)
                .build();
    }

    /**
     * 배송완료시 Callback 요청 데이터 생성
     *
     * @param deliveryCompletedEvent
     * @param rider
     * @param fileUrl
     * @return
     */
    public static TheHyundaiDeliveryStatusCallbackDTO of(@NotNull final DeliveryCompletedEvent deliveryCompletedEvent,
                                                         final Rider rider,
                                                         final String fileUrl) {

        final String orderId = deliveryCompletedEvent.getDeliveryId().toString();
        final String slipNo = CustomerUtil.extractOrganizationCodeAndUniqueCode(deliveryCompletedEvent.getCustomerOrderId()).getRight();
        final LocalDateTime completedDt = deliveryCompletedEvent.getDeliveryCompletedDt();
        final DeliveryCompletedType deliveryCompletedType = deliveryCompletedEvent.getDeliveryCompletedType();
        final String deliveryCompletedMessage = deliveryCompletedEvent.getDeliveryCompletedMessage();
        final String statusType = convertStatusType(deliveryCompletedType);
        final String remark = DeliveryCompletedType.OTHER.equals(deliveryCompletedType) ? deliveryCompletedMessage + "%" + fileUrl : fileUrl;
        final String changeDt = Optional.ofNullable(completedDt).map(t -> t.format(DateTimeFormatter.ofPattern("yyyyMMdd"))).orElse("");
        final String changeDs = Optional.ofNullable(completedDt).map(t -> t.toLocalTime().format(DateTimeFormatter.ofPattern("HHmmss"))).orElse("");

        return TheHyundaiDeliveryStatusCallbackDTO.builder()
                .orderId(orderId)
                .slipNo(slipNo)
                .deliveryStatus(TheHyundaiConstant.TheHyundaiDeliveryStatus.DELIVERED)
                .statusType(statusType)
                .remark(Optional.ofNullable(remark).orElse(""))
                .changeDt(changeDt)
                .changeDs(changeDs)
                .driverNm(Optional.ofNullable(rider).map(Rider::getName).orElse(""))
                .driverHp(Optional.ofNullable(rider).map(Rider::getMobile).orElse(""))
                .recvHp("")
                .sendMsg("")
                .build();
    }

    /**
     * 배송실패시 Callback 요청 데이터 생성
     *
     * @param deliveryFailureEvent
     * @param rider
     * @return
     */
    public static TheHyundaiDeliveryStatusCallbackDTO of(@NotNull final DeliveryFailureEvent deliveryFailureEvent,
                                                         final Rider rider) {

        final String orderId = deliveryFailureEvent.getDeliveryId().toString();
        final String slipNo = CustomerUtil.extractOrganizationCodeAndUniqueCode(deliveryFailureEvent.getCustomerOrderId()).getRight();
        final LocalDateTime failureDt = deliveryFailureEvent.getDeliveryFailureDt();
        final DeliveryFailureType deliveryFailureType = deliveryFailureEvent.getDeliveryFailureType();
        final String deliveryFailureMessage = deliveryFailureEvent.getDeliveryFailureMessage();
        final String statusType = convertStatusType(deliveryFailureType);
        final String changeDt = Optional.ofNullable(failureDt).map(t -> t.format(DateTimeFormatter.ofPattern("yyyyMMdd"))).orElse("");
        final String changeDs = Optional.ofNullable(failureDt).map(t -> t.toLocalTime().format(DateTimeFormatter.ofPattern("HHmmss"))).orElse("");

        return TheHyundaiDeliveryStatusCallbackDTO.builder()
                .orderId(orderId)
                .slipNo(slipNo)
                .deliveryStatus(TheHyundaiConstant.TheHyundaiDeliveryStatus.FAILED)
                .statusType(statusType)
                .remark(Optional.ofNullable(deliveryFailureMessage).orElse(""))
                .changeDt(changeDt)
                .changeDs(changeDs)
                .driverNm(Optional.ofNullable(rider).map(Rider::getName).orElse(""))
                .driverHp(Optional.ofNullable(rider).map(Rider::getMobile).orElse(""))
                .recvHp("")
                .sendMsg("")
                .build();
    }

    /**
     * 배송완료, 배송실패시 중구분 변환
     *
     * @param type
     * @return
     */
    public static String convertStatusType(final Object type) {

        String statusType = "";
        if (type instanceof DeliveryCompletedType) {
            final DeliveryCompletedType deliveryCompletedType = (DeliveryCompletedType) type;

            switch (deliveryCompletedType) {
                case NORMAL_DELIVERY_CONTACT:
                    statusType = "대면배송";
                    break;
                case DOOR:
                    statusType = "비대면배송 (문앞)";
                    break;
                case SECURITY_OFFICE:
                    statusType = "비대면배송 (경비실)";
                    break;
                case STORAGE_BOX:
                    statusType = "비대면배송 (택배보관함)";
                    break;
                case OTHER:
                    statusType = "기타";
                    break;
                default:
                    break;
            }
        } else if (type instanceof DeliveryFailureType) {
            final DeliveryFailureType deliveryFailureType = (DeliveryFailureType) type;

            switch (deliveryFailureType) {
                case WRONG_ADDR:
                    statusType = "주소불명";
                    break;
                case NOBODY_HOME:
                    statusType = "고객부재";
                    break;
                case UNVISIT:
                    statusType = "미방문";
                    break;
                case POSTPONEMENT:
                    statusType = "배송연기";
                    break;
                case DELIVERY_CANCELLATION:
                    statusType = "배송취소";
                    break;
                default:
                    break;
            }
        }

        return statusType;
    }

}
