package com.logisteq.tms.external.glovis.domain;

import com.logisteq.tms.external.glovis.dto.GlovisDeliveryCodeDTOVer1;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @created 2021-08-24
 * @project tms-service
 */
@Deprecated
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(indexes = @Index(columnList = "zipCode"))
@EntityListeners(value = {AuditingEntityListener.class})
@Entity
public class GlovisDeliveryCodeVer1 {

    // Hibernate disables insert batching at the JDBC level transparently
    // if you use an identity identifier generator.
    // https://www.baeldung.com/hibernate-identifiers
    @SequenceGenerator(name = "GlovisDeliveryCodeVer1SequenceGenerator",
            sequenceName = "GlovisDeliveryCodeVer1Sequence",
            initialValue = 1,
            // 아래 allocationSize의 값은 application.yml의 spring.jpa.properties.hibernate.jdbc.batch_size와 일치해야하는 듯.
            allocationSize = 1000
    )
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GlovisDeliveryCodeVer1SequenceGenerator")
    private Long id;

    // 우편번호
    @Column
    private String zipCode;

    // 시
    @Column
    private String si;

    // 구
    @Column
    private String gu;

    // 동
    @Column
    private String dong;

    // 집배소요
    @Column
    private Long postTime;

    // 센터코드
    @Column
    private String centerCode;

    // 센터명
    @Column
    private String centerName;

    // 터미널코드
    @Column
    private String terminalCode;

    // 터미널명
    @Column
    private String terminalName;

    // 집배구역
    @Column
    private String postRegionCode;

    // 동미만주소
    @Column
    private String detailAddr;

    // 중분류코드
    @Column
    private String middleCategoryCode;

    // 소분류코드
    @Column
    private String subCategoryCode;

    // 집배구역명
    @Column
    private String postRegionName;

    // 성명
    @Column
    private String name;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    public static GlovisDeliveryCodeVer1 of(final GlovisDeliveryCodeDTOVer1 dto) {

        final GlovisDeliveryCodeVer1 deliveryCode = GlovisDeliveryCodeVer1.builder()
                .zipCode(dto.getZipCode())
                .si(dto.getSi())
                .gu(dto.getGu())
                .dong(dto.getDong())
                .postTime(dto.getPostTime())
                .centerCode(dto.getCenterCode())
                .centerName(dto.getCenterName())
                .terminalCode(dto.getTerminalCode())
                .terminalName(dto.getTerminalName())
                .postRegionCode(dto.getPostRegionCode())
                .detailAddr(dto.getDetailAddr())
                .middleCategoryCode(dto.getMiddleCategoryCode())
                .subCategoryCode(dto.getSubCategoryCode())
                .postRegionName(dto.getPostRegionName())
                .name(dto.getName())
                .build();

        return deliveryCode;
    }

}
