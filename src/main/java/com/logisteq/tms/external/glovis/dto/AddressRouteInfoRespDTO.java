package com.logisteq.tms.external.glovis.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class AddressRouteInfoRespDTO {

    // Response Data

    // 우편번호 ("13609")
    private String zipCode;

    // 주소 ("경기도 성남시 분당구 정자동 50-10")
    private String address;

    // 출하센터 ("WE01")
    // TC코드 : YI, GJ, IS, IC
    private String deliveryWarehouse;

    // 출하센터명 ("센터명")
    // TC명 : 용인TC, 광주TC, 일산TC, 인천TC
    private String deliveryWarehouseName;

    // 분류코드 ("IS-A-999")
    // TC코드 + 중분류코드 + 소분류코드 조합
    // TC코드 : YI, GJ, IS, IC (용인, 광주, 일산, 인천)
    // 중분류코드 : TC내 작업라인(A or B) 분류 기준
    // 소분류코드 : 기사마다 부여되는 고유 코드 (01~999)
    private String deliveryDestributionCode;

    // 권역코드 ("RU1")
    // v1.9에서 삭제됨
//    private String deliveryRegionCode;

    // 배송원명 ("홍길동")
    // 추후 글로비스에서 전달할 문서에 정보 추가 예정. 그 전까지 없으면 공백으로 리턴
    private String deliveryPersonName;

    // 약식주소 : 권역명 (글로비스에서 설정한 권역명)
    // 집배권역
    private String shortAddress;

    // 집배소코드
    // 협력사코드 : 0001, 0002
    private String partnerCode;

    // 집배소명
    // 협력사명 : 재성, 퓨처피플
    private String partnerName;
}
