package com.logisteq.tms.external.oliveyoung.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OliveYoung24hOrderDataRespDTO {

    // 올리브영 주문번호
    @JsonProperty("oyOrdNo")
    private String oyOrdNo;

    // 매장코드
    @JsonProperty("strNo")
    private String strNo;

    // 배송지 고객명
    @JsonProperty("rmitNm")
    private String rmitNm;

    // 배송지 고객 연락처
    @JsonProperty("rmitCellNo")
    private String rmitCellNo;

    // 배송지 우편번호
    @JsonProperty("rmitPostNo")
    private String rmitPostNo;

    // 배송지 기본주소(도로명)
    @JsonProperty("stnmRmitPostAddr")
    private String stnmRmitPostAddr;

    // 배송지 상세주소(도로명)
    @JsonProperty("stnmRmitPostDtlAddr")
    private String stnmRmitPostDtlAddr;

    // 배송지 기본주소(지번)
    @JsonProperty("rmitPostAddr")
    private String rmitPostAddr;

    // 배송지 상세주소(지번)
    @JsonProperty("rmitPostDtlAddr")
    private String rmitPostDtlAddr;

    // 배송 예정일시
    @JsonProperty("dlvSchdlDt")
    private String dlvSchdlDt; // yyyy-MM-dd HH:mm:ss

    // 배송 요청 메세지
    @JsonProperty("dlvpMemoCont")
    private String dlvpMemoCont;

    // 공동현관 비밀번호
    @JsonProperty("visitTypeDesc")
    private String visitTypeDesc;

    // 픽업시퀀스
    @JsonProperty("pickupSeq")
    private String pickupSeq;

    // 배송대행사 관리번호
    @JsonProperty("dlvCorpNo")
    private String dlvCorpNo;

    // 운송장번호
    @JsonProperty("invNo")
    private String invNo;

    // 주문 총 금액
    @JsonProperty("ordTotalPrc")
    private Long ordTotalPrc;

}
