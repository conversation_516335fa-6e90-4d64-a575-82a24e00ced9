package com.logisteq.tms.external.oliveyoung.dto;

import com.logisteq.tms.delivery.domain.suppl.DeliveryFailureType;
import com.logisteq.tms.external.oliveyoung.constant.OliveYoungConstant;
import com.logisteq.tms.rider.domain.Rider;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OliveYoungReturningDTO extends OliveYoungNewCallbackBaseDTO {

    // 배달상태사유
    private String reason;

    // 배달상태비고
    private String remark;

    // 라이더명
    private String riderName;

    // 라이더전화번호
    private String riderPhoneNo;

    // 추가정보
    private String addedInfo;

    public static OliveYoungReturningDTO of(@NotBlank final String ordNo,
                                            @NotBlank final String changeTime,
                                            final String reason,
                                            final String remark,
                                            final Rider rider,
                                            final String addedInfo) {

        OliveYoungReturningDTO returningDTO = new OliveYoungReturningDTO();
        returningDTO.setDeliveryAgentId(OliveYoungConstant.OY_DELIVERY_AGENT_ID);
        returningDTO.setDeliveryAgentName(OliveYoungConstant.OY_DELIVERY_AGENT_NAME);
        returningDTO.setOrdNo(ordNo);
        returningDTO.setChangeTime(changeTime);
        returningDTO.setReason(Optional.ofNullable(reason).orElse(DeliveryFailureType.OTHER.getTypeString()));
        returningDTO.setRemark(Optional.ofNullable(remark).orElse(""));
        returningDTO.setRiderName(Optional.ofNullable(rider).map(Rider::getName).orElse(""));
        returningDTO.setRiderPhoneNo(Optional.ofNullable(rider).map(Rider::getMobile).orElse(""));
        returningDTO.setAddedInfo(addedInfo);

        return returningDTO;
    }

}
