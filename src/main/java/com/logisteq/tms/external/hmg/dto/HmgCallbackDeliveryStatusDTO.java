package com.logisteq.tms.external.hmg.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.common.util.DateTimeUtil;
import com.logisteq.tms.delivery.domain.event.DeliveryCompletedEvent;
import com.logisteq.tms.delivery.domain.event.DeliveryGoingEvent;
import com.logisteq.tms.delivery.domain.event.DeliveryUnloadingStartedEvent;
import com.logisteq.tms.external.etc.utils.CustomerUtil;
import com.logisteq.tms.external.hmg.constant.HmgConstant;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @created 2021-06-03
 * @project tms-service
 */
@Getter
@Setter
@Builder
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class HmgCallbackDeliveryStatusDTO {

    // 현대백화점 주문아이디
    @NotBlank(message = "현대백화점 주문아이디가 비어있습니다.")
    private String orderAgencyOrderId;

    // 상점아이디
    @NotBlank(message = "상점아이디가 비어있습니다.")
    private String orderAgencyStoreId;

    // 배달대행사 아이디
    @NotBlank(message = "배달대행사 아이디가 비어있습니다.")
    private String deliveryAgencyId;

    // 배달상태
    @NotBlank(message = "배달상태가 비어있습니다.")
    private String deliveryStatus;

    // 배달상태 변경일시
    @NotNull(message = "배달상태 변경일시가 없습니다.")
    private Long changedAt;

    // 할증요금 목록
    private List<?> extraCharges;

    // 비대면 배달 요청시 사진 URL 첨부
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String dropImageUrl;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE, false, false, false, null);
    }

    public static HmgCallbackDeliveryStatusDTO of(@NotNull final DeliveryGoingEvent deliveryGoingEvent,
                                                  @NotEmpty final String theHyundaiAgencyStoreId) {

        final String orderAgencyOrderId = CustomerUtil.extractOrganizationCodeAndUniqueCode(deliveryGoingEvent.getCustomerOrderId()).getRight();
        final String deliveryAgencyId = HmgConstant.THE_HYUNDAI_DELIVERY_AGENCY_ID;
        final Long changedAt = DateTimeUtil.convertLocalDateTimeToEpochMillis(deliveryGoingEvent.getRealGoingDateTime());

        return HmgCallbackDeliveryStatusDTO.builder()
                .orderAgencyOrderId(orderAgencyOrderId)
                .orderAgencyStoreId(theHyundaiAgencyStoreId)
                .deliveryAgencyId(deliveryAgencyId)
                .deliveryStatus("ALLOCATED")
                .changedAt(changedAt)
                .extraCharges(Collections.emptyList())
                .build();
    }

    public static HmgCallbackDeliveryStatusDTO of(@NotNull final DeliveryUnloadingStartedEvent deliveryUnloadingStartedEvent,
                                                  @NotEmpty final String theHyundaiAgencyStoreId) {

        final String orderAgencyOrderId = CustomerUtil.extractOrganizationCodeAndUniqueCode(deliveryUnloadingStartedEvent.getCustomerOrderId()).getRight();
        final String deliveryAgencyId = HmgConstant.THE_HYUNDAI_DELIVERY_AGENCY_ID;
        final Long changedAt = DateTimeUtil.convertLocalDateTimeToEpochMillis(deliveryUnloadingStartedEvent.getStartUnloadingDateTime());

        return HmgCallbackDeliveryStatusDTO.builder()
                .orderAgencyOrderId(orderAgencyOrderId)
                .orderAgencyStoreId(theHyundaiAgencyStoreId)
                .deliveryAgencyId(deliveryAgencyId)
                .deliveryStatus("PICKUP_FINISHED")
                .changedAt(changedAt)
                .extraCharges(Collections.emptyList())
                .build();
    }

    public static HmgCallbackDeliveryStatusDTO of(@NotNull final DeliveryCompletedEvent deliveryCompletedEvent,
                                                  @NotEmpty final String theHyundaiAgencyStoreId,
                                                  final List<String> completedFileURLList) {

        final String orderAgencyOrderId = CustomerUtil.extractOrganizationCodeAndUniqueCode(deliveryCompletedEvent.getCustomerOrderId()).getRight();
        final String deliveryAgencyId = HmgConstant.THE_HYUNDAI_DELIVERY_AGENCY_ID;
        final Long changedAt = DateTimeUtil.convertLocalDateTimeToEpochMillis(deliveryCompletedEvent.getDeliveryCompletedDt());
        final String dropImageUrl = CollectionUtils.lastElement(completedFileURLList);

        return HmgCallbackDeliveryStatusDTO.builder()
                .orderAgencyOrderId(orderAgencyOrderId)
                .orderAgencyStoreId(theHyundaiAgencyStoreId)
                .deliveryAgencyId(deliveryAgencyId)
                .deliveryStatus("DROP_FINISHED")
                .changedAt(changedAt)
                .extraCharges(Collections.emptyList())
                .dropImageUrl(dropImageUrl)
                .build();
    }

}