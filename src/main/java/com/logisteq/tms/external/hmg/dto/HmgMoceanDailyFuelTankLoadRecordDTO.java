package com.logisteq.tms.external.hmg.dto;

import com.logisteq.common.component.excel.annotation.PxlColumn;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.math3.util.Precision;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 차량별 일별 평균온도 정보
 *
 * <AUTHOR>
 * @created 2021-08-30
 * @project tms-service
 */
@Getter
@Builder
public class HmgMoceanDailyFuelTankLoadRecordDTO {

    // 차량번호
    @NotNull
    @PxlColumn(name = "차량번호", importEnabled = false, exportColumnWidth = 3500)
    private String licensePlate;

    // 발생일
    @NotNull
    @PxlColumn(name = "발생일", importEnabled = false, exportColumnWidth = 3000)
    private LocalDate eventDate;

    // 연료잔량(충전잔량), 단위:%
    @NotNull
    @PxlColumn(name = "연료잔량(%)", importEnabled = false, exportColumnWidth = 3000)
    private Double averageFuelTankLoad;

    public static HmgMoceanDailyFuelTankLoadRecordDTO of(final String licensePlate,
                                                         final LocalDate eventDate,
                                                         final Double averageFuelTankLoad) {

        return HmgMoceanDailyFuelTankLoadRecordDTO.builder()
                .licensePlate(licensePlate)
                .eventDate(eventDate)
                .averageFuelTankLoad(Precision.round(averageFuelTankLoad, 1))
                .build();
    }

}
