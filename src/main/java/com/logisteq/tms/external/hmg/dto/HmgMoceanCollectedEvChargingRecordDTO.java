package com.logisteq.tms.external.hmg.dto;

import com.logisteq.common.component.excel.annotation.PxlColumn;
import com.logisteq.common.component.excel.styler.title.PxlTitleWrapTextStyler;
import com.logisteq.common.feign.mocean.dto.StatusVehicleDTO;
import lombok.Builder;
import lombok.Getter;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 차량별 EV충전정보
 *
 * <AUTHOR>
 * @created 2021-08-30
 * @project tms-service
 */
@Getter
@Builder
public class HmgMoceanCollectedEvChargingRecordDTO {

    // 차량번호
    @NotNull
    @PxlColumn(name = "차량번호", importEnabled = false, exportColumnWidth = 3500)
    private String licensePlate;

    // 발생시간, ISO-8601
    @NotNull
    @PxlColumn(name = "발생시간", importEnabled = false, exportColumnWidth = 5000, exportPattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime eventTime;

    // EV 충전중 여부 상태
    // 0 (discharging), 1 (charging)
    @NotNull
    @PxlColumn(name = "EV 충전중\n여부 상태", importEnabled = false, exportColumnMandatoryTitleCellStyler = PxlTitleWrapTextStyler.class, exportColumnWidth = 2500)
    private String charging;

    // EV 충전기 연결 상태
    // 0 (discharging), 1 (charging)
    @NotNull
    @PxlColumn(name = "EV 충전기\n연결 상태", importEnabled = false, exportColumnMandatoryTitleCellStyler = PxlTitleWrapTextStyler.class, exportColumnWidth = 2500)
    private String chargerConnection;

    // EV 충전 상태
    // 0 (완속), 1 (고속)
    @NotNull
    @PxlColumn(name = "EV 충전\n상태", importEnabled = false, exportColumnMandatoryTitleCellStyler = PxlTitleWrapTextStyler.class, exportColumnWidth = 2000)
    private String evCharging;

    // EV 충전 잔여시간 - 고속, 단위:분
    @NotNull
    @PxlColumn(name = "EV 충전\n잔여시간 - 고속(분)", importEnabled = false, exportColumnMandatoryTitleCellStyler = PxlTitleWrapTextStyler.class, exportColumnWidth = 4500)
    private Double fastChargingRemainTime;

    // EV 충전 잔여시간 - 완속, 단위:분
    @NotNull
    @PxlColumn(name = "EV 충전\n잔여시간 - 완속(분)", importEnabled = false, exportColumnMandatoryTitleCellStyler = PxlTitleWrapTextStyler.class, exportColumnWidth = 4500)
    private Double slowChargingRemainTime;

    public static HmgMoceanCollectedEvChargingRecordDTO of(final StatusVehicleDTO statusVehicleDTO) {

        final String charging = "1".equals(statusVehicleDTO.getCharging()) ? "ON" : "OFF";
        final String chargerConnection = "1".equals(statusVehicleDTO.getChargerConnection()) ? "ON" : "OFF";
        final String evCharging = "1".equals(statusVehicleDTO.getEvCharging()) ? "고속" : "완속";

        return HmgMoceanCollectedEvChargingRecordDTO.builder()
                .licensePlate(statusVehicleDTO.getLicensePlate())
                .eventTime(statusVehicleDTO.getEventTime())
                .charging(charging)
                .chargerConnection(chargerConnection)
                .evCharging(evCharging)
                .fastChargingRemainTime(statusVehicleDTO.getFastChargingRemainTime())
                .slowChargingRemainTime(statusVehicleDTO.getSlowChargingRemainTime())
                .build();
    }

}
