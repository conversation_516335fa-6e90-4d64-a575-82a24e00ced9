package com.logisteq.tms.external.hmg.dto;

import com.logisteq.common.component.excel.annotation.PxlSheet;
import com.logisteq.common.component.excel.annotation.PxlWorkbook;
import com.logisteq.common.component.excel.annotation.PxlWorkbookName;
import com.logisteq.common.component.excel.constant.PxlFileFormat;
import com.logisteq.common.component.excel.styler.title.PxlTitleHorizontalCenterTextStyler;
import lombok.Builder;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @created 2021-08-30
 * @project tms-service
 */
@Builder
@PxlWorkbook(exportFileFormat = PxlFileFormat.SXSSF, exportWorkbookMandatoryTitleCellStyler = PxlTitleHorizontalCenterTextStyler.class)
public class HmgMoceanOverallTemperatureExcelDTO {

    @NotBlank
    @PxlWorkbookName
    private String workbookName;

    @Valid
    @PxlSheet(name = "차량별 수집된 온도", importEnabled = false)
    private List<HmgMoceanCollectedTemperatureRecordDTO> collectedTemperatureList;

    @Valid
    @PxlSheet(name = "차량별 일별 평균온도", importEnabled = false)
    private List<HmgMoceanDailyTemperatureRecordDTO> dailyTemperatureList;

    @Valid
    @PxlSheet(name = "차량별 시간대별 평균온도", importEnabled = false)
    private List<HmgMoceanHourlyTemperatureRecordDTO> hourlyTemperatureList;

}
