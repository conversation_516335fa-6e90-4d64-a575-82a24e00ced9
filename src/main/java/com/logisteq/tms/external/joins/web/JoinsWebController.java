package com.logisteq.tms.external.joins.web;

import com.logisteq.common.exception.CustomException;
import com.logisteq.tms.common.component.ProfileManager;
import com.logisteq.tms.external.joins.domain.DailyDelificate;
import com.logisteq.tms.external.joins.domain.Delificate;
import com.logisteq.tms.external.joins.repository.DailyDelificateRepository;
import com.logisteq.tms.external.joins.repository.DelificateRepository;
import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.service.ProjectBasicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Objects;

@Slf4j
@Validated
@Controller
public class JoinsWebController {

    private final DelificateRepository delificateRepository;
    private final DailyDelificateRepository dailyDelificateRepository;
    private final ProjectBasicService projectBasicService;
    private final String mode;

    @Autowired
    public JoinsWebController(final ProfileManager profileManager,
                              final DelificateRepository delificateRepository,
                              final DailyDelificateRepository dailyDelificateRepository,
                              final ProjectBasicService projectBasicService) {

        this.delificateRepository = delificateRepository;
        this.dailyDelificateRepository = dailyDelificateRepository;
        this.projectBasicService = projectBasicService;
        this.mode = profileManager.isDemoProfiles() ? ProfileManager.DEMO : ProfileManager.PROD;
    }

    /**
     * Joins 제공용 deep link API
     * Map과 Left-Panel 포함하는 페이지 반환
     * https://aloa-dev.logisteq.com:8181/rider-main?sendDate=20201201&riderMobile=01012345678
     *
     * @param sendDate
     * @param riderMobile
     * @param model
     * @return
     */
    @GetMapping("/rider-main")
    public String riderMain(@RequestParam @NotBlank final String sendDate,
                            @RequestParam @NotBlank final String riderMobile,
                            @NotNull final Model model) {

        log.info("riderMain(): sendDate={}, riderMobile={}", sendDate, riderMobile);

        final MultiValueMap<String, Long> platformInfo = this.getAloaPlatformInfo(sendDate, riderMobile);
        if (Objects.isNull(platformInfo)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "해당 정보를 찾을 수 없습니다. 입력 파라미터를 다시 확인해주세요.", false);
        }

        final List<Long> projectId = platformInfo.get("projectId");
        final List<Long> riderId = platformInfo.get("riderId");
        final List<Long> vehicleId = platformInfo.get("vehicleId");

        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId.get(0));

        if (project.getDoneDateTime() == null) {
            log.error("프로젝트가 완료되지 않았습니다. projectId : {} ", projectId.get(0));
            throw new CustomException(HttpStatus.BAD_REQUEST, project.getName() + " 프로젝트가 완료되지 않아 제공할 수 없습니다.", false);
        }

        model.addAttribute("projectId", projectId);
        model.addAttribute("riderId", riderId);
        model.addAttribute("vehicleId", vehicleId);

        log.info("riderMain(): projectId={}, riderId={}, vehicleId={}", projectId, riderId, vehicleId);

        return mode + "/rider/rider_main"; // rider/rider_main.html
    }

    /**
     * Joins 제공용 deep link API
     * Left-Panel 제외하고 Map만 포함하는 페이지 반환
     * https://aloa-dev.logisteq.com:8181/rider-map-link?sendDate=20201201&riderMobile=01012345678
     *
     * @param sendDate
     * @param riderMobile
     * @param model
     * @return
     */
    @GetMapping("/rider-map-link")
    public String riderMap(@RequestParam @NotBlank final String sendDate,
                           @RequestParam @NotBlank final String riderMobile,
                           @RequestParam(required = false) final Boolean skipCheck, // 디버깅용 파라미터, true를 주면 프로젝트 완료 여부 체크하지 않는다.
                           @NotNull final Model model) {

        log.info("riderMap(): sendDate={}, riderMobile={}", sendDate, riderMobile);

        final MultiValueMap<String, Long> platformInfo = this.getAloaPlatformInfo(sendDate, riderMobile);
        if (Objects.isNull(platformInfo)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "해당 정보를 찾을 수 없습니다. 입력 파라미터를 다시 확인해주세요.", false);
        }

        final Long projectId = platformInfo.get("projectId").get(0);
        final Long riderId = platformInfo.get("riderId").get(0);
        final Long vehicleId = platformInfo.get("vehicleId").get(0);

        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);

        if (!Boolean.TRUE.equals(skipCheck)) {
            if (project.getDoneDateTime() == null) {
                log.error("프로젝트가 완료되지 않았습니다. projectId : {} ", projectId);
                throw new CustomException(HttpStatus.BAD_REQUEST, project.getName() + " 프로젝트가 완료되지 않아 제공할 수 없습니다.", false);
            }
        }

        model.addAttribute("projectId", projectId);
        model.addAttribute("riderId", riderId);
        model.addAttribute("vehicleId", vehicleId);

        log.info("riderMap(): projectId={}, riderId={}, vehicleId={}", projectId, riderId, vehicleId);

        return mode + "/rider/map"; // rider/map.html
    }

    /**
     * Joins 제공용 deep link API
     * https://aloa-dev.logisteq.com:8181/rider-map?projectId=2693&riderId=51&vehicleId=1
     * (실제 Joins에 노출하는 url은 아니고, rider_main.html에서 이 url을 iframe으로 감싸고 있다.)
     *
     * @param projectId
     * @param riderId
     * @param vehicleId
     * @param model
     * @return
     */
    @GetMapping("/rider-map")
    public String riderMap(@RequestParam @NotNull @Positive final Long projectId,
                           @RequestParam @NotNull @Positive final Long riderId,
                           @RequestParam @NotNull @Positive final Long vehicleId,
                           @NotNull final Model model) {

        model.addAttribute("projectId", projectId);
        model.addAttribute("riderId", riderId);
        model.addAttribute("vehicleId", vehicleId);

        return mode + "/rider/map"; // rider/map.html
    }

    private MultiValueMap<String, Long> getAloaPlatformInfo(@NotBlank final String sendDate,
                                                            @NotBlank final String riderMobile) {

        final DailyDelificate dailyDelificate = dailyDelificateRepository.findTopBySendDateOrderByCreatedAtDesc(sendDate);
/*
        final String projectName = DailyDelificateDTO.makeProjectName(sendDate);
        final DailyDelificate dailyDelificate = dailyDelificateRepository.findTopByAloaProjectNameIsStartingWithOrderByCreatedAtDesc(projectName);
*/

        if (Objects.nonNull(dailyDelificate)) {
            final List<Delificate> delificateList = delificateRepository.findByAloaProjectIdAndRiderMobile(dailyDelificate.getAloaProjectId(), riderMobile);

            if (CollectionUtils.isNotEmpty(delificateList)) {
                final Delificate delificate = delificateList.get(0);
                final Long projectId = delificate.getAloaProjectId();
                final Long riderId = delificate.getAloaRiderId();
                final Long vehicleId = delificate.getAloaVehicleId();

                if (Objects.isNull(projectId) || Objects.isNull(riderId) || Objects.isNull(vehicleId)) {
                    return null;
                }

                final MultiValueMap<String, Long> platformInfo = new LinkedMultiValueMap<>();
                platformInfo.add("projectId", projectId);
                platformInfo.add("riderId", riderId);
                platformInfo.add("vehicleId", vehicleId);

                return platformInfo;
            }
        }

        return null;
    }

}
