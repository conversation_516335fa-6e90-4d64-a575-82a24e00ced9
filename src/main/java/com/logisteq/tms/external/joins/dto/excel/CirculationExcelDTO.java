package com.logisteq.tms.external.joins.dto.excel;

import com.logisteq.common.component.excel.annotation.PxlColumn;
import lombok.Builder;
import lombok.Setter;

/**
 * <AUTHOR>
 * @created 2021-01-21
 * @project tms-service
 */
@Setter
@Builder
public class CirculationExcelDTO {

    @PxlColumn(name = "날짜")
    private String sendDate;

    @PxlColumn(name = "면")
    private String pageName;

    @PxlColumn(name = "코스번호")
    private Integer courNum;

    @PxlColumn(name = "코스명")
    private String courName;

    @PxlColumn(name = "차량번호")
    private String licensePlate;

    @PxlColumn(name = "판명")
    private String editionName;

    @PxlColumn(name = "지역판명")
    private String areaEditionName;

    @PxlColumn(name = "센터명")
    private String centerName;

    @PxlColumn(name = "매체명")
    private String midiCodeName;

    @PxlColumn(name = "소속")
    private Integer restPackCnt;

    @PxlColumn(name = "정속")
    private Integer basicPackCnt;

    @PxlColumn(name = "합계")
    private Integer totalCnt;

    @PxlColumn(name = "코스합계")
    private Integer courTotalCnt;

}
