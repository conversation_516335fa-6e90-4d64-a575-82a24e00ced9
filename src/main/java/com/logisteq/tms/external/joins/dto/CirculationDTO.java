package com.logisteq.tms.external.joins.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.external.joins.constant.DelificateConstant;
import com.logisteq.tms.external.joins.domain.Circulation;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 센터별 발행 부수
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CirculationDTO implements Comparable<CirculationDTO> {

    // 기사별 방문지 목록을 1:N 그룹핑하기 위해 기사의 식별자
    @JsonIgnore
    private String groupingKey;

    /**
     * 송달증 데이터 중 기사앱에서 보여지는 필드
     */
    // 센터명
    private String centerName;
    // 매체명
    private String midiCodeName;
    // 소속뭉치수
    private Integer restPactCnt;
    // 정속뭉치수
    private Integer basicPackCnt;
    // 총뭉치수
    private Integer totalCnt;

    /**
     * 송달증 데이터 중 기사앱에서 보여지지 않는 필드
     */
    // 센터 주소
    private String centerAddr;
    // 센터 전화번호
    private String centerTel;
    // 센터장 전화번호
    private String centerHp;
    // 센터 코드 (app에서 joins에 부수정보 api를 호출하기위해 알아야하는 정보)
    private String centerCode;
    // 분국 코드 (app에서 joins에 부수정보 api를 호출하기위해 알아야하는 정보)
    private String childCode;
    // 매체구분
    private String midiDivid;
    // 매체 코드
    private String midiCode;
    // 중계지 여부
    private Boolean isRelay;
    // 중계지 주소 (RA_ADDR)
    private String relayAddr;
    // 공장 주소 (FA_ADDR)
    private String factoryAddr;
    // 공장 전화번호
    private String factoryTel;
    // 코스
    private Integer courSeq;
    // 중계순서
    private Integer relaySeq;
    // 경유순서
    private Integer passSeq;

    /**
     * ALOA Platform과 매핑되는 필드
     */
    // 배송 아이디
    private Long aloaDeliveryId;

    private Boolean isCompleted;

    @Override
    public int compareTo(CirculationDTO circulationDto) {

        final int courSeqCompare = ObjectUtils.compare(this.getCourSeq(), circulationDto.getCourSeq(), true);
        if (courSeqCompare != 0) {
            return courSeqCompare;
        }

        final int relaySeqCompare = ObjectUtils.compare(this.getRelaySeq(), circulationDto.getRelaySeq(), true);
        if (relaySeqCompare != 0) {
            return relaySeqCompare;
        }

        final int passSeqCompare = ObjectUtils.compare(this.getPassSeq(), circulationDto.getPassSeq(), true);
        return passSeqCompare;
    }

    @Override
    public String toString() {
        return this.getCourSeq() + "_"
                + this.getRelaySeq() + "_"
                + this.getPassSeq() + "_"
                + this.getCenterName() + "_"
                + this.getCenterCode();
    }

    public static CirculationDTO of(final Circulation circulation) {

        if (Objects.isNull(circulation)) {
            return null;
        }

        final CirculationDTO dto = CirculationDTO.builder()
                .groupingKey(circulation.getGroupingKey())
                .centerName(circulation.getCenterName())
                .midiCodeName(circulation.getMidiCodeName())
                .restPactCnt(circulation.getRestPactCnt())
                .basicPackCnt(circulation.getBasicPackCnt())
                .totalCnt(circulation.getTotalCnt())
                .centerAddr(circulation.getCenterAddr())
                .centerTel(circulation.getCenterTel())
                .centerHp(circulation.getCenterHp())
                .centerCode(circulation.getCenterCode())
                .childCode(circulation.getChildCode())
                .midiDivid(circulation.getMidiDivid())
                .midiCode(circulation.getMidiCode())
                .isRelay(circulation.getIsRelay())
                .relayAddr(circulation.getRelayAddr())
                .courSeq(circulation.getCourSeq())
                .relaySeq(circulation.getRelaySeq())
                .passSeq(circulation.getPassSeq())
                .aloaDeliveryId(circulation.getAloaDeliveryId())
                .isCompleted(circulation.getIsCompleted())
                .build();

        return dto;
    }

    public static List<CirculationDTO> of(final List<Circulation> circulationList) {

        return circulationList.stream().map(CirculationDTO::of).collect(Collectors.toList());
    }

    public static CirculationDTO of(final RawDataDTO rawDataDTO) {

        if (Objects.isNull(rawDataDTO)) {
            return null;
        }

        final CirculationDTO dto = CirculationDTO.builder()
                .groupingKey(rawDataDTO.getGroupingKey())
                .centerName(rawDataDTO.getCentName())
                .midiCodeName(rawDataDTO.getMidiCodeName())
                .restPactCnt(rawDataDTO.getRestPactCnt())
                .basicPackCnt(rawDataDTO.getBasicPackCnt())
                .totalCnt(rawDataDTO.getTotPactCnt())
                .centerAddr(StringUtils.trim(rawDataDTO.getCaAddr()))
                .centerTel(rawDataDTO.getCaTelNo())
                .centerHp(rawDataDTO.getCentHp())
                .centerCode(rawDataDTO.getCentCode())
                .childCode(rawDataDTO.getChildCode())
                .midiDivid(rawDataDTO.getMidiDivid())
                .midiCode(rawDataDTO.getMidiCode())
                .isRelay(rawDataDTO.isRelay())
                .relayAddr(StringUtils.trim(rawDataDTO.getRaAddr()))
                .factoryAddr(StringUtils.trim(rawDataDTO.getFaAddr()))
                .factoryTel(rawDataDTO.getFactTel().replace("-", ""))
                .courSeq(rawDataDTO.getCourSeq())
                .relaySeq(rawDataDTO.getRelaySeq())
                .passSeq(rawDataDTO.getPassSeq())
                .build();

        return dto;
    }

    @JsonIgnore
    public String getName() {

        return Boolean.TRUE.equals(this.isRelay)
                ? DelificateConstant.JOINS_DEFAULT_RELAY_NAME
                : this.centerName + "_" + this.centerCode;
    }

    @JsonIgnore
    public String getAddress() {

        return Boolean.TRUE.equals(this.isRelay)
                ? this.relayAddr
                : this.centerAddr;
    }

}