package com.logisteq.tms.external.joins.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.logisteq.tms.external.joins.constant.DelificateConstant;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.*;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RawDataUpdatedDTO {

    // 발송일자 PK
    @NotBlank(message = "발송일자[SEND_DATE]가 비어있습니다.")
    @JsonProperty("SEND_DATE")
    private String sendDate;

    // 코스 PK
    @Positive
    @NotNull(message = "코스[COUR_SEQ]가 없습니다.")
    @JsonProperty("COUR_SEQ")
    private Integer courSeq;

    // 코스명
    @Size(max = DelificateConstant.COUR_NAME_MAX)
    @NotBlank(message = "코스명[COUR_NAME]이 비어있습니다.")
    @JsonProperty("COUR_NAME")
    private String courName;

    // 중계순서
    @PositiveOrZero
    @NotNull(message = "중계순서[RELAY_SEQ]가 없습니다.")
    @JsonProperty("RELAY_SEQ")
    private Integer relaySeq;

    // 경유순서
    @PositiveOrZero
    @NotNull(message = "경유순서[PASS_SEQ]가 없습니다.")
    @JsonProperty("PASS_SEQ")
    private Integer passSeq;

    // 센터코드 PK
    @Size(max = DelificateConstant.CENT_CODE_MAX)
    @NotBlank(message = "센터코드[CENT_CODE]가 비어있습니다.")
    @JsonProperty("CENT_CODE")
    private String centCode;

    // 센터명
    @Size(max = DelificateConstant.CENT_NAME_MAX)
    //@NotBlank(message = "센터명[CENT_NAME]이 비어있습니다.")   // 중계지인경우 비어있을 수 있다.
    @JsonProperty("CENT_NAME")
    private String centName;

    // 분국 PK
    @Size(max = DelificateConstant.CHILD_CODE_MAX)
    @NotBlank(message = "분국[CHILD_CODE]이 비어있습니다.")
    @JsonProperty("CHILD_CODE")
    private String childCode;

    // 매체구분 PK
    @Size(max = DelificateConstant.MIDI_DIVID_MAX)
    @NotBlank(message = "매체구분[MIDI_DIVID]이 비어있습니다.")
    @JsonProperty("MIDI_DIVID")
    private String midiDivid;

    // 매체구분명
    @Size(max = DelificateConstant.MIDI_DIVID_NAME_MAX)
    @NotBlank(message = "매체구분명[MIDI_DIVID_NAME]이 비어있습니다.")
    @JsonProperty("MIDI_DIVID_NAME")
    private String midiDividName;

    // 매체코드 PK
    @Size(max = DelificateConstant.MIDI_CODE_MAX)
    @NotBlank(message = "매체코드[MIDI_CODE]가 비어있습니다.")
    @JsonProperty("MIDI_CODE")
    private String midiCode;

    // 매체명
    @Size(max = DelificateConstant.MIDI_CODE_NAME_MAX)
    @NotBlank(message = "매체명[MIDI_CODE_NAME]이 비어있습니다.")
    @JsonProperty("MIDI_CODE_NAME")
    private String midiCodeName;

    // 생성공장
    @Size(max = DelificateConstant.FACT_CODE_MAX)
    @NotBlank(message = "생성공장[FACT_CODE]이 비어있습니다.")
    @JsonProperty("FACT_CODE")
    private String factCode;

    // 발송공장
    @Size(max = DelificateConstant.CARRY_FACT_CODE_MAX)
    @NotBlank(message = "발송공장[CARRY_FACT_CODE]이 비어있습니다.")
    @JsonProperty("CARRY_FACT_CODE")
    private String carryFactCode;

    // 발송부수
    @PositiveOrZero
    @NotNull(message = "발송부수[SEND_COUNT]가 없습니다.")
    @JsonProperty("SEND_COUNT")
    private Integer sendCount;

    // 정속부수
    @PositiveOrZero
    @NotNull(message = "정속부수[BASIC_PACK_COUNT]가 없습니다.")
    @JsonProperty("BASIC_PACK_COUNT")
    private Integer basicPackCount;

    // 정속뭉치수
    @PositiveOrZero
    @NotNull(message = "정속뭉치수[BASIC_PACK_CNT]가 없습니다.")
    @JsonProperty("BASIC_PACK_CNT")
    private Integer basicPackCnt;

    // 소속뭉치수
    @PositiveOrZero
    @NotNull(message = "소속뭉치수[REST_PACK_CNT]가 없습니다.")
    @JsonProperty("REST_PACK_CNT")
    private Integer restPactCnt;

    // 소속1
    @PositiveOrZero
    //@NotNull(message = "소속1[REST_CNT1]이 없습니다.")
    @JsonProperty("REST_CNT1")
    private Integer restCnt1;

    // 소속2
    @PositiveOrZero
    //@NotNull(message = "소속2[REST_CNT2]가 없습니다.")
    @JsonProperty("REST_CNT2")
    private Integer restCnt2;

    // 총뭉치수
    @PositiveOrZero
    @NotNull(message = "총뭉치수[TOT_PACK_CNT]가 없습니다.")
    @JsonProperty("TOT_PACK_CNT")
    private Integer totPactCnt;

    @Override
    public String toString() {
        return this.getCourSeq() + "_"
                + this.getCourName() + "_"
                + this.getRelaySeq() + "_"
                + this.getPassSeq() + "_"
                + this.getCentName() + "_"
                + this.getCentCode();
    }

}