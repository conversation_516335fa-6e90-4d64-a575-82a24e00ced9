package com.logisteq.tms.external.kep.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DispatchReqDto {
    private String companyCode; //LaaS 회원사 코드
    private String shipperCode; //LaaS 화주 코드
    private List<DispatchItemReqDto> dispatchList;
}
