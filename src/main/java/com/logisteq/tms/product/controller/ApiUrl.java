package com.logisteq.tms.product.controller;

import com.logisteq.common.constant.BaseUrl;

public class ApiUrl {
    private ApiUrl() {
        throw new AssertionError();
    }

    public static final String PRODUCT = BaseUrl.PRODUCTS_URL;

    public static final String PRODUCT_PROJECT = PRODUCT + "/projectId/{projectId}";

    public static final String PRODUCT_ID = "/{productId}";

    public static final String ITEM_CODE = "/{itemCode}";

    public static final String PRODUCT_BARCODE = "/productBarcode/{productBarcode}";

    public static final String INSPECTION = "/inspection";

    public static final String INSPECTION_BY_RIDER_MOBILE = "/inspection/mobile";

    public static final String STATUS = "/status";

    public static final String OVERALL_STATUS = "/overall-status";

    public static final String START_UNLOADING = "/start-unloading";

    public static final String DELIVERY_ID = "/{deliveryId}";

    public static final String CHECK_UNLOADING = "/check-unloading";
}
