package com.logisteq.tms.product.domain.suppl;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.logisteq.common.component.excel.annotation.PxlExportValue;
import com.logisteq.common.component.excel.annotation.PxlImportValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum StorageType {

    COLD(
            1,
            Arrays.asList("COLD"),
            Arrays.asList("냉장")
    ),
    FROZEN(
            2,
            Arrays.asList("FROZEN"),
            Arrays.asList("냉동")
    ),
    ROOMTEMP(
            3,
            Arrays.asList("ROOMTEMP"),
            Arrays.asList("상온")
    ),
    UNUSED(
            4,
            Arrays.asList("UNUSED"),
            Arrays.asList("미사용")
    ),
    ;

    public static final String COLUMN_DEFINITION = "TINYINT NULL DEFAULT NULL COMMENT '" +
            "1:냉장, " +
            "2:냉동, " +
            "3:상온, " +
            "4:미사용'";

    private Integer code;
    private final List<String> english;
    private final List<String> korean;

    private static final Map<Integer, StorageType> codeToEnum = Stream
            .of(values())
            .collect(Collectors.toMap(e -> e.code, e -> e));

    private static final Map<String, StorageType> englishToEnum = Stream
            .of(values())
            .map(e -> e.getEnglish().stream().collect(Collectors.toMap(str -> StringUtils.deleteWhitespace(str).toUpperCase(), str -> e)))
            .flatMap(map -> map.entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    private static final Map<String, StorageType> koreanToEnum = Stream
            .of(values())
            .map(e -> e.getKorean().stream().collect(Collectors.toMap(str -> StringUtils.deleteWhitespace(str), str -> e)))
            .flatMap(map -> map.entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    public static StorageType fromEnglish(final String str) {

        return englishToEnum.get(StringUtils.deleteWhitespace(str).toUpperCase());
    }

    public static StorageType fromKorean(final String str) {

        return koreanToEnum.get(StringUtils.deleteWhitespace(str));
    }

    @JsonCreator
    public static StorageType getStorageTypeFromJson(final String str) {

        return fromKorean(str);
    }

    @JsonValue
    public String getJsonFromStorageType() {

        return korean.get(0);
    }

    @Override
    public String toString() {

        return korean.get(0);
    }

    @Converter(autoApply = true)
    public static class StorageTypeConverter implements AttributeConverter<StorageType, Integer> {

        @Override
        public Integer convertToDatabaseColumn(StorageType storageType) {

            if (Objects.isNull(storageType)) {
                return null;
            }

            return storageType.getCode();
        }

        @Override
        public StorageType convertToEntityAttribute(Integer code) {

            if (Objects.isNull(code)) {
                return null;
            }

            return StorageType.codeToEnum.get(code);
        }
    }

    @PxlImportValue
    public static StorageType toPxlImportValue(final String str) {

        return fromKorean(str);
    }

    @PxlExportValue
    public String toPxlExportValue() {

        return korean.get(0);
    }
}
