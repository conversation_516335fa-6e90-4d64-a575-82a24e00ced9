package com.logisteq.tms.product.domain;

import com.logisteq.tms.product.domain.suppl.ProductInspectionStatus;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(indexes = @Index(columnList = "projectId, riderId"))
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class ProductAllocation {

    /**
     * 상품 할당 아이디
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long productAllocationId;

    /**
     * 상품 정보
     */
    @ManyToOne(fetch = FetchType.EAGER, cascade = CascadeType.MERGE, optional = false)
    @JoinColumn(name = "product_id", nullable = false)
    private Product product;

    /**
     * 프로젝트 아이디
     */
    @Column(nullable = false)
    private Long projectId;

    /**
     * 기사 아이디
     */
    @Column(nullable = false)
    private Long riderId;

    /**
     * 상품 검수 상태
     */
    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = ProductInspectionStatus.COLUMN_DEFINITION, nullable = false)
    private ProductInspectionStatus productInspectionStatus;

    /**
     * 현재 수량 (주문가능 수량)
     */
    @Column(nullable = false)
    private Long currentQuantity;

    /**
     * 예약 수량 (주문접수된 수량)
     */
    @Column
    private Long reservationQuantity;

    /**
     * 합계 상차수량
     */
    @Column
    private Long totalLoading;

    /**
     * 합계 하차수량
     */
    @Column
    private Long totalUnloading;

    /**
     * 합계 폐기수량
     */
    @Column
    private Long totalDisposal;

    /**
     * 박스번호
     */
    @Column
    private String boxNumber;

    /**
     * 생성 일시
     */
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createAt;

    /**
     * 변경 일시
     */
    @LastModifiedDate
    @Column(nullable = false)
    private LocalDateTime updateAt;
}
