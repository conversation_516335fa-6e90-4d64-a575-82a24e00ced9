package com.logisteq.tms.product.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.product.domain.Product;
import com.logisteq.tms.product.domain.ProductAllocation;
import com.logisteq.tms.product.domain.ProductHistory;
import com.logisteq.tms.product.domain.suppl.ProductCategory;
import com.logisteq.tms.product.domain.suppl.ProductInspectionStatus;
import com.logisteq.tms.product.service.ProductHistoryService;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;


@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RiderProductsDTO {

    private Long productId;
    private Long productAllocationId;
    private String itemCode;
    private String productBarcode;
    private String itemName;
    private Long currentQuantity;
    private Long reservationQuantity;
    private Long totalLoading;
    private Long totalUnloading;
    private Long totalDisposal;
    private String storageType;
    private String warehouseNumber;
    private ProductCategory productCategory;
    private Long productPrice;
    private Long itemQuantity;
    private Long productWeight;
    private String remark;
    private String boxNumber;

    public static RiderProductsDTO parseFromProductSetList(final ProductAllocation productAllocation,
                                                           final ProductHistoryService productHistoryService) {

        if (Objects.isNull(productAllocation)) {
            return null;
        }

        final List<ProductHistory> productHistories = productHistoryService.getProductHistories(productAllocation.getProductAllocationId());
        final Product product = productAllocation.getProduct();
        StringBuffer sb = new StringBuffer();
        String remark = null;

        if (CollectionUtils.isNotEmpty(productHistories)) {
            productHistories.forEach(history -> {
                        final Long disposalQty = history.getDisposalQuantity();
                        final Long unloadingQty = history.getUnloadingQuantity();
                        String reason = history.getReason();

                        if ((Objects.nonNull(disposalQty) && disposalQty > 0 && !StringUtils.equals(reason, ProductInspectionStatus.DISPOSAL.toString()))
                                || (Objects.nonNull(unloadingQty) && unloadingQty > 0 && !StringUtils.equals(reason, ProductInspectionStatus.UNLOADING.toString()) && !StringUtils.equals(reason, "일반하차") && !StringUtils.equals(reason, ProductInspectionStatus.CANCELLATION.toString()))
                        ) {
                            if (StringUtils.isNotBlank(reason)) {
                                sb.append("- " + reason + "<br/>");
                            }
                        }
                    });

            if (sb.length() > 0) {
                remark = sb.toString();
            }
        }

        return RiderProductsDTO.builder()
                .productId(product.getProductId())
                .productAllocationId(productAllocation.getProductAllocationId())
                .itemCode(product.getItemCode())
                .productBarcode(product.getProductBarcode())
                .itemName(product.getItemName())
                .currentQuantity(productAllocation.getCurrentQuantity())
                .reservationQuantity(productAllocation.getReservationQuantity())
                .totalLoading(productAllocation.getTotalLoading())
                .totalUnloading(productAllocation.getTotalUnloading())
                .totalDisposal(productAllocation.getTotalDisposal())
                .storageType(product.getStorageType().toString())
                .warehouseNumber(product.getWarehouseNumber())
                .productCategory(product.getProductCategory())
                .productPrice(product.getProductPrice())
                .itemQuantity(product.getItemQuantity())
                .productWeight(product.getProductWeight())
                .remark(remark)
                .boxNumber(productAllocation.getBoxNumber())
                .build();
    }
}
