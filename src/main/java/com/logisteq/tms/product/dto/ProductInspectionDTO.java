package com.logisteq.tms.product.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.product.domain.suppl.ProductInspectionStatus;
import lombok.*;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductInspectionDTO {

    @NotNull(message = "단품코드(실물바코드)는 필수값입니다.")
    private String productBarcode;

    @NotNull(message = "상품 검수상태는 필수값입니다.")
    private ProductInspectionStatus inspectionStatus;

    @NotNull(message = "상품 검수수량은 필수값입니다.")
    private Long inspectionQuantity;

    private String reason;

    private Long deliveryId; // 상품주문 배송에 대한 하차검수시 앱에서 올려줘야한다.

    private String boxNumber; // 상차검수시 기사에 의해 결정된다.

    @NotNull(message = "상품코드는 필수값입니다.")
    private String itemCode;
}
