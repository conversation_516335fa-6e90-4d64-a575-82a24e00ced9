package com.logisteq.tms.product.service;

import com.logisteq.common.exception.CustomException;
import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.DeliveryAllocation;
import com.logisteq.tms.delivery.domain.DeliveryProduct;
import com.logisteq.tms.delivery.domain.event.DeliveryCompletedEvent;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.repository.DeliveryAllocationRepository;
import com.logisteq.tms.delivery.repository.DeliveryProductRepository;
import com.logisteq.tms.delivery.repository.DeliveryRepository;
import com.logisteq.tms.external.hmg.constant.HmgConstant;
import com.logisteq.tms.product.domain.Product;
import com.logisteq.tms.product.domain.ProductAllocation;
import com.logisteq.tms.product.domain.ProductHistory;
import com.logisteq.tms.product.domain.suppl.ProductCategory;
import com.logisteq.tms.product.domain.suppl.ProductInspectionStatus;
import com.logisteq.tms.product.domain.suppl.StorageType;
import com.logisteq.tms.product.dto.*;
import com.logisteq.tms.product.repository.ProductAllocationRepository;
import com.logisteq.tms.product.repository.ProductRepository;
import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.domain.suppl.ProjectAttribute;
import com.logisteq.tms.project.repository.ProjectRepository;
import com.logisteq.tms.project.service.ProjectBasicService;
import com.logisteq.tms.push.service.PushService;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.service.RiderService;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.service.OrganizationService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Validated
@Service
public class ProductService {

    private final RiderService riderService;
    private final ProductRepository productRepository;
    private final ProductAllocationRepository productAllocationRepository;
    private final ProductHistoryService productHistoryService;
    private final ProjectBasicService projectBasicService;
    private final PushService pushService;
    private final DeliveryProductRepository deliveryProductRepository;
    private final ProjectRepository projectRepository;
    private final DeliveryRepository deliveryRepository;
    private final DeliveryAllocationRepository deliveryAllocationRepository;
    private final OrganizationService organizationService;

    @Autowired
    public ProductService(final RiderService riderService,
                          final ProductRepository productRepository,
                          final ProductAllocationRepository productAllocationRepository,
                          final ProductHistoryService productHistoryService,
                          final ProjectBasicService projectBasicService,
                          final PushService pushService,
                          final DeliveryProductRepository deliveryProductRepository,
                          final ProjectRepository projectRepository,
                          final DeliveryRepository deliveryRepository,
                          final DeliveryAllocationRepository deliveryAllocationRepository,
                          final OrganizationService organizationService) {

        this.riderService = riderService;
        this.productRepository = productRepository;
        this.productAllocationRepository = productAllocationRepository;
        this.productHistoryService = productHistoryService;
        this.projectBasicService = projectBasicService;
        this.pushService = pushService;
        this.deliveryProductRepository = deliveryProductRepository;
        this.projectRepository = projectRepository;
        this.deliveryRepository = deliveryRepository;
        this.deliveryAllocationRepository = deliveryAllocationRepository;
        this.organizationService = organizationService;
    }

    /**
     * 개별상품 등록
     *
     * @param product
     * @return
     */
    @Transactional
    public Product saveProduct(final Product product) {

        return productRepository.save(product);
    }

    /**
     * 배송지 등록시 상품정보 등록 또는 업데이트
     *
     * @param orderDTO
     * @param orgCodeName
     */
    public void saveProductByOrder(final OrderItemDTO orderDTO,
                                   final String orgCodeName) {

        final String itemName = orderDTO.getItemName(); // 상품명
        final String itemCode = orderDTO.getItemCode(); // 상품코드
        final String productBarCode = orderDTO.getProductBarcode(); // 상품바코드
        final Long itemQuantity = orderDTO.getOrderQuantity(); // 상품수량

        final Product foundProduct = this.getProductByProductBarcodeAndItemCode(productBarCode, itemCode, orgCodeName).orElse(null);
        if (Objects.isNull(foundProduct)) {
            // 상품정보 등록
            final Product regProduct = this.saveProduct(Product.builder()
                    .itemCode(itemCode)
                    .productBarcode(productBarCode)
                    .itemName(itemName)
                    .itemQuantity(itemQuantity)
                    .build());

            if (Objects.isNull(regProduct)) {
                log.error("[saveProductByOrder] 상품정보 등록 실패, itemName: {}, itemCode:{}, productBarcode:{}", itemName, itemCode, productBarCode);
            }
        } else {
            // 상품정보 업데이트
            final ProductDTO productDTO = ProductDTO.builder()
                    .itemCode(itemCode)
                    .productBarcode(productBarCode)
                    .itemName(itemName)
                    .itemQuantity(itemQuantity)
                    .build();

            final Long productId = foundProduct.getProductId();
            final Product updProduct = this.updateProduct(productId, productDTO);
            if (Objects.isNull(updProduct)) {
                log.error("[saveProductByOrder] 상품정보 업데이트 실패, productId: {}, itemName: {}, itemCode:{}, productBarcode:{}", productId, itemName, itemCode, productBarCode);
            }
        }
    }

    /**
     * 상품 주문 목록으로 상품정보 등록 또는 업데이트
     *
     * @param orderItemList
     * @param organization
     */
    public void saveProductsByOrderList(final List<OrderItemDTO> orderItemList,
                                        final Organization organization) {

        if (CollectionUtils.isNotEmpty(orderItemList)) {
            final String orgCodeName = Optional.ofNullable(organization).map(Organization::getCodeName).orElse("");
            if (!HmgConstant.HMG_ORG_CODE_NAME.equals(orgCodeName)) {
                for (final OrderItemDTO orderDTO : orderItemList) {
                    this.saveProductByOrder(orderDTO, orgCodeName);
                }
            }
        }
    }

    /**
     * 프로젝트의 전체 기사별 개별상품 목록 조회
     *
     * @param projectId
     * @return
     */
    public List<ProjectRiderProductsDTO> getProjectRiderProducts(final Long projectId) {

        final List<ProductAllocation> allocations = productAllocationRepository.findByProjectId(projectId);
        List<Rider> riderList = null;

        if (Objects.isNull(allocations)) {
            return null;
        }

        if (allocations.size() == 0) {
            riderList = riderService.getRiders(projectId, false);
        } else {
            final Set<Long> riderIdSet = allocations.stream()
                    .map(ProductAllocation::getRiderId)
                    .collect(Collectors.toSet());

            riderList = riderIdSet.stream()
                    .map(riderService::getRider)
                    .collect(Collectors.toList());
        }

        List<ProjectRiderProductsDTO> projectRiderProductsDTOList = ProjectRiderProductsDTO.parseFromRiderSetList(riderList);

        if (allocations.size() != 0) {
            for (final ProductAllocation allocation : allocations) {
                final Long riderId = allocation.getRiderId();
                final ProjectRiderProductsDTO projectRiderProductsDTO = projectRiderProductsDTOList.stream()
                        .filter(p -> p.getRiderId().equals(riderId))
                        .findFirst()
                        .orElse(null);

                if (Objects.nonNull(projectRiderProductsDTO)) {
                    final RiderProductsDTO riderProductDTO = RiderProductsDTO.parseFromProductSetList(allocation, this.productHistoryService);

                    if(riderProductDTO.getTotalLoading() > 0)
                        projectRiderProductsDTO.getRiderProductDTOList().add(riderProductDTO);
                }
            }
        }

        return projectRiderProductsDTOList;
    }

    /**
     * 개별상품 전체 목록 조회
     *
     * @return
     */
    public List<Product> getProducts() {

        return productRepository.findAll();
    }

    /**
     * itemCode로 개별상품 조회
     *
     * @param itemCode
     * @return
     */
    public List<Product> getProductsByItemCode(@NotNull final String itemCode) {

        return productRepository.findByItemCodeOrderByUpdateAtDesc(itemCode);
    }

    /**
     * productBarcode로 개별상품 목록 조회
     *
     * @param productBarcode
     * @return
     */
    public List<Product> getProductsByProductBarcode(@NotNull final String productBarcode) {

        return productRepository.findByProductBarcodeOrderByUpdateAtDesc(productBarcode);
    }

    /**
     * productBarcode, itemCode로 개별상품 조회
     *
     * @param productBarcode
     * @param itemCode
     * @return
     */
    public List<Product> getProductsByProductBarcodeAndItemCode(@NotNull String productBarcode,
                                                             @NotNull final String itemCode) {

        productBarcode = Product.convertScannedProductBarcode(productBarcode, "");
        return productRepository.findByProductBarcodeAndItemCode(productBarcode, itemCode);
    }


    /**
     * 차량의 현재 상품 현황 조회
     *
     * @param projectId
     * @param riderId
     * @return
     */
    public ProjectRiderProductsDTO getProductCurrentStatus(final Long projectId,
                                                       final Long riderId) {

        final List<ProductAllocation> allocations = productAllocationRepository.findByProjectIdAndRiderIdOrderByUpdateAtDesc(projectId, riderId);
        if (CollectionUtils.isEmpty(allocations)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "해당 차량에 조회할 상품이 없습니다." + " (projectId: " + projectId + ", riderId: " + riderId + ")", false);
        }

        List<Rider> riderList = new ArrayList<>();
        riderList.add(riderService.getRider(riderId));

        final List<ProjectRiderProductsDTO> projectRiderProductsDTOList = ProjectRiderProductsDTO.parseFromRiderSetList(riderList);

        final ProjectRiderProductsDTO projectRiderProductsDTO = projectRiderProductsDTOList.stream()
                .filter(p -> riderId.equals(p.getRiderId()))
                .findFirst()
                .orElse(null);

        if (Objects.nonNull(projectRiderProductsDTO)) {
            for (ProductAllocation allocation : allocations) {
                final RiderProductsDTO riderProductsDTO = RiderProductsDTO.parseFromProductSetList(allocation, this.productHistoryService);
                projectRiderProductsDTO.getRiderProductDTOList().add(riderProductsDTO);
            }
        }

        return projectRiderProductsDTO;
    }

    /**
     * 개별상품 업데이트
     *
     * @param productId
     * @param productDTO
     * @return
     */
    @Transactional
    public Product updateProduct(@NotNull final Long productId,
                          final ProductDTO productDTO) {

        final Product product = this.getProductByProductId(productId).orElse(null);
        if (Objects.isNull(product)) {
            return null;
        }

        final String itemCode = productDTO.getItemCode();
        final String productBarcode = productDTO.getProductBarcode();
        final String itemName = productDTO.getItemName();
        final StorageType storageType = productDTO.getStorageType();
        final ProductCategory productCategory = productDTO.getProductCategory();
        final Long productPrice = productDTO.getProductPrice();
        final Long itemQuantity = productDTO.getItemQuantity();
        final Long productWeight = productDTO.getProductWeight();
        final String remark = productDTO.getRemark();

        if (Objects.nonNull(itemCode) && !StringUtils.isBlank(itemCode)) {
            product.setItemCode(itemCode);
        }
        if (Objects.nonNull(productBarcode)) {
            product.setProductBarcode(productBarcode);
        }
        if (Objects.nonNull(itemName)) {
            product.setItemName(itemName);
        }
        if (Objects.nonNull(storageType)) {
            product.setStorageType(storageType);
        }
        if (Objects.nonNull(productCategory)) {
            product.setProductCategory(productCategory);
        }
        if (Objects.nonNull(productPrice)) {
            product.setProductPrice(productPrice);
        }
        if (Objects.nonNull(itemQuantity)) {
            product.setItemQuantity(itemQuantity);
        }
        if (Objects.nonNull(productWeight)) {
            product.setProductWeight(productWeight);
        }
        if (Objects.nonNull(remark)) {
            product.setRemark(remark);
        }

        return productRepository.save(product);
    }

    public Optional<Product> getProductByProductId(final Long productId) {

        return productRepository.findByProductId(productId);
    }

    public Optional<Product> getProductByProductBarcode(String productBarcode) {

        productBarcode = Product.convertScannedProductBarcode(productBarcode, "");
        return productRepository.findTop1ByProductBarcodeOrderByUpdateAtDesc(productBarcode);
    }

    /**
     * 바코드, 상품코드로 가장 최신 상품정보 단일 조회
     *
     * @param productBarcode
     * @param itemCode
     * @param orgCodeName
     * @return
     */
    public Optional<Product> getProductByProductBarcodeAndItemCode(String productBarcode,
                                                                final String itemCode,
                                                                final String orgCodeName) {

        productBarcode = Product.convertScannedProductBarcode(productBarcode, orgCodeName);
        return productRepository.findTop1ByProductBarcodeAndItemCodeOrderByUpdateAtDesc(productBarcode, itemCode);
    }

    /**
     * 개별상품 삭제
     *
     * @param productId
     * @return
     */
    @Transactional
    public Integer deleteByProductId(@NotNull @Positive final Long productId) {

        productRepository.findByProductId(productId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "삭제하려는 productId:" + productId + "가 존재하지 않습니다.", false));

        return productRepository.deleteByProductId(productId);
    }

    /**
     * 개별상품 검수상태 변경
     *
     * @param projectId
     * @param riderId
     * @param inspectionDTO
     * @param idx
     * @return
     */
    @Transactional
    public ProductAllocation updateProductInspectionStatus(final Long projectId,
                                              final Long riderId,
                                              final ProductInspectionDTO inspectionDTO,
                                              final int idx) {

        String productBarcode = inspectionDTO.getProductBarcode();
        final String itemCode = inspectionDTO.getItemCode();
        final ProductInspectionStatus inspectionStatus = inspectionDTO.getInspectionStatus();
        final Long inspectionQuantity = inspectionDTO.getInspectionQuantity();
        final Long deliveryId = inspectionDTO.getDeliveryId();
        final String boxNumber = inspectionDTO.getBoxNumber();
        Long currentQuantity = null;
        Long reservationQuantity = null;
        ProductAllocation retAllocation = null;
        Boolean isNoUnloading = false;

        if (Objects.nonNull(productBarcode) && Objects.nonNull(inspectionStatus)) {
            productBarcode = Product.convertScannedProductBarcode(productBarcode, "");
            log.info("[updateProductInspectionStatus] productBarcode: {}, itemCode: {}, inspectionStatus: {}, inspectionQuantity: {}", productBarcode, itemCode, inspectionStatus.toString(), inspectionQuantity);
            final Product product = this.getProductByProductBarcodeAndItemCode(productBarcode, itemCode, "").orElse(null);

            if (Objects.nonNull(product)) {
                final ProductAllocation productAllocation = this.getLastProductAllocation(projectId, riderId, product.getProductId());

                if (Objects.nonNull(productAllocation)) {
                    reservationQuantity = Objects.nonNull(productAllocation.getReservationQuantity()) ? productAllocation.getReservationQuantity() : 0L;
                    currentQuantity = this.calcCurrentQuantity(inspectionStatus, inspectionQuantity, productAllocation.getCurrentQuantity(), reservationQuantity, deliveryId);
                    final Long totalLoading = Objects.nonNull(productAllocation.getTotalLoading()) ? productAllocation.getTotalLoading() : 0L;

                    switch (inspectionStatus) {
                        case LOADING:
                            if (projectBasicService.isProjectTerminated(projectId)) {
                                throw new CustomException(HttpStatus.BAD_REQUEST, "종료된 프로젝트로 상차검수 요청되었습니다. " + "projectId:" + projectId + ", riderId:" + riderId, false);
                            }

                            productAllocation.setTotalLoading(totalLoading + inspectionQuantity);
                            if (Objects.nonNull(boxNumber)) {
                                productAllocation.setBoxNumber(boxNumber);
                            }
                            break;

                        case CANCELLATION:
                            if (inspectionQuantity > productAllocation.getCurrentQuantity()) {
                                throw new CustomException(HttpStatus.NOT_ACCEPTABLE, "현재수량을 초과한 상차취소는 할 수 없습니다.", false);
                            }

                            productAllocation.setTotalLoading(totalLoading - inspectionQuantity);
                            break;

                        case UNLOADING:
                            if (Objects.nonNull(deliveryId)) {
                                // 배송하차
                                final List<DeliveryProduct> deliveryProductList = deliveryProductRepository.findByDeliveryIdAndProductBarcodeAndItemCode(deliveryId, productBarcode, itemCode);

                                if (CollectionUtils.isNotEmpty(deliveryProductList)) {
                                    DeliveryProduct deliveryProduct = null;

                                    if (StringUtils.equals("9999999999999", productBarcode)) {
                                        deliveryProduct = deliveryProductList.get(idx);
                                    } else {
                                        deliveryProduct = deliveryProductList.get(0);
                                    }

                                    if (Objects.nonNull(deliveryProduct)) {
                                        final String itemName = deliveryProduct.getItemName();

                                        if (!deliveryProduct.getIsUnloading()) {
                                            log.info("[updateProductInspectionStatus][배송하차] deliveryId:{}, productBarcode:{}, itemCode: {}, itemName: {}", deliveryId, productBarcode, itemCode, itemName);

                                            deliveryProduct.setIsUnloading(true);
                                            deliveryProductRepository.save(deliveryProduct);

                                            final Long totalUnloading = Objects.nonNull(productAllocation.getTotalUnloading()) ? productAllocation.getTotalUnloading() : 0L;
                                            productAllocation.setTotalUnloading(totalUnloading + inspectionQuantity);
                                            // 하차시 주문예약수량이 존재하면 해당 수량을 차감
                                            if (Objects.nonNull(reservationQuantity) && reservationQuantity > 0) {
                                                reservationQuantity -= inspectionQuantity;
                                            }
                                        } else {
                                            isNoUnloading = true;
                                            log.error("[updateProductInspectionStatus] deliveryId:{}, productBarcode:{}, itemCode: {}, itemName: {} 이미 하차접수된 상품입니다.", deliveryId, productBarcode, itemCode, itemName);
                                        }
                                    } else {
                                        isNoUnloading = true;
                                        log.error("[updateProductInspectionStatus] deliveryId:{}, productBarcode:{}, itemCode: {} deliveryProduct is null.", deliveryId, productBarcode, itemCode);
                                    }
                                }
                            } else {
                                // 일반하차
                                if (inspectionQuantity > productAllocation.getCurrentQuantity()) {
                                    throw new CustomException(HttpStatus.NOT_ACCEPTABLE, "현재수량을 초과한 일반하차는 할 수 없습니다.", false);
                                }

                                productAllocation.setTotalLoading(totalLoading - inspectionQuantity);
                            }
                            break;

                        case DISPOSAL:
                            if (inspectionQuantity > productAllocation.getCurrentQuantity()) {
                                throw new CustomException(HttpStatus.NOT_ACCEPTABLE, "현재수량을 초과한 폐기는 할 수 없습니다.", false);
                            }

                            final Long totalDisposal = Objects.nonNull(productAllocation.getTotalDisposal()) ? productAllocation.getTotalDisposal() : 0L;
                            productAllocation.setTotalDisposal(totalDisposal + inspectionQuantity);
                            break;

                        default:
                            break;
                    }

                    if (isNoUnloading) {
                        return null;
                    }

                    if (currentQuantity < 0) {
                        currentQuantity = 0L;
                    }
                    if (reservationQuantity < 0) {
                        reservationQuantity = 0L;
                    }

                    productAllocation.setProductInspectionStatus(inspectionStatus);
                    productAllocation.setCurrentQuantity(currentQuantity);
                    productAllocation.setReservationQuantity(reservationQuantity);
                    retAllocation = this.saveProductAllocation(productAllocation);
                } else {
                    if (!ProductInspectionStatus.LOADING.equals(inspectionStatus)) {
                        log.error("[updateProductInspectionStatus] productBarcode: {}, itemCode: {} 상차되지 않은 상태에서 {} 프로세스를 진행할 수 없습니다.", productBarcode, itemCode, inspectionStatus.toString());
                        return null;
                    }

                    currentQuantity = this.calcCurrentQuantity(inspectionStatus, inspectionQuantity, 0L, 0L, null);

                    final ProductAllocation allocation = ProductAllocation.builder()
                            .product(product)
                            .projectId(projectId)
                            .riderId(riderId)
                            .productInspectionStatus(inspectionStatus)
                            .currentQuantity(currentQuantity)
                            .reservationQuantity(0L)
                            .totalLoading(ProductInspectionStatus.LOADING.equals(inspectionStatus) ? inspectionQuantity : 0L)
                            .totalUnloading(ProductInspectionStatus.UNLOADING.equals(inspectionStatus) ? inspectionQuantity : 0L)
                            .totalDisposal(ProductInspectionStatus.DISPOSAL.equals(inspectionStatus) ? inspectionQuantity : 0L)
                            .boxNumber(boxNumber)
                            .build();

                    retAllocation = this.saveProductAllocation(allocation);
                }
            } else {
                log.error("[updateProductInspectionStatus] productBarcode:{}, itemCode: {} 로 등록된 상품이 없습니다.", productBarcode, itemCode);
            }
        } else {
            log.error("[updateProductInspectionStatus] 바코드, 상품코드, 검수상태가 없는 요청입니다.");
        }

        return retAllocation;
    }

    public ProductAllocation getLastProductAllocation(final Long projectId,
                                                      final Long riderId,
                                                      final Long productId) {

        return productAllocationRepository.findTop1ByProjectIdAndRiderIdAndProductProductIdOrderByUpdateAtDesc(projectId, riderId, productId);
    }

    public Long calcCurrentQuantity(final ProductInspectionStatus inspectionStatus,
                                    final Long inspectionQuantity,
                                    final Long currentQuantity,
                                    final Long reservationQuantity,
                                    final Long deliveryId) {

        Long calcCurrentQuantity = currentQuantity;

        switch (inspectionStatus) {
            case LOADING:
                calcCurrentQuantity += inspectionQuantity;
                break;

            case UNLOADING:
                if (Objects.isNull(deliveryId)) {
                    calcCurrentQuantity -= inspectionQuantity;
                } else {
                    // 배송하차시 주문예약수량이 없으면 주문가능한 현재수량을 차감
                    if (Objects.isNull(reservationQuantity) || reservationQuantity == 0L) {
                        calcCurrentQuantity -= inspectionQuantity;
                    }
                }
                break;

            case DISPOSAL:
            case CANCELLATION:
                calcCurrentQuantity -= inspectionQuantity;
                break;

            default:
                break;
        }

        return calcCurrentQuantity;
    }

    @Transactional
    public ProductAllocation saveProductAllocation(final ProductAllocation productAllocation) {

        final Project project = Optional.ofNullable(projectBasicService.getProjectById(productAllocation.getProjectId()))
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "프로젝트를 찾을 수 없습니다.", false));

        if(!project.getIsProductCheckEnabled())
        {
            pushService.sendNewProductLoadToWeb(project.getUser().getUserId(), project.getId(), true);
            projectBasicService.updateProjectAttribute(project, ProjectAttribute.builder()
                    .isProductCheckEnabled(true)
                    .build());
        }

        return productAllocationRepository.save(productAllocation);
    }

    /**
     * 상품 주문에 따른 업데이트
     *
     * @param productAllocationId
     * @param quantity
     * @param isOrderReception
     * @param failureType
     */
    @Transactional
    public void updateByProductOrder(final Long productAllocationId,
                                     final Long quantity,
                                     final Boolean isOrderReception,
                                    final String failureType) {

        final ProductAllocation allocation = productAllocationRepository.findByProductAllocationId(productAllocationId)
                .orElse(null);

        if (Objects.nonNull(allocation)) {
            final String productBarcode = allocation.getProduct().getProductBarcode();
            final String itemCode = allocation.getProduct().getItemCode();
            Long currentQuantity = Objects.nonNull(allocation.getCurrentQuantity()) ? allocation.getCurrentQuantity() : 0L;
            Long reservationQuantity = Objects.nonNull(allocation.getReservationQuantity()) ? allocation.getReservationQuantity() : 0L;

            if (isOrderReception && (currentQuantity < 1 || currentQuantity < quantity)) {
                log.error("[updateByProductOrder] 바코드:{}, 상품코드:{} 상품의 주문가능한 재고가 없습니다. 주문가능수량: {}, 주문접수수량: {}", productBarcode, itemCode, currentQuantity, quantity);
                return;
            }

            if (!isOrderReception && (reservationQuantity < 1 || reservationQuantity < quantity)) {
                log.error("[updateByProductOrder] 바코드:{}, 상품코드:{} 상품은 예약수량보다 많이 주문취소를 할 수 없습니다. 예약수량: {}, 주문취소수량: {}", productBarcode, itemCode, reservationQuantity, quantity);
                return;
            }

            if (isOrderReception) {
                currentQuantity -= quantity;
                reservationQuantity += quantity;
            } else {
                currentQuantity += quantity;
                reservationQuantity -= quantity;
            }

            allocation.setReservationQuantity(reservationQuantity);
            allocation.setCurrentQuantity(currentQuantity);
            final ProductAllocation newAllocation = this.saveProductAllocation(allocation);

            if (Objects.nonNull(newAllocation)) {
                productHistoryService.saveProductHistoryByOrder(newAllocation, quantity, isOrderReception, failureType);
            }
        }
    }

    /**
     * 상품 주문 배송의 DeliveryAllocation 생성후 product_allocation, product_history 업데이트
     *
     * @param deliveryProductList
     * @param projectId
     * @param riderId
     */
    public void handleOrderItemReception(final List<DeliveryProduct> deliveryProductList,
                                          final Long projectId,
                                          final Long riderId) {

        final Organization organization = organizationService.getOrganizationByProjectId(projectId);

        if (CollectionUtils.isNotEmpty(deliveryProductList)
                && Objects.nonNull(organization) && HmgConstant.HMG_ORG_CODE_NAME.equals(organization.getCodeName())) {
            deliveryProductList
                    .forEach(dp -> {
                        final String productBarcode = dp.getProductBarcode();
                        final String itemCode = dp.getItemCode();
                        final Long orderQuantity = dp.getOrderQuantity();
                        final Product product = this.getProductByProductBarcodeAndItemCode(productBarcode, itemCode, "").orElse(null);
                        final Long productId = Objects.nonNull(product) ? product.getProductId() : null;

                        if (Objects.nonNull(productId)) {
                            final ProductAllocation productAllocation = this.getLastProductAllocation(projectId, riderId, productId);

                            if (Objects.nonNull(productAllocation)) {
                                this.updateByProductOrder(productAllocation.getProductAllocationId(), orderQuantity, true, null);
                            } else {
                                log.error("[handleOrderItemReception] 기사에게 할당된 상품이 없습니다. riderId: {}, projectId: {}, productBarcode: {}, itemCode: {}", riderId, projectId, productBarcode, itemCode);
                            }
                        } else {
                            log.error("[handleOrderItemReception] 조회된 상품이 없습니다. productBarcode: {}, itemCode: {}", productBarcode, itemCode);
                        }
                    });

            final Long userId = projectRepository.findUserIdByProjectId(projectId);
            pushService.sendProductInspectionAndOrderToWeb(userId, projectId);
        }
    }

    /**
     * 주문 상품이 차량에 적재되어 배달가능한지 검사
     *
     * @param deliveryId
     * @param riderId
     * @return
     */
    public Boolean checkDeliveryAvailability(final Long deliveryId,
                                        final Long riderId) {

        Boolean isDeliveryAvailable = true;
        final Delivery delivery = deliveryRepository.findById(deliveryId).orElse(null);

        if (Objects.nonNull(delivery)) {
            String customerOrderId = Optional.ofNullable(delivery.getDetail().getCustomerOrderId()).orElse(Long.toString(delivery.getId()));

            final Long projectId = delivery.getProjectId();
            final List<DeliveryProduct> deliveryProducts = deliveryProductRepository.findByDeliveryId(deliveryId);

            if (CollectionUtils.isNotEmpty(deliveryProducts)) {
                final Organization organization = organizationService.getOrganizationByUserId(delivery.getUserId());

                if (Objects.isNull(organization) || HmgConstant.HMG_ORG_CODE_NAME.equals(organization.getCodeName())) {
                    final List<ProductAllocation> productAllocations = productAllocationRepository.findByProjectIdAndRiderIdOrderByUpdateAtDesc(projectId, riderId);

                    if (CollectionUtils.isNotEmpty(productAllocations)) {
                        String logProduct = String.join(",", productAllocations.stream()
                                .map(pa -> pa.getProduct().getItemName()
                                        +  "  ( productBarcode : " + pa.getProduct().getProductBarcode()
                                        + ", itemCode : " + pa.getProduct().getItemCode()
                                        + ", currentQty : " + pa.getCurrentQuantity()
                                        + ", reservationQty : " + pa.getReservationQuantity()
                                        + ", totalUnloadingQty : " + pa.getTotalUnloading()
                                        + ", totalLoadingQty : " + pa.getTotalLoading()  +" ) ")
                                .collect(Collectors.toList()));

                        log.info("[OnDemand] 기사({}) 의 현재 물품 상태 => {}", riderId, logProduct);

                        for (DeliveryProduct dp : deliveryProducts) {
                            final Optional<ProductAllocation> allocation = productAllocations.stream()
                                    .filter(pa -> (dp.getProductBarcode().equals(pa.getProduct().getProductBarcode()) && dp.getItemCode().equals(pa.getProduct().getItemCode())))
                                    .findAny();

                            if (!allocation.isPresent() || allocation.get().getCurrentQuantity() < dp.getOrderQuantity()) {
                                log.info("[OnDemand] 기사({}) 에게 물품이 부족합니다. (  물품 : {} ( {}, {} ) 배송지({}) => 현재 수량 {}, 주문 수량 : {} ) "
                                        , riderId, dp.getItemName(),  dp.getProductBarcode(), dp.getItemCode(), customerOrderId,
                                        allocation.isPresent() ? allocation.get().getCurrentQuantity() : "없음", dp.getOrderQuantity());

                                isDeliveryAvailable = false;
                                break;
                            } else {
                                log.info("[OnDemand] 기사({}) 에게 배달할 물품이 존재합니다. ( 물품 : {} ( {}, {} ) 배송지({}) => 현재 수량 {}, 주문 수량 : {} )"
                                        , riderId, dp.getItemName(), dp.getProductBarcode(), dp.getItemCode(), customerOrderId,
                                        allocation.isPresent() ? allocation.get().getCurrentQuantity() : "없음", dp.getOrderQuantity());
                            }
                        }
                    } else {
                        log.info("[OnDemand] 기사({}) 는 물품이 전혀 실려 있지 않습니다.  배송지({}) 에 대한 배차는 불가능 합니다 ", riderId, customerOrderId);
                        isDeliveryAvailable = false;
                    }
                }
            } else {
                log.info("[OnDemand] deliveryId:{}의 DeliveryProduct 리스트가 없습니다.", deliveryId);
                isDeliveryAvailable = false;
            }
        } else {
            isDeliveryAvailable = false;
        }

        return isDeliveryAvailable;
    }

    /**
     * 개별상품의 당일(프로젝트) 전체 현황 조회
     *
     * @param projectId
     * @return
     */
    public List<RiderProductsDTO> getProductsOverallStatusByProject(final Long projectId) {

        final List<ProductAllocation> allocations = productAllocationRepository.findByProjectId(projectId);

        if (CollectionUtils.isNotEmpty(allocations)) {
            List<RiderProductsDTO> riderProductsDTOList = new ArrayList<>();

            final List<Long> overallProductIdList = allocations.stream()
                    .map(ProductAllocation::getProduct)
                    .map(Product::getProductId)
                    .distinct()
                    .collect(Collectors.toList());

            overallProductIdList
                    .forEach(productId -> {
                        Long totalLoading = 0L; // 특정 상품의 당일 전체 상차수량
                        Long totalUnloading = 0L; // 특정 상품의 당일 전체 하차수량 (판매수량)
                        Long totalRemaining = 0L; // 특정 상품의 당일 전체 현재수량 (재고수량)
                        Long totalDisposal = 0L; // 특정 상품의 당일 전체 폐기수량
                        Long totalReservation = 0L; // 특정 상품의 당일 전체 예약수량 (배송완료되지 않은 주문수량)
                        String remark = ""; // 특정 상품의 당일 전체 폐기 사유
                        final Product product = this.getProductByProductId(productId).orElse(null);

                        if (Objects.nonNull(product)) {
                            for (final ProductAllocation allocation : allocations) {
                                if (productId.equals(allocation.getProduct().getProductId())) {
                                    final List<ProductHistory> productHistories = productHistoryService.getProductHistories(allocation.getProductAllocationId());
                                    StringBuffer sb = new StringBuffer();
                                    String disposalReason = null;

                                    String riderName = "";
                                    final Rider rider = riderService.getRider(allocation.getRiderId());
                                    if (Objects.nonNull(rider)) {
                                        riderName = rider.getName();
                                    }

                                    if (CollectionUtils.isNotEmpty(productHistories)) {
                                        for (final ProductHistory history : productHistories) {
                                            final Long disposalQty = history.getDisposalQuantity();
                                            final Long unloadingQty = history.getUnloadingQuantity();
                                            String reason = history.getReason();

                                            if ((Objects.nonNull(disposalQty) && disposalQty > 0 && !StringUtils.equals(reason, ProductInspectionStatus.DISPOSAL.toString()))
                                                    || (Objects.nonNull(unloadingQty) && unloadingQty > 0 && !StringUtils.equals(reason, ProductInspectionStatus.UNLOADING.toString()) && !StringUtils.equals(reason, "일반하차") && !StringUtils.equals(reason, ProductInspectionStatus.CANCELLATION.toString()))
                                            ) {
                                                if (StringUtils.isNotBlank(reason)) {
                                                    sb.append("[" + riderName + "] " + reason + "<br/>");
                                                }
                                            }
                                        }

                                        if (sb.length() > 0) {
                                            disposalReason = sb.toString();
                                        }
                                    }

                                    if (Objects.nonNull(disposalReason)) {
                                        remark += disposalReason;
                                    }

                                    if (Objects.nonNull(allocation.getTotalLoading())) {
                                        totalLoading += allocation.getTotalLoading();
                                    }
                                    if (Objects.nonNull(allocation.getTotalUnloading())) {
                                        totalUnloading += allocation.getTotalUnloading();
                                    }
                                    if (Objects.nonNull(allocation.getCurrentQuantity())) {
                                        totalRemaining += allocation.getCurrentQuantity();
                                    }
                                    if (Objects.nonNull(allocation.getTotalDisposal())) {
                                        totalDisposal += allocation.getTotalDisposal();
                                    }
                                    if (Objects.nonNull(allocation.getReservationQuantity())) {
                                        totalReservation += allocation.getReservationQuantity();
                                    }
                                }
                            }

                            final RiderProductsDTO riderProductsDTO = RiderProductsDTO.builder()
                                    .productId(productId)
                                    .itemCode(product.getItemCode())
                                    .productBarcode(product.getProductBarcode())
                                    .itemName(product.getItemName())
                                    .storageType(product.getStorageType().toString())
                                    .totalLoading(totalLoading)
                                    .totalUnloading(totalUnloading)
                                    .currentQuantity(totalRemaining)
                                    .totalDisposal(totalDisposal)
                                    .reservationQuantity(totalReservation)
                                    .remark(remark)
                                    .build();

                            if (Objects.nonNull(riderProductsDTO) && (riderProductsDTO.getTotalLoading() > 0)) {
                                riderProductsDTOList.add(riderProductsDTO);
                            }
                        }
                    });

            return riderProductsDTOList;
        } else {
            throw new CustomException(HttpStatus.NO_CONTENT, "projectId:" + projectId + "에 해당하는 상품 현황이 없습니다.", false);
        }
    }

    /**
     * 고객에게 상품패킹 알림을 위한 하차시작 설정
     *
     * @param deliveryId
     * @param projectId
     * @param riderId
     */
    @Transactional
    public void setStartProductUnloading(final Long deliveryId,
                                         final Long projectId,
                                         final Long riderId) {

        log.info("[setStartProductUnloading][하차시작] deliveryId:{}", deliveryId);

        DeliveryAllocation deliveryAllocation = null;

        if (Objects.nonNull(projectId) && Objects.nonNull(riderId)) {
            deliveryAllocation = deliveryAllocationRepository.findByProjectIdAndRiderIdAndDeliveryId(projectId, riderId, deliveryId).orElse(null);
        } else if (Objects.isNull(projectId) && Objects.nonNull(riderId)) {
            deliveryAllocation = deliveryAllocationRepository.findByRiderIdAndDeliveryId(riderId, deliveryId).orElse(null);
        } else {
            final List<DeliveryAllocation> deliveryAllocations = deliveryAllocationRepository.findByDeliveryId(deliveryId);
            if (CollectionUtils.isNotEmpty(deliveryAllocations)) {
                deliveryAllocation = deliveryAllocations.get(0);
            }
        }

        if (Objects.nonNull(deliveryAllocation)) {
            if (Objects.nonNull(deliveryAllocation.getStartUnloadingDateTime())) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "이미 하차검수 시작되었습니다. (deliveryId:" + deliveryId + ")", false);
            }

            deliveryAllocation.setStartUnloadingDateTime(LocalDateTime.now());
            deliveryAllocationRepository.save(deliveryAllocation);
        } else {
            log.error("[setStartProductUnloading][하차시작] deliveryAllocation is null. (deliveryId: {})", deliveryId);
        }
    }

    /**
     * 주문접수된 개별상품의 하차여부 조회
     *
     * @param deliveryId
     * @return
     */
    public List<DeliveryProductDTO> checkProductUnloading(final Long deliveryId) {

        List<DeliveryProductDTO> deliveryProductDTOList = new ArrayList<>();
        final List<DeliveryProduct> deliveryProductList = deliveryProductRepository.findByDeliveryId(deliveryId);

        if (CollectionUtils.isNotEmpty(deliveryProductList)) {
            deliveryProductList.forEach(dp -> {
                deliveryProductDTOList.add(DeliveryProductDTO.builder()
                        .itemCode(dp.getItemCode())
                        .productBarcode(dp.getProductBarcode())
                        .itemName(dp.getItemName())
                        .orderQuantity(dp.getOrderQuantity())
                        .isUnloading(dp.getIsUnloading())
                        .build());
                    });
        } else {
            throw new CustomException(HttpStatus.NOT_FOUND, "deliveryId:"  + deliveryId + " 로 주문접수된 상품이 없습니다.", false);
        }

        return deliveryProductDTOList;
    }

    /**
     * 배송완료 이벤트 리스너
     *
     * @param deliveryCompletedEvent
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    @Transactional
    public void onDeliveryCompleted(@NonNull final DeliveryCompletedEvent deliveryCompletedEvent) {

        final DeliveryStatus deliveryStatus = deliveryCompletedEvent.getDeliveryStatus();
        if (!DeliveryStatus.COMPLETED.equals(deliveryStatus)) {
            return;
        }

        final Long deliveryId = deliveryCompletedEvent.getDeliveryId();
        final List<DeliveryProduct> deliveryProductList = deliveryProductRepository.findByDeliveryId(deliveryId);
        if (CollectionUtils.isEmpty(deliveryProductList)) {
            log.info("[ProductService::onDeliveryCompleted] deliveryId:{}로 즉시배송 주문접수된 상품이 없습니다.", deliveryId);
            return;
        }

        this.performAutomaticUnloading(deliveryProductList, deliveryCompletedEvent.getDeliveryAllocationId());
    }

    /**
     * 자동하차 수행
     *
     * @param deliveryProductList
     * @param deliveryAllocationId
     */
    public void performAutomaticUnloading(final List<DeliveryProduct> deliveryProductList,
                                          final Long deliveryAllocationId) {

        final DeliveryAllocation deliveryAllocation = deliveryAllocationRepository.findById(deliveryAllocationId).orElse(null);
        if (Objects.isNull(deliveryAllocation)) {
            return;
        }

        final Long projectId = deliveryAllocation.getProjectId();
        final String orgCodeName = organizationService.getOrganizationCodeByProjectId(projectId);
        if (!StringUtils.equals(HmgConstant.HMG_ORG_CODE_NAME, orgCodeName)) {
            return;
        }

        if (deliveryProductRepository.countByIsUnloading(false) > 0) {
            final Long riderId = deliveryAllocation.getRiderId();

            for (int i = 0; i < deliveryProductList.size(); i++) {
                final DeliveryProduct deliveryProduct = deliveryProductList.get(i);

                if (Boolean.FALSE.equals(deliveryProduct.getIsUnloading())) {
                    log.info("[performAutomaticUnloading] 미하차된 배송완료 상품을 하차 처리합니다. (productBarcode:{}, itemCode:{}, itemName:{}, deliveryId:{})", deliveryProduct.getProductBarcode(), deliveryProduct.getItemCode(), deliveryProduct.getItemName(), deliveryProduct.getDelivery().getId());

                    final ProductInspectionDTO inspectionDTO = ProductInspectionDTO.builder()
                            .productBarcode(deliveryProduct.getProductBarcode())
                            .inspectionStatus(ProductInspectionStatus.UNLOADING)
                            .inspectionQuantity(deliveryProduct.getOrderQuantity())
                            .deliveryId(deliveryProduct.getDelivery().getId())
                            .itemCode(deliveryProduct.getItemCode())
                            .build();

                    final ProductAllocation allocation = this.updateProductInspectionStatus(projectId, riderId, inspectionDTO, i);
                    if (Objects.nonNull(allocation)) {
                        productHistoryService.saveProductHistory(allocation, inspectionDTO);
                    }
                }
            }

            final Long userId = projectRepository.findUserIdByProjectId(projectId);
            pushService.sendProductInspectionAndOrderToWeb(userId, projectId);
        }
    }

    /**
     * 기간별 상품 판매 갯수 함수
     *
     * @param fromDateTime
     * @param toDateTime
     * @param isUnloading
     * @return
     */
    public List<HmgPocProductSheetDTO> getStatProductItemTotalSellCount(final LocalDateTime fromDateTime,
                                                                        final LocalDateTime toDateTime,
                                                                        final Boolean isUnloading) {

        List<DeliveryProduct> deliveryProductList = deliveryProductRepository.findByUpdateAtBetweenAndIsUnloading(fromDateTime, toDateTime, isUnloading);

        List<HmgPocProductSheetDTO> hmgPocProductSheetDTOList = deliveryProductList.stream()
                .collect(Collectors.groupingBy(DeliveryProduct::getProductBarcode))
                .entrySet().stream()
                .map(entry -> {
                    String productBarcode = entry.getKey();
                    List<DeliveryProduct> dpList = entry.getValue();
                    Long totalSellCount = dpList.stream().mapToLong(dp -> dp.getOrderQuantity()).sum();
                    return HmgPocProductSheetDTO.of(productBarcode, dpList.get(0).getItemCode(), dpList.get(0).getItemName(), totalSellCount);
                })
                .sorted(Comparator.comparing(HmgPocProductSheetDTO::getTotalSellCount).reversed()) // 오름 차순으로 변경함.
                .limit(20) //상위 20개만 찾게 함.
                .collect(Collectors.toList());

        return hmgPocProductSheetDTOList;
    }
}
