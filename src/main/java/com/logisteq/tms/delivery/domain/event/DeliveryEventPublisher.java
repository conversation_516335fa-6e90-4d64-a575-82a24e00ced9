package com.logisteq.tms.delivery.domain.event;

import com.logisteq.tms.delivery.domain.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DeliveryEventPublisher {

    private final ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    public DeliveryEventPublisher(final ApplicationEventPublisher applicationEventPublisher) {

        this.applicationEventPublisher = applicationEventPublisher;
    }

    /**
     * 배송출발 이벤트를 발생시킨다.
     * DeliveryAllocationEntityListener에서 배송상태가 GOING으로 변경될 때 호출된다.
     *
     * @param deliveryAllocation
     */
    public void publishDeliveryGoing(final DeliveryAllocation deliveryAllocation) {

        final DeliveryGoingEvent deliveryGoingEvent = DeliveryGoingEvent.of(deliveryAllocation);

        log.info("deliveryId {}에 대한 배송출발 이벤트를 Publish합니다.", deliveryGoingEvent.getDeliveryId());

        applicationEventPublisher.publishEvent(deliveryGoingEvent);
    }

    /**
     * 전달시작 이벤트를 발생시킨다.
     * DeliveryAllocationEntityListener에서 배송상태가 SERVICING으로 변경될 때 호출된다.
     *
     * @param deliveryAllocation
     */
    public void publishDeliveryServicing(final DeliveryAllocation deliveryAllocation) {

        final DeliveryServicingEvent deliveryServicingEvent = DeliveryServicingEvent.of(deliveryAllocation);

        log.info("deliveryId {}에 대한 전달시작 이벤트를 Publish합니다.", deliveryServicingEvent.getDeliveryId());

        applicationEventPublisher.publishEvent(deliveryServicingEvent);
    }

    /**
     * 배송종료(완료/실패 포함) 이벤트를 발생시킨다.
     * DeliveryAllocationEntityListener에서 배송상태가 REJECTED/FAILURE/COMPLETED로 변경될 때 호출된다.
     *
     * @param deliveryAllocation
     */
    public void publishDeliveryEnded(final DeliveryAllocation deliveryAllocation) {

        final DeliveryEndedEvent deliveryEndedEvent = DeliveryEndedEvent.of(deliveryAllocation);

        log.info("deliveryId {}에 대한 배송종료(완료/실패/거절) 이벤트를 Publish합니다.", deliveryEndedEvent.getDeliveryId());

        applicationEventPublisher.publishEvent(deliveryEndedEvent);
    }

    /**
     * 하차검수시작 이벤트를 발생시킨다.
     * DeliveryAllocationEntityListener에서 하차시작시각이 등록될 때 호출된다.
     *
     * @param deliveryAllocation
     */
    public void publishUnloadingStarted(final DeliveryAllocation deliveryAllocation) {

        final DeliveryUnloadingStartedEvent deliveryUnloadingStartedEvent = DeliveryUnloadingStartedEvent.of(deliveryAllocation);

        log.info("deliveryId {}에 대한 하차검수 이벤트를 Publish합니다.", deliveryUnloadingStartedEvent.getDeliveryId());

        applicationEventPublisher.publishEvent(deliveryUnloadingStartedEvent);
    }

    /**
     * 배송완료 이벤트를 발생시킨다.
     * DeliveryCompletedEntityListener에서 신규 배송완료 이력이 등록될 때 호출된다.
     *
     * @param deliveryCompletedHistory
     */
    public void publishDeliveryCompleted(final DeliveryCompletedHistory deliveryCompletedHistory) {

        final DeliveryCompletedEvent deliveryCompletedEvent = DeliveryCompletedEvent.of(deliveryCompletedHistory);

        log.info("deliveryId {}에 대한 배송완료 이벤트를 Publish합니다.", deliveryCompletedEvent.getDeliveryId());

        applicationEventPublisher.publishEvent(deliveryCompletedEvent);
    }

    /**
     * 배송실패 이벤트를 발생시킨다.
     * DeliveryFailureEntityListener에서 신규 배송실패 이력이 등록될 때 호출된다.
     *
     * @param deliveryFailureHistory
     */
    public void publishDeliveryFailure(final DeliveryFailureHistory deliveryFailureHistory) {

        final DeliveryFailureEvent deliveryFailureEvent = DeliveryFailureEvent.of(deliveryFailureHistory);

        log.info("deliveryId {}에 대한 배송실패 이벤트를 Publish합니다.", deliveryFailureEvent.getDeliveryId());

        applicationEventPublisher.publishEvent(deliveryFailureEvent);
    }

    /**
     * 관제자가 배송 완료 승인 이벤트 발생시킴
     * publishDeliveryCs 신규 배송완료 이력이 등록될 때 호출된다.
     *
     * @param deliveryCsHistory
     */
    public void publishDeliveryCs(final DeliveryCsHistory deliveryCsHistory) {

        final DeliveryCsEvent deliveryCsEvent = DeliveryCsEvent.of(deliveryCsHistory);

        log.info("deliveryId {}에 대한 관제자 배송 완료 이벤트를 Publish합니다.", deliveryCsEvent.getDeliveryId());

        applicationEventPublisher.publishEvent(deliveryCsEvent);
    }

    /**
     * 간선 상/하차 이벤트를 발생시킨다.
     *
     * @param trunkLine
     * @param riderName
     * @param riderMobile
     */
    public void publishTrunkLineEvent(final TrunkLine trunkLine,
                                      final String riderName,
                                      final String riderMobile) {

        final TrunkLineEvent trunkLineEvent = TrunkLineEvent.of(trunkLine, riderName, riderMobile);

        log.info("[publishTrunkLineEvent] {}에 대한 {} 이벤트를 Publish합니다.", trunkLineEvent.getSlipNo(), trunkLine.getTrunkLineStatus().toString());

        applicationEventPublisher.publishEvent(trunkLineEvent);
    }

}
