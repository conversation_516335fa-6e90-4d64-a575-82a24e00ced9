package com.logisteq.tms.delivery.domain;

import com.logisteq.tms.delivery.domain.suppl.InspectionStatus;
import lombok.*;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table
@EntityListeners(AuditingEntityListener.class)
public class DeliveryInspectionHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long historyId;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "delivery_allocation_id", nullable = false)
    private DeliveryAllocation deliveryAllocation;

    @Column(nullable = false)
    private String qrBarCode;

    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = InspectionStatus.COLUMN_DEFINITION)
    private InspectionStatus inspectionStatus;

    @Column(columnDefinition = "DATETIME(3)", nullable = false, updatable = false)
    private LocalDateTime statusUpdateDt;
}
