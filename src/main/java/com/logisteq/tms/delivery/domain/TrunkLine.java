package com.logisteq.tms.delivery.domain;

import com.logisteq.tms.delivery.domain.event.TrunkLineEntityListener;
import com.logisteq.tms.delivery.domain.suppl.TrunkLineStatus;
import com.logisteq.tms.user.domain.Organization;
import lombok.*;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@EntityListeners(value = {AuditingEntityListener.class, TrunkLineEntityListener.class})
@Table(indexes = {
        @Index(columnList = "riderId")
})
public class TrunkLine {

    /**
     * 간선 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long trunkLineId;

    /**
     * 조직
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "organization_id", nullable = false)
    private Organization organization;

    /**
     * 기사 ID
     */
    @Column
    private Long riderId;

    /**
     * 스캔한 코드
     */
    @Column(nullable = false)
    private String scanCode;

    /**
     * 간선 상태
     */
    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = TrunkLineStatus.COLUMN_DEFINITION)
    private TrunkLineStatus trunkLineStatus;

    /**
     * 스캔 일시
     */
    @Column(columnDefinition = "DATETIME(3)", nullable = false)
    private LocalDateTime scanDt;

}
