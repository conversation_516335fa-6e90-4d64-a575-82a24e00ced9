package com.logisteq.tms.delivery.domain.event;

import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.DeliveryAllocation;
import com.logisteq.tms.delivery.domain.DeliveryCompletedHistory;
import com.logisteq.tms.delivery.domain.suppl.DeliveryCompletedType;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @created 2021-06-30
 * @project tms-service
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeliveryCompletedEvent {

    // 배송 할당 아이디
    private Long deliveryAllocationId;

    // 배송 아이디
    private Long deliveryId;

    // 고객사 주문 아이디
    private String customerOrderId;

    // 사용자 아이디
    private Long userId;

    // 배송 상태
    private DeliveryStatus deliveryStatus;

    // 배송 완료 종류
    private DeliveryCompletedType deliveryCompletedType;

    // 배송 완료 메시지
    private String deliveryCompletedMessage;

    // 배송 완료 시각
    private LocalDateTime deliveryCompletedDt;

    // 강제배송완료/실패 여부
    private Boolean isForcedComplete;

    public static DeliveryCompletedEvent of(final DeliveryCompletedHistory deliveryCompletedHistory) {

        final DeliveryAllocation deliveryAllocation = deliveryCompletedHistory.getDeliveryAllocation();
        final Delivery delivery = deliveryAllocation.getDelivery();
        final Long deliveryId = delivery.getId();
        final String customerOrderId = delivery.getDetail().getCustomerOrderId();
        final Long userId = delivery.getUserId();
        final DeliveryStatus deliveryStatus = deliveryAllocation.getStatus();
        final Boolean isForcedComplete = deliveryAllocation.getIsForcedComplete();

        return DeliveryCompletedEvent.builder()
                .deliveryAllocationId(deliveryAllocation.getId())
                .deliveryId(deliveryId)
                .customerOrderId(customerOrderId)
                .userId(userId)
                .deliveryStatus(deliveryStatus)
                .deliveryCompletedType(deliveryCompletedHistory.getDeliveryCompletedType())
                .deliveryCompletedMessage(deliveryCompletedHistory.getDeliveryCompletedMessage())
                .deliveryCompletedDt(deliveryCompletedHistory.getDeliveryCompletedDt())
                .isForcedComplete(isForcedComplete)
                .build();
    }

}
