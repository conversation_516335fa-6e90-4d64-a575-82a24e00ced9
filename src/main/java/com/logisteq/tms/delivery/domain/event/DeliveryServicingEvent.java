package com.logisteq.tms.delivery.domain.event;

import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.DeliveryAllocation;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeliveryServicingEvent {

    // 배송 할당 아이디
    private Long deliveryAllocationId;

    // 배송 아이디
    private Long deliveryId;

    // 고객사 주문 아이디
    private String customerOrderId;

    // 사용자 아이디
    private Long userId;

    // 배송 상태 - Servicing이어야 정상임
    private DeliveryStatus deliveryStatus;

    // 배송 상태가 Servicing으로 변경된 시각
    private LocalDateTime realServicingDateTime;

    // 도착예정시각
    private LocalDateTime estimatedArrivalDateTime;

    // 강제배송완료/실패 여부
    private Boolean isForcedComplete;

    public static DeliveryServicingEvent of(final DeliveryAllocation deliveryAllocation) {

        final Delivery delivery = deliveryAllocation.getDelivery();
        final Long deliveryId = delivery.getId();
        final String customerOrderId = delivery.getDetail().getCustomerOrderId();
        final Long userId = delivery.getUserId();
        final DeliveryStatus deliveryStatus = deliveryAllocation.getStatus();
        final LocalDateTime realServicingDateTime = Optional
                .ofNullable(deliveryAllocation.getStatusHistoryTime(deliveryStatus))
                .orElseGet(LocalDateTime::now);
        final LocalDateTime estimatedArrivalDateTime = deliveryAllocation.getRouteRun().getEtaDateTime();
        final Boolean isForcedComplete = deliveryAllocation.getIsForcedComplete();

        return DeliveryServicingEvent.builder()
                .deliveryAllocationId(deliveryAllocation.getId())
                .deliveryId(deliveryId)
                .customerOrderId(customerOrderId)
                .userId(userId)
                .deliveryStatus(deliveryStatus)
                .realServicingDateTime(realServicingDateTime)
                .estimatedArrivalDateTime(estimatedArrivalDateTime)
                .isForcedComplete(isForcedComplete)
                .build();
    }

}
