package com.logisteq.tms.delivery.domain.event;

import com.logisteq.common.util.BeanUtil;
import com.logisteq.tms.delivery.domain.DeliveryAllocation;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.domain.suppl.InspectionStatus;
import com.logisteq.tms.file.domain.suppl.FileCategory;
import com.logisteq.tms.file.domain.suppl.FileType;
import com.logisteq.tms.file.repository.FileRepository;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.PostLoad;
import javax.persistence.PostPersist;
import javax.persistence.PostUpdate;
import javax.persistence.PreUpdate;
import java.time.LocalDateTime;
import java.util.Objects;

@Slf4j
public class DeliveryAllocationEntityListener {

    @PostPersist
    public void onPostPersist(final DeliveryAllocation deliveryAllocation) {

        final DeliveryStatus deliveryStatus = deliveryAllocation.getStatus();
        deliveryAllocation.setPreStatus(deliveryStatus);

        final LocalDateTime startUnloadingDateTime = deliveryAllocation.getStartUnloadingDateTime();
        deliveryAllocation.setPreStartUnloadingDateTime(startUnloadingDateTime);

        deliveryAllocation.setPreInspectionStatus(deliveryAllocation.getInspectionStatus());
        deliveryAllocation.setPreIsInspectionEnded(deliveryAllocation.getIsInspectionEnded());
    }

    @PostLoad
    public void onPostLoad(final DeliveryAllocation deliveryAllocation) {

        final DeliveryStatus deliveryStatus = deliveryAllocation.getStatus();
        deliveryAllocation.setPreStatus(deliveryStatus);

        final LocalDateTime startUnloadingDateTime = deliveryAllocation.getStartUnloadingDateTime();
        deliveryAllocation.setPreStartUnloadingDateTime(startUnloadingDateTime);

        deliveryAllocation.setPreInspectionStatus(deliveryAllocation.getInspectionStatus());
        deliveryAllocation.setPreIsInspectionEnded(deliveryAllocation.getIsInspectionEnded());

        if (deliveryStatus.isDeliveryEnded() || deliveryStatus.isDeliveryServicing()) {
            try {
                final FileRepository fileRepository = BeanUtil.getBean(FileRepository.class);
                final Long fkey = deliveryAllocation.getId();
                final Long fileCountForCompleted = fileRepository.countByFkeyAndCategoryAndType(fkey, FileCategory.DELIVERY_COMPLETE, FileType.IMAGE);
//                final Specification<File> spec = FileSpecs.composeSpecs(fkey, FileCategory.DELIVERY_COMPLETE, FileType.IMAGE);
//                final long fileCountForCompleted = fileRepository.count(spec);

                deliveryAllocation.setFileCountForCompleted(fileCountForCompleted);
            } catch (Exception ignored) {
            }
        }
    }

    @PreUpdate
    public void onPreUpdate(final DeliveryAllocation deliveryAllocation) {

        final InspectionStatus inspectionStatus = deliveryAllocation.getInspectionStatus();
        final Boolean isInspectionEnded = deliveryAllocation.getIsInspectionEnded();

        if (Boolean.TRUE.equals(isInspectionEnded) && InspectionStatus.UNINSPECTED.equals(inspectionStatus)) {
            deliveryAllocation.setIsEffective(false);
        }
    }

    @PostUpdate
    public void onPostUpdate(final DeliveryAllocation deliveryAllocation) {

        final Long deliveryAllocationId = deliveryAllocation.getId();
        final DeliveryStatus preDeliveryStatus = deliveryAllocation.getPreStatus();
        final DeliveryStatus deliveryStatus = deliveryAllocation.getStatus();
        final LocalDateTime preStartUnloadingDateTime = deliveryAllocation.getPreStartUnloadingDateTime();
        final LocalDateTime startUnloadingDateTime = deliveryAllocation.getStartUnloadingDateTime();
        final InspectionStatus preInspectionStatus = deliveryAllocation.getPreInspectionStatus();
        final InspectionStatus inspectionStatus = deliveryAllocation.getInspectionStatus();
        final Boolean preIsInspectionEnded = deliveryAllocation.getPreIsInspectionEnded();
        final Boolean isInspectionEnded = deliveryAllocation.getIsInspectionEnded();

        if (!deliveryStatus.equals(preDeliveryStatus)) {
            log.info("[DeliveryAllocationEntityListener::onPostUpdate] deliveryAllocationId: {}, deliveryStatus: {} -> {}",
                    deliveryAllocationId, preDeliveryStatus, deliveryStatus);
        }

        if (!inspectionStatus.equals(preInspectionStatus)) {
            log.info("[DeliveryAllocationEntityListener::onPostUpdate] deliveryAllocationId: {}, inspectionStatus: {} -> {}",
                    deliveryAllocationId, preInspectionStatus, inspectionStatus);
        }
        if (!isInspectionEnded.equals(preIsInspectionEnded)) {
            log.info("[DeliveryAllocationEntityListener::onPostUpdate] deliveryAllocationId: {}, isInspectionEnded: {} -> {}",
                    deliveryAllocationId, preIsInspectionEnded, isInspectionEnded);
        }

        //리스너 안에서는 의존성 주입(autowired)이 안되기 때문에 아래와 같이 얻어온다.
        final DeliveryEventPublisher deliveryEventPublisher = BeanUtil.getBean(DeliveryEventPublisher.class);

        final boolean isDeliveryGoing = preDeliveryStatus.isDeliveryReady() && deliveryStatus.isDeliveryGoing();
        if (isDeliveryGoing) {
            deliveryEventPublisher.publishDeliveryGoing(deliveryAllocation);
        }

        final boolean isDeliveryServicing = preDeliveryStatus.isDeliveryGoing() && deliveryStatus.isDeliveryServicing();
        if (isDeliveryServicing) {
            deliveryEventPublisher.publishDeliveryServicing(deliveryAllocation);
        }

        final boolean isDeliveryEnded = !preDeliveryStatus.isDeliveryEnded() && deliveryStatus.isDeliveryEnded();
        if (isDeliveryEnded) {
            deliveryEventPublisher.publishDeliveryEnded(deliveryAllocation);
        }

        final boolean isUnloadingStarted = Objects.isNull(preStartUnloadingDateTime) && Objects.nonNull(startUnloadingDateTime);
        if (isUnloadingStarted) {
            deliveryEventPublisher.publishUnloadingStarted(deliveryAllocation);
        }

        /*
        if (deliveryAllocation.getDelivery().getDetail().getCustomerOrderId() != null) {

            final String customerOrderId = getOrganizationManageCode(deliveryAllocation.getDelivery().getDetail().getCustomerOrderId());

            if (customerOrderId.equals("joins")) {
                // 중앙일보에 노티를 주기위한 부분....
            } else if (customerOrderId.equals("timf")) {
                //팀프레시에 노티를 주기 위한 부분...
            } else if (customerOrderId.equals("bunjang")) {
                //번개장터에 노티를 주기 위한 부분...
            }
        }
        */

        deliveryAllocation.setPreStatus(deliveryStatus);
        deliveryAllocation.setPreStartUnloadingDateTime(startUnloadingDateTime);
    }

    private static String getOrganizationManageCode(final String customerOrderId) {

        final String[] ret = customerOrderId.split("_");

        return ret[0];
    }

}
