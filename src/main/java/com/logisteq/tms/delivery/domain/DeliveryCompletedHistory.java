package com.logisteq.tms.delivery.domain;

import com.logisteq.tms.delivery.domain.event.DeliveryCompletedEntityListener;
import com.logisteq.tms.delivery.domain.id.DeliveryCompletedHistoryId;
import com.logisteq.tms.delivery.domain.suppl.DeliveryCompletedType;
import lombok.*;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

@Setter
@Getter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "delivery_completed_history", indexes = {@Index(columnList = "delivery_allocation_id")})
@EntityListeners(value = {AuditingEntityListener.class, DeliveryCompletedEntityListener.class})
@IdClass(DeliveryCompletedHistoryId.class)
public class DeliveryCompletedHistory {
    @Id
    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = "delivery_allocation_id", nullable = false)
    private DeliveryAllocation deliveryAllocation;

    @Id
    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = DeliveryCompletedType.COLUMN_DEFINITION)
    private DeliveryCompletedType deliveryCompletedType;

    @Id
    @Column(nullable = false, updatable = false)
    private LocalDateTime deliveryCompletedDt;

    @Column(nullable = false)
    private String deliveryCompletedMessage;

}
