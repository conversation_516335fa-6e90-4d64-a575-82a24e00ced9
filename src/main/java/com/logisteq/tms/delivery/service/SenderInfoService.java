package com.logisteq.tms.delivery.service;

import com.logisteq.tms.delivery.domain.SenderInfo;
import com.logisteq.tms.delivery.repository.SenderInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@Validated
@Service
public class SenderInfoService {

    private final SenderInfoRepository senderInfoRepository;

    @Autowired
    public SenderInfoService(final SenderInfoRepository senderInfoRepository) {
        this.senderInfoRepository = senderInfoRepository;
    }

    public SenderInfo getSenderInfo(final long senderInfoId) {
        return senderInfoRepository.findById(senderInfoId).orElse(null);
    }

    @Transactional
    public SenderInfo saveSenderInfo(SenderInfo senderInfo) {
        return senderInfoRepository.save(senderInfo);
    }

    //없을 경우 새로 시작함.
    @Deprecated
    @Transactional
    public SenderInfo ifExistSenderInfoThenMappingElseAdd(final String senderName,
                                                          final String senderPhoneNumber,
                                                          final String senderCompanyName,
                                                          final String senderImageUrl,
                                                          final String senderMemo,
                                                          final String baseAddress,
                                                          final String detailAddress) {

        SenderInfo foundSenderInfo = null;

        // 같은 정보를 찾는다.
        try {
            foundSenderInfo = senderInfoRepository.findBySenderNameAndSenderPhoneNumberAndBaseAndDetail(senderName, senderPhoneNumber, baseAddress, detailAddress);
        } catch (Exception e) {
            List<SenderInfo> foundSenderInfoList = senderInfoRepository.findBySenderNameAndSenderPhoneNumberAndBaseAndDetailOrderByCreateAtDesc(senderName, senderPhoneNumber, baseAddress, detailAddress);
            if (CollectionUtils.isNotEmpty(foundSenderInfoList)) {
                foundSenderInfo = foundSenderInfoList.get(0);
            }
        }

        if (Objects.isNull(foundSenderInfo)) {
            final SenderInfo senderInfo = SenderInfo.builder()
                    .senderName(senderName)
                    .senderPhoneNumber(senderPhoneNumber)
                    .senderCompanyName(senderCompanyName)
                    .senderImageUrl(senderImageUrl)
                    .senderMemo(senderMemo)
                    .base(baseAddress)
                    .detail(detailAddress)
                    .build();
            foundSenderInfo = senderInfoRepository.save(senderInfo);
        }

        return foundSenderInfo;
    }

    @Transactional
    public SenderInfo createSenderInfo(final String senderName,
                                       final String senderPhoneNumber,
                                       final String senderCompanyName,
                                       final String senderImageUrl,
                                       final String senderMemo,
                                       final String baseAddress,
                                       final String detailAddress) {

        final SenderInfo senderInfo = SenderInfo.builder()
                .senderName(senderName)
                .senderPhoneNumber(senderPhoneNumber)
                .senderCompanyName(senderCompanyName)
                .senderImageUrl(senderImageUrl)
                .senderMemo(senderMemo)
                .base(baseAddress)
                .detail(detailAddress)
                .build();

        return senderInfoRepository.save(senderInfo);
    }

    public void setPrivacyDataToNull(SenderInfo senderInfo ) {
        if (Objects.nonNull(senderInfo.getSenderName())) {
            senderInfo.setSenderName(null);
        }
        if (Objects.nonNull(senderInfo.getSenderPhoneNumber())) {
            senderInfo.setSenderPhoneNumber(null);
        }
        if (Objects.nonNull(senderInfo.getSenderCompanyName())) {
            senderInfo.setSenderCompanyName(null);
        }
        if (Objects.nonNull(senderInfo.getSenderImageUrl())) {
            senderInfo.setSenderImageUrl(null);
        }
        if (Objects.nonNull(senderInfo.getSenderMemo())) {
            senderInfo.setSenderMemo(null);
        }
        if (Objects.nonNull(senderInfo.getBase())) {
            senderInfo.setBase(null);
        }
        if (Objects.nonNull(senderInfo.getDetail())) {
            senderInfo.setDetail(null);
        }

    }

    /**
     * 발신인 개인정보 파기
     *
     * @param fromDt
     * @param toDt
     */
    @Transactional
    public void destroySenderInfo(final LocalDateTime fromDt,
                                  final LocalDateTime toDt) {

        List<SenderInfo> senderInfoList = senderInfoRepository.findByCreateAtBetween(fromDt, toDt);
        if (CollectionUtils.isNotEmpty(senderInfoList)) {
            for (SenderInfo senderInfo : senderInfoList) {
                setPrivacyDataToNull(senderInfo);
            }

            senderInfoRepository.saveAll(senderInfoList);
            log.info("[destroySenderInfo] {}~{}, {}건의 발신인 개인정보 파기", fromDt, toDt, senderInfoList.size());
        } else {
            log.info("[destroySenderInfo] {}~{}, 파기할 발신인 개인정보가 없습니다.", fromDt, toDt);
        }
    }

}
