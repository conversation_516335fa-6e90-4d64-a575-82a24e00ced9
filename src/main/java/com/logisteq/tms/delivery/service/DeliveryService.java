package com.logisteq.tms.delivery.service;

import com.logisteq.common.dto.track.TrackTotalDistanceDTO;
import com.logisteq.common.exception.CustomException;
import com.logisteq.common.exception.DeliveryNotFoundException;
import com.logisteq.common.exception.InvalidParameterException;
import com.logisteq.common.exception.ItemNotFoundException;
import com.logisteq.common.feign.track.TrackServiceClient;
import com.logisteq.common.redis.DfrStatus;
import com.logisteq.common.util.CommonUtil;
import com.logisteq.common.util.GeometryUtil;
import com.logisteq.tms.address.domain.Address;
import com.logisteq.tms.address.dto.AddressDTO;
import com.logisteq.tms.address.service.AddressService;
import com.logisteq.tms.common.component.TransformerColumnKeyLoader;
import com.logisteq.tms.common.tracksredis.service.TracksRedisService;
import com.logisteq.tms.delivery.constant.DeliveryConstant;
import com.logisteq.tms.delivery.domain.*;
import com.logisteq.tms.delivery.domain.spec.DeliverySpecs;
import com.logisteq.tms.delivery.domain.suppl.DeliveryCategory;
import com.logisteq.tms.delivery.domain.suppl.*;
import com.logisteq.tms.delivery.dto.*;
import com.logisteq.tms.delivery.dto.web.WebProjectDeliveryDTO;
import com.logisteq.tms.delivery.repository.*;
import com.logisteq.tms.delivery.repository.dao.DeliveryCustomerOrderIdDAO;
import com.logisteq.tms.delivery.repository.dao.DeliveryMultipleProjectDAO;
import com.logisteq.tms.external.etc.service.CustomerService;
import com.logisteq.tms.external.etc.utils.CustomerUtil;
import com.logisteq.tms.external.glovis.dto.OrderInvoiceInfoDTO;
import com.logisteq.tms.external.hmg.constant.HmgConstant;
import com.logisteq.tms.external.thehyundai.constant.TheHyundaiCenter;
import com.logisteq.tms.external.thehyundai.constant.TheHyundaiConstant;
import com.logisteq.tms.external.thehyundai.constant.TheHyundaiStore;
import com.logisteq.tms.file.domain.File;
import com.logisteq.tms.file.domain.suppl.FileCategory;
import com.logisteq.tms.file.domain.suppl.FileType;
import com.logisteq.tms.file.dto.FileDTO;
import com.logisteq.tms.file.repository.FileRepository;
import com.logisteq.tms.file.service.FileService;
import com.logisteq.tms.notification.service.NotificationService;
import com.logisteq.tms.privacy.dto.PrivacyRecordDto;
import com.logisteq.tms.privacy.service.PrivacyRecordService;
import com.logisteq.tms.privacy.suppl.PrivacyDataType;
import com.logisteq.tms.privacy.suppl.PrivacyRecordType;
import com.logisteq.tms.privacy.suppl.PrivacyUsageType;
import com.logisteq.tms.product.domain.Product;
import com.logisteq.tms.product.domain.ProductAllocation;
import com.logisteq.tms.product.dto.OrderItemDTO;
import com.logisteq.tms.product.service.ProductService;
import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.domain.suppl.ProjectStatus;
import com.logisteq.tms.project.dto.ProjectDelivery;
import com.logisteq.tms.project.dto.excel.leftpanel.ProjectDestinationSheetDTO;
import com.logisteq.tms.project.repository.ProjectRepository;
import com.logisteq.tms.project.repository.specs.ProjectSpecs;
import com.logisteq.tms.project.service.ProjectBasicService;
import com.logisteq.tms.project.utils.AddressParseUtil;
import com.logisteq.tms.push.service.PushService;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.domain.RiderOrgStatus;
import com.logisteq.tms.rider.domain.RiderProjectSetting;
import com.logisteq.tms.rider.repository.RiderOrgStatusRepository;
import com.logisteq.tms.rider.repository.RiderWorkCompletionRepository;
import com.logisteq.tms.rider.service.RiderProjectSettingService;
import com.logisteq.tms.rider.service.RiderService;
import com.logisteq.tms.route.domain.RouteAppPlan;
import com.logisteq.tms.route.domain.RouteRun;
import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.domain.*;
import com.logisteq.tms.user.repository.RiderDepartmentRepository;
import com.logisteq.tms.user.service.*;
import com.logisteq.tms.vehicle.types.VehicleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.WKTWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Validated
@Service
public class DeliveryService {

    //최대 업로드 갯수
    private static final int MAX_UPLOAD_SAVED_IMAGE_COUNT = 24;
    private static final long SERVICING_DELAY_MIN = 30;
    public static final String NO_GROUP_TEMP_NAME = "권역선택안함";

    public static final Integer FIRST_SPLIT_NUMBER = 1;
    public static final Integer SECOND_SPLIT_NUMBER = 2;

    // DEMO 임시 신규 배송 정보 저장
    // key : 호출한 사용자 아이디
    // value : 배송정보
    private Map<Long, DeliveryDTO> demoNewDeliveryStore = new ConcurrentHashMap<>();

    private final ProjectBasicService projectBasicService;
    private final DeliveryRepository deliveryRepository;
    private final DeliveryAllocationRepository deliveryAllocationRepository;
    private final DeliveryStatusHistoryService deliveryStatusHistoryService;
    private final TrackServiceClient trackServiceClient;
    private final DeliveryBasicService deliveryBasicService;
    private final DeliveryRouteService deliveryRouteService;
    private final RiderService riderService;
    private final RiderProjectSettingService riderProjectSettingService;
    private final RiderWorkCompletionRepository riderWorkCompletionRepository;
    private final GroupService groupService;
    private final ReceiverService receiverService;
    private final SenderInfoService senderInfoService;
    private final AddressService addressService;
    private final FileService fileService;
    private final OrganizationService organizationService;
    private final DeliveryFailureHistoryService deliveryFailureHistoryService;
    private final DeliveryCompletedHistoryService deliveryCompletedHistoryService;
    private final NotificationService notificationService;
    private final PushService pushService;
    private final DeliveryInspectionHistoryService deliveryInspectionHistoryService;
    private final CustomerService customerService;
    private final TracksRedisService tracksRedisService;
    private final UserService userService;
    private final DepartmentService departmentService;
    private final UserDepartmentService userDepartmentService;
    private final ProductService productService;
    private final ProjectRepository projectRepository;
    private final DeliveryVocMessageRepository deliveryVocMessageRepository;
    private final DeliveryGlovisRepository deliveryGlovisRepository;
    private final FileRepository fileRepository;
    private final TransformerColumnKeyLoader transformerColumnKeyLoader;
    private final PrivacyRecordService privacyRecordService;
    private final DeliveryProductRepository deliveryProductRepository;
    private final RiderOrgStatusRepository riderOrgStatusRepository;
    private final TrunkLineRepository trunkLineRepository;
    private final RiderDepartmentRepository riderDepartmentRepository;

    private static final int MAX_PERSONAL_INFO_DESTRUCTION_RECORDS = 300;

    @Autowired
    public DeliveryService(final ProjectBasicService projectBasicService,
                           final DeliveryRepository deliveryRepository,
                           final DeliveryAllocationRepository deliveryAllocationRepository,
                           final DeliveryStatusHistoryService deliveryStatusHistoryService,
                           final TrackServiceClient trackServiceClient,
                           final DeliveryBasicService deliveryBasicService,
                           final DeliveryRouteService deliveryRouteService,
                           final RiderService riderService,
                           final RiderProjectSettingService riderProjectSettingService,
                           final RiderWorkCompletionRepository riderWorkCompletionRepository,
                           final GroupService groupService,
                           final ReceiverService receiverService,
                           final SenderInfoService senderInfoService,
                           final AddressService addressService,
                           final FileService fileService,
                           final OrganizationService organizationService,
                           final DeliveryFailureHistoryService deliveryFailureHistoryService,
                           final DeliveryCompletedHistoryService deliveryCompletedHistoryService,
                           final NotificationService notificationService,
                           final PushService pushService,
                           final DeliveryInspectionHistoryService deliveryInspectionHistoryService,
                           final CustomerService customerService,
                           final TracksRedisService tracksRedisService,
                           final UserService userService,
                           final DepartmentService departmentService,
                           final UserDepartmentService userDepartmentService,
                           final ProductService productService,
                           final ProjectRepository projectRepository,
                           final DeliveryVocMessageRepository deliveryVocMessageRepository,
                           final DeliveryGlovisRepository deliveryGlovisRepository,
                           final FileRepository fileRepository,
                           final TransformerColumnKeyLoader transformerColumnKeyLoader,
                           final PrivacyRecordService privacyRecordService,
                           final DeliveryProductRepository deliveryProductRepository,
                           final RiderOrgStatusRepository riderOrgStatusRepository,
                           final TrunkLineRepository trunkLineRepository,
                           final RiderDepartmentRepository riderDepartmentRepository) {

        this.projectBasicService = projectBasicService;
        this.deliveryRepository = deliveryRepository;
        this.deliveryAllocationRepository = deliveryAllocationRepository;
        this.deliveryStatusHistoryService = deliveryStatusHistoryService;
        this.trackServiceClient = trackServiceClient;
        this.deliveryBasicService = deliveryBasicService;
        this.deliveryRouteService = deliveryRouteService;
        this.riderService = riderService;
        this.senderInfoService = senderInfoService;
        this.riderProjectSettingService = riderProjectSettingService;
        this.riderWorkCompletionRepository = riderWorkCompletionRepository;
        this.groupService = groupService;
        this.receiverService = receiverService;
        this.addressService = addressService;
        this.fileService = fileService;
        this.organizationService = organizationService;
        this.deliveryFailureHistoryService = deliveryFailureHistoryService;
        this.deliveryCompletedHistoryService = deliveryCompletedHistoryService;
        this.notificationService = notificationService;
        this.pushService = pushService;
        this.deliveryInspectionHistoryService = deliveryInspectionHistoryService;
        this.customerService = customerService;
        this.tracksRedisService = tracksRedisService;
        this.userService = userService;
        this.departmentService = departmentService;
        this.userDepartmentService = userDepartmentService;
        this.productService = productService;
        this.projectRepository = projectRepository;
        this.deliveryVocMessageRepository = deliveryVocMessageRepository;
        this.deliveryGlovisRepository = deliveryGlovisRepository;
        this.fileRepository = fileRepository;
        this.transformerColumnKeyLoader = transformerColumnKeyLoader;
        this.privacyRecordService = privacyRecordService;
        this.deliveryProductRepository = deliveryProductRepository;
        this.riderOrgStatusRepository = riderOrgStatusRepository;
        this.trunkLineRepository = trunkLineRepository;
        this.riderDepartmentRepository = riderDepartmentRepository;
    }

    @Transactional
    public Delivery getDeliveryByDeliverDTO(Long userId,
                                            Long projectId,
                                            DeliveryDTO deliveryDto,
                                            Address address,
                                            Group group) {

        checkCustomerProductImageSize(deliveryDto);

        //Group 정보
        Group foundGroup;
        if (Objects.nonNull(deliveryDto.getGroupName()) && Objects.isNull(group)) {
            foundGroup = groupService.ifExistGroupThenMappingElseAdd(userId, deliveryDto.getGroupName());
        } else {
            foundGroup = group;
        }
        Long groupId = null;
        String groupName = "";
        if (Objects.nonNull(foundGroup) && Objects.nonNull(foundGroup.getGroupId())) {
            groupId = foundGroup.getGroupId();
            groupName = foundGroup.getGroupName();
        }

        // Address 정보
        if (Objects.isNull(address)) {
            final AddressDTO addressDto = deliveryDto.getDestinationAddress();
            address = addressService.createAddress(addressDto.getBase(), addressDto.getDetail());
        }

        // Sender 정보
        SenderInfo foundSenderInfo = null;
        if (Objects.nonNull(deliveryDto.getSenderName())
                || Objects.nonNull(deliveryDto.getSenderPhoneNumber())
                || Objects.nonNull(deliveryDto.getSenderCompanyName())
                || Objects.nonNull(deliveryDto.getSenderImageUrl())
                || Objects.nonNull(deliveryDto.getSenderMemo())
                || Objects.nonNull(deliveryDto.getSenderBaseAddress())
                || Objects.nonNull(deliveryDto.getSenderDetailAddress())) {

            foundSenderInfo = senderInfoService.createSenderInfo(deliveryDto.getSenderName(),
                    deliveryDto.getSenderPhoneNumber(),
                    deliveryDto.getSenderCompanyName(),
                    deliveryDto.getSenderImageUrl(),
                    deliveryDto.getSenderMemo(),
                    deliveryDto.getSenderBaseAddress(),
                    deliveryDto.getSenderDetailAddress());
        }

        // Receiver정보
        Receiver foundReceiver = receiverService.createReceiver(deliveryDto.getReceiverName(), deliveryDto.getReceiverOwner(), deliveryDto.getReceiverPhoneNumber(), deliveryDto.getReceiverRealPhoneNumber());

        final List<OrderItemDTO> orderItemList = deliveryDto.getOrderItemList();
        final String productName = ProjectDestinationSheetDTO.convertProductName(orderItemList, deliveryDto.getProductName());

        Delivery newDelivery = Delivery.builder()
                .userId(userId)
                .projectId(projectId)
                .type(deliveryDto.getDeliveryType())
                .groupId(groupId)
                .groupName(groupName)
                .userDefinedOrderNum(deliveryDto.getUserDefinedOrderNum())
                .visitType(Optional.ofNullable(deliveryDto.getVisitType()).orElse(VisitType.DELIVERY))
                .callUserId(Optional.ofNullable(deliveryDto.getCallUserId()).orElse(null))
                .allocations(new ArrayList<>())
                .detail(DeliveryDetail.builder()
                        .productName(productName)
                        .destinationAddress(address)
                        .deliveryStartTime(deliveryDto.getDeliveryStartTime())
                        .deliveryEndTime(deliveryDto.getDeliveryEndTime())
                        .estimatedStayTime(deliveryDto.getDuration())
                        .receiver(foundReceiver)
                        .senderInfo(foundSenderInfo)
                        .productQuantity(Optional.ofNullable(deliveryDto.getProductQuantity()).orElse(1))
                        .productSize(deliveryDto.getProductSize())
                        .deliveryMessage(deliveryDto.getDeliveryMessage())
                        .customerOrderId(appendOrganizationCodeByUserId(deliveryDto.getCustomerOrderId(), userId))
                        .trackingNumber(appendOrganizationCodeByUserId(deliveryDto.getTrackingNumber(), userId))
                        .clientId(appendOrganizationCodeByUserId(deliveryDto.getClientId(), userId))
                        .customerProductImageUrl(DeliveryDTO.convertProductImageUrlsToSingleList(deliveryDto.getCustomerProductImageUrls()))
                        .qrBarCode(deliveryDto.getQrBarCode())
                        .developerField(deliveryDto.getDeveloperField())
                        .orderAmount(deliveryDto.getOrderAmount())
                        .uniqueCode(deliveryDto.getUniqueCode())
                        .warehouseCode(deliveryDto.getWarehouseCode())
                        .deliveryCategory(deliveryDto.getDeliveryCategory())
                        .deliveryTime(deliveryDto.getDeliveryTime())
                        .joCd(deliveryDto.getJoCd())
                        .whGubn(deliveryDto.getWhGubn())
                        .storeCd(deliveryDto.getStoreCd())
                        .storageType(deliveryDto.getStorageType())
                        .customerServiceContact((deliveryDto.getCustomerServiceContact()))
                        .productWidth(deliveryDto.getProductWidth())
                        .productLength(deliveryDto.getProductLength())
                        .productHeight(deliveryDto.getProductHeight())
                        .productWeight(deliveryDto.getProductWeight())
                        .build())
                .pickupPlace(Optional.ofNullable(deliveryDto.getPickupPlace())
                        .map(pickupPlace -> deliveryRepository.findById(pickupPlace.getId()).get())
                        .orElse(null))
                .hailingType(Optional.ofNullable(deliveryDto.getHailingType()).orElse(null))
                .isSendingToCall(Optional.ofNullable(deliveryDto.getIsSendingToCall()).orElse(null))
                .isOnDemand(false/*CollectionUtils.isNotEmpty(orderItemList)*/)
                .deliveryProducts(new ArrayList<>())
                .build();

        return newDelivery;
    }

    public void setDeliveryProductsByOrderItem(final List<OrderItemDTO> orderItemList,
                                               final Delivery delivery) {

        if (CollectionUtils.isNotEmpty(orderItemList) && Objects.nonNull(delivery)) {
            final List<DeliveryProduct> deliveryProducts = orderItemList.stream()
                    .map(order -> DeliveryProduct.builder()
                            .delivery(delivery)
                            .itemCode(order.getItemCode())
                            .productBarcode(Product.convertScannedProductBarcode(order.getProductBarcode(), ""))
                            .itemName(order.getItemName())
                            .orderQuantity(Optional.ofNullable(order.getOrderQuantity()).orElse(1L))
                            .build())
                    .collect(Collectors.toList());

//            final String customerCode = CustomerUtil.extractOrganizationCodeAndUniqueCode(delivery.getDetail().getCustomerOrderId()).getLeft();

            delivery.setDeliveryProducts(deliveryProducts);
            delivery.setIsOnDemand(false/*StringUtils.equals(TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME, customerCode) ? false : true*/);

            long productQuantity = orderItemList.stream()
                    .filter(o -> Objects.nonNull(o.getOrderQuantity()))
                    .mapToLong(OrderItemDTO::getOrderQuantity)
                    .sum();

            delivery.getDetail().setProductQuantity(Integer.valueOf(productQuantity > 1 ? (int) productQuantity : 1));
        } else {
            delivery.setIsOnDemand(false);
        }
    }

    /**
     * 배송 정보 저장
     *
     * @param userId      관리자 사용자 아이디
     * @param projectId   프로젝트 아이디
     * @param deliveryDto 배송 정보
     * @return 저장된 배송 정보
     */
    @Deprecated
    @Transactional
    public Delivery saveDelivery(Long userId,
                                 Long projectId,
                                 DeliveryDTO deliveryDto) {

        Delivery newDelivery = this.getDeliveryByDeliverDTO(userId, projectId, deliveryDto, null, null);
        return deliveryRepository.save(newDelivery);
    }


    /**
     * 배송 정보 저장한다 이미 동일한 CustomerOrderID 가 존재하는 경우 업데이트 한다
     *
     * @param userId      관리자 사용자 아이디
     * @param projectId   프로젝트 아이디
     * @param deliveryDto 배송 정보
     * @param address
     * @return 저장된 배송 정보
     */
    public Delivery updateOrCreateDeliveryOnSameCustomerOrderId(Long userId,
                                                                Long projectId,
                                                                DeliveryDTO deliveryDto,
                                                                Address address) {

        Delivery delivery = null;
        final String customerOrderId = deliveryDto.getCustomerOrderId();

        if (Objects.nonNull(customerOrderId)) {//TODO: address 업데이트
            delivery = this.getDeliveryByProjectIdAndUserIdAndCustomerOrderId(projectId, userId, customerOrderId);
        }

        if (Objects.nonNull(delivery)) {//기존 배송지 업데이트
            log.info("[saveDeliveryWithUpdateSameCustomerId] 동일한 customerID({})를 업데이트합니다.", customerOrderId);
            deliveryDto.setDeliveryId(delivery.getId());
            delivery = this.updateDelivery(deliveryDto, userId, address);
        } else {
            delivery = saveDeliveryWithAddress(userId, projectId, deliveryDto, address);
        }

        return delivery;
    }

    public Delivery getDeliveryByProjectIdAndUserIdAndCustomerOrderId(final Long projectId,
                                                                      final Long userId,
                                                                      final String customerOrderId) {

        if (StringUtils.isBlank(customerOrderId) || Objects.isNull(userId)) {
            return null;
        }

        final Organization org = organizationService.getOrganizationByUserId(userId);
        final String codeName = Optional.ofNullable(org).map(Organization::getCodeName).orElse("");

        return Optional.ofNullable(customerService.getDeliveryInfoByCustomerId(codeName, customerOrderId, projectId, null, null)).orElse(null);
    }

    /**
     * 이미 Address정보가 있을때 배송 정보 저장
     *
     * @param userId      관리자 사용자 아이디
     * @param projectId   프로젝트 아이디
     * @param deliveryDto 배송 정보
     * @param address     배송 정보
     * @return 저장된 배송 정보
     */
    @Transactional
    public Delivery saveDeliveryWithAddress(Long userId,
                                            Long projectId,
                                            DeliveryDTO deliveryDto,
                                            Address address) {

        Delivery newDelivery = this.getDeliveryByDeliverDTO(userId, projectId, deliveryDto, address, null);
        return deliveryRepository.save(newDelivery);
    }

    /**
     * 주소 목록이 있을 때 배송 목록 저장
     *
     * @param userId
     * @param projectId
     * @param deliveryDTOList
     * @param addressList
     * @return
     */
    @Transactional
    public List<Delivery> saveDeliveriesWithAddressList(final Long userId,
                                                        final Long projectId,
                                                        final List<DeliveryDTO> deliveryDTOList,
                                                        final List<Address> addressList) {

        List<Delivery> deliveries = new ArrayList<>();

        for (int i = 0; i < deliveryDTOList.size(); i++) {
            final Address address = addressList.get(i);
            if (AddressService.isValidAddress(address)) {
                final Delivery delivery = this.getDeliveryByDeliverDTO(userId, projectId, deliveryDTOList.get(i), address, null);
                if (Objects.nonNull(delivery)) {
                    deliveries.add(delivery);
                }
            }
        }

        return deliveryRepository.saveAll(deliveries);
    }

    /**
     * 배송 정보  List 저장
     *
     * @param userId        관리자 사용자 아이디
     * @param projectId     프로젝트 아이디
     * @param reqDeliveries 배송 정보 List
     * @return 저장된 배송 정보
     */
    @Transactional
    public List<Delivery> saveDeliveries(Long userId,
                                         Long projectId,
                                         final List<DeliveryDTO> reqDeliveries) {
        List<Delivery> newDeliveries = new ArrayList<>();
        reqDeliveries.forEach(deliveryDTO -> {
            Delivery newDelivery = this.getDeliveryByDeliverDTO(userId, projectId, deliveryDTO, null, null);
            newDeliveries.add(newDelivery);
        });

        return newDeliveries;
    }

    /**
     * 고객사의 customers order id를 Organization에 있는 code와 결합하여 생성한다 :: 타 고객사와의 혹시나 모를 중복을 막기위해.... : 프리픽스만 봐도 어느 고객사의 아이디인지 알기 쉽게
     *
     * @param uniqueCode
     * @param userId
     * @return
     */
    public String appendOrganizationCodeByUserId(String uniqueCode,
                                                 Long userId) {

        if (Objects.nonNull(uniqueCode)) {
            Organization org = organizationService.getOrganizationByUserId(userId);

            if (Objects.nonNull(org) && !uniqueCode.contains(org.getCodeName())) {
                return CustomerUtil.appendOrganizationCode(org.getCodeName(), uniqueCode);
            } else {
                return uniqueCode;
            }
        }

        return null;
    }

    /**
     * 배송 정보 업데이트 - 배차에 영향을 주지 않는 단순 데이터만 업데이트 한다
     *
     * @param deliveryDto
     * @param userId
     * @return
     */
    @Transactional
    public Delivery updateSimpleDataDelivery(DeliveryDTO deliveryDto,
                                             Long userId) {

        Delivery delivery = deliveryRepository.findById(deliveryDto.getDeliveryId()).orElseThrow(() -> new ItemNotFoundException("기사를 찾을 수 없습니다. (" + deliveryDto.getDeliveryId() + ")"));

        DeliveryDetail deliveryDetail = delivery.getDetail();

        log.info("동일 배송지를 업데이트합니다. customerOrderId: {} ", deliveryDetail.getCustomerOrderId());

        if (deliveryDto.getDeliveryMessage() != null) {
            deliveryDetail.setDeliveryMessage(deliveryDto.getDeliveryMessage());
        }

        if (deliveryDto.getQrBarCode() != null) {
            deliveryDetail.setQrBarCode(deliveryDto.getQrBarCode());
        }

        if (deliveryDto.getCustomerProductImageUrls() != null && deliveryDto.getCustomerProductImageUrls().length > 0) {
            deliveryDetail.setCustomerProductImageUrl(DeliveryDTO.convertProductImageUrlsToSingleList(deliveryDto.getCustomerProductImageUrls()));
        }

        if (StringUtils.isNotEmpty(deliveryDto.getTrackingNumber())) {
            String trackingNumberIdWithCode = appendOrganizationCodeByUserId(deliveryDto.getTrackingNumber(), userId);
            deliveryDetail.setTrackingNumber(trackingNumberIdWithCode);
        }

        if (StringUtils.isNotEmpty(deliveryDto.getClientId())) {
            String clientIdWithCode = appendOrganizationCodeByUserId(deliveryDto.getClientId(), userId);
            deliveryDetail.setClientId(clientIdWithCode);
        }

        if (deliveryDto.getProductSize() != null) {
            deliveryDetail.setProductSize(deliveryDto.getProductSize());
        }

        if (deliveryDto.getReceiverName() != null || deliveryDto.getReceiverOwner() != null || deliveryDto.getReceiverPhoneNumber() != null) {
            Receiver foundReceiver = receiverService.createReceiver(deliveryDto.getReceiverName(), deliveryDto.getReceiverOwner(), deliveryDto.getReceiverPhoneNumber(), deliveryDto.getReceiverRealPhoneNumber());
            deliveryDetail.setReceiver(foundReceiver);
        }

        return deliveryRepository.save(delivery);
    }

    @Transactional
    public Delivery updateDelivery(DeliveryDTO deliveryDto,
                                   Long userId,
                                   Address address) {

        checkCustomerProductImageSize(deliveryDto);

        Delivery delivery = deliveryRepository.findById(deliveryDto.getDeliveryId()).orElseThrow(() -> new ItemNotFoundException("기사를 찾을 수 없습니다. (" + deliveryDto.getDeliveryId() + ")"));

        Receiver foundReceiver = receiverService.createReceiver(deliveryDto.getReceiverName(), deliveryDto.getReceiverOwner(), deliveryDto.getReceiverPhoneNumber(), deliveryDto.getReceiverRealPhoneNumber());

        delivery.setUserDefinedOrderNum(deliveryDto.getUserDefinedOrderNum());

        if (Objects.nonNull(deliveryDto.getVisitType())) {
            delivery.setVisitType(deliveryDto.getVisitType());
        }

        DeliveryDetail deliveryDetail = delivery.getDetail();

        //group Allocation 변경
        if (delivery.getGroupName() != deliveryDto.getGroupName()) {
            delivery.setGroupName(deliveryDto.getGroupName());
            if (Objects.isNull(deliveryDto.getGroupName()) || NO_GROUP_TEMP_NAME.equals(deliveryDto.getGroupName())) {
                delivery.setGroupId(null);
            } else {
                Long groupId = groupService.getGroupIdByProjectIdAndGroupName(deliveryDto.getProjectId(), deliveryDto.getGroupName());

                if (Objects.isNull(groupId)) {
                    Group newGroup = groupService.ifExistGroupThenMappingElseAdd(userId, deliveryDto.getGroupName());
                    groupId = newGroup.getGroupId();
                }
                delivery.setGroupId(groupId);
            }
        }

        final List<OrderItemDTO> orderItemList = deliveryDto.getOrderItemList();
        final List<DeliveryProduct> deliveryProductList = delivery.getDeliveryProducts();
        if (CollectionUtils.isEmpty(deliveryProductList) && CollectionUtils.isNotEmpty(orderItemList)) {
            this.setDeliveryProductsByOrderItem(orderItemList, delivery);
        } else if (CollectionUtils.isNotEmpty(deliveryProductList)) {
            long productQuantity = deliveryProductList.stream()
                    .filter(dp -> Objects.nonNull(dp.getOrderQuantity()))
                    .mapToLong(DeliveryProduct::getOrderQuantity)
                    .sum();

            deliveryDetail.setProductQuantity(Integer.valueOf(productQuantity > 1 ? (int) productQuantity : 1));
        } else {
            deliveryDetail.setProductQuantity(deliveryDto.getProductQuantity());
        }

        final String qrBarCode = deliveryDto.getQrBarCode();

        deliveryDetail.setProductName(ProjectDestinationSheetDTO.convertProductName(orderItemList, deliveryDto.getProductName()));
        deliveryDetail.setDeliveryStartTime(deliveryDto.getDeliveryStartTime());
        deliveryDetail.setDeliveryEndTime(deliveryDto.getDeliveryEndTime());
        deliveryDetail.setEstimatedStayTime(deliveryDto.getDuration());
        deliveryDetail.setDeliveryMessage(deliveryDto.getDeliveryMessage());
        deliveryDetail.setQrBarCode(qrBarCode);

        if (Objects.nonNull(qrBarCode)) {
            deliveryDetail.setQrBarCode(qrBarCode);
        }

        if (deliveryDto.getCustomerProductImageUrls() != null && deliveryDto.getCustomerProductImageUrls().length > 0) {
            deliveryDetail.setCustomerProductImageUrl(DeliveryDTO.convertProductImageUrlsToSingleList(deliveryDto.getCustomerProductImageUrls()));
        }

        if (StringUtils.isNotBlank(deliveryDto.getCustomerOrderId())) {
            String customerOrderIdWithCode = appendOrganizationCodeByUserId(deliveryDto.getCustomerOrderId(), userId);
            deliveryDetail.setCustomerOrderId(customerOrderIdWithCode);
        }

        if (StringUtils.isNotBlank(deliveryDto.getTrackingNumber())) {
            String trackingNumberIdWithCode = appendOrganizationCodeByUserId(deliveryDto.getTrackingNumber(), userId);
            deliveryDetail.setTrackingNumber(trackingNumberIdWithCode);
        }

        if (StringUtils.isNotBlank(deliveryDto.getClientId())) {
            String clientIdWithCode = appendOrganizationCodeByUserId(deliveryDto.getClientId(), userId);
            deliveryDetail.setClientId(clientIdWithCode);
        }

        if (ObjectUtils.isNotEmpty(deliveryDto.getProductWidth())) {
            deliveryDetail.setProductWidth(deliveryDto.getProductWidth());
        }

        if (ObjectUtils.isNotEmpty(deliveryDto.getProductLength())) {
            deliveryDetail.setProductLength(deliveryDto.getProductLength());
        }

        if (ObjectUtils.isNotEmpty(deliveryDto.getProductHeight())) {
            deliveryDetail.setProductHeight(deliveryDto.getProductHeight());
        }

        if (ObjectUtils.isNotEmpty(deliveryDto.getProductWeight())) {
            deliveryDetail.setProductWeight(deliveryDto.getProductWeight());
        }

        deliveryDetail.setReceiver(foundReceiver);
        deliveryDetail.setProductSize(deliveryDto.getProductSize());

        if (Objects.nonNull(address)) {
            deliveryDetail.setDestinationAddress(address);
        } else {
            AddressDTO addressDTO = deliveryDto.getDestinationAddress();
            if (Objects.nonNull(addressDTO)) {
                final String baseAddr = deliveryDetail.getDestinationAddress().getBase();
                final String detailAddr = deliveryDetail.getDestinationAddress().getDetail();
                final String dtoBaseAddr = Optional.ofNullable(addressDTO.getBase()).map(String::trim).orElse(null);
                final String dtoDetailAddr = Optional.ofNullable(addressDTO.getDetail()).map(String::trim).orElse(null);

                if (!StringUtils.equals(baseAddr, dtoBaseAddr) || !StringUtils.equals(detailAddr, dtoDetailAddr)) {
                    final Address newAddress = addressService.createAddress(addressDTO.getBase(), addressDTO.getDetail());
                    if (Objects.nonNull(newAddress)) {
                        deliveryDetail.setDestinationAddress(newAddress);
                    }
                }
            }
        }

        return deliveryRepository.save(delivery);
    }

    /**
     * 배송 목록 저장
     * (데모용)
     *
     * @param userId        관리자 유저 아이디
     * @param projectId     프로젝트 아이디
     * @param riderId
     * @param reqDeliveries 배송 목록 정보
     */
    @Deprecated
    @Transactional
    public List<Delivery> saveDeliveriesByRiderId(Long userId,
                                                  Long projectId,
                                                  Long riderId,
                                                  final List<DeliveryDTO> reqDeliveries) {//EV_Demo add riderId

        List<DeliveryDTO> newDeliveries = reqDeliveries;
        List<Delivery> deliveries = this.getAllDeliveries(projectId, null, null, null, userId, riderId);//EV_Demo riderId추가함.
        if (!deliveries.isEmpty()) {

            // 배송 순서가 변경되는 첫번째 배송지를 찾는다.
            Delivery selectDelivery = deliveries.stream()
                    .filter(d -> reqDeliveries.stream()
                            .filter(rd -> rd.getDeliveryId() != null)
                            .anyMatch(rd -> rd.getDeliveryId().longValue() == d.getId().longValue())
                    )
                    .sorted((d1, d2) -> {
                        DeliveryAllocation allocationTmp1 = d1.getAllocations().get(0); // TODO : riderId값을 파라미터로 받아와야한다.
                        DeliveryAllocation allocationTmp2 = d2.getAllocations().get(0); // TODO : riderId값을 파라미터로 받아와야한다.
                        return allocationTmp1.getOrderNum().compareTo(allocationTmp2.getOrderNum());
                    })
                    .findFirst()
                    .orElse(null);

            // 변경되는 첫번째 배송지 부터 나머지 배송지에 대한 경로 정보를 변경한다.
            deliveries.stream()
                    .filter(d -> {
                        assert selectDelivery != null;
                        return d.getAllocations().get(0).getOrderNum() >= selectDelivery.getAllocations().get(0).getOrderNum();
                    })
                    .forEach(delivery -> {
                        DeliveryDTO reqDelivery = reqDeliveries.stream()
                                .filter(reqD -> reqD.getDeliveryId() != null)
                                .filter(reqD -> reqD.getDeliveryId().longValue() == delivery.getId().longValue())
                                .findAny().orElse(null);

                        DeliveryAllocation allocation = delivery.getAllocations().get(0);
                        if (reqDelivery != null) {

                            allocation.setOrderNum(reqDelivery.getOrderNum());

                            if (reqDelivery.getRoutePath().size() == 1) {//[mobile-demo] demo 에서 들어간 코드 부분. 필요한지는 체크 바람
                                reqDelivery.getRoutePath().add(reqDelivery.getRoutePath().get(0));        // ALOA-131 동일한 위치가 연속되는 경우에는 경로가 점으로 온다. 따라서, 점을 하나 더 찍어서 선으로 만든다.
                            }

                            final LineString routePath = GeometryUtil.createLineStringFromCoordinateDTO(reqDelivery.getRoutePath());

                            deliveryRouteService.updateInitialRoutes(allocation,
                                    reqDelivery.getPredictionDistance(),
                                    reqDelivery.getPredictionTime(),
                                    routePath,
                                    Optional.ofNullable(delivery.getDetail().getEstimatedStayTime()).orElse(0L) * 60L);
                        }
                    });

            // 신규 배송지 필터링 (deliveryId가 없으면 신규 배송지)
            newDeliveries = reqDeliveries.stream()
                    .filter(d -> d.getDeliveryId() == null)
                    .collect(Collectors.toList());
        }

        // 중복 데이터 유효성 검사
        newDeliveries.stream()
                .filter(delivery -> delivery.getProjectId() != null && delivery.getRiderId() != null && delivery.getOrderNum() != null)
                .collect(Collectors.groupingBy(delivery -> Triple.of(delivery.getProjectId(), delivery.getRiderId(), delivery.getOrderNum())))
                .forEach((key, value) -> {
                    if (value.size() > 1) {
                        throw new RuntimeException("요청 데이터 내에 중복된 배송 순서가 존재 합니다. projectId(" + key.getLeft() + "), riderId(" + key.getMiddle() + "), orderNum(" + key.getRight() + ")");
                    }
                });

        // 신규 배송지 추가
        for (DeliveryDTO dto : newDeliveries) {
            // Address 정보
            final AddressDTO addressDto = dto.getDestinationAddress();
            Address address = addressService.createAddress(addressDto.getBase(), addressDto.getDetail());

            final List<OrderItemDTO> orderItemList = dto.getOrderItemList();
            final String productName = ProjectDestinationSheetDTO.convertProductName(orderItemList, dto.getProductName());

            // 배송 정보
            Delivery newDelivery = Delivery.builder()
                    .userId(userId)
                    .projectId(projectId)
                    .type(dto.getDeliveryType())
                    .detail(DeliveryDetail.builder()
                            .productName(productName)
                            .productSize(dto.getProductSize())
                            .destinationAddress(address)
                            .customerOrderId(dto.getCustomerOrderId())
                            .trackingNumber(dto.getTrackingNumber())
                            .clientId(dto.getClientId())
                            .customerProductImageUrl(DeliveryDTO.convertProductImageUrlsToSingleList(dto.getCustomerProductImageUrls()))
                            .build())
                    .callUserId(dto.getCallUserId())
                    .hailingType(dto.getHailingType())
                    .isSendingToCall(false)
                    .isOnDemand(false/*CollectionUtils.isNotEmpty(orderItemList)*/)
                    .build();

            if (dto.getRiderId() != null) {
                long count = deliveryRepository.countByUserIdAndProjectIdAndAllocationsRiderIdAndAllocationsOrderNum(userId, projectId, dto.getRiderId(), dto.getOrderNum());
                if (count > 0) {
                    throw new RuntimeException("중복되는 배송 순서가 이미 존재 합니다. projectId(" + projectId + "), riderId(" + dto.getRiderId() + "), orderNum(" + dto.getOrderNum() + ")");
                }

                final LineString routePath = GeometryUtil.createLineStringFromCoordinateDTO(dto.getRoutePath());
                final Double estimatedMeters = dto.getPredictionDistance();
                final Long estimatedSeconds = dto.getPredictionTime();
                final Long staySeconds = Optional.ofNullable(dto.getDuration()).orElse(0L) * 60L;

                newDelivery.setAllocations(Collections.singletonList(
                        DeliveryAllocation.builder()
                                .delivery(newDelivery)
                                .projectId(projectId)
                                .riderId(dto.getRiderId())
                                .orderNum(dto.getOrderNum())
                                .routeAppPlan(RouteAppPlan.builder()
                                        .estimatedMeters(estimatedMeters)
                                        .estimatedSeconds(estimatedSeconds)
                                        .routePath(routePath)
                                        .staySeconds(staySeconds)
                                        .build())
                                .routeRun(RouteRun.builder()
                                        .estimatedMeters(0.)
                                        .estimatedSeconds(0L)
                                        .routePath(null)
                                        .build())
                                .build()));
            }

            deliveries.add(newDelivery);
        }

        // 배송 목록 저장
        List<Delivery> savedDeliveries = deliveryRepository.saveAll(deliveries);

        List<DeliveryAllocation> deliveryAllocationList = deliveryBasicService.getDeliveryAllocationListByProjectId(projectId);

        // 배송 상태 내역 저장
        deliveryStatusHistoryService.insertDeliveryStatusHistoryOfNewDeliveries(deliveryAllocationList);

        return savedDeliveries;
    }

    /**
     * 배송 목록 조회 (with paging)
     *
     * @param projectId          프로젝트 아이디
     * @param pageable           페이징 정보
     * @param dateFrom           시작 날짜
     * @param dateTo             종료 날짜
     * @param deliveryStatusList 배송 상태 목록
     * @param userId             관리자 사용자 아이디
     * @param riderId            라이더 아이디
     * @return 배송 목록
     */
    public Page<Delivery> getDeliveries(Long projectId,
                                        Pageable pageable,
                                        LocalDateTime dateFrom,
                                        LocalDateTime dateTo,
                                        List<DeliveryStatus> deliveryStatusList,
                                        Long userId,
                                        Long riderId,
                                        List<Long> deliveryIds) {

        Specification<Delivery> specs = DeliverySpecs.composeSpecs(userId, riderId, projectId, dateFrom, dateTo, deliveryStatusList, deliveryIds);

        specs = Objects.requireNonNull(specs).and(DeliverySpecs.eqDeleted(false));
        Page<Delivery> deliveryList = deliveryRepository.findAll(specs, pageable);

        deliveryList.getContent().forEach(d -> d.setGroupName(d.getGroupId() != null ? groupService.getGroup(d.getGroupId()).getGroupName() : ""));//delivery에 그룹네임추가

        return deliveryList;
    }

    /**
     * 전체 배송 목록 조회
     *
     * @param projectId          프로젝트 아이디
     * @param dateFrom           시작 날짜
     * @param dateTo             종료 날짜
     * @param deliveryStatusList 배송 상태 목록
     * @param userId             관리자 사용자 아이디
     * @param riderId            라이더 아이디
     * @return 배송 목록
     */
    public List<Delivery> getAllDeliveries(final Long projectId,
                                           final LocalDateTime dateFrom,
                                           final LocalDateTime dateTo,
                                           final List<DeliveryStatus> deliveryStatusList,
                                           final Long userId,
                                           final Long riderId) {

        Specification<Delivery> specs = DeliverySpecs.composeSpecs(userId, riderId, projectId, dateFrom, dateTo, deliveryStatusList, null);
        specs = Objects.requireNonNull(specs).and(DeliverySpecs.eqDeleted(false));
        final List<Delivery> deliveryList = deliveryRepository.findAll(specs);

        if (CollectionUtils.isNotEmpty(deliveryList)) {
            final Map<Long, String> groupNameMap = deliveryList.stream()
                    .map(Delivery::getGroupId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toMap(groupId -> groupId, groupId -> groupService.getGroup(groupId).getGroupName()));

            deliveryList.forEach(d -> d.setGroupName(Optional.ofNullable(d.getGroupId()).map(groupNameMap::get).orElse("")));
        }

        return deliveryList;
    }

    /**
     * 배송 목록 갯수 조회
     *
     * @param projectId          프로젝트 아이디
     * @param dateFrom           시작 날짜
     * @param dateTo             종료 날짜
     * @param deliveryStatusList 배송 상태 목록
     * @param userId             관리자 사용자 아이디
     * @param riderId            라이더 아이디
     * @return 배송 목록 갯수
     */
    public long getCountOfDeliveries(final Long projectId,
                                     final LocalDateTime dateFrom,
                                     final LocalDateTime dateTo,
                                     final List<DeliveryStatus> deliveryStatusList,
                                     final Long userId,
                                     final Long riderId,
                                     final List<Long> deliveryIds) {

        Specification<Delivery> specs = DeliverySpecs.composeSpecs(userId, riderId, projectId, dateFrom, dateTo, deliveryStatusList, deliveryIds);
        specs = Objects.requireNonNull(specs).and(DeliverySpecs.eqDeleted(false));
        return deliveryRepository.count(specs);
    }

    /**
     * 방문지 목록 조회 (방문지 관리에 사용)
     *
     * @param userRole
     * @param userId
     * @param filterDepartmentIdList
     * @param keyword
     * @param locationKeyword
     * @param dateFrom
     * @param dateTo
     * @param pageable
     * @return
     */
    public Slice<Delivery> getDeliveriesForManagement(final RoleType userRole,
                                                      final Long userId,
                                                      final List<Long> filterDepartmentIdList,
                                                      final String keyword,
                                                      final String locationKeyword,
                                                      final LocalDateTime dateFrom,
                                                      final LocalDateTime dateTo,
                                                      final Pageable pageable) {

        Slice<Delivery> deliveryPage = null;
        String receiverName = null;
        String addressLocation = null;

        if (StringUtils.isNotEmpty(keyword) && !StringUtils.isWhitespace(keyword)) {
            receiverName = "%" + StringUtils.strip(keyword) + "%";
        }
        if (StringUtils.isNotEmpty(locationKeyword)) {
            addressLocation = locationKeyword + "%";
        }

        final User user = userService.getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));

        if (RoleType.ROLE_ADMIN.equals(userRole)) {
            deliveryPage = deliveryRepository.findAdminDeliveryAllPage(pageable, null, null, null, receiverName, addressLocation, null, dateFrom, dateTo, transformerColumnKeyLoader.getSecretKey());
        } else if (RoleType.ROLE_ORG_ADMIN.equals(userRole) || RoleType.ROLE_COMMON.equals(userRole)) {
            final List<Project> authorizedProjectList = this.getAuthorizedProjectList(user, filterDepartmentIdList);
            if (CollectionUtils.isEmpty(authorizedProjectList)) {
                return new SliceImpl<>(Collections.emptyList(), pageable, false);
            }
            final List<Long> authorizedProjectIdList = authorizedProjectList.stream().map(Project::getId).collect(Collectors.toList());

            deliveryPage = deliveryRepository.findProjectDeliveryAllPage(pageable, authorizedProjectIdList, null, null, null, receiverName, addressLocation, null, dateFrom, dateTo, transformerColumnKeyLoader.getSecretKey());
        } else {
            deliveryPage = deliveryRepository.findUserDeliveryAllPage(pageable, Arrays.asList(userId), null, null, null, receiverName, addressLocation, null, dateFrom, dateTo, transformerColumnKeyLoader.getSecretKey());
        }

        deliveryPage.getContent()
                .forEach(d -> d.setGroupName(d.getGroupId() != null ? groupService.getGroup(d.getGroupId()).getGroupName() : ""));//delivery에 그룹네임추가

        return deliveryPage;
    }

    /**
     * 배송지(VOC) 목록 조회 API (배송지(VOC) 관리에 사용)
     *
     * @param userRole
     * @param userId
     * @param orgCodeName
     * @param searchValue
     * @param filterDepartmentIdList
     * @param keyword
     * @param vocStatus
     * @param dateFrom
     * @param dateTo
     * @param pageable
     * @return
     */
    public Slice<Delivery> getDeliveriesForManagement(final RoleType userRole,
                                                      final Long userId,
                                                      final String orgCodeName,
                                                      final Long searchValue,
                                                      final List<Long> filterDepartmentIdList,
                                                      final String keyword,
                                                      final VOCStatus vocStatus,
                                                      final LocalDateTime dateFrom,
                                                      final LocalDateTime dateTo,
                                                      final Pageable pageable) {

        Slice<Delivery> deliveryPage = null;
        String customerOrderId = null;
        String receiverOwner = null;
        String receiverPhoneNumber = null;
        String vocStatusString = null;

        if (StringUtils.isNotEmpty(keyword) && !StringUtils.isWhitespace(keyword)) {
            if (searchValue == 0) {
                if (StringUtils.isNotEmpty(orgCodeName)) {
                    customerOrderId = orgCodeName + "%" + StringUtils.strip(keyword) + "%";
                } else {
                    customerOrderId = "%" + StringUtils.strip(keyword) + "%";
                }
            } else if (searchValue == 1) {
                receiverOwner = StringUtils.strip(keyword);
            } else if (searchValue == 2) {
                receiverPhoneNumber = StringUtils.strip(keyword);
            }
        }

        if (vocStatus != null) {
            vocStatusString = vocStatus.getEnglish().get(0);
        }

        final User user = userService.getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));

        if (RoleType.ROLE_ADMIN.equals(userRole)) {
            deliveryPage = deliveryRepository.findAdminDeliveryAllPage(pageable, customerOrderId, receiverOwner, receiverPhoneNumber, null, null, vocStatusString, dateFrom, dateTo, transformerColumnKeyLoader.getSecretKey());
        } else if (RoleType.ROLE_ORG_ADMIN.equals(userRole) || RoleType.ROLE_COMMON.equals(userRole)) {
            final List<Project> authorizedProjectList = this.getAuthorizedProjectList(user, filterDepartmentIdList);
            if (CollectionUtils.isEmpty(authorizedProjectList)) {
                return new SliceImpl<>(Collections.emptyList(), pageable, false);
            }
            final List<Long> authorizedProjectIdList = authorizedProjectList.stream().map(Project::getId).collect(Collectors.toList());

            deliveryPage = deliveryRepository.findProjectDeliveryAllPage(pageable, authorizedProjectIdList, customerOrderId, receiverOwner, receiverPhoneNumber, null, null, vocStatusString, dateFrom, dateTo, transformerColumnKeyLoader.getSecretKey());
        } else {
            deliveryPage = deliveryRepository.findUserDeliveryAllPage(pageable, Arrays.asList(userId), customerOrderId, receiverOwner, receiverPhoneNumber, null, null, vocStatusString, dateFrom, dateTo, transformerColumnKeyLoader.getSecretKey());
        }

        deliveryPage.getContent()
                .forEach(d -> d.setGroupName(d.getGroupId() != null ? groupService.getGroup(d.getGroupId()).getGroupName() : ""));//delivery에 그룹네임추가

        return deliveryPage;
    }

    /**
     * VOC 관리 목록 조회 (프로젝트별)
     */
    public Slice<Delivery> getProjectDeliveriesForManagement(final Long projectId,
                                                             final String orgCodeName,
                                                             final Long searchValue,
                                                             final String keyword,
                                                             final LocalDateTime dateFrom,
                                                             final LocalDateTime dateTo,
                                                             final Pageable pageable) {

        String customerOrderId = null;
        String receiverOwner = null;
        String receiverPhoneNumber = null;

        if (StringUtils.isNotEmpty(keyword) && !StringUtils.isWhitespace(keyword)) {
            if (searchValue == 0) {
                if (StringUtils.isNotEmpty(orgCodeName)) {
                    customerOrderId = orgCodeName + "%" + StringUtils.strip(keyword) + "%";
                } else {
                    customerOrderId = "%" + StringUtils.strip(keyword) + "%";
                }
            } else if (searchValue == 1) {
                receiverOwner = StringUtils.strip(keyword);
            } else if (searchValue == 2) {
                receiverPhoneNumber = StringUtils.strip(keyword);
            }
        }

        final Slice<Delivery> deliveryPage = deliveryRepository.findProjectDeliveryAllPage(pageable, Arrays.asList(projectId), customerOrderId, receiverOwner, receiverPhoneNumber, null, null, null, dateFrom, dateTo, transformerColumnKeyLoader.getSecretKey());

        deliveryPage.getContent()
                .forEach(d -> d.setGroupName(d.getGroupId() != null ? groupService.getGroup(d.getGroupId()).getGroupName() : ""));//delivery에 그룹네임추가

        return deliveryPage;
    }

    public List<Project> getAuthorizedProjectList(@NotNull final User user,
                                                  final List<Long> filterDepartmentIdList) {

        final RoleType userRole = userService.getUserRoleByUser(user);
        final Long orgId = user.getOrganizationId();
        final Organization organization = organizationService.getOrganizationById(orgId);
        final boolean orgHasDepartment = departmentService.existDepartmentOfOrganization(organization);
        final List<User> sameOrgUserList = Optional.ofNullable(orgId)
                .map(userService::getUserByOrganization)
                .orElseGet(() -> Arrays.asList(user));
        final List<Department> authorizedDepartmentList = userDepartmentService.getAuthorizedDepartmentListOfUser(user, organization, true);
        final List<Department> filterDepartmentList = departmentService.getDescendantDepartmentListOfDepartmentIdList(filterDepartmentIdList);

        if (CollectionUtils.isNotEmpty(authorizedDepartmentList)) {
            log.info("getAuthorizedProjectIdList: 부서아이디 {}에 대해 조회합니다.",
                    authorizedDepartmentList.stream().map(Department::getDepartmentId).collect(Collectors.toList()));
        }

        Specification<Project> projectSpecs = ProjectSpecs.hasAuthority(userRole, user, organization, orgHasDepartment, sameOrgUserList, authorizedDepartmentList, filterDepartmentList);
        // 빈 프로젝트를 project list에 안보이기 해기 위함. 더 좋은 방법이 없을까요.
        projectSpecs = projectSpecs.and(ProjectSpecs.isNotNullClusterDone());
        projectSpecs = projectSpecs.and(ProjectSpecs.eqDeleted(false));

        final List<Project> authorizedProjectList = projectRepository.findAll(projectSpecs);

        return authorizedProjectList;
    }

    /**
     * 방문지들 삭제 (방문지 관리에 사용)
     *
     * @param userIdList
     * @param deliveryIdList
     * @param userId
     * @return
     */
    @Transactional
    public void deleteDeliveriesForManagement(final Set<Long> userIdList,
                                              @NotEmpty final List<Long> deliveryIdList,
                                              final Long userId) {

        List<Delivery> filteredDeliveryList;

        if (!CollectionUtils.isEmpty(userIdList)) {
            filteredDeliveryList = deliveryRepository.findByIdInAndUserIdIn(deliveryIdList, userIdList);
        } else {
            filteredDeliveryList = deliveryRepository.findByIdIn(deliveryIdList);
        }

        if (!CollectionUtils.isEmpty(filteredDeliveryList)) {
            deliveryBasicService.deleteMultipleDeliveries(filteredDeliveryList, userId);
            deliveryRepository.saveAll(filteredDeliveryList);
        }
    }

    /**
     * 배송 상태 변경
     *
     * @param deliveryId        배송 아이디
     * @param riderId           라이더 아이디
     * @param deliveryStatusDTO 배송 상태 정보
     * @param hasEverDeliveryDeparted
     * @return
     */
    @Transactional
    public DeliveryAllocation updateDeliveryStatus(Long deliveryId,
                                                   Long riderId,
                                                   DeliveryStatusDTO deliveryStatusDTO,
                                                   boolean hasEverDeliveryDeparted) {

        final DeliveryStatus requestedDeliveryStatus = deliveryStatusDTO.getDeliveryStatus();
        final DeliveryAllocation riderDeliveryAllocation = deliveryBasicService.getDeliveryAllocationOrThrowException(riderId, deliveryId);
        final DeliveryStatus currentDeliveryStatus = riderDeliveryAllocation.getStatus();

        if (!currentDeliveryStatus.isTransferrableTo(requestedDeliveryStatus)) {
            throw new CustomException(HttpStatus.BAD_REQUEST,
                    "배송아이디 " + deliveryId + "의 배송상태 " + currentDeliveryStatus + "에서 " + requestedDeliveryStatus + "로 변경될 수 없습니다.", false);
        }
        log.info("\triderId:{}, deliveryId:{}, deliveryStatus:{} -> {}", riderId, deliveryId, currentDeliveryStatus, requestedDeliveryStatus);

        final DeliveryStatusHistory deliveryStatusHistory = deliveryStatusHistoryService.insertDeliveryStatusHistory(riderDeliveryAllocation, requestedDeliveryStatus, false);

        if (Objects.nonNull(deliveryStatusHistory)) {
            riderDeliveryAllocation.setStatus(requestedDeliveryStatus);
            deliveryAllocationRepository.save(riderDeliveryAllocation);

            postProcessUpdateDeliveryStatus(riderDeliveryAllocation, riderId, deliveryStatusDTO, hasEverDeliveryDeparted);
        } else {
            log.error("[updateDeliveryStatus] deliveryId:{}의 변경하려는 배송상태({})가 이미 배송상태이력에 존재합니다.", deliveryId, requestedDeliveryStatus.getDescription());
        }

        return riderDeliveryAllocation;
    }

    /**
     * 관제 화면에서 배송 상태 강제 변경
     *
     * @param customerCode      orgCodeName
     * @param deliveryIds       배송지들 아이디
     * @param customerOrderIds
     * @param deliveryStatusDTO 배송 상태 정보
     */
    @Transactional
    public void updateForceDeliveryStatus(String customerCode,
                                          List<Long> deliveryIds,
                                          List<String> customerOrderIds,
                                          DeliveryStatusDTO deliveryStatusDTO) {

        final DeliveryStatus requestedDeliveryStatus = deliveryStatusDTO.getDeliveryStatus();
        List<DeliveryAllocation> deliveryAllocationList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(deliveryIds)) {
            deliveryAllocationList = deliveryIds.stream()
                    .map(deliveryBasicService::getDeliveryAllocations)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(daList -> daList.get(0))
                    .collect(Collectors.toList());
        } else if (CollectionUtils.isNotEmpty(customerOrderIds)) {

            /////////////예외 적이 상항 allocation이 없을 경우 강제적으로 셋팅함./////////////////////////
            customerOrderIds.forEach(customerOrderId -> {
                String customerOrderCode = customerCode + "_" + customerOrderId;
                Delivery delivery = deliveryBasicService.getDeliveryByCustomerOrderIdOrderByUpdateAtDesc(customerOrderCode);

                /////////만약에 기사가 할당 안되어 있을 경우 프로젝트 처음 기사를 자동 세팅함.
                Rider rider = riderService.getRiders(delivery.getProjectId(), false).get(0);

                if (CollectionUtils.isEmpty(delivery.getAllocations())) {
                    final List<DeliveryAllocation> allocations = delivery.getAllocations();
                    allocations.clear();
                    DeliveryAllocation deliveryAllocation = setDeliveryAllocation(delivery, delivery.getProjectId(), rider.getId(), false);
                    allocations.add(deliveryAllocation);

                    deliveryRepository.save(delivery);
                }
            });

            deliveryAllocationList = customerOrderIds.stream()
                    .map(customerOrderId
                            -> deliveryBasicService.getDeliveryByCustomerOrderIdOrderByUpdateAtDesc(customerCode + "_" + customerOrderId).getAllocations().get(0))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(deliveryAllocationList)) {

            //배송완료/실패된 것들 찾기
            List<DeliveryAllocation> endedDeliveryAllocationList = deliveryAllocationList.stream()
                    .filter(da -> DeliveryStatus.COMPLETED.equals(da.getStatus()) || DeliveryStatus.FAILURE.equals(da.getStatus()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(endedDeliveryAllocationList)) {
                endedDeliveryAllocationList.forEach(da -> log.info("[updateForceDeliveryStatus] customerOrderId: {}, 이미 {} 상태입니다.", da.getDelivery().getDetail().getCustomerOrderId(), da.getStatus()));
                throw new CustomException(HttpStatus.BAD_REQUEST, "이미 완료 또는 실패 처리된 배송입니다.", false);
            }

            //배송이 변경되어야 하는 것들
            List<DeliveryAllocation> notEndedDeliveryAllocationList = deliveryAllocationList.stream()
                    .filter(da -> !DeliveryStatus.COMPLETED.equals(da.getStatus()) || !DeliveryStatus.FAILURE.equals(da.getStatus()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(notEndedDeliveryAllocationList)) {
                notEndedDeliveryAllocationList.forEach(da -> {
                    if (DeliveryStatus.COMPLETED.equals(deliveryStatusDTO.getDeliveryStatus())) {
                        // DeliveryStatusHistory에 저장
                        deliveryStatusHistoryService.insertDeliveryStatusHistory(da, DeliveryStatus.READY, true);
                        deliveryStatusHistoryService.insertDeliveryStatusHistory(da, DeliveryStatus.GOING, true);
                        deliveryStatusHistoryService.insertDeliveryStatusHistory(da, DeliveryStatus.SERVICING, true);
                        deliveryStatusHistoryService.insertDeliveryStatusHistory(da, DeliveryStatus.COMPLETED, true);
                        deliveryCompletedHistoryService.updateForceDeliveryCompletedHistory(da, deliveryStatusDTO);

                    } else if (DeliveryStatus.FAILURE.equals(deliveryStatusDTO.getDeliveryStatus())) {
                        deliveryFailureHistoryService.saveForceDeliveryFailureHistory(da, deliveryStatusDTO, da.getRiderId());
                    } else {
                        throw new CustomException(HttpStatus.BAD_REQUEST, "변경이 필요한 상태 정보를 잘못 입력하였습니다.", false);
                    }

                    da.setStatus(requestedDeliveryStatus);
                    deliveryAllocationRepository.save(da);
                });
            } else {
                throw new CustomException(HttpStatus.BAD_REQUEST, "이미 완료 또는 실패 처리된 배송입니다.", false);
            }
        } else {
            throw new CustomException(HttpStatus.BAD_REQUEST, "변경해야할 배송지 정보가 없습니다.", false);
        }
    }

    /**
     * 관제 화면에서 배송 상태 강제 변경
     *
     * @param projectId
     * @param deliveryIds       배송지들 아이디
     * @param customerOrderIds
     * @param deliveryStatusDTO 배송 상태 정보
     */
//    @Transactional
//    public void updateForceDeliveryStatus(Long projectId,
//                                          List<Long> deliveryIds,
//                                          List<String> customerOrderIds,
//                                          DeliveryStatusDTO deliveryStatusDTO) {
//
//        final DeliveryStatus requestedDeliveryStatus = deliveryStatusDTO.getDeliveryStatus();
//        List<DeliveryAllocation> deliveriesAllocation = new ArrayList<>();
//
//        if (CollectionUtils.isNotEmpty(deliveryIds)) {
//            deliveriesAllocation = this.getDeliveryAllocationListByDeliveryIdsAndProjectId(deliveryIds, projectId);
//        } else if (CollectionUtils.isNotEmpty(customerOrderIds)) {
//            final List<Delivery> deliveryList = deliveryBasicService.getDeliveryAllocationListByCustomerOderIdListAndProjectId(customerOrderIds, projectId);
//            if (CollectionUtils.isNotEmpty(deliveryList)) {
//                deliveriesAllocation = deliveryList.stream()
//                        .filter(d -> d.getAllocations().size() > 0)
//                        .map(d -> d.getAllocations().get(0))
//                        .collect(Collectors.toList());
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(deliveriesAllocation)) {
//            deliveriesAllocation.forEach(da -> {
//                if (deliveryStatusDTO.getDeliveryStatus().equals(DeliveryStatus.COMPLETED)) {
//                    deliveryCompletedHistoryService.updateForceDeliveryCompletedHistory(da, deliveryStatusDTO);
//
//                } else if (deliveryStatusDTO.getDeliveryStatus().equals(DeliveryStatus.FAILURE)) {
//                    deliveryFailureHistoryService.saveForceDeliveryFailureHistory(da, deliveryStatusDTO, da.getRiderId());
//                }else{
//                    throw new CustomException(HttpStatus.BAD_REQUEST,"변경이 필요한 상태 정보를 잘못 입력하였습니다.", false);
//                }
//
//                da.setStatus(requestedDeliveryStatus);
//                deliveryAllocationRepository.save(da);
//            });
//        }else{
//            throw new CustomException(HttpStatus.BAD_REQUEST,"변경해야할 배송지 정보가 없습니다.", false);
//        }
//    }


    /**
     * 관제 화면에서 배송 상태 강제 변경
     *
     * @param deliveryId        배송 아이디
     * @param riderId           라이더 아이디
     * @param deliveryStatusDTO 배송 상태 정보
     */
    @Transactional
    public DeliveryAllocation updateForceDeliveryStatus(Long deliveryId,
                                                        Long riderId,
                                                        DeliveryStatusDTO deliveryStatusDTO) {

        final DeliveryStatus requestedDeliveryStatus = deliveryStatusDTO.getDeliveryStatus();
        final DeliveryAllocation riderDeliveryAllocation = deliveryBasicService.getDeliveryAllocationOrThrowException(riderId, deliveryId);

        deliveryStatusHistoryService.saveDeliveryStatusHistory(riderDeliveryAllocation, requestedDeliveryStatus, true);

        if (DeliveryStatus.COMPLETED.equals(requestedDeliveryStatus)) {
            deliveryCompletedHistoryService.updateForceDeliveryCompletedHistory(riderDeliveryAllocation, deliveryStatusDTO);
        } else if (DeliveryStatus.FAILURE.equals(requestedDeliveryStatus)) {
            deliveryFailureHistoryService.saveForceDeliveryFailureHistory(riderDeliveryAllocation, deliveryStatusDTO, riderId);
        }

        riderDeliveryAllocation.setStatus(requestedDeliveryStatus);

        return deliveryAllocationRepository.save(riderDeliveryAllocation);
    }

    public DeliveryStatusDTO getDeliveryStatus(Long deliveryId,
                                               Long riderId,
                                               Boolean onlySuccessOrFail) {

        final DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocationOrThrowException(riderId, deliveryId);

        if (Objects.nonNull(deliveryAllocation)) {
            final DeliveryStatus deliveryStatus = deliveryAllocation.getStatus();
            DeliveryFailureHistory deliveryFailureHistory = null;
            DeliveryCompletedHistory deliveryCompletedHistory = null;

            if (DeliveryStatus.FAILURE.equals(deliveryStatus)) {
                deliveryFailureHistory = deliveryFailureHistoryService.getDeliveryFailureHistory(deliveryId, riderId);
            } else if (DeliveryStatus.COMPLETED.equals(deliveryStatus)) {
                deliveryCompletedHistory = deliveryCompletedHistoryService.getDeliveryCompletedHistory(deliveryId, riderId);
            } else if (onlySuccessOrFail) {
                throw new ItemNotFoundException("배송 성공/실패에 해당하는 배송이 없습니다.");
            }

            DeliveryStatusDTO deliveryStatusDTO = DeliveryStatusDTO.builder()
                    .deliveryStatus(deliveryStatus)
                    .deliveryFailureType(deliveryFailureHistory != null ? deliveryFailureHistory.getDeliveryFailureType() : null)
                    .deliveryFailureMessage(deliveryFailureHistory != null ? deliveryFailureHistory.getDeliveryFailureMessage() : "")
                    .deliveryCompletedType(deliveryCompletedHistory != null ? deliveryCompletedHistory.getDeliveryCompletedType() : null)
                    .deliveryCompletedMessage(deliveryCompletedHistory != null ? deliveryCompletedHistory.getDeliveryCompletedMessage() : "")
                    .build();

            return deliveryStatusDTO;
        } else {
            throw new ItemNotFoundException("할당된 배송이 없습니다.");
        }
    }

    /**
     * updateDeliveryStatus 후처리 메서드
     *
     * @param deliveryAllocation
     * @param riderId
     * @param deliveryStatusDTO
     * @param hasEverDeliveryDeparted
     */
    @Transactional
    public void postProcessUpdateDeliveryStatus(DeliveryAllocation deliveryAllocation,
                                                Long riderId,
                                                DeliveryStatusDTO deliveryStatusDTO,
                                                boolean hasEverDeliveryDeparted) {

        final DeliveryStatus deliveryStatus = deliveryAllocation.getStatus();
        final Delivery delivery = deliveryAllocation.getDelivery();
        final Long deliveryId = delivery.getId();
        final Long projectId = delivery.getProjectId();
        final Project project = projectBasicService.getProjectById(projectId);
        final String customerOrderId = delivery.getDetail().getCustomerOrderId();

        final RiderProjectSetting riderProjectSetting = riderProjectSettingService.loadProjectSetting(riderId, projectId);

        if (DeliveryStatus.GOING.equals(deliveryStatus)) {
            final LineString routePathReal = deliveryRouteService.getRouteRunPath(deliveryAllocation);

            if (routePathReal != null) {
                Optional.ofNullable(riderProjectSetting)
                        .ifPresent(setting -> {
                            tracksRedisService.saveTracksRedis(setting.getProjectId(), setting.getVehicle().getVehicleId(), setting.getRider().getId(), deliveryId,
                                    Optional.ofNullable(routePathReal).map(r -> new WKTWriter().write(r)).orElse(null),
                                    DfrStatus.NONE, deliveryAllocation.getRouteAppPlan().getEtaDateTime(),
                                    deliveryAllocation.getDelivery().getDetail().getDeliveryEndTime(), null,
                                    false, null);
                        });
            }

            // 앱에서 배송시작(출발)을 누른 시점 최초 1건만 Noti 발생
            // 첫번째 배송순서를 배송출발없이 완료처리하는 경우를 고려하여 배송출발 이력 여부로 판단
            if (!hasEverDeliveryDeparted) {
                // 운행시작 - 배송출발 취소 - 다시 운행시작시 알림 발생 안시켜야
                final List<DeliveryAllocation> riderDeliveryAllocationList = deliveryBasicService.getRiderDeliveryAllocations(riderId, projectId);
                final long etaCount = riderDeliveryAllocationList.stream()
                        .filter(rda -> Objects.nonNull(rda.getEstimatedDtOfArrival()))
                        .count();

                if (etaCount < 1) {
                    notificationService.notificationOccurDeliveryStatus(riderId, deliveryStatusDTO.getDeliveryStatus(), projectId, delivery,
                            Objects.nonNull(customerOrderId) ? CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight() : String.valueOf(deliveryId));
                }
            }
        } else if (DeliveryStatus.SERVICING.equals(deliveryStatus)) {

            deliveryRouteService.makeRouteSectionComplete(deliveryAllocation, false);   // 위치 옮기지 마세요.
            deliveryRouteService.calcRiderRouteRunEtdEta(deliveryAllocation.getProjectId(), deliveryAllocation.getRiderId());
            deliveryAllocationRepository.save(deliveryAllocation);

            // tracks의 redis에서 삭제   : 완료 되었을때 삭제 하는 로직
            Optional.ofNullable(riderProjectSetting)
                    .ifPresent(setting -> {
                        tracksRedisService.deleteTracksRedis(tracksRedisService.createTrackRedisKey(setting.getRider().getId(), setting.getVehicle().getVehicleId()));
                    });

            final DeliveryStatusHistory servicingHistory = deliveryStatusHistoryService.getDeliveryStatusHistory(deliveryAllocation.getId(), DeliveryStatus.SERVICING);

            if (servicingHistory != null) {
                long duration = 0;

                if (deliveryAllocation.getDelivery().getDetail().getEstimatedStayTime() != null) {
                    duration = deliveryAllocation.getDelivery().getDetail().getEstimatedStayTime();
                }
                LocalDateTime servicingOverDt = servicingHistory.getStatusUpdateDt().plusMinutes(duration + SERVICING_DELAY_MIN);

                log.info("[postProcessUpdateDeliveryStatus] deliveryId:{}, 서비스지연 시간:{}", deliveryId, servicingOverDt);

                final LineString routePathReal = deliveryRouteService.getRouteRunPath(deliveryAllocation);

                Optional.ofNullable(riderProjectSetting)
                        .ifPresent(setting -> {
                            tracksRedisService.saveTracksRedis(setting.getProjectId(), setting.getVehicle().getVehicleId(), setting.getRider().getId(), deliveryId,
                                    Optional.ofNullable(routePathReal).map(r -> new WKTWriter().write(r)).orElse(null),
                                    DfrStatus.NONE, deliveryAllocation.getRouteAppPlan().getEtaDateTime(),
                                    deliveryAllocation.getDelivery().getDetail().getDeliveryEndTime(), servicingOverDt,
                                    null, false);
                        });
            } else {
                log.warn("[postProcessUpdateDeliveryStatus] deliveryId:{}의 Servicing 이력을 찾을 수 없습니다.", deliveryId);
            }
        } else if (deliveryStatus.isDeliveryEnded()) {

            deliveryRouteService.updateStaySeconds(deliveryAllocation);
            deliveryRouteService.calcRiderRouteRunEtdEta(deliveryAllocation.getProjectId(), deliveryAllocation.getRiderId());

            final String orgCodeName = organizationService.getOrganizationCodeByProjectId(projectId);
            final boolean isTheHyundaiOrg = StringUtils.equals(orgCodeName, TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME);

            if (DeliveryStatus.FAILURE.equals(deliveryStatus)) {
                if (Objects.isNull(deliveryStatusDTO.getDeliveryFailureType())) {
                    if (isTheHyundaiOrg) {
                        deliveryStatusDTO.setDeliveryFailureType(DeliveryFailureType.WRONG_ADDR);
                    } else {
                        deliveryStatusDTO.setDeliveryFailureType(DeliveryFailureType.OTHER);
                    }
                }

                deliveryFailureHistoryService.saveDeliveryFailureHistory(deliveryAllocation, deliveryStatusDTO, riderId);

                // 배송실패시 Noti 발생
                notificationService.notificationOccurDeliveryStatusFailure(riderId, deliveryStatusDTO.getDeliveryStatus(), projectId,
                        deliveryStatusDTO.getDeliveryFailureType(), deliveryStatusDTO.getDeliveryFailureMessage(),
                        Objects.nonNull(customerOrderId) ? CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight() : String.valueOf(deliveryId),
                        delivery);

                // [현대차 PoC] 배송실패(주문취소 외) 처리
//                if (DeliveryFailureType.ORDER_CANCELLATION.equals(deliveryStatusDTO.getDeliveryFailureType())) {
                final List<DeliveryProduct> deliveryProductList = delivery.getDeliveryProducts();

                if (CollectionUtils.isNotEmpty(deliveryProductList)) {
                    final String failureType = deliveryStatusDTO.getDeliveryFailureType().getDescription();
                    log.info("[postProcessUpdateDeliveryStatus][즉시배송][배송실패] deliveryId:{}, deliveryFailureType:{}", deliveryId, failureType);
                    deliveryProductList
                            .forEach(dp -> {
                                final String productBarcode = dp.getProductBarcode();
                                final String itemCode = dp.getItemCode();
                                final Product product = productService.getProductByProductBarcodeAndItemCode(productBarcode, itemCode, "").orElse(null);
                                final Long productId = Objects.nonNull(product) ? product.getProductId() : null;

                                if (Objects.nonNull(productId)) {
                                    final ProductAllocation productAllocation = productService.getLastProductAllocation(projectId, riderId, productId);

                                    if (Objects.nonNull(productAllocation)) {
                                        productService.updateByProductOrder(productAllocation.getProductAllocationId(), dp.getOrderQuantity(), false, failureType);
                                    } else {
                                        log.error("[postProcessUpdateDeliveryStatus][즉시배송][배송실패] 기사에게 할당된 상품이 없습니다. riderId: {}, projectId: {}, productBarcode: {}, itemCode: {}", riderId, projectId, productBarcode, itemCode);
                                    }
                                } else {
                                    log.error("[postProcessUpdateDeliveryStatus][즉시배송][배송실패] 조회된 상품이 없습니다. productBarcode: {}, itemCode: {}", productBarcode, itemCode);
                                }
                            });

                    final Long userId = projectRepository.findUserIdByProjectId(projectId);
                    pushService.sendProductInspectionAndOrderToWeb(userId, projectId);
                }
//                }
            } else if (DeliveryStatus.COMPLETED.equals(deliveryStatus)) {
                if (Objects.isNull(deliveryStatusDTO.getDeliveryCompletedType())) {
                    if (isTheHyundaiOrg) {
                        deliveryStatusDTO.setDeliveryCompletedType(DeliveryCompletedType.DOOR);
                    } else {
                        deliveryStatusDTO.setDeliveryCompletedType(DeliveryCompletedType.NORMAL_DELIVERY);
                    }
                }

                deliveryCompletedHistoryService.saveDeliveryCompletedHistory(deliveryAllocation, deliveryStatusDTO);

                // 배송완료시 Noti 발생
                notificationService.notificationOccurDeliveryStatusCompleted(riderId, deliveryStatusDTO.getDeliveryStatus(), projectId,
                        deliveryStatusDTO.getDeliveryCompletedType(), deliveryStatusDTO.getDeliveryCompletedMessage(),
                        Objects.nonNull(customerOrderId) ? CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight() : String.valueOf(deliveryId),
                        delivery);

                // 모든배송완료시 Noti 발생
                if (!project.getIsOnDemandEnabled()) {
                    if (checkAllProjectRiderDeliveryCompleted(projectId, riderId)) {
                        notificationService.notificationOccurDeliveryStatus(riderId, deliveryStatusDTO.getDeliveryStatus(), projectId, delivery,
                                Objects.nonNull(customerOrderId) ? CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight() : String.valueOf(deliveryId));
                    }
                }
            }

            // #4925 배송완료/실패시 실제소요시간 업데이트
            final Long deliveryAllocationId = deliveryAllocation.getId();
            final DeliveryStatusHistory goingHistory = deliveryStatusHistoryService.getDeliveryStatusHistory(deliveryAllocationId, DeliveryStatus.GOING);
            final DeliveryStatusHistory endedHistory = Optional.ofNullable(deliveryStatusHistoryService.getDeliveryStatusHistory(deliveryAllocationId, DeliveryStatus.COMPLETED))
                    .orElseGet(() -> deliveryStatusHistoryService.getDeliveryStatusHistory(deliveryAllocationId, DeliveryStatus.FAILURE));

            if (Objects.nonNull(goingHistory) && Objects.nonNull(endedHistory)) {
                final Duration duration = Duration.between(goingHistory.getStatusUpdateDt(), endedHistory.getStatusUpdateDt());
                if (Objects.nonNull(duration)) {
                    deliveryAllocation.setRealSeconds(Long.valueOf(duration.getSeconds()));
                    deliveryAllocationRepository.save(deliveryAllocation);
                }
            }

            // tracks의 redis에서 삭제   : 완료 되었을때 삭제 하는 로직
            Optional.ofNullable(riderProjectSetting)
                    .ifPresent(setting -> {
                        tracksRedisService.deleteTracksRedis(tracksRedisService.createTrackRedisKey(setting.getRider().getId(), setting.getVehicle().getVehicleId()));
                    });
        }

        if (customerOrderId != null && project.getCallbackUri() != null) {
            customerService.callbackUriProcessor(project, customerOrderId, deliveryStatus);
        }
    }

    /**
     * 배송 순서 변경
     *
     * @param riderId     라이더 아이디
     * @param deliveryId  배송 아이디
     * @param newOrderNum 변경될 순서 번호
     */
    @Deprecated
    @Transactional
    public List<DeliveryAllocation> changeDeliveryOrder(@NotNull Long riderId,
                                                        @NotNull Long deliveryId,
                                                        @NotNull Integer newOrderNum) {

        // 타겟 배송 정보 조회
        final Delivery targetDelivery = deliveryRepository.findById(deliveryId).orElseThrow(() -> new DeliveryNotFoundException(deliveryId));
        final Long projectId = targetDelivery.getProjectId();

        if (projectId == null) {
            throw new RuntimeException("프로젝트에 할당되지 않는 배송 정보 입니다.");
        }
        if (targetDelivery.getAllocations() == null || targetDelivery.getAllocations().isEmpty()) {
            throw new RuntimeException("배차 되지 않는 배송 정보 입니다.");
        }

        // 배송 할당 정보 조회
        final DeliveryAllocation targetAllocation = targetDelivery.getAllocations().stream()
                .filter(a -> a.getRiderId().equals(riderId))
                .findAny().orElseThrow(() -> new RuntimeException("기사 정보가 없습니다."));
        final Integer oldOrderNum = targetAllocation.getOrderNum();

        // 배송지의 순서와 변경될 순서가 동일한 경우 예외처리
        if (oldOrderNum.equals(newOrderNum)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "배송지의 순서와 변경될 순서가 동일 합니다.");
        }

        // 기사의 배송 목록 조회
        final List<Delivery> deliveries = deliveryRepository.findByProjectIdAndAllocationsRiderId(targetDelivery.getProjectId(), riderId);

        final List<DeliveryAllocation> deliveryAllocations = deliveries.stream()
                .map(d -> d.getAllocations().stream()
                        .filter(da -> da.getRiderId().equals(riderId))
                        .findAny()
                        .orElseThrow(RuntimeException::new))
                .collect(Collectors.toList());

        // newOrderNum 대상 배송할당정보 조회
        final DeliveryAllocation sourceDeliveryAllocation = deliveryAllocations.stream()
                .filter(d -> d.getOrderNum().equals(newOrderNum))
                .findAny()
                .orElseThrow(() -> new CustomException(HttpStatus.BAD_REQUEST, "toOrderNum 의 대상 배송지를 찾을수 없습니다."));

        // 배송준비 상태가 아닌 경우 예외처리
        if (sourceDeliveryAllocation.getStatus() != DeliveryStatus.READY) {
            throw new CustomException(HttpStatus.BAD_REQUEST, newOrderNum + "번째 배송지 상태가 'READY'가 아닙니다.");
        }

        // 예외 처리
        if (newOrderNum <= 0) {
            throw new ConstraintViolationException("배송순서번호가 0 보다 커야 합니다.", null);
        } else if (newOrderNum > deliveries.size()) {
            throw new ConstraintViolationException("배송순서번호가 배송 갯수보다 많습니다.", null);
        }

        // 필터링할 배송순서 범위값 설정
        final Integer minOrderNum = Math.min(newOrderNum, oldOrderNum);
        final Integer maxOrderNum = Math.max(newOrderNum, oldOrderNum);

        // 배송순서번호 시작값 설정
        AtomicInteger orderCounter = new AtomicInteger(minOrderNum);
        if (newOrderNum < oldOrderNum) {
            orderCounter.incrementAndGet();
        }

        // 변경될 배송 할당 목록 필터링
        List<DeliveryAllocation> allocations = deliveryAllocations.stream()
                .filter(a -> a.getOrderNum() >= minOrderNum
                        && a.getOrderNum() <= maxOrderNum
                        && !a.getDelivery().getId().equals(deliveryId))
                .sorted(Comparator.comparing(DeliveryAllocation::getOrderNum))
                .collect(Collectors.toList());

        // 배송 순서 변경 및 저장
        allocations.forEach(a -> a.setOrderNum(orderCounter.getAndIncrement()));
        targetAllocation.setOrderNum(newOrderNum);
        allocations.add(targetAllocation);

        // 순서가 변경된 배송지에 대한 경로를 새로 탐색하여 갱신한다.
        {
            final VehicleType vehicleType = riderProjectSettingService.getVehicleTypeOfProjectRider(riderId, projectId);
            final Point workingStartPoint = (minOrderNum == 1) ?
                    riderProjectSettingService.loadProjectSetting(riderId, projectId).getWorkingStartLocation() :
                    deliveryAllocations.stream()
                            .filter(a -> a.getOrderNum() == minOrderNum - 1)
                            .findFirst()
                            .map(a -> a.getDelivery().getDetail().getDestinationAddress().getLocationByVehicleType(vehicleType))
                            .orElseThrow(() -> new ItemNotFoundException("이전 방문지를 찾을 수 없습니다."));

            // 순서가 변경된 방문지들 기준 다음 방문지의 경로도 변경될 것이므로 그 방문지도 포함한다.
            deliveryAllocations.stream()
                    .filter(a -> a.getOrderNum() == maxOrderNum + 1)
                    .findFirst()
                    .ifPresent(allocations::add);

            deliveryRouteService.updateRoutePathOfDeliveryAllocations(riderId, deliveryId, projectId, workingStartPoint, allocations);

            deliveryRouteService.calcRiderRouteAppPlanEtdEta(projectId, riderId, null);
        }

        return deliveryAllocationRepository.saveAll(allocations);
    }

    /**
     * 기존 배송/배차 정보 변경
     * 기존 DeliveryAllocation을 삭제하고 새로 추가하는 방식
     *
     * @param deliveryId    배송 아이디
     * @param allocationDto 할당 정보
     */
    @Deprecated
    @Transactional
    public DeliveryAllocation allocateDelivery(Long deliveryId,
                                               DeliveryAllocationDTO allocationDto) {

        final Delivery delivery = deliveryBasicService.getDeliveryInfo(deliveryId);
        if (Objects.isNull(delivery.getAllocations())) {
            delivery.setAllocations(new ArrayList<>());
        }

        final List<DeliveryAllocation> deliveryAllocations = delivery.getAllocations();

        final DeliveryAllocation foundDeliveryAllocation = deliveryAllocations.stream()
                .filter(da -> da.getRiderId().equals(allocationDto.getRiderId()))
                .findFirst()
                .orElse(null);

        if (Objects.nonNull(foundDeliveryAllocation)) {
            log.info("이미 배차되어 있습니다. 기존 Delivery를 업데이트 합니다  RiderId(" + allocationDto.getRiderId() + "), deliveryId(" + deliveryId + ")");

            deliveryAllocationRepository.delete(foundDeliveryAllocation);
            deliveryAllocations.remove(foundDeliveryAllocation);
        }

        final DeliveryAllocation newDeliveryAllocation = deliveryAllocationRepository.save(allocationDto.createDeliveryAllocationEntity(delivery));

        final Double estimatedMeters = Optional.ofNullable(allocationDto.getPredictionDistance()).orElse(0.0);
        final Long estimatedSeconds = Optional.ofNullable(allocationDto.getPredictionTime()).orElse(0L);
        final LineString routePath = GeometryUtil.convertLineStringFromPointList(allocationDto.getRoutePath());
        final Long staySeconds = Optional.ofNullable(delivery.getDetail().getEstimatedStayTime()).orElse(0L) * 60L;

        deliveryRouteService.updateInitialRoutes(newDeliveryAllocation, estimatedMeters, estimatedSeconds, routePath, staySeconds);

        deliveryAllocations.add(newDeliveryAllocation);
        deliveryRepository.save(delivery);

        deliveryStatusHistoryService.insertDeliveryStatusHistory(newDeliveryAllocation, DeliveryStatus.READY, false);

        final InspectionStatus inspectionStatus = this.setDefaultInspectionStatus(delivery);
        deliveryInspectionHistoryService.saveDeliveryInspectionHistory(newDeliveryAllocation, inspectionStatus);

        return newDeliveryAllocation;
    }

    /**
     * 배송 물품을 이전한다 . 특정 기사에서 다른 기사로 이전한다.
     *
     * @param fromRider null 일 경우
     * @param toRider   null 일경우 상품을 폐기한다
     * @param delivery
     */
    public void changeClusterDeliveryProductAllocation(Long fromRider,
                                                       Long toRider,
                                                       Delivery delivery) {

        final List<DeliveryProduct> deliveryProductList = delivery.getDeliveryProducts();
        Long projectId = delivery.getProjectId();

        if (Objects.nonNull(fromRider) && Objects.nonNull(toRider)) {
            log.info("[changeClusterDeliveryProductAllocation] 기사({})에서 기사({})로 {}개의 상품을 이전합니다.", fromRider, toRider, deliveryProductList.size());
        } else if (Objects.nonNull(fromRider)) {
            log.info("[changeClusterDeliveryProductAllocation] 기사({})의 {}개의 상품의 예약을 취소합니다.", fromRider, deliveryProductList.size());
        } else if (Objects.nonNull(toRider)) {
            log.info("[changeClusterDeliveryProductAllocation] 기사({})의 {}개의 상품을 새로 할당합니다.", toRider, deliveryProductList.size());
        }

        //이전 기사의 물품 취소함 - postProcessUpdateDeliveryStatus 중복 코드.
        if (Objects.nonNull(fromRider) && CollectionUtils.isNotEmpty(deliveryProductList)) {
            deliveryProductList.forEach(dp -> {
                final String productBarcode = dp.getProductBarcode();
                final String itemCode = dp.getItemCode();
                final Product product = productService.getProductByProductBarcodeAndItemCode(productBarcode, itemCode, "").orElse(null);
                final Long productId = Objects.nonNull(product) ? product.getProductId() : null;
                final String failureType = DeliveryFailureType.TRANSFER_ITEM.getDescription();

                if (Objects.nonNull(productId)) {
                    final ProductAllocation productAllocation = productService.getLastProductAllocation(projectId, fromRider, productId);

                    if (Objects.nonNull(productAllocation)) {
                        productService.updateByProductOrder(productAllocation.getProductAllocationId(), dp.getOrderQuantity(), false, failureType);
                    } else {
                        final String orgCodeName = organizationService.getOrganizationCodeByProjectId(projectId);
                        if (HmgConstant.HMG_ORG_CODE_NAME.equals(orgCodeName)) {
                            log.error("[changeClusterDeliveryProductAllocation][즉시배송][배송실패] 기사에게 할당된 상품이 없습니다. riderId: {}, projectId: {}, productBarcode: {}, itemCode: {}", fromRider, projectId, productBarcode, itemCode);
                        }
                    }
                } else {
                    log.error("[changeClusterDeliveryProductAllocation][즉시배송][배송실패] 조회된 상품이 없습니다. productBarcode: {}, itemCode: {}", productBarcode, itemCode);
                }
            });
        }

        //세로운 기사에게 물품 할당
        if (Objects.nonNull(toRider)) {
            productService.handleOrderItemReception(deliveryProductList, projectId, toRider);
        }
    }

    /**
     * 기존 기사 정보 또는 순서만 배송/배차 정보 변경
     * 기존 DeliveryAllocation을 삭제하고 새로 추가하는 방식
     *
     * @param deliveryId            배송 아이디
     * @param deliveryRiderOrderDto 할당 정보
     * @return 배송 할당 정보
     */
    @Transactional
    public boolean changeDeliveryCluster(@NotNull Long deliveryId,
                                         @NotNull DeliveryRiderOrderDTO deliveryRiderOrderDto) {

        final Long projectId = deliveryRiderOrderDto.getProjectId();
        final Long riderId = deliveryRiderOrderDto.getRiderId();
        log.info("[changeDeliveryCluster] deliveryId: {}, projectId: {}, riderId: {}", deliveryId, projectId, riderId);

        Optional.ofNullable(deliveryAllocationRepository.findByDeliveryId(deliveryId))
                .ifPresent(allocations -> {//deliveryAllocation 1개만 고려 하는 데 list로 나올시 어떻게 해야 하는지??
                    allocations.forEach(da -> {
                        if (Objects.nonNull(riderId)) {
                            final Delivery delivery = da.getDelivery();
                            if (this.hasDeliveryProducts(delivery)) {
                                this.changeClusterDeliveryProductAllocation(da.getRiderId(), riderId, delivery);
                            }

                            //이전할 배송지의 상태가 배송중,서비스중일 경우 READY로 변경한다
                            DeliveryStatus status = da.getStatus();
                            if (DeliveryStatus.GOING.equals(status) || DeliveryStatus.SERVICING.equals(status)) {
                                log.info("[changeDeliveryCluster] 이전할 배송지({})가 {} 상태여서 READY로 변경합니다.", deliveryId, status);
                                da.setStatus(DeliveryStatus.READY);
                            }

                            da.setRiderId(riderId);
                            deliveryAllocationRepository.save(da);
                        }
                    });
                });

        //클러스터링을 이전하면 다시 검수 상태를 미검수로 돌린다
        this.cancelInspectionEnd(projectId, riderId);

        return true;
    }

    /**
     * 신규 배송지 저장
     *
     * @param callUserId  콜러 아이디
     * @param reqDelivery 배송 정보
     */
    public void saveNewDeliveries(@Min(1) Long callUserId,
                                  DeliveryDTO reqDelivery) {

        this.demoNewDeliveryStore.put(callUserId, reqDelivery);
    }

    /**
     * 신규 배송지 조회
     *
     * @param callUserId 콜러 아이디
     * @param projectId  프로젝트 아이디
     * @return 배송 목록
     */
    public List<DeliveryDTO> getNewDeliveries(Long callUserId,
                                              Long projectId) {

        // 필터링 조건
        Predicate<? super DeliveryDTO> condition = d -> d.getIsNewDelivery()
                && d.getProjectId().longValue() == projectId.longValue()
                && d.getCallUserId().longValue() == callUserId.longValue();

        // 데이터 가공
        List<DeliveryDTO> deliveries = this.demoNewDeliveryStore.values().stream()
                .filter(condition)
                .collect(Collectors.toList());

        // 신규 배송 플레그 FALSE 설정
        this.demoNewDeliveryStore.values().stream()
                .filter(condition)
                .forEach(delivery -> delivery.setIsNewDelivery(false));

        return deliveries;
    }

    public TrackTotalDistanceDTO getTotalDistanceDTO(Long riderId,
                                                     Long projectId,
                                                     Long vehicleId,
                                                     LocalDateTime from,
                                                     LocalDateTime to) {

        String startTime = from.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String endTime = to.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        return trackServiceClient.getRiderDistances(riderId, projectId, vehicleId, startTime, endTime);
    }

    @Transactional
    public void terminateDeliveriesOfProject(@NotNull @Positive final Long projectId) {

        final List<DeliveryAllocation> terminatedDeliveryAllocations = new ArrayList<>();

        // 해당 프로젝트에 work complete된 기사아이디 목록을 만든다.
        final List<Long> workCompletedRiderIdListInProject = riderWorkCompletionRepository.findByProjectId(projectId)
                .stream()
                .filter(r -> Boolean.TRUE.equals(r.getIsWorkCompleted()))
                .map(r -> r.getRider().getId())
                .collect(Collectors.toList());

        final List<DeliveryAllocation> goingDeliveryAllocations = deliveryBasicService.getDeliveryAllocationListByProjectIdAndStatus(projectId, DeliveryStatus.GOING);
        for (final DeliveryAllocation deliveryAllocation : goingDeliveryAllocations) {
            // 기사가 업무완료했으면 이미 경로는 정리된 상태이므로 수행하지 않는다.
            if (!workCompletedRiderIdListInProject.contains(deliveryAllocation.getRiderId())) {
                final boolean terminated = deliveryRouteService.makeRouteSectionCompleted(deliveryAllocation);
                if (terminated) {
                    terminatedDeliveryAllocations.add(deliveryAllocation);
                }
            }
        }

        // READY인데 현재 주행경로가 있을수도 있다.
        final List<DeliveryAllocation> readyDeliveryAllocations = deliveryBasicService.getDeliveryAllocationListByProjectIdAndStatus(projectId, DeliveryStatus.READY);
        for (final DeliveryAllocation deliveryAllocation : readyDeliveryAllocations) {
            // 기사가 업무완료했으면 이미 경로는 정리된 상태이므로 수행하지 않는다.
            if (!workCompletedRiderIdListInProject.contains(deliveryAllocation.getRiderId())) {
                final boolean terminated = deliveryRouteService.makeRouteSectionCompleted(deliveryAllocation);
                if (terminated) {
                    terminatedDeliveryAllocations.add(deliveryAllocation);
                }
            }
        }

        final Set<Long> riderIds = terminatedDeliveryAllocations.stream().map(DeliveryAllocation::getRiderId).collect(Collectors.toSet());
        riderIds.forEach(rId -> {
            final RiderProjectSetting riderProjectSetting = riderProjectSettingService.loadProjectSetting(rId, projectId);

            // tracks의 redis에서 삭제   : 완료 되었을때 삭제 하는 로직
            Optional.ofNullable(riderProjectSetting)
                    .ifPresent(setting -> tracksRedisService.deleteTracksRedis(tracksRedisService.createTrackRedisKey(setting.getRider().getId(), setting.getVehicle().getVehicleId())));
        });
    }

    @Transactional
    public RiderProjectSetting terminateDeliveriesOfRiderInProject(@NotNull @Positive final Long riderId,
                                                                   @NotNull @Positive final Long projectId) {

        // 프로젝트가 종료되었으면 이미 경로는 정리된 상태이므로 수행하지 않는다.
        if (projectBasicService.isProjectTerminated(projectId)) {
            return null;
        }

        final RiderProjectSetting riderProjectSetting = riderProjectSettingService.loadProjectSetting(riderId, projectId);

        final List<DeliveryAllocation> terminatedDeliveryAllocations = new ArrayList<>();

        final List<DeliveryAllocation> goingDeliveryAllocations = deliveryBasicService.getDeliveryAllocationListByRiderIdAndProjectIdAndStatus(riderId, projectId, DeliveryStatus.GOING);
        for (final DeliveryAllocation deliveryAllocation : goingDeliveryAllocations) {
            final boolean terminated = deliveryRouteService.makeRouteSectionCompleted(deliveryAllocation);
            if (terminated) {
                terminatedDeliveryAllocations.add(deliveryAllocation);
            }
        }

        // READY인데 현재 주행경로가 있을수도 있다.
        final List<DeliveryAllocation> readyDeliveryAllocations = deliveryBasicService.getDeliveryAllocationListByRiderIdAndProjectIdAndStatus(riderId, projectId, DeliveryStatus.READY);
        for (final DeliveryAllocation deliveryAllocation : readyDeliveryAllocations) {
            final boolean terminated = deliveryRouteService.makeRouteSectionCompleted(deliveryAllocation);
            if (terminated) {
                terminatedDeliveryAllocations.add(deliveryAllocation);
            }
        }

        // tracks의 redis에서 삭제   : 완료 되었을때 삭제 하는 로직
        Optional.ofNullable(riderProjectSetting)
                .ifPresent(setting -> tracksRedisService.deleteTracksRedis(tracksRedisService.createTrackRedisKey(setting.getRider().getId(), setting.getVehicle().getVehicleId())));

        return riderProjectSetting;
    }

    @Transactional
    public List<DeliveryAllocation> setDeliveriesBundleEnded(@NotNull @Positive final Long projectId,
                                                             @NotNull @Positive final Long riderId,
                                                             @NotEmpty @Valid final List<DeliveryBundleEndedDTO> requestedDeliveryBundleEndedDTOList) {

        // 배송 정보 조회 (by 프로젝트 아이디, 기사 아이디)
        final List<DeliveryAllocation> allocations = deliveryAllocationRepository.findByProjectIdAndRiderIdOrderByOrderNumAsc(projectId, riderId);

        if (CollectionUtils.isEmpty(allocations)) {
            throw new CustomException(HttpStatus.BAD_REQUEST,
                    "프로젝트아이디 " + projectId + ", 기사아이디 " + riderId + "에 해당하는 배송할당 건을 찾을 수 없습니다.", false);
        }

        // 배송아이디가 중복으로 전달되는 경우가 있어 중복배송아이디 제거
        final List<DeliveryBundleEndedDTO> deliveryBundleEndedDTOList = requestedDeliveryBundleEndedDTOList.stream()
                .distinct()
                .collect(Collectors.toList());

        // 첫번째배송과 나머지배송 분리
        final DeliveryBundleEndedDTO firstDeliveryEndedDTO = deliveryBundleEndedDTOList.remove(0);
        final Long firstDeliveryId = firstDeliveryEndedDTO.getDeliveryId();

        if (CollectionUtils.isNotEmpty(deliveryBundleEndedDTOList)) {

            // 배송 순서 조정 및 경로 재탐색 (강제배송완료되는 배송건들을 첫번째 배송완료되는 배송건 바로 다음으로 위치한다)
            log.info("[setDeliveriesBundleEnded] 묶음배송완료에 의해 프로젝트아이디 {}, 기사아이디 {}의 배송순서 및 경로를 변경합니다.", projectId, riderId);

            final List<DeliveryRoutesRequestDTO> orderChangedList = new ArrayList<>();

            final int firstDeliveryIndex = IterableUtils.indexOf(allocations, da -> da.getDelivery().getId().equals(firstDeliveryId));
            if (firstDeliveryIndex < 0) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "배송아이디 " + firstDeliveryId + "를 찾을 수 없습니다.", false);
            }

            // 강제배송되는 배송은 첫번째 배송보다 순서가 항상 뒤에 있다고 가정함.
            for (int i = deliveryBundleEndedDTOList.size() - 1; i >= 0; i--) {

                final DeliveryBundleEndedDTO deliveryBundleEndedDTO = deliveryBundleEndedDTOList.get(i);
                final Long bundleDeliveryId = deliveryBundleEndedDTO.getDeliveryId();

                final int bundleDeliveryIndex = IterableUtils.indexOf(allocations, da -> da.getDelivery().getId().equals(bundleDeliveryId));
                if (bundleDeliveryIndex < 0) {
                    log.error("[setDeliveriesBundleEnded] 묶음배송처리될 배송아이디 {} 를 찾을 수 없어 무시합니다.", bundleDeliveryId);
                    deliveryBundleEndedDTO.setValidDelivery(false);
                    continue;
                }

                deliveryBundleEndedDTO.setValidDelivery(true);
                allocations.add(firstDeliveryIndex + 1, allocations.remove(bundleDeliveryIndex));
            }

            for (int i = firstDeliveryIndex + 1; i < allocations.size(); i++) {

                final DeliveryAllocation da = allocations.get(i);
                final Integer oldOrderNum = da.getOrderNum();
                final int newOrderNum = i + 1;
                if (!oldOrderNum.equals(newOrderNum)) {
                    final Long changedDeliveryId = da.getDelivery().getId();
                    orderChangedList.add(DeliveryRoutesRequestDTO.builder().deliveryId(changedDeliveryId).order(newOrderNum).build());
                }
            }

            if (CollectionUtils.isNotEmpty(orderChangedList)) {
                deliveryRouteService.updateDeliveryRoutePath(riderId, projectId, orderChangedList);
            } else {
                log.info("[setDeliveriesBundleEnded] 묶음배송완료에 의한 배송순서 및 경로 변경은 없습니다.");
            }
        }

        final boolean hasEverDeliveryDeparted = this.hasEverDeliveryDeparted(riderId, projectId);
        final List<DeliveryAllocation> endedDeliveryAllocationList = new ArrayList<>();
        final List<File> fileList = new ArrayList<>();

        // 첫번째 배송에 대해 일반/강제배송완료 처리
        if (Objects.isNull(firstDeliveryEndedDTO.getIsForced())) {
            log.info("[setDeliveriesBundleEnded] 배송아이디 {}를 일반배송완료 처리합니다.", firstDeliveryEndedDTO.getDeliveryId());

            final DeliveryAllocation firstDeliveryAllocation = updateDeliveryStatus(firstDeliveryEndedDTO.getDeliveryId(), riderId, DeliveryStatusDTO.of(firstDeliveryEndedDTO), hasEverDeliveryDeparted);
            endedDeliveryAllocationList.add(firstDeliveryAllocation);

            // 파일 서버에 파일이 있는 정보를 가져옴
            fileList.addAll(fileService.getFileList(firstDeliveryAllocation.getId(), FileCategory.DELIVERY_COMPLETE, FileType.IMAGE));
        } else {
            log.info("[setDeliveriesBundleEnded] 배송아이디 {}를 강제배송완료 처리합니다.", firstDeliveryEndedDTO.getDeliveryId());
            final List<DeliveryAllocation> firstDeliveryAllocation = setDeliveryEnded(DeliveryEndedDTO.of(projectId, riderId, firstDeliveryEndedDTO));
            endedDeliveryAllocationList.addAll(firstDeliveryAllocation);

            // 파일 서버에 파일이 있는 정보를 가져옴
            fileList.addAll(fileService.getFileList(firstDeliveryAllocation.get(0).getId(), FileCategory.DELIVERY_COMPLETE, FileType.IMAGE));
        }

        // 나머지 배송들에 대해 강제배송완료 처리
        for (final DeliveryBundleEndedDTO deliveryBundleEndedDTO : deliveryBundleEndedDTOList) {
            if (deliveryBundleEndedDTO.isValidDelivery()) {
                log.info("[setDeliveriesBundleEnded] 배송아이디 {}를 강제배송완료 처리합니다.", deliveryBundleEndedDTO.getDeliveryId());

                final List<DeliveryAllocation> otherDeliveryAllocations = setDeliveryEnded(DeliveryEndedDTO.of(projectId, riderId, deliveryBundleEndedDTO));
                endedDeliveryAllocationList.addAll(otherDeliveryAllocations);

                //처음 deliveryAllocation 파일이 있을 경우에 다른 deliveryAllocation 저장하게 함.
                if (ObjectUtils.isNotEmpty(fileList)) {
                    for (File file : fileList) {
                        file.setFolderName(file.generateFolderName(projectId));
                        File newFile = File.builder()
                                .category(file.getCategory())
                                .fkey(otherDeliveryAllocations.get(0).getId())
                                .orgName(file.getOrgName())
                                .savedName(file.getSavedName())
                                .type(file.getType())
                                .build();
                        log.info(file.getFolderName());
                        fileRepository.save(newFile);

                        newFile.setFolderName(newFile.generateFolderName(projectId));

                        try {
                            fileService.copyPublicFile(file.getFolderName(), file.getSavedName(), newFile.generateFolderName(projectId), newFile.getSavedName());
                        } catch (Exception e) {
                            log.warn("복사 실패했습니다. {}: {}", e.getClass().getSimpleName(), e.getMessage());
                        }
                    }
                }
            }
        }

        return endedDeliveryAllocationList;
    }

    @Transactional
    public List<DeliveryAllocation> setDeliveryEnded(final DeliveryEndedDTO deliveryEndedDTO) {

        final Long projectId = deliveryEndedDTO.getProjectId();
        final Long riderId = deliveryEndedDTO.getRiderId();
        final Long deliveryId = deliveryEndedDTO.getDeliveryId();
        final Boolean isForced = deliveryEndedDTO.getIsForced();
        final DeliveryStatus deliveryStatusChange = deliveryEndedDTO.getDeliveryStatus();

        final List<Delivery> notEndedDeliveries = deliveryBasicService.getDeliveriesByProjectIdAndRiderIdAndStatusIn(projectId, riderId, DeliveryStatus.notEndedDeliveryStatusList).stream()
                .sorted(Comparator.comparing(this::getOrderNumByDelivery, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());

        final Delivery targetDelivery = IterableUtils.find(notEndedDeliveries, d -> Objects.equals(d.getId(), deliveryId));
        if (Objects.isNull(targetDelivery)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "deliveryId: " + deliveryId + "를 미배송 목록에서 찾을 수 없습니다. 이미 완료/실패처리된 배송일 수 있습니다.", false);
        }

        // deliveryId가 현재의 첫번째 방문지가 아니면 먼저 순서를 조정한다.
        final Delivery firstNotEndedDelivery = notEndedDeliveries.get(0);
        if (!Objects.equals(targetDelivery, firstNotEndedDelivery)) {
            final Integer firstNotEndedOrderNum = this.getOrderNumByDelivery(firstNotEndedDelivery);
            final Integer targetOrderNum = this.getOrderNumByDelivery(targetDelivery);
            final List<DeliveryRoutesRequestDTO> orderChangedList = new ArrayList<>();

            notEndedDeliveries.forEach(d -> {
                final Integer orderNum = this.getOrderNumByDelivery(d);
                if (orderNum < targetOrderNum) {
                    orderChangedList.add(DeliveryRoutesRequestDTO.builder().deliveryId(d.getId()).order(orderNum + 1).build());
                } else if (Objects.equals(orderNum, targetOrderNum)) {
                    orderChangedList.add(DeliveryRoutesRequestDTO.builder().deliveryId(d.getId()).order(firstNotEndedOrderNum).build());
                }
            });

            if (CollectionUtils.isNotEmpty(orderChangedList)) {
                deliveryRouteService.updateDeliveryRoutePath(riderId, projectId, orderChangedList);
            }
        }

        List<DeliveryAllocation> allocations;
        final String orgCodeName = organizationService.getOrganizationCodeByProjectId(projectId);
        final boolean isTheHyundaiOrg = StringUtils.equals(orgCodeName, TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME);

        if (DeliveryStatus.COMPLETED.equals(deliveryStatusChange)) {
            allocations = this.makeDeliveryEnded(projectId, riderId, deliveryId, isForced, deliveryStatusChange);
            allocations.forEach(da -> {
                DeliveryCompletedType deliveryCompletedType = deliveryEndedDTO.getDeliveryCompletedType();
                if (Objects.isNull(deliveryCompletedType)) {
                    deliveryCompletedType = isTheHyundaiOrg ? DeliveryCompletedType.DOOR : DeliveryCompletedType.NORMAL_DELIVERY;
                }
                final String deliveryCompletedMessage = Optional.ofNullable(deliveryEndedDTO.getDeliveryCompletedMessage()).orElse("");

                DeliveryStatusDTO deliveryStatusDTO = DeliveryStatusDTO.builder()
                        .deliveryStatus(DeliveryStatus.COMPLETED)
                        .deliveryFailureType(null)
                        .deliveryFailureMessage("")
                        .deliveryCompletedType(deliveryCompletedType)
                        .deliveryCompletedMessage(deliveryCompletedMessage)
                        .build();

                deliveryCompletedHistoryService.saveDeliveryCompletedHistory(da, deliveryStatusDTO);

                // 배송완료시 Noti 발생
                final String customerOrderId = da.getDelivery().getDetail().getCustomerOrderId();
                final String displayId = Objects.nonNull(customerOrderId) ? CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight() : String.valueOf(da.getDelivery().getId());

                notificationService.notificationOccurDeliveryStatusCompleted(riderId, DeliveryStatus.COMPLETED, projectId,
                        deliveryCompletedType, deliveryCompletedMessage,
                        displayId, da.getDelivery());
            });
        } else if (DeliveryStatus.FAILURE.equals(deliveryStatusChange)) {
            allocations = this.makeDeliveryEnded(projectId, riderId, deliveryId, isForced, deliveryStatusChange);
            allocations.forEach(da -> {
                DeliveryFailureType deliveryFailureType = deliveryEndedDTO.getDeliveryFailureType();
                if (Objects.isNull(deliveryFailureType)) {
                    deliveryFailureType = isTheHyundaiOrg ? DeliveryFailureType.WRONG_ADDR : DeliveryFailureType.OTHER;
                }
                final String deliveryFailureMessage = Optional.ofNullable(deliveryEndedDTO.getDeliveryFailureMessage()).orElse("");

                DeliveryStatusDTO deliveryStatusDTO = DeliveryStatusDTO.builder()
                        .deliveryStatus(DeliveryStatus.FAILURE)
                        .deliveryFailureType(deliveryFailureType)
                        .deliveryFailureMessage(deliveryFailureMessage)
                        .deliveryCompletedType(null)
                        .deliveryCompletedMessage("")
                        .build();

                deliveryFailureHistoryService.saveDeliveryFailureHistory(da, deliveryStatusDTO, riderId);

                // 배송실패시 Noti 발생
                final String customerOrderId = da.getDelivery().getDetail().getCustomerOrderId();
                final String displayId = Objects.nonNull(customerOrderId) ? CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight() : String.valueOf(da.getDelivery().getId());

                notificationService.notificationOccurDeliveryStatusFailure(riderId, DeliveryStatus.FAILURE, projectId,
                        deliveryFailureType, deliveryFailureMessage,
                        displayId, da.getDelivery());
            });
        } else if (DeliveryStatus.REJECTED.equals(deliveryStatusChange)) {
            allocations = this.makeDeliveryEnded(projectId, riderId, deliveryEndedDTO.getDeliveryId(), deliveryEndedDTO.getIsForced(), deliveryStatusChange);
        } else {
            throw new CustomException(HttpStatus.BAD_REQUEST,
                    "배송아이디 " + deliveryId + "에 대해 강제배송완료 요청된 배송상태 " + deliveryStatusChange + "가 잘못되었습니다.", false);
        }

        return allocations;
    }

    @Transactional
    public List<DeliveryAllocation> makeDeliveryEnded(Long projectId,
                                                      Long riderId,
                                                      Long deliveryId,
                                                      Boolean isForced,
                                                      DeliveryStatus deliveryStatusChange) {

        if (Objects.isNull(deliveryId)) {
            final List<DeliveryAllocation> deliveryAllocationList = deliveryAllocationRepository
                    .findByProjectIdAndRiderIdOrderByOrderNumAsc(projectId, riderId);

            return deliveryAllocationList.stream()
                    .map(da -> makeSingleDeliveryEnded(da, isForced, deliveryStatusChange))
                    .collect(Collectors.toList());
        } else {
            final Optional<DeliveryAllocation> deliveryAllocation = deliveryBasicService.getDeliveryAllocation(riderId, deliveryId);

            return deliveryAllocation
                    .map(da -> makeSingleDeliveryEnded(da, isForced, deliveryStatusChange))
                    .map(Collections::singletonList)
                    .orElseGet(Collections::emptyList);
        }
    }

    @Transactional
    public DeliveryAllocation makeSingleDeliveryEnded(DeliveryAllocation deliveryAllocation,
                                                      boolean isForced,
                                                      DeliveryStatus deliveryStatusChange) {

        deliveryRouteService.makeRouteSectionComplete(deliveryAllocation, true);    // 위치 옮기지 마세요.

        if (DeliveryStatus.READY.equals(deliveryAllocation.getStatus())) {
            deliveryAllocation.setIsForcedComplete(isForced);
            deliveryStatusHistoryService.insertDeliveryStatusHistory(deliveryAllocation, DeliveryStatus.GOING, isForced);
            deliveryAllocation.setStatus(DeliveryStatus.GOING);
            CommonUtil.delayMs(5);
        }

        if (DeliveryStatus.GOING.equals(deliveryAllocation.getStatus())) {
            deliveryAllocation.setIsForcedComplete(isForced);
            deliveryStatusHistoryService.insertDeliveryStatusHistory(deliveryAllocation, DeliveryStatus.SERVICING, isForced);
            deliveryAllocation.setStatus(DeliveryStatus.SERVICING);
            CommonUtil.delayMs(5);
        }

        if (DeliveryStatus.SERVICING.equals(deliveryAllocation.getStatus())) {
            if (deliveryStatusChange.equals(DeliveryStatus.COMPLETED)) {
                deliveryAllocation.setIsForcedComplete(isForced);
                deliveryStatusHistoryService.insertDeliveryStatusHistory(deliveryAllocation, DeliveryStatus.COMPLETED, isForced);
                deliveryAllocation.setStatus(DeliveryStatus.COMPLETED);
            } else if (deliveryStatusChange.equals(DeliveryStatus.FAILURE)) {
                deliveryAllocation.setIsForcedComplete(isForced);
                deliveryStatusHistoryService.insertDeliveryStatusHistory(deliveryAllocation, DeliveryStatus.FAILURE, isForced);
                deliveryAllocation.setStatus(DeliveryStatus.FAILURE);
            } else if (deliveryStatusChange.equals(DeliveryStatus.REJECTED)) {
                deliveryAllocation.setIsForcedComplete(isForced);
                deliveryAllocation.setStatus(DeliveryStatus.REJECTED);
            } else {
                throw new CustomException(HttpStatus.BAD_REQUEST, "변경하고자하는 배송상태가 없습니다.");
            }

            deliveryRouteService.updateStaySeconds(deliveryAllocation, 0L);
            deliveryRouteService.calcRiderRouteRunEtdEta(deliveryAllocation.getProjectId(), deliveryAllocation.getRiderId());
        }

        return deliveryAllocationRepository.save(deliveryAllocation);
    }

    /**
     * 배송품 검수 상태 변경
     *
     * @param riderId
     * @param deliveryInspectionDTO
     * @param isInspectionEnded
     */
    @Transactional
    public DeliveryInspectionRespDTO updateInspectionStatus(final Long riderId,
                                                            final DeliveryInspectionDTO deliveryInspectionDTO,
                                                            final Boolean isInspectionEnded) {

        final String qrBarCode = deliveryInspectionDTO.getQrBarCode();
        InspectionStatus requestedInspectionStatus = deliveryInspectionDTO.getInspectionStatus();
        final Long deliveryId = deliveryInspectionDTO.getDeliveryId();

        if (StringUtils.isEmpty(qrBarCode) || Objects.isNull(requestedInspectionStatus) || Objects.isNull(deliveryId)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "요청 필수값이 없습니다.", false);
        }

        final Delivery delivery = deliveryRepository.findById(deliveryId)
                .orElseThrow(() -> new CustomException(HttpStatus.BAD_REQUEST, "배송 정보가 없습니다.", false));

        String codeName = "";
        final RiderOrgStatus riderOrgStatus = riderOrgStatusRepository.findTop1ByRiderIdAndIsDeletedOrderByIdDesc(riderId, false);
        final Organization organization = organizationService.getOrganizationById(Optional.ofNullable(riderOrgStatus).map(RiderOrgStatus::getOrgId).orElse(null));
        if (Objects.isNull(organization)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "해당하는 조직이 없습니다.", false);
        } else {
            codeName = organization.getCodeName();
            log.info("[updateInspectionStatus] organization: {}", codeName);
        }

        final DeliveryDetail deliveryDetail = delivery.getDetail();
        final DeliveryCategory deliveryCategory = deliveryDetail.getDeliveryCategory();
        final Integer whGubn = deliveryDetail.getWhGubn();
        final Boolean isWhGubnProduct = Objects.nonNull(deliveryCategory) && Objects.nonNull(whGubn);
        final List<DeliveryProduct> deliveryProductList = delivery.getDeliveryProducts();

        final boolean isRegistration = VisitType.COLLECTION.equals(delivery.getVisitType());    // 바코드 등록
        if (isRegistration) {
            if (StringUtils.equals(qrBarCode, deliveryDetail.getQrBarCode())) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "배송에 이미 등록된 바코드입니다.", false);
            }

            final Long projectId = deliveryInspectionDTO.getProjectId();
            final Delivery sameQrBarCodeDelivery = deliveryBasicService.getDeliveryByProjectIdAndQrBarCode(projectId, qrBarCode);
            if (Objects.nonNull(sameQrBarCodeDelivery)) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "다른 배송에 이미 등록된 바코드입니다.", false);
            }

            // 수거일 때는 QRBarCode 저장
            deliveryDetail.setQrBarCode(qrBarCode);
            deliveryRepository.save(delivery);
        } else {
            // [현대백화점] 본부상품이면 주문등록시 qrBarCode에 OM에서 물류코드(productBarcode)로 전달하고
            // delivery_detail.qr_bar_code에 productBarcode로 저장한다.
            if (Boolean.TRUE.equals(isWhGubnProduct)) {
                if (DeliveryCategory.HOLIDAY.equals(deliveryCategory) && TheHyundaiConstant.WHGUBN_HQ.equals(whGubn)) {
                    // 명절-본부상품은 물류코드로 검수
                    if (CollectionUtils.isEmpty(deliveryProductList)) {
                        throw new CustomException(HttpStatus.NOT_FOUND, "본부상품 정보가 없습니다.", false);
                    }
                    final List<String> productBarcodeList = deliveryProductList.stream()
                            .map(DeliveryProduct::getProductBarcode)
                            .collect(Collectors.toList());
                    if (!productBarcodeList.contains(qrBarCode)) {
                        throw new CustomException(HttpStatus.BAD_REQUEST, "물류코드가 올바르지 않습니다.", false);
                    }
                } else if (!DeliveryCategory.HOLIDAY.equals(deliveryCategory) || TheHyundaiConstant.WHGUBN_G.equals(whGubn)) {
                    // 근거리, 원거리, 명절-일반상품은 전표번호로 검수
                    String customerOrderCode = "";
                    try {
                        customerOrderCode = CustomerUtil.appendOrganizationCode(codeName, qrBarCode.substring(0, qrBarCode.length() - 2));
                    } catch (Exception e) {
                        log.error("[updateInspectionStatus] Exception: " + e.getMessage());
                        throw new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, "실행 오류입니다.", e, false);
                    }
                    if (!StringUtils.equals(customerOrderCode, deliveryDetail.getCustomerOrderId())) {
                        throw new CustomException(HttpStatus.BAD_REQUEST, "전표번호가 올바르지 않습니다.", false);
                    }
                }
            } else if (!StringUtils.equals(qrBarCode, deliveryDetail.getQrBarCode())) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "잘못된 검수상태 변경 요청입니다.", false);
            }
        }

        final DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocationOrThrowException(riderId, deliveryId);
        Long orderQuantity = 1L;
        Long toBePickupQuantity = null;
        Long toBeDropoffQuantity = null;
        final Long asIsPickupQuantity = Optional.ofNullable(deliveryAllocation.getPickupQuantity()).orElse(0L);
        final Long asIsDropoffQuantity = Optional.ofNullable(deliveryAllocation.getDropoffQuantity()).orElse(0L);

        if (Boolean.TRUE.equals(isWhGubnProduct)) {
            if (DeliveryCategory.HOLIDAY.equals(deliveryCategory) && TheHyundaiConstant.WHGUBN_HQ.equals(whGubn)) {
                // 명절-본부상품일 경우
                final DeliveryProduct deliveryProduct = deliveryProductList.stream()
                        .filter(dp -> qrBarCode.equals(dp.getProductBarcode()))
                        .findFirst()
                        .orElse(null);
                if (Objects.isNull(deliveryProduct)) {
                    throw new CustomException(HttpStatus.NOT_FOUND, "본부상품 정보가 없습니다.", false);
                }
                if (!StringUtils.equals(deliveryProduct.getProductBarcode(), qrBarCode)) {
                    throw new CustomException(HttpStatus.BAD_REQUEST, "물류코드가 올바르지 않습니다.", false);
                }

                orderQuantity = deliveryProduct.getOrderQuantity();
                if (Objects.isNull(orderQuantity) || orderQuantity < 1L) {
                    throw new CustomException(HttpStatus.NOT_FOUND, "상품수량 정보가 없습니다.", false);
                }

                switch (requestedInspectionStatus) {
                    case PICKUP:
                        if (asIsPickupQuantity < orderQuantity) {
                            if (!asIsPickupQuantity.equals((orderQuantity - 1L))) {
                                requestedInspectionStatus = InspectionStatus.UNINSPECTED;
                            }
                            deliveryAllocation.setInspectionStatus(requestedInspectionStatus); // UNINSPECTED --> UNINSPECTED --> PICKUP
                            toBePickupQuantity = asIsPickupQuantity + 1L;
                            deliveryAllocation.setPickupQuantity(toBePickupQuantity);
                        } else {
                            if (!isInspectionEnded) {
                                throw new CustomException(HttpStatus.BAD_REQUEST, "더 이상 상차검수할 수 없습니다.", false);
                            }
                        }
                        break;

                    case DROPOFF:
                        if (asIsDropoffQuantity < orderQuantity) {
                            if (!asIsDropoffQuantity.equals((orderQuantity - 1L))) {
                                requestedInspectionStatus = InspectionStatus.PICKUP;
                            }
                            deliveryAllocation.setInspectionStatus(requestedInspectionStatus); // PICKUP --> PICKUP --> DROPOFF
                            toBeDropoffQuantity = asIsDropoffQuantity + 1L;
                            deliveryAllocation.setDropoffQuantity(toBeDropoffQuantity);
                        } else {
                            throw new CustomException(HttpStatus.BAD_REQUEST, "더 이상 하차검수할 수 없습니다.", false);
                        }
                        break;

                    case UNINSPECTED:
                        // 상차검수 취소
                        if (asIsPickupQuantity < 1L) {
                            throw new CustomException(HttpStatus.BAD_REQUEST, "더 이상 상차검수 취소할 수 없습니다.", false);
                        }
                        toBePickupQuantity = asIsPickupQuantity - 1L;
                        deliveryAllocation.setPickupQuantity(toBePickupQuantity);
                        deliveryAllocation.setInspectionStatus(requestedInspectionStatus); // PICKUP --> UNINSPECTED --> UNINSPECTED
                        break;

                    default:
                        break;
                }
            } else if (!DeliveryCategory.HOLIDAY.equals(deliveryCategory) || TheHyundaiConstant.WHGUBN_G.equals(whGubn)) {
                // 명절-본부상품이 아닐 경우
                if (CollectionUtils.isEmpty(deliveryProductList)) {
                    throw new CustomException(HttpStatus.NOT_FOUND, "일반상품 정보가 없습니다.", false);
                }

                orderQuantity = Optional.ofNullable(deliveryDetail.getProductQuantity())
                        .map(pq -> Long.valueOf(pq.longValue()))
                        .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "상품수량 정보가 없습니다.", false));

                switch (requestedInspectionStatus) {
                    case PICKUP:
                        if (asIsPickupQuantity < orderQuantity) {
                            if (!asIsPickupQuantity.equals((orderQuantity - 1L))) {
                                requestedInspectionStatus = InspectionStatus.UNINSPECTED;
                            }
                            deliveryAllocation.setInspectionStatus(requestedInspectionStatus); // UNINSPECTED --> UNINSPECTED --> PICKUP
                            toBePickupQuantity = asIsPickupQuantity + 1L;
                            deliveryAllocation.setPickupQuantity(toBePickupQuantity);
                        } else {
                            if (!isInspectionEnded) {
                                throw new CustomException(HttpStatus.BAD_REQUEST, "더 이상 상차검수할 수 없습니다.", false);
                            }
                        }
                        break;

                    case DROPOFF:
                        if (asIsDropoffQuantity < orderQuantity) {
                            if (!asIsDropoffQuantity.equals((orderQuantity - 1L))) {
                                requestedInspectionStatus = InspectionStatus.PICKUP;
                            }
                            deliveryAllocation.setInspectionStatus(requestedInspectionStatus); // PICKUP --> PICKUP --> DROPOFF
                            toBeDropoffQuantity = asIsDropoffQuantity + 1L;
                            deliveryAllocation.setDropoffQuantity(toBeDropoffQuantity);
                        } else {
                            throw new CustomException(HttpStatus.BAD_REQUEST, "더 이상 하차검수할 수 없습니다.", false);
                        }
                        break;

                    case UNINSPECTED:
                        // 상차검수 취소
                        if (asIsPickupQuantity < 1L) {
                            throw new CustomException(HttpStatus.BAD_REQUEST, "더 이상 상차검수 취소할 수 없습니다.", false);
                        }
                        toBePickupQuantity = asIsPickupQuantity - 1L;
                        deliveryAllocation.setPickupQuantity(toBePickupQuantity);
                        deliveryAllocation.setInspectionStatus(requestedInspectionStatus); // PICKUP --> UNINSPECTED --> UNINSPECTED
                        break;

                    default:
                        break;
                }
            }
        } else {
            switch (requestedInspectionStatus) {
                case PICKUP:
                    toBePickupQuantity = 1L;
                    deliveryAllocation.setPickupQuantity(toBePickupQuantity);
                    break;

                case DROPOFF:
                    toBeDropoffQuantity = 1L;
                    deliveryAllocation.setDropoffQuantity(toBeDropoffQuantity);
                    break;

                case UNINSPECTED:
                    // 상차검수 취소
                    toBePickupQuantity = 0L;
                    deliveryAllocation.setPickupQuantity(toBePickupQuantity);
                    break;

                default:
                    break;
            }
        }

        // 바코드 등록일 경우는 다시 등록 가능하도록 함.
        final DeliveryInspectionHistory deliveryInspectionHistory = isRegistration ?
                deliveryInspectionHistoryService.saveDeliveryInspectionHistoryForcibly(deliveryAllocation, requestedInspectionStatus) :
                deliveryInspectionHistoryService.saveDeliveryInspectionHistory(deliveryAllocation, requestedInspectionStatus);

        if (Objects.isNull(deliveryInspectionHistory)) {
            if (!isInspectionEnded && StringUtils.isNotBlank(qrBarCode) && !isWhGubnProduct) {
                // [현대백화점] 본부/일반 상품일 때는 아래 에러로 리턴하면 안됨
                throw new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, "이전 검수상태와 동일합니다.", false);
            }
        } else {
            deliveryAllocation.setInspectionStatus(requestedInspectionStatus);

            // 상차검수시 확정된 배송 순서대로 물품 번호 설정
            if (InspectionStatus.PICKUP.equals(requestedInspectionStatus) && Objects.isNull(deliveryAllocation.getBoxNum())) {
                deliveryAllocation.setBoxNum(Optional.ofNullable(deliveryAllocation.getOrderNum()).orElse(null));
            }
        }

        // 검수마감 여부 설정
        if (isInspectionEnded) {
            // 검수마감시 미검수인 항목은 미배송으로 처리
            if (InspectionStatus.UNINSPECTED.equals(requestedInspectionStatus)) {
                log.info("[updateInspectionStatus] deliveryId: {} 를 DeliveryStatus.UNDELIVERED 로 설정하고 검수마감", deliveryId);
                deliveryStatusHistoryService.insertDeliveryStatusHistory(deliveryAllocation, DeliveryStatus.UNDELIVERED,  false);
                deliveryAllocation.setStatus(DeliveryStatus.UNDELIVERED);
            }
            deliveryAllocation.setIsInspectionEnded(isInspectionEnded);
        }
        log.info("[updateInspectionStatus] 검수상태 업데이트 --- deliveryId: {}, deliveryAllocationId: {}, qrBarCode: {}, inspectionStatus: {}, isInspectionEnded: {}, pickup: {}/{}, dropoff: {}/{}",
                deliveryId, deliveryAllocation.getId(), qrBarCode, requestedInspectionStatus, isInspectionEnded, toBePickupQuantity, orderQuantity, toBeDropoffQuantity, orderQuantity);

        deliveryAllocationRepository.save(deliveryAllocation);

        return DeliveryInspectionRespDTO.builder()
                .qrBarCode(qrBarCode)
                .inspectionStatus(requestedInspectionStatus)
                .orderQuantity(orderQuantity)
                .pickupQuantity(Optional.ofNullable(deliveryAllocation.getPickupQuantity()).orElse(0L))
                .dropoffQuantity(Optional.ofNullable(deliveryAllocation.getDropoffQuantity()).orElse(0L))
                .deliveryId(deliveryId)
                .projectId(delivery.getProjectId())
                .riderId(riderId)
                .build();
    }

    @Transactional
    public List<DeliveryInspectionRespDTO> setInspectionEnded(Long riderId,
                                                              List<DeliveryInspectionDTO> deliveryInspectionDTOList) {

        // 검수마감 단계에서 각 배송의 검수상태 변경이 제대로 됐는지 확인
        return deliveryInspectionDTOList.stream()
                .map(dto -> updateInspectionStatus(riderId, dto, true))
                .collect(Collectors.toList());
    }

    /**
     * 배송 완료 사진 중에 prefix로 사용하는 이미지 삭제
     *
     * @param prefix
     * @param deliveryAllocation
     * @param existingFiles
     * @return
     */
private void deleteDeliveryCompleteImageByPrefix(String prefix, DeliveryAllocation deliveryAllocation, List<FileDTO> existingFiles) {
    try {
        if (StringUtils.isNotEmpty(prefix)) {
            existingFiles.stream()
                .filter(fileDTO -> fileDTO.getFileUrl().contains(prefix))
                .forEach(fileDTO -> {
                    try {
                        File file = fileService.getFileByIdOrElseThrow(fileDTO.getFileId());
                        log.info("[deleteDeliveryCompleteImageByPrefix] url: {}, deliveryAllocation: {} ",
                                 fileDTO.getFileUrl(), deliveryAllocation.getId());
                        fileService.deleteFile(file, deliveryAllocation);
                    } catch (Exception e) {
                        log.error("[deleteDeliveryCompleteImageByPrefix] Error deleting file: {}, Error: {}",
                                  fileDTO.getFileUrl(), e.getMessage());
                    }
                });
        }
    } catch (Exception e) {
        log.error("[deleteDeliveryCompleteImageByPrefix] Unexpected error occurred: {}", e.getMessage());
    }
}



    /**
     * 배송 완료 사진 upload
     *
     * @param projectId
     * @param riderId
     * @param deliveryId
     * @param imgList
     * @return
     */
    public List<FileDTO> uploadDeliveryCompleteImage(Long projectId,
                                                     Long riderId,
                                                     Long deliveryId,
                                                     List<MultipartFile> imgList,
                                                     String prefix ) {

        List<FileDTO> result = new ArrayList<>();

        try {
            DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocationOrThrowException(riderId, deliveryId);
            if (deliveryAllocation.getProjectId().longValue() != projectId) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "project 정보가 일치하지 않습니다.", false);
            }

            List<FileDTO> fileDTOList = getUploadDeliveryCompleteImage(projectId, riderId, deliveryId, Boolean.FALSE);

            // prefix로 시작하는 기존 파일들 삭제
            deleteDeliveryCompleteImageByPrefix(prefix, deliveryAllocation, fileDTOList);

            int existImgCnt = fileDTOList.size();
            if (existImgCnt + imgList.size() > MAX_UPLOAD_SAVED_IMAGE_COUNT) {
                throw new CustomException(HttpStatus.PRECONDITION_FAILED,
                        "저장되는 이미지 개수는 총 " + MAX_UPLOAD_SAVED_IMAGE_COUNT + " 장을 초과할 수 없습니다. existImgCnt : " + existImgCnt + ", uploadCnt : " + imgList.size(), false);
            }

            for (MultipartFile img : imgList) {
                result.add(fileService.saveFileWithPrefix(img, FileCategory.DELIVERY_COMPLETE, deliveryAllocation, prefix ));
            }
        } catch (CustomException ce) {
            throw ce;
        } catch (Exception e) {
            throw new CustomException(HttpStatus.BAD_REQUEST, e);
        }

        return result;
    }

    /**
     * 배송 완료 사진 조회
     *
     * @param projectId
     * @param riderId
     * @param deliveryId
     * @param isBundleEnded
     * @return
     */
    public List<FileDTO> getUploadDeliveryCompleteImage(final Long projectId,
                                                        final Long riderId,
                                                        final Long deliveryId,
                                                        final Boolean isBundleEnded) {

        final DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocationOrThrowException(riderId, deliveryId);

        if (!deliveryAllocation.getProjectId().equals(projectId)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "project 정보가 일치하지 않습니다.", false);
        }

        List<DeliveryAllocation> deliveryAllocations = new ArrayList<>();
        final Long bundleEndedId = Boolean.TRUE.equals(isBundleEnded) ? deliveryAllocation.getBundleEndedId() : null;

        if (Objects.nonNull(bundleEndedId)) {
            log.info("[getUploadDeliveryCompleteImage] bundleEndedId: {}", bundleEndedId);
            deliveryAllocations = deliveryBasicService.getDeliveryAllocationListByBundleEndedId(bundleEndedId);
        } else {
            deliveryAllocations.add(deliveryAllocation);
        }

        List<FileDTO> result = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(deliveryAllocations)) {
            for (DeliveryAllocation da : deliveryAllocations) {
                final List<File> fileList = fileService.getFileList(da.getId(), FileCategory.DELIVERY_COMPLETE, FileType.IMAGE);

                if (CollectionUtils.isNotEmpty(fileList)) {
                    final List<FileDTO> fileDTOList = fileList.stream()
                            .map(f -> {
                                final String fileUrl = fileService.getFileUrl(f, deliveryAllocation, false);
                                return FileDTO.builder()
                                        .fileId(f.getId())
                                        .fileUrl(fileUrl)
                                        .fileCategory(FileCategory.DELIVERY_COMPLETE)
                                        .build();
                            })
                            .collect(Collectors.toList());

                    result.addAll(fileDTOList);
                }
            }
        }

        return result;
    }

    /**
     * 배송완료 상태에 포함된 파일 목록을 조회
     *
     * @param projectId
     * @return
     */
    public List<File> getFileListForDeliveryCompleted(@NotNull @Positive final Long projectId) {

        final Map<Long, DeliveryCustomerOrderIdDAO> daoMap = deliveryBasicService.getDeliveryCustomerOrderIdByProjectId(projectId);

        final List<File> fileList = fileService.getFileList(daoMap.keySet(), FileCategory.DELIVERY_COMPLETE, FileType.IMAGE);
        fileList.forEach(file -> {
            // FolderName을 만들어줘야 한다.
            file.setFolderName(file.generateFolderName(projectId));

            final DeliveryCustomerOrderIdDAO dao = daoMap.get(file.getFkey());
            final String outputFolderName = StringUtils.defaultIfBlank(dao.getCustomerOrderId(), dao.getDeliveryId().toString());
            file.setOutputFolderName(outputFolderName);
        });

        return fileList;
    }

    /**
     * 배송완료 상태에 포함된 파일 목록을 조회
     *
     * @param projectId
     * @param riderId
     * @param deliveryId
     * @return
     */
    public List<File> getFileListForDeliveryCompleted(@NotNull @Positive final Long projectId,
                                                      @NotNull @Positive final Long riderId,
                                                      @NotNull @Positive final Long deliveryId) {

        final DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocationOrThrowException(riderId, deliveryId);
        final Long deliveryAllocationId = deliveryAllocation.getId();

        if (!deliveryAllocation.getProjectId().equals(projectId)) {
            throw new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, "프로젝트 정보가 일치하지 않습니다.");
        }

        final List<File> fileList = fileService.getFileList(deliveryAllocationId, FileCategory.DELIVERY_COMPLETE, FileType.IMAGE);
        // FolderName을 만들어줘야 한다.
        fileList.forEach(file -> file.setFolderName(file.generateFolderName(deliveryAllocation.getProjectId())));

        return fileList;
    }

    /**
     * 배송완료사진 Get URL function
     */
    public List<String> getDeliveryCompletedFilesURLList(final DeliveryAllocation da,
                                                         final boolean isShorten) {

        final List<File> fileList = fileService.getFileList(da.getId(), FileCategory.DELIVERY_COMPLETE, FileType.IMAGE);
        final List<String> deliveriesURL = fileList.stream()
                .map(file -> fileService.getFileUrl(file, da, isShorten))
                .collect(Collectors.toList());

        return deliveriesURL;
    }

    public List<String> getDeliveryCompletedFilesURLList(final Long deliveryAllocationId) {

        return deliveryAllocationRepository.findById(deliveryAllocationId)
                .map(da -> getDeliveryCompletedFilesURLList(da, false))
                .orElseGet(Collections::emptyList);
    }

    /**
     * 배송 정보에 포함된 모든 이미지 파일들을 압축한 ZipOutputStream을 생성
     *
     * @param response
     * @param projectId
     * @param riderId
     * @param deliveryId
     * @param filename
     * @return
     */
    public void downloadDeliveryAttachedPhotos(@NotNull final HttpServletResponse response,
                                               @NotNull @Positive final Long projectId,
                                               @NotNull @Positive final Long riderId,
                                               @NotNull @Positive final Long deliveryId,
                                               @NotEmpty final String filename) {

        final List<File> attachedPhotos = getFileListForDeliveryCompleted(projectId, riderId, deliveryId);
        if (CollectionUtils.isEmpty(attachedPhotos)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "배송아이디 " + deliveryId + "에 해당하는 첨부파일이 없습니다.", false);
        }

        try {
            if (attachedPhotos.size() == 1) {
                final File file = attachedPhotos.get(0);
                fileService.createOutputStreamFromSingleFile(file, response.getOutputStream());

                final ContentDisposition contentDisposition = ContentDisposition
                        .builder("attachment")
                        .filename(filename + "_" + file.getOrgName(), StandardCharsets.UTF_8)
                        .build();
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
                response.setStatus(HttpServletResponse.SC_OK);
            } else {
                fileService.createZipOutputStreamFromFiles(attachedPhotos, response.getOutputStream());

                final ContentDisposition contentDisposition = ContentDisposition
                        .builder("attachment")
                        .filename(filename + ".zip", StandardCharsets.UTF_8)
                        .build();
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
                response.setContentType("application/zip");
                response.setStatus(HttpServletResponse.SC_OK);
            }
        } catch (Exception e) {
            throw new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, e);
        }
    }

    /**
     * 배송 완료 사진 삭제
     *
     * @param projectId
     * @param riderId
     * @param deliveryId
     * @param fileId
     */
    public void deleteUploadDeliveryCompleteImage(Long projectId,
                                                  Long riderId,
                                                  Long deliveryId,
                                                  Long fileId) {

        DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocationOrThrowException(riderId, deliveryId);
        if (deliveryAllocation.getProjectId().longValue() != projectId) {
            throw new RuntimeException("project 정보가 일치하지 않습니다.");
        }

        File file = fileService.getFileByIdOrElseThrow(fileId);

        if (file.getCategory() != FileCategory.DELIVERY_COMPLETE || file.getFkey().equals(deliveryId)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "파일정보가 존재하지 않습니다.");
        }

        fileService.deleteFile(file, deliveryAllocation);
    }

    private void checkCustomerProductImageSize(DeliveryDTO deliveryDTO) {

        if (deliveryDTO.getCustomerProductImageUrls() != null && (deliveryDTO.getCustomerProductImageUrls().length > DeliveryConstant.MAX_PRODUCT_IMAGE_URLS)) {
            throw new InvalidParameterException("최대 " + DeliveryConstant.MAX_PRODUCT_IMAGE_URLS + "개까지 상품 사진 등록이 가능합니다.");
        }
    }

    /**
     * Address의 동일 주소지의 배송리스트를 모두 불러온다
     *
     * @param destinationAddressId
     * @param userIdList
     * @return
     */
    public List<Delivery> getSameAddressDeliveriesByAddressIdAndUserIdList(Long destinationAddressId,
                                                                           Set<Long> userIdList) {

        if (Objects.nonNull(destinationAddressId) && CollectionUtils.isNotEmpty(userIdList)) {
            Address address = addressService.getAddress(destinationAddressId);
            List<Address> list = addressService.findSameAddressEntities(address.getBase(), address.getDetail());
            Set<Long> addressIdList = list.stream().map(Address::getId).collect(Collectors.toSet());//null이 나올리 없다.
            List<Delivery> deliveryList = deliveryRepository.findByDetailDestinationAddressIdInAndUserIdIn(addressIdList, userIdList);

            if (CollectionUtils.isNotEmpty(deliveryList)) {
                deliveryList.forEach(d -> {
                    final String customerOrderId = d.getDetail().getCustomerOrderId();
                    final String extCustomerOrderId = CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight();
                    d.getDetail().setCustomerOrderId(extCustomerOrderId);
                });
            }

            return deliveryList;
        }

        return null;
    }

    /**
     * 목적지가 address를 찾을수 없을때 체크
     *
     * @param destinations
     * @return
     */
    @Deprecated     // 절대 사용하지 마세요. findOrCreateValidAddress()가 너무 느립니다.
    public Map<String, Object> geoCodingValidationCheck(List<ProjectDestinationSheetDTO> destinations) {

        List<ProjectDestinationSheetDTO> responseError = new ArrayList<>();
        List<ProjectDestinationSheetDTO> responseSuccess = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(destinations)) {
            StringBuilder logDeliveryInfo = new StringBuilder();
            for (ProjectDestinationSheetDTO destination : destinations) {
                try {
                    Address address = addressService.findOrCreateValidAddress(destination.getViaAddress(), destination.getViaDetailAddress());
                    if (Objects.isNull(address)) {
                        log.warn("[geoCodingValidationCheck] 주소 오류 Id: {}, 주소: {} {}", destination.getCustomerOrderId(), destination.getViaAddress(), destination.getViaDetailAddress());
                        responseError.add(destination);
                    } else {
                        responseSuccess.add(destination);
                    }
                    logDeliveryInfo.append(destination.getCustomerOrderId());
                    logDeliveryInfo.append(" ");

                } catch (Exception e) {
                    log.warn("[geoCodingValidationCheck] Exception 주소 오류 Id: {}, 주소: {} {}", destination.getCustomerOrderId(), destination.getViaAddress(), destination.getViaDetailAddress());
                    responseError.add(destination);
                }
            }

            log.info("[geoCodingValidationCheck]  총 {} 개  : {} ", destinations.size(), logDeliveryInfo);
        }

        Map<String, Object> result = new HashMap<String, Object>();
        result.put("responseError", responseError);
        result.put("responseSuccess", responseSuccess);

        return result;
    }

    /**
     * 배송할당 리스트에서 실시간배송이 있는지 확인
     *
     * @param deliveryAllocationList
     * @return
     */
    @Deprecated
    public Boolean checkProductOrderDelivery(final List<DeliveryAllocation> deliveryAllocationList) {

        Boolean isOnDemand = false;

        if (CollectionUtils.isNotEmpty(deliveryAllocationList)) {
            for (final DeliveryAllocation da : deliveryAllocationList) {
                if (Boolean.TRUE.equals(da.getDelivery().getIsOnDemand())) {
                    isOnDemand = true;
                    break;
                }
            }
        }

        return isOnDemand;
    }

    /**
     * 프로젝트의 배송이 모두 끝났는지 확인
     *
     * @param projectId
     * @return
     */
    public boolean checkAllProjectDeliveryEnded(Long projectId) {

        // 배송완료시 실시간배송이 있는지 확인
        // 실시간배송이 있으면 자동으로 프로젝트 종료하지 않기 위해 모든 배송완료로 처리하지 않는다.
        if (deliveryAllocationRepository.existsByProjectIdAndIsOnDemand(projectId, true)) {
            return false;
        }

        // TODO : 모든 배송 완료의 기준 정의 필요 (우선은 배차된 배송지들의 배송이 모두 끝나면 성공)
        final Long totalDeliveryAllocationCount = deliveryAllocationRepository.countByProjectIdAndIsEffective(projectId, true);
        final Long totalDeliveryCount = this.getCountOfDeliveries(projectId, null, null, null, null, null, null);
        final Long endedDeliveryCount = deliveryAllocationRepository.countByProjectIdAndIsEffectiveAndStatusIn(projectId, true, DeliveryStatus.endedDeliveryStatusList);

        log.info("[checkAllProjectDeliveryEnded] projectId : {} ,  totalDeliveryAllocationCount : {} , totalDeliveryCount : {} , endedDeliveryCount : {}",
                projectId, totalDeliveryAllocationCount, totalDeliveryCount, endedDeliveryCount);

        return totalDeliveryCount.equals(endedDeliveryCount);
    }

    public boolean checkAllProjectRiderDeliveryEnded(Long projectId,
                                                     Long riderId) {

        // TODO : 모든 배송 완료의 기준 정의 필요 (우선은 배차된 배송지들의 배송이 모두 끝나면 성공)
        final Long totalDeliveryCount = deliveryAllocationRepository.countByProjectIdAndRiderIdAndIsEffective(projectId, riderId, true);
        final Long endedDeliveryCount = deliveryAllocationRepository.countByProjectIdAndRiderIdAndIsEffectiveAndStatusIn(projectId, riderId, true, DeliveryStatus.endedDeliveryStatusList);

        return totalDeliveryCount.equals(endedDeliveryCount);
    }

    public boolean checkAllProjectRiderDeliveryCompleted(Long projectId,
                                                         Long riderId) {

        // 배송완료시 실시간배송이 있는지 확인
        // 실시간배송이 있으면 자동으로 프로젝트 종료하지 않기 위해 모든 배송완료로 처리하지 않는다.
        if (deliveryAllocationRepository.existsByProjectIdAndRiderIdAndIsOnDemand(projectId, riderId, true)) {
            return false;
        }

        // TODO : 모든 배송 완료의 기준 정의 필요 (우선은 배차된 배송지들의 배송이 모두 끝나면 성공)
        final Long totalDeliveryCount = deliveryAllocationRepository.countByProjectIdAndRiderIdAndIsEffective(projectId, riderId, true);
        final Long endedDeliveryCount = deliveryAllocationRepository.countByProjectIdAndRiderIdAndIsEffectiveAndStatus(projectId, riderId, true, DeliveryStatus.COMPLETED);

        return totalDeliveryCount.equals(endedDeliveryCount);
    }

    /**
     * 배송이 완료된(Success) delivery 목록
     *
     * @param projectId
     * @return
     */
    public List<Delivery> getCompleteDelivery(Long projectId) {

        final List<Delivery> deliveryList = getAllDeliveries(projectId, null, null, null, null, null);
        if (CollectionUtils.isEmpty(deliveryList)) {
            return deliveryList;
        }

        return deliveryList.stream()
                .filter(d -> {
                    final List<DeliveryAllocation> deliveryAllocationList = d.getAllocations();
                    // delivery에 배차되어 있는 배송지의 완료 갯수를 구한다
                    long cnt = deliveryAllocationList.stream().filter(da -> da.getStatus() == DeliveryStatus.COMPLETED).count();
                    return cnt == deliveryAllocationList.size();
                })
                .collect(Collectors.toList());
    }

    /**
     * 해당 프로젝트에 첨부된 파일의 갯수 반환
     *
     * @param projectId
     * @return
     */
    public Long getFileCountForCompleted(final Long projectId) {

        final List<Long> deliveryAllocationIdList = deliveryBasicService.getDeliveryAllocationIdListByProjectId(projectId);

        if (CollectionUtils.isEmpty(deliveryAllocationIdList)) {
            return 0L;
        } else {
            return fileService.getFileCount(deliveryAllocationIdList, FileCategory.DELIVERY_COMPLETE, FileType.IMAGE);
        }
    }

    /**
     * 기본 검수상태 설정
     *
     * @param delivery
     * @return
     */
    public InspectionStatus setDefaultInspectionStatus(final Delivery delivery) {

        final String qrBarCode = delivery.getDetail().getQrBarCode();
        InspectionStatus inspectionStatus = InspectionStatus.UNINSPECTED;

        if (StringUtils.isBlank(qrBarCode) || Boolean.TRUE.equals(delivery.getIsOnDemand())) {
            inspectionStatus = InspectionStatus.NA;
        }

        return inspectionStatus;
    }

    /**
     * 기본 분류검증상태 설정
     *
     * @param delivery
     * @return
     */
    public PickupVerificationStatus setDefaultPickupVerificationStatus(final Delivery delivery) {

        final String qrBarCode = delivery.getDetail().getQrBarCode();
        PickupVerificationStatus pickupVerificationStatus = PickupVerificationStatus.UNVERIFIED;

        if (StringUtils.isBlank(qrBarCode)) {
            pickupVerificationStatus = PickupVerificationStatus.NA;
        }

        return pickupVerificationStatus;
    }

    /**
     * 두 기사의 배차를 서로 바꾼다
     *
     * @param projectId
     * @param riderId1
     * @param riderId2
     */
    @Transactional
    public void switchRiderClustering(Long projectId,
                                      Long riderId1,
                                      Long riderId2) {

        final List<DeliveryAllocation> deliveryAllocationList1 = deliveryAllocationRepository.findByProjectIdAndRiderIdOrderByOrderNumAsc(projectId, riderId1);
        final List<DeliveryAllocation> deliveryAllocationList2 = deliveryAllocationRepository.findByProjectIdAndRiderIdOrderByOrderNumAsc(projectId, riderId2);

        if (CollectionUtils.isNotEmpty(deliveryAllocationList1)) {
            deliveryAllocationList1.forEach(deliveryAllocation -> deliveryAllocation.setRiderId(riderId2));
        }
        if (CollectionUtils.isNotEmpty(deliveryAllocationList2)) {
            deliveryAllocationList2.forEach(deliveryAllocation -> deliveryAllocation.setRiderId(riderId1));
        }
    }

    /**
     * 가배차 기사의 배송을 실배차 기사에게 전달함.
     *
     * @param projectId
     * @param simulationRiderId
     * @param dispatchRiderId
     */
    @Transactional
    public void switchSimulationRiderToDispatchRider(Long projectId,
                                                     Long simulationRiderId,
                                                     Long dispatchRiderId) {

        final List<DeliveryAllocation> deliveryAllocationList = deliveryAllocationRepository.findByProjectIdAndRiderIdOrderByOrderNumAsc(projectId, simulationRiderId);

        if (CollectionUtils.isNotEmpty(deliveryAllocationList)) {
            deliveryAllocationList.forEach(deliveryAllocation -> deliveryAllocation.setRiderId(dispatchRiderId));
            deliveryAllocationRepository.saveAll(deliveryAllocationList);
        }
    }


    /**
     * 방문지 목록에 포함된 각 방문지 타입별 개수 계산
     *
     * @param deliveries
     * @param deliveryType
     * @return
     */
    @Deprecated
    public static long getCountByDeliveryType(final List<Delivery> deliveries,
                                              final DeliveryType deliveryType) {

        if (CollectionUtils.isEmpty(deliveries)) {
            return 0L;
        }

        return deliveries.stream()
                .filter(d -> d.getType().equals(deliveryType))
                .count();
    }

    /**
     * 픽업장소의 갯수를 구한다
     *
     * @param deliveries
     * @param pickupDeliveryType
     * @return
     */
    public static int getCountPickUpPlaces(final List<Delivery> deliveries,
                                           final DeliveryType pickupDeliveryType) {

        return findPickupPlacesInDeliveries(deliveries, pickupDeliveryType).size();
    }

    /**
     * PickupPlace Delivery 찾기
     *
     * @param deliveries
     * @param pickupDeliveryType
     * @return
     */
    public static List<Delivery> findPickupPlacesInDeliveries(final List<Delivery> deliveries,
                                                              final DeliveryType pickupDeliveryType) {

        if (CollectionUtils.isEmpty(deliveries) || Objects.isNull(pickupDeliveryType)) {
            return new ArrayList<>();
        }

        List<Delivery> pickupPlaces = deliveries.stream()
                .filter(d -> d.getType() != null && d.getType().equals(pickupDeliveryType))
                .collect(Collectors.toList());

        if (pickupPlaces.isEmpty()) {//pickupDeliveryType 으로 찾지 못하면 거점,물류센터 모두 픽업 장소가 될수 있다.
            pickupPlaces = findPickupPlacesInDeliveries(deliveries);
        }

        return pickupPlaces;
    }

    /**
     * PickupPlace Delivery 찾기 , 물류선터는 무조건 픽업 장소가 된다.
     *
     * @param deliveries
     * @return
     */
    public static List<Delivery> findPickupPlacesInDeliveries(final List<Delivery> deliveries) {

        if (CollectionUtils.isEmpty(deliveries)) {
            return new ArrayList<>();
        }

        return deliveries.stream()
                .filter(d -> (Objects.nonNull(d.getType()) && d.getType().equals(DeliveryType.HUB)  /* pickupDeliveryType */))
                .collect(Collectors.toList());
    }

    /**
     * 배차 설정
     *
     * @param delivery
     * @param projectId
     * @param riderId
     * @param isPickupPlace
     */
    public DeliveryAllocation setDeliveryAllocation(final Delivery delivery,
                                                    final Long projectId,
                                                    final Long riderId,
                                                    final boolean isPickupPlace) {

        final InspectionStatus inspectionStatus = setDefaultInspectionStatus(delivery);
        final PickupVerificationStatus pickupVerificationStatus = setDefaultPickupVerificationStatus(delivery);

        final DeliveryAllocation newAllocation = DeliveryAllocation.builder()
                .delivery(delivery)
                .projectId(projectId)
                .riderId(riderId)
                .groupName(delivery.getGroupName())
                .orderAmount(delivery.getDetail().getOrderAmount())
                .isPickupPlace(isPickupPlace)
                .status(DeliveryStatus.READY)
                .inspectionStatus(inspectionStatus)
                .pickupVerificationStatus(pickupVerificationStatus)
                .build();

        final List<DeliveryAllocation> allocations = delivery.getAllocations();

        // 배차 고려사항
        // 1. pickup 할당 : riderId 조회해서 없으면 추가 (다른 기사들 할당 유지)
        // 2. 배송지 할당 : riderId 조회해서 없으면 추가 (다른 기사들 할당 삭제)

        final boolean notExistAllocation = allocations.stream()
                .noneMatch(allocation -> allocation.getRiderId().longValue() == newAllocation.getRiderId().longValue());

        if (notExistAllocation) {
            if (isPickupPlace) {
                allocations.add(newAllocation);
            } else {
                final DeliveryAllocation foundAllocation = allocations.stream()
                        .filter(allocation -> !allocation.getIsPickupPlace())
                        .findAny()
                        .orElse(null);

                if (Objects.isNull(foundAllocation)) {
                    allocations.add(newAllocation);
                } else {
                    foundAllocation.setRiderId(riderId);
                    return foundAllocation;
                }
            }
        }

        return newAllocation;
    }

    /**
     * 강제로 특정 배송지를 특정 기사에 할당한다
     *
     * @param deliveryId
     * @param projectId
     * @param riderId
     * @return
     */
    @Transactional
    public DeliveryAllocation setDeliveryAllocationManually(final Long deliveryId,
                                                            final Long projectId,
                                                            final Long riderId) {

        log.info("[setDeliveryAllocationManually] 배송지를 기사에게 할당 deliveryId: {}, projectId: {}, riderId: {}", deliveryId, projectId, riderId);
        final Delivery delivery = deliveryRepository.findById(deliveryId).orElseThrow(() -> new DeliveryNotFoundException(deliveryId));
        final List<DeliveryAllocation> allocations = delivery.getAllocations();
        allocations.clear();
        final DeliveryAllocation deliveryAllocation = setDeliveryAllocation(delivery, projectId, riderId, false);
        deliveryAllocationRepository.save(deliveryAllocation);
        allocations.add(deliveryAllocation);

        //상품 할당하기
        if (this.hasDeliveryProducts(delivery)) {
            this.changeClusterDeliveryProductAllocation(null, riderId, delivery);
        }

        deliveryRepository.save(delivery);

        return deliveryAllocation;
    }

    @Transactional
    public List<Long> deleteDeliveryIdsInfo(final List<Long> deliveries, final Long userId) {

        List<Delivery> deliveryList = deliveryRepository.findByIdIn(deliveries);
        List<Long> updateRiderId = new ArrayList<>();

        deliveryList.forEach(delivery -> {
            final Long riderId = this.getRiderIdByDelivery(delivery);
            if (Objects.nonNull(riderId)) {
                updateRiderId.add(riderId);
            }

            this.deleteDeliveryInfo(delivery, userId);
        });

        // 다른 사용자들에 대한 status 변경을 위해서 사용함.
        if (CollectionUtils.isNotEmpty(deliveryList)) {
            pushService.sendReloadProjectMessageToWeb(userId, deliveryList.get(0).getProjectId(), "프로젝트 상태가 변경되었습니다.");
        }

        return new ArrayList<>(new HashSet<>(updateRiderId)); //중복 제거
    }

    /**
     * 방문지 삭제 외에 notification 수행및 경로/배차 수행
     *
     * @param delivery
     * @return
     */
    @Transactional
    public Long deleteDeliveryInfo(final Delivery delivery, final Long userId) {

        final Long deliveryUserId = delivery.getUserId();
        final Long projectId = delivery.getProjectId();
        final Project project = projectBasicService.getProjectById(projectId);

        //배송 완료 시간 지날시 - 완료 되어도 삭제가 가능하므로 이부분은 우선 없애자
//        if (project.getCutoffTime() != null && LocalDateTime.now().isAfter(project.getCutoffTime())) {
//            throw new CustomException(HttpStatus.NOT_ACCEPTABLE, "배송지 마감 시간이 지났습니다. (" + project.getCutoffTime() + ")", false);
//        }

        if (CollectionUtils.isNotEmpty(delivery.getAllocations())) {
            DeliveryAllocation allocation = delivery.getAllocations().get(0);
            if (!DeliveryStatus.WAITING.equals(allocation.getStatus()) && !DeliveryStatus.READY.equals(allocation.getStatus())) { //배송전을 제외하고 삭제할수 없다
                throw new CustomException(HttpStatus.NOT_ACCEPTABLE, "배송중/배송완료된 배송지는 삭제할 수 없습니다. 현재 배송 상태는 " + allocation.getStatus() + " 입니다.", false);
            }
        }

        if (ProjectStatus.DONE.equals(project.getStatus())) {
            throw new CustomException(HttpStatus.NOT_ACCEPTABLE, "프로젝트가 이미 종료되었습니다", false);
        }

        final Long deliveryId = delivery.getId();
        final String customerOrderId = delivery.getDetail().getCustomerOrderId();
        final Address address = delivery.getDetail().getDestinationAddress();
        final String parsedAddr = AddressParseUtil.getParsedKorBaseAddress(address.getBase() + (StringUtils.isNotBlank(address.getDetail()) ? " " + address.getDetail() : ""));

        //배송지 삭제시에 배송 물품 취소
        if (this.hasDeliveryProducts(delivery)) {
            if (CollectionUtils.isNotEmpty(delivery.getAllocations())) {
                DeliveryAllocation allocation = delivery.getAllocations().get(0);
                this.changeClusterDeliveryProductAllocation(allocation.getRiderId(), null, delivery);
            }
        }

        deliveryBasicService.deleteSingleDelivery(delivery, userId);

        final User user = userService.getUser(deliveryUserId).orElse(null);
        final String userName = Objects.nonNull(user) ? user.getName() : "";
        final String timeDeleted = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")); // LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE)
        final String projectName = project.getName();
        final String description = "- 주소 : " + parsedAddr + "\n" + "- 관리자 : " + userName + "\n" + "- 날짜 : " + timeDeleted + "\n" + "- 프로젝트명 : " + projectName;
        final String displayId = Objects.nonNull(customerOrderId) ? CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight() : String.valueOf(deliveryId);

        // 방문지 삭제시 notification
        notificationService.notificationOccurDeleteDestination(projectId, displayId, description, deliveryId);
        pushService.sendNotificationToWeb(deliveryUserId, projectId, 1L);

        return delivery.getId();
    }

    public Long getRiderIdByDelivery(final Delivery delivery) {

        Long riderId = null;

        if (Objects.nonNull(delivery)) {
            final List<DeliveryAllocation> deliveryAllocations = delivery.getAllocations();
            if (CollectionUtils.isNotEmpty(deliveryAllocations)) {
                riderId = Optional.ofNullable(deliveryAllocations.get(0)).map(DeliveryAllocation::getRiderId).orElse(null);
            }
        }

        return riderId;
    }

    @Transactional// Transactional 롤백이 동작하지 않는다 (rollbackFor = {Exception.class})
    public Delivery updateOnDemandDeliveryWithOrderItemList(Long userId,
                                                            Long projectId,
                                                            DeliveryDTO deliveryDto,
                                                            Address address) {

        Delivery delivery = null;
        try {
            delivery = this.getDeliveryByDeliverDTO(userId, projectId, deliveryDto, address, null);
            final List<OrderItemDTO> orderItemList = deliveryDto.getOrderItemList();

            this.setDeliveryProductsByOrderItem(orderItemList, delivery);
            return deliveryBasicService.saveDelivery(delivery);
        } catch (Exception e) {
            if (Objects.nonNull(delivery)) {
                deliveryBasicService.deleteSingleDelivery(delivery, null);//Exception이 발생 하였는데 롤백이 안된다. 그래서 강제적으로 delete처리한다
            }
            throw e;
        }
    }

    /**
     * 배송지에 물품이 있는지 체크함
     *
     * @param delivery
     * @return
     */
    public boolean hasDeliveryProducts(final Delivery delivery) {

        return Objects.nonNull(delivery) && CollectionUtils.isNotEmpty(delivery.getDeliveryProducts());
    }

    public boolean hasDeliveryProductsByDeliveryId(final Long deliveryId) {

        final Delivery delivery = deliveryBasicService.getDeliveryInfo(deliveryId);
        return Objects.nonNull(delivery) && CollectionUtils.isNotEmpty(delivery.getDeliveryProducts());
    }

    @Transactional
    public DeliveryVocMessage addDeliveryVocMessage(final Delivery delivery,
                                                    final String userName,
                                                    final VOCStatus vocStatus,
                                                    final String vocMessages) {

        DeliveryVocMessage dv = DeliveryVocMessage.builder()
                .delivery(delivery)
                .userName(userName)
                .isDeleted(false)
                .vocStatus(vocStatus)
                .deliveryVocMessage(vocMessages)
                .build();

        deliveryVocMessageRepository.save(dv);

        return dv;
    }

    public DeliveryVocMessageDTO getDeliveryVOCMessages(final Long deliveryId,
                                                        final VOCStatus vocStatus) {

        if (vocStatus != null) {
            //상태에 맞는 조건의 voc만 찾아서 넘겨줌
            Optional<DeliveryVocMessage> deliveryVocMessage = deliveryVocMessageRepository.findFirstByDeliveryIdAndVocStatusOrderByCreateAtDesc(deliveryId, vocStatus);
            if (deliveryVocMessage.isPresent()) {
                return DeliveryVocMessageDTO.of(deliveryVocMessage.get());
            } else
                return null;
        } else {
            //최신 한개만 가져오게 함
            Optional<DeliveryVocMessage> deliveryVocMessage = deliveryVocMessageRepository.findFirstByDeliveryIdOrderByCreateAtDesc(deliveryId);
            if (deliveryVocMessage.isPresent()) {
                return DeliveryVocMessageDTO.of(deliveryVocMessage.get());
            } else
                return null;
        }

//한꺼번에 모든 VOC 메시지를 String
//        List<DeliveryVocMessage> deliveryVocMessageList = deliveryVocMessageRepository.findByDeliveryId(deliveryId);
//
//        if (CollectionUtils.isNotEmpty(deliveryVocMessageList)) {
//            final String vocMessages = deliveryVocMessageList.stream()
//                    .map(dv -> this.getDeliveryVOCMessageString(dv.getUserName(), dv.getDeliveryVocMessage()))
//                    .reduce("", String::concat);
//
//            return vocMessages;
//        } else
//            return null;
    }

    public String getDeliveryVOCMessageString(String userName,
                                              String deliveryVocMessage) {

        return userName + ":" + deliveryVocMessage + "\n";
    }

    /**
     * 근무 종료 배송지 삭제
     *
     * @param projectDeliveries
     * @param userId
     * @return
     */
    public List<ProjectDelivery> removeReturnPlaceDelivery(List<ProjectDelivery> projectDeliveries,
                                                           Long userId) {

        projectDeliveries = projectDeliveries.stream()
                .filter(projectDelivery -> {
                    Delivery delivery = projectDelivery.getDelivery();
                    if (DeliveryType.RETURN_DESTINATION.equals(delivery.getType())) {
                        deliveryBasicService.deleteSingleDelivery(delivery, userId);
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());

        return projectDeliveries;
    }

    /**
     * 같은 소속의 사용자목록 생성
     *
     * @param userId
     * @return userIdList
     */
    public Set<Long> getSameOrganizationUserIdList(@NotNull final Long userId) {

        if (userService == null) {
            throw new CustomException(HttpStatus.PARTIAL_CONTENT, "사용자 정보가 없습니다.");
        }

        final User user = userService.getUser(userId).orElseThrow(() -> new CustomException(HttpStatus.PARTIAL_CONTENT, "사용자 정보가 없습니다."));
        final RoleType roleType = userService.getUserRoleByUser(user);
        final Set<Long> userIdList;
        if (RoleType.ROLE_ADMIN.equals(roleType)) {
            userIdList = null;
        } else if (RoleType.ROLE_ORG_ADMIN.equals(roleType) || RoleType.ROLE_COMMON.equals(roleType)) {
            final Long orgId = user.getOrganizationId();
            final List<User> userList = userService.getUserByOrganization(orgId);
            userIdList = userList.stream().map(User::getUserId).collect(Collectors.toSet());
        } else {
            userIdList = Stream.of(userId).collect(Collectors.toSet());
        }

        return userIdList;
    }

    /**
     * orgId로 가지고 같은 소속의 사용자목록 생성
     *
     * @param orgId
     * @return userIdList
     */
    public Set<Long> getSameOrganizationIdList(@NotNull final Long orgId) {

        if (userService == null) {
            throw new CustomException(HttpStatus.PARTIAL_CONTENT, "사용자 정보가 없습니다.");
        }

        final Set<Long> userIdList;
        final List<User> userList = userService.getUserByOrganization(orgId);
        userIdList = userList.stream().map(User::getUserId).collect(Collectors.toSet());

        return userIdList;
    }

    /**
     * delivery_glovis 테이블에 저장
     *
     * @param delivery
     * @param orderInvoiceInfoDTO
     * @return
     */
    @Transactional
    public DeliveryGlovis saveDeliveryGlovis(final Delivery delivery,
                                             final OrderInvoiceInfoDTO orderInvoiceInfoDTO) {

        if (Objects.nonNull(delivery) && Objects.nonNull(orderInvoiceInfoDTO)) {
            final DeliveryGlovis deliveryGlovis = DeliveryGlovis.builder()
                    .delivery(delivery)
                    .invoiceNumber(orderInvoiceInfoDTO.getInvoiceNumber())
                    .boxTypeCode(orderInvoiceInfoDTO.getBoxTypeCode())
                    .boxQty(orderInvoiceInfoDTO.getBoxQty())
                    .receiverName(orderInvoiceInfoDTO.getReceiverName())
                    .receiverTelephoneNo1(orderInvoiceInfoDTO.getReceiverTelephoneNo1())
                    .receiverTelephoneNo2(orderInvoiceInfoDTO.getReceiverTelephoneNo2())
                    .receiverTelephoneNo3(orderInvoiceInfoDTO.getReceiverTelephoneNo3())
                    .receiverCellphoneNo1(orderInvoiceInfoDTO.getReceiverCellphoneNo1())
                    .receiverCellphoneNo2(orderInvoiceInfoDTO.getReceiverCellphoneNo2())
                    .receiverCellphoneNo3(orderInvoiceInfoDTO.getReceiverCellphoneNo3())
                    .receiverZipCode(orderInvoiceInfoDTO.getReceiverZipCode())
                    .receiverAddress(orderInvoiceInfoDTO.getReceiverAddress())
                    .receiverAddressDetail(orderInvoiceInfoDTO.getReceiverAddressDetail())
                    .remark1(orderInvoiceInfoDTO.getRemark1())
                    .skuDescription(orderInvoiceInfoDTO.getSkuDescription())
                    .build();

            return deliveryGlovisRepository.save(deliveryGlovis);
        } else {
            return null;
        }
    }

    /**
     * 주문송장번호(invoiceNumber)로 DeliveryGlovis 조회
     *
     * @param invoiceNumber
     * @return
     */
    public DeliveryGlovis getDeliveryGlovisByInvoiceNumber(final String invoiceNumber) {

        return deliveryGlovisRepository.findFirstByInvoiceNumberOrderByUpdateAtDesc(invoiceNumber);
    }

    /**
     * 수화인 우편번호(receiverZipCode)로 DeliveryGlovis List 조회
     *
     * @param zipCode
     * @return
     */
    public List<DeliveryGlovis> getDeliveryGlovisListByReceiverZipCode(final String zipCode) {

        return deliveryGlovisRepository.findByReceiverZipCodeOrderByUpdateAtDesc(zipCode);
    }

    public Boolean isSplitDelivery(Delivery delivery) {

        return (delivery.getSplitNumber() != null)
                && (delivery.getSplitNumber().equals(FIRST_SPLIT_NUMBER) || delivery.getSplitNumber().equals(SECOND_SPLIT_NUMBER));
    }

    /**
     * 다른 분할 배송지 찾기
     *
     * @param delivery
     * @return
     */
    public Delivery getOtherSplitDelivery(Delivery delivery) {

        Delivery otherSplitDelivery = null;

        if (this.isSplitDelivery(delivery)) {
            final String customerOrderId = Optional.ofNullable(delivery.getDetail().getCustomerOrderId()).orElseThrow(() -> new ItemNotFoundException("분할된 배송지를 찾을수 없습니다. (" + delivery.getId() + ")"));
            final Integer splitNumber = Optional.ofNullable(delivery.getSplitNumber()).orElseThrow(() -> new ItemNotFoundException("분할된 배송지를 찾을수 없습니다. (" + delivery.getId() + ")"));
            Integer findSplitNumber = null;
            if (splitNumber.equals(FIRST_SPLIT_NUMBER)) {
                findSplitNumber = SECOND_SPLIT_NUMBER;
            } else if (splitNumber.equals(SECOND_SPLIT_NUMBER)) {
                findSplitNumber = FIRST_SPLIT_NUMBER;
            }
            List<Delivery> splitDeliveries = null;
            if (findSplitNumber != null) {
                splitDeliveries = deliveryRepository.findByDetailCustomerOrderIdAndSplitNumberAndDeletedOrderByUpdateAtDesc(customerOrderId, findSplitNumber, false);
            }

            if (CollectionUtils.isEmpty(splitDeliveries)) {
                throw new ItemNotFoundException("분할된 배송지를 찾을수 없습니다. (" + delivery.getId() + ")");
            }

            otherSplitDelivery = splitDeliveries.get(0);

        }

        return otherSplitDelivery;
    }

    /**
     * 경로 생성 없이 기사들에게 전송시 orderNum 자동으로 셋팅하게 함.
     *
     * @param projectId
     * @return
     */
    @Transactional
    public void setAutoOrderNumIndexDeliveryAllocation(Long projectId) {
        //OderNum가 없는 프로젝트의 deliveryAllocationList를 반환 받는다.
        List<DeliveryAllocation> deliveryAllocationList = deliveryBasicService.getDeliveryAllocationListByProjectIdAndOrderNumIsNull(projectId);

        
        if (CollectionUtils.isNotEmpty(deliveryAllocationList)) {
            // 기사별로 DeliveryAllocation 리스트를 그룹화
            Map<Long, List<DeliveryAllocation>> riderAllocations = deliveryAllocationList.stream()
                    .collect(Collectors.groupingBy(DeliveryAllocation::getRiderId));

            // 각 기사별로 처리
            riderAllocations.forEach((riderId, allocations) -> {
                // 각 기사의 배송지에 대해 orderNum 설정
                this.setRiderAutoOrderNumIndex(allocations);
                // 같은 좌표를 가진 배송지들의 orderNum 보정
                correctOrderNumForSameCoordinates(allocations);
            });
        }
    }

    /**
     * 경로 생성 없이 기사별에게 전송시 orderNum 자동으로 셋팅하게 함.
     *
     * @param deliveryAllocations
     * @return
     */
    public void setRiderAutoOrderNumIndex(List<DeliveryAllocation> deliveryAllocations) {

        int index = 1;
        for (DeliveryAllocation da : deliveryAllocations) {
            if (Objects.isNull(da.getOrderNum())) {
                //로그 출력
                log.info("[setRiderAutoOrderNumIndex] deliveryId: {}, orderNum: {}", da.getDelivery().getId(), index);
                da.setOrderNum(index);
                index++;
            }
        }
    }

    private Integer getOrderNumByDelivery(final Delivery delivery) {

        if (Objects.nonNull(delivery) && CollectionUtils.isNotEmpty(delivery.getAllocations())) {
            return delivery.getAllocations().get(0).getOrderNum();
        } else {
            return null;
        }
    }

    /**
     * 배송중(GOING)인 배송지가 배송순서가 유효한지 체크함. 첫번째 배송지가 아니면 출발/서비스 순서를 막아야함
     * 관제웹에서 순서를 바꾸고 동기화 안된 상태에서 모바일앱에서 배송중 상태를 바뀌는 케이스를 막기위해서이다
     *
     * @param projectId
     * @param riderId
     * @param deliveryId
     * @return
     */
    public boolean isFirstNotEndedDelivery(final Long projectId,
                                           final Long riderId,
                                           final Long deliveryId) {

        final List<Delivery> notEndedDeliveries = deliveryBasicService.getDeliveriesByProjectIdAndRiderIdAndStatusIn(projectId, riderId, DeliveryStatus.notEndedDeliveryStatusList).stream()
                .sorted(Comparator.comparing(this::getOrderNumByDelivery, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(notEndedDeliveries)) {
            return false;
        } else {
            return Objects.equals(notEndedDeliveries.get(0).getId(), deliveryId);
        }
    }

    /**
     * 검수완료 취소
     *
     * @param projectId
     * @param riderId
     */
    @Transactional
    public void cancelInspectionEnd(final Long projectId,
                                    final Long riderId) {

        List<DeliveryAllocation> deliveryAllocations = deliveryAllocationRepository.findByProjectIdAndRiderIdAndIsInspectionEnded(projectId, riderId, true);

        if (CollectionUtils.isNotEmpty(deliveryAllocations)) {
            deliveryAllocations.forEach(da -> {
                log.info("[cancelInspectionEnd] 검수상태를 취소합니다. deliveryId: {}, deliveryAllocationId: {}, projectId: {}, riderId: {}", da.getDelivery().getId(), da.getId(), projectId, riderId);
                deliveryStatusHistoryService.deleteDeliveryStatusHistory(da, DeliveryStatus.UNDELIVERED);
                da.setIsInspectionEnded(false);
                if (DeliveryStatus.UNDELIVERED.equals(da.getStatus())) {
                    da.setStatus(DeliveryStatus.READY);
                }
            });

            deliveryAllocationRepository.saveAll(deliveryAllocations);
        }
    }

    /**
     * 배송지를 새로운 프로젝트에 이관함.
     *
     * @param riderId
     * @param preProjectId
     * @param newProjectId
     */
    @Transactional
    public void changedProjectIdDeliveries(@NotNull final Long riderId,
                                           @NotNull final Long preProjectId,
                                           @NotNull final Long newProjectId) {

        final List<Delivery> deliveryList = this.getAllDeliveries(preProjectId, null, null, null, null, riderId);

        deliveryList.forEach(delivery -> {
            delivery.setProjectId(newProjectId);
            List<DeliveryAllocation> allocations = delivery.getAllocations();
            if (allocations.size() > 0) {
                allocations.get(0).setProjectId(newProjectId);
                //rout에 대한 정보를 삭제 해야함.
//                        routeService.initializeRoutePlan(allocations.get(0));
//                        routeService.initializeRouteAppPlan(allocations.get(0));
//                        routeService.initializeRouteRun(allocations.get(0));
            }
        });

        deliveryBasicService.saveDeliveries(deliveryList);
    }

    /**
     * 기사에게 할당된 배송 정보를 미할당으로 변경
     *
     * @param projectId
     * @param deliveryId
     */
    @Transactional
    public void changeToUnallocatedDelivery(@NotNull final Long projectId,
                                            @NotNull final Long deliveryId) {

        final Delivery delivery = deliveryRepository.findByProjectIdAndId(projectId, deliveryId).orElse(null);
        if (Objects.nonNull(delivery)) {
            delivery.setAllocations(new ArrayList<>());
            deliveryRepository.save(delivery);
        }

        final List<DeliveryAllocation> deliveryAllocations = deliveryAllocationRepository.findByProjectIdAndDeliveryId(projectId, deliveryId);
        if (CollectionUtils.isNotEmpty(deliveryAllocations)) {
            log.info("[changeToUnallocatedDelivery] 배차취소 projectId: {}, deliveryId: {}", projectId, deliveryId);
            deliveryAllocationRepository.deleteAll(deliveryAllocations);
        }
    }

    /**
     * orderNum이 비었을 때 마지막 orderNum으로 설정
     *
     * @param deliveryId
     * @param projectId
     * @param riderId
     */
    @Transactional
    public void setOrderNumWhenEmpty(@NotNull final Long deliveryId,
                                     @NotNull final Long projectId,
                                     @NotNull final Long riderId) {

        DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocation(deliveryId).orElse(null);

        if (Objects.nonNull(deliveryAllocation) && Objects.isNull(deliveryAllocation.getOrderNum())) {
            final List<DeliveryAllocation> allocations = deliveryBasicService.getRiderDeliveryList(projectId, null, riderId); // order by order_num

            if (CollectionUtils.isNotEmpty(allocations)) {
                final Integer lastOrderNum = allocations.get(allocations.size() - 1).getOrderNum();
                log.info("[setOrderNumWhenEmpty] deliveryId: {}, projectId: {}, riderId: {}, lastOrderNum: {}", deliveryId, projectId, riderId, lastOrderNum);

                if (Objects.nonNull(lastOrderNum)) {
                    deliveryAllocation.setOrderNum(lastOrderNum + 1);
                } else {
                    deliveryAllocation.setOrderNum(1);
                }
                deliveryAllocationRepository.save(deliveryAllocation);
            }
        }
    }

    /**
     * 기사에게 할당된 정보를 삭제하는 기능
     * 할당된 배송지를 미할당된 배송지로 변경함.
     *
     * @param projectId
     * @param deliveryId
     */
    @Transactional
    public void changedNotAssignmentDelivery(@NotNull final Long projectId,
                                             @NotNull final Long deliveryId) {

        final Delivery delivery = deliveryRepository.findByProjectIdAndId(projectId, deliveryId).orElse(null);
        if (Objects.nonNull(delivery)) {
            delivery.setAllocations(new ArrayList<>());
            deliveryRepository.save(delivery);
        }

        final List<DeliveryAllocation> deliveryAllocations = deliveryAllocationRepository.findByProjectIdAndDeliveryId(projectId, deliveryId);
        if (CollectionUtils.isNotEmpty(deliveryAllocations)) {
            deliveryAllocationRepository.deleteAll(deliveryAllocations);
        }
    }

    public List<DeliveryStatusHistory> getAllDeliveryStatusHistoryByCustomerOrderId(@NotNull final String customerOrderId) {

        List<DeliveryStatusHistory> results = new ArrayList<>();
        Delivery delivery = Optional.ofNullable(deliveryBasicService.getDeliveryByCustomerOrderIdOrderByUpdateAtDesc(customerOrderId)).orElse(null);
//        Delivery delivery = Optional.ofNullable(deliveryBasicService.getDeliveryByCustomerOrderId(customerOrderId)).orElse(null);
        if (Objects.isNull(delivery)) {
            throw new DeliveryNotFoundException("해당 배송지를 찾을 수 없습니다. (" + customerOrderId + ")");
        }

        if (CollectionUtils.isNotEmpty(delivery.getAllocations())) {
            DeliveryAllocation allocation = delivery.getAllocations().get(0);
            results = deliveryStatusHistoryService.getAllDeliveryStatusHistory(allocation);
        }

        return results;
    }

    public List<DeliveryDTO> getDeliveriesNotCluster(Long projectId) {

        // 배송 목록 조회
        List<Delivery> deliveryList = deliveryBasicService.getDeliveriesByProjectId(projectId);
        List<DeliveryDTO> notClusteredList = deliveryList.stream()
                .filter(d -> Boolean.FALSE.equals(deliveryBasicService.getDeliveryAllocation(d.getId()).isPresent()))
                .map(delivery -> new DeliveryDTO(delivery, null, null))
                .collect(Collectors.toList());

        return notClusteredList;
    }

    /**
     * 바코드 스캔 배차
     *
     * @param qrBarCode
     * @param projectId
     * @param riderId
     * @param deliveryIds
     * @param deliveryRiderOrderDto
     * @return
     */
    @Transactional
    public List<Long> dispatchByBarcode(final String qrBarCode,
                                        final Long projectId,
                                        final Long riderId,
                                        List<Long> deliveryIds,
                                        DeliveryRiderOrderDTO deliveryRiderOrderDto) {

        if (StringUtils.isBlank(qrBarCode)) {
            return deliveryIds;
        }

        final int qrBarCodeLength = qrBarCode.length();
        final String orgCodeName = organizationService.getOrganizationCodeByProjectId(projectId);
        Long previouslyAllocatedRiderId = null;
        Delivery delivery = null;

        if (TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME.equals(orgCodeName)) {
            String customerOrderCode = "";
            try {
                customerOrderCode = CustomerUtil.appendOrganizationCode(orgCodeName, qrBarCode.substring(0, qrBarCodeLength - 2));
            } catch (Exception e) {
                log.error("[dispatchByBarcode] Exception: " + e.getMessage());
                throw new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, "실행 오류입니다.", e, false);
            }
            delivery = deliveryRepository.findTop1ByDetailCustomerOrderIdAndProjectIdAndDeletedOrderByUpdateAtDesc(customerOrderCode, projectId, false);
        } else {
            delivery = deliveryBasicService.getDeliveryByQrBarCodeAndProjectIdAndDeleted(qrBarCode, projectId, false);
        }
        if (Objects.isNull(delivery)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "해당하는 배송이 없습니다.", false);
        }

        if (TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME.equals(orgCodeName)) {
            // 전표번호 묶음수 초과한 바코드의 스캔 배차 제한
            try {
                final int bundleNo = Integer.parseInt(qrBarCode.substring(qrBarCodeLength - 2, qrBarCodeLength));
                if (bundleNo > delivery.getDetail().getProductQuantity().intValue()) {
                    throw new CustomException(HttpStatus.BAD_REQUEST, "묶음수와 바코드가 맞지 않습니다.", false);
                }
            } catch (Exception e) {
                log.error("[dispatchByBarcode] Exception: " + e.getMessage());
                throw new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, "실행 오류입니다.", e, false);
            }
        }

        // 프로젝트에 속한 QR/바코드인지 확인
        if (!Objects.equals(projectId, delivery.getProjectId())) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "프로젝트에 속한 QR/바코드가 아닙니다.", false);
        }

        if (CollectionUtils.isEmpty(deliveryIds)) {
            deliveryIds = new ArrayList<>();
        }
        deliveryIds.clear();
        deliveryIds.add(delivery.getId());

        // 이미 바코드 스캔 배차된 경우 동일 기사 또는 다른 기사가 바코드 중복 스캔시 예외처리
        // 미배차목록에서 수동배차시 Transaction끼리의 충돌이 발생할 것을 예상하여 우선적으로 Lock을 건다.
        if (CollectionUtils.isNotEmpty(deliveryIds)) {
            log.info("[dispatchByBarcode] 바코드 스캔 배차 deliveryId: {}, projectId: {}, riderId: {}", deliveryIds.get(0), projectId, riderId);
            final Optional<DeliveryAllocation> deliveryAllocation = deliveryBasicService.getLockedDeliveryAllocation(deliveryIds.get(0));
            if (deliveryAllocation.isPresent()) {
                previouslyAllocatedRiderId = deliveryAllocation.map(DeliveryAllocation::getRiderId).orElse(null);
                if (Objects.nonNull(riderId)) {
                    final String riderName = riderService.getRider(previouslyAllocatedRiderId).getName();
                    throw new CustomException(HttpStatus.BAD_REQUEST, riderName + "에게 이미 배차되었습니다.", false);
                }
            }
        } else {
            throw new CustomException(HttpStatus.BAD_REQUEST, "배송 ID 목록이 비었습니다.", false);
        }

        projectBasicService.updateFirstRoutingDone(projectId, Boolean.TRUE);

        return deliveryIds;
    }

    /**
     * 배송지를 특정 기사에게 할당
     *
     * @param projectId
     * @param riderId
     * @param deliveryIds
     * @param deliveryRiderOrderDto
     */
    public void allocateDeliveryToRider(final Long projectId,
                                        final Long riderId,
                                        List<Long> deliveryIds,
                                        DeliveryRiderOrderDTO deliveryRiderOrderDto) {

        if (CollectionUtils.isEmpty(deliveryIds)) {
            return;
        }

        //배차하기전에 1개라도 배차할 물품이 부족하면 에러 처리함
        deliveryIds.forEach(deliveryId -> {
            if (this.hasDeliveryProductsByDeliveryId(deliveryId)) {
                boolean isAvailableProduct = productService.checkDeliveryAvailability(deliveryId, riderId);//웬지 배송지에 물품정보가 없으면 true를 리넡해야 하는데 이상하게 false를 리턴하내.
                if (!isAvailableProduct) {
                    throw new ItemNotFoundException("배달할 물품이 부족하여 기사님께 배차를 할 수 없습니다.");
                }
            }
        });

        deliveryIds.forEach(aLong -> {
            List<DeliveryAllocation> allocations = deliveryBasicService.getDeliveryAllocations(aLong);
            if (CollectionUtils.isNotEmpty(allocations)) {//이미 배차되어있을때
                if (Objects.isNull(riderId)) {
                    // riderId가 null이면 이미 배차된 배송을 미배차로 변경
                    this.changeToUnallocatedDelivery(projectId, aLong);
                } else {
                    this.changeDeliveryCluster(aLong, deliveryRiderOrderDto);
                    this.setOrderNumWhenEmpty(aLong, projectId, riderId);
                }
            } else {//배차가 되지 않은 경우
                if (Objects.nonNull(projectId) && Objects.nonNull(riderId)) {
                    log.info("[allocateDeliveryToRider] 배차 deliveryId: {}, projectId: {}, riderId: {}", aLong, projectId, riderId);
                    final DeliveryAllocation deliveryAllocation = this.setDeliveryAllocationManually(aLong, projectId, riderId);
                    this.setOrderNumWhenEmpty(aLong, projectId, riderId);

                    //READY HISTORY 추가
                    if (Objects.nonNull(deliveryAllocation)) {
                        deliveryStatusHistoryService.insertDeliveryStatusHistory(deliveryAllocation, DeliveryStatus.READY, false);
                    }
                }
            }
        });
    }

    /**
     * 개인정보 파기
     *
     * @param fromDt
     * @param toDt
     */
        @Transactional
    public void destroyPersonalInfo(final LocalDateTime fromDt,
                                    final LocalDateTime toDt) {

        final List<Delivery> deliveries = deliveryRepository.findAllByCreateAtBetweenAndDeletedIsFalseAndPrivacyDisposedIsFalse(fromDt, toDt);
        
        // 최대 처리 건수 제한
        final List<Delivery> limitedDeliveries = deliveries.stream()
                .limit(MAX_PERSONAL_INFO_DESTRUCTION_RECORDS)
                .collect(Collectors.toList());

        for (final Delivery delivery : limitedDeliveries) {
            deliveryBasicService.destroyPersonalInfoByDelivery(delivery);
            delivery.setPrivacyDisposed(true);
            deliveryRepository.save(delivery);
        }

        //AWS S3에 저장된 배송 이미지 삭제 로직 추가 - 웰스토리만 적용되록 함. 
        // try {
        //     log.info("[destroyPersonalInfo] 배송 이미지 삭제 시작. 대상 배송 건수: {}", limitedDeliveries.size());

        //     for (final Delivery delivery : limitedDeliveries) {
        //         // DeliveryAllocation 조회
        //         final List<DeliveryAllocation> deliveryAllocations = deliveryAllocationRepository.findByDeliveryId(delivery.getId());

        //         for (final DeliveryAllocation deliveryAllocation : deliveryAllocations) {
        //             // 배송 완료 이미지 삭제 (delivery_complete)
        //             final List<File> deliveryCompleteFiles = fileService.getFileList(
        //                     deliveryAllocation.getId(),
        //                     FileCategory.DELIVERY_COMPLETE,
        //                     FileType.IMAGE
        //             );

        //             for (final File file : deliveryCompleteFiles) {
        //                 try {
        //                     log.info("[destroyPersonalInfo] 배송 완료 이미지 삭제: deliveryAllocationId={}, fileId={}, fileName={}",
        //                             deliveryAllocation.getId(), file.getId(), file.getSavedName());
        //                     fileService.deleteFile(file, deliveryAllocation);
        //                 } catch (Exception e) {
        //                     log.error("[destroyPersonalInfo] 배송 완료 이미지 삭제 실패: deliveryAllocationId={}, fileId={}, error={}",
        //                             deliveryAllocation.getId(), file.getId(), e.getMessage());
        //                 }
        //             }
        //         }
        //     }

        //     log.info("[destroyPersonalInfo] 배송 이미지 삭제 완료");

        // } catch (Exception e) {
        //     log.error("[destroyPersonalInfo] 배송 이미지 삭제 중 예상치 못한 오류 발생: {}", e.getMessage(), e);
        // }

        try {
            privacyRecordService.saveRecordArray(limitedDeliveries.stream()
                    .map(delivery -> {
                        Long userId = delivery.getUserId();
                        User user = Optional.ofNullable(userId).map(userService::getUserById).orElse(null);
                        Organization organization = null;
                        if (user != null) {
                            organization = organizationService.getOrganizationById(user.getOrganizationId());
                        }

                        return PrivacyRecordDto.builder()
                                .recordType(PrivacyRecordType.DELIVERY)
                                .type(PrivacyUsageType.DELETE)
                                .userId(userId)
                                .orgId(Optional.ofNullable(organization).map(Organization::getId).orElse(null))
                                .dataType(PrivacyDataType.NAME_MOBILE_ADDRESS)
                                .func("destroyPersonalInfo")
                                .build();
                    })
                    .collect(Collectors.toList())
            );
        } catch (Exception e) {
            log.error("Error occurred while saving privacy records: ", e);
        }
    }

    /**
     * 간선 상/하차 검수
     *
     * @param riderId
     * @param trunkLineDTO
     */
    @Transactional
    public void updateTrunkLineStatus(final Long riderId,
                                      final TrunkLineDTO trunkLineDTO) {

        final RiderOrgStatus riderOrgStatus = riderOrgStatusRepository.findTop1ByRiderIdAndIsDeletedOrderByIdDesc(riderId, false);
        if (Objects.isNull(riderOrgStatus)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "담당자 조직 정보를 찾을 수 없습니다.", false);
        }
        final Organization organization = organizationService.getOrganizationById(riderOrgStatus.getOrgId());
        if (Objects.isNull(organization)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "조직 정보를 찾을 수 없습니다.", false);
        }

        final String scanCode = trunkLineDTO.getScanCode();
        final TrunkLineStatus trunkLineStatus = trunkLineDTO.getTrunkLineStatus();
        final Boolean isCancelled = Optional.ofNullable(trunkLineDTO.getIsCancelled()).orElse(Boolean.FALSE);
        final TrunkLine prevTrunkLine = trunkLineRepository.findTop1ByScanCodeAndTrunkLineStatusAndOrganizationOrderByScanDtDesc(scanCode, trunkLineStatus, organization);

        if (Objects.nonNull(prevTrunkLine)) {
            if (isCancelled) {
                trunkLineRepository.delete(prevTrunkLine);
            } else {
                log.info("[updateTrunkLineStatus] {}: 이미 {} 상태입니다.", scanCode, prevTrunkLine.getTrunkLineStatus().toString());
                if (organizationService.isCodeNameOrganization(organization, TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME)) {
                    throw new CustomException(HttpStatus.BAD_REQUEST, "이미 스캔한 전표입니다.", false);
                } else {
                    throw new CustomException(HttpStatus.BAD_REQUEST, "이미 스캔한 코드입니다.", false);
                }
            }
        } else {
            if (isCancelled) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "스캔취소할 수 없습니다.", false);
            } else {
                final TrunkLine trunkLine = TrunkLine.builder()
                        .organization(organization)
                        .riderId(riderId)
                        .scanCode(scanCode)
                        .trunkLineStatus(trunkLineStatus)
                        .scanDt(LocalDateTime.now())
                        .build();

                trunkLineRepository.save(trunkLine);
            }
        }
    }

    /**
     * 분류상품의 배차정보 조회
     *
     * @param scanRiderId
     * @param scanCode    전표번호(customerOrderId)+개수(2자리) 또는 물류코드(productBarcode)
     * @return
     */
    public ProductDispatchRespDTO getProductDispatchInfo(final Long scanRiderId,
                                                         final String scanCode) {

        String codeName = "";
        final RiderOrgStatus scanRiderOrgStatus = riderOrgStatusRepository.findTop1ByRiderIdAndIsDeletedOrderByIdDesc(scanRiderId, false);
        final Organization scanRiderOrg = organizationService.getOrganizationById(Optional.ofNullable(scanRiderOrgStatus).map(RiderOrgStatus::getOrgId).orElse(null));
        if (Objects.isNull(scanRiderOrg)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "해당하는 조직이 없습니다.", false);
        } else {
            codeName = scanRiderOrg.getCodeName();
            log.info("[getProductDispatchInfo] organization: {}", codeName);
        }

        Long dispatchNo = null;
        Integer orderNo = null;
        List<ProductDispatchDTO> productDispatchList = new ArrayList<>();
        boolean isTheHyundaiOrg = organizationService.isCodeNameOrganization(scanRiderOrg, TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME);

        // scanCode가 7자리 이상이면 전표번호(일반), 미만이면 물류코드(본부)로 인지한다. 물류코드는 10-05 또는 105-10 형식으로 최대 6자리이다.
        if (scanCode.length() > TheHyundaiConstant.MAX_PRODUCT_BARCODE_LENGTH) {
            // 일반분류 : 호차, 순번 표출
            String orderId = "";
            if (isTheHyundaiOrg) {
                // 전표번호 바코드(15) = 전표번호(13) + 갯수(2)
                try {
                    orderId = scanCode.substring(0, scanCode.length() - 2); // 전표번호(13)
                } catch (Exception e) {
                    log.error("[getProductDispatchInfo] Exception: " + e.getMessage());
                    throw new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, "실행 오류입니다.", e, false);
                }
            } else {
                // 투홈 바코드(11) = H(1) + 고유값(10)
                orderId = scanCode;
            }

            final String customerOrderCode = CustomerUtil.appendOrganizationCode(codeName, orderId);
            final Delivery delivery = deliveryBasicService.getDeliveryByCustomerOrderIdOrderByUpdateAtDesc(customerOrderCode);
            if (Objects.isNull(delivery)) {
                log.info("[getProductDispatchInfo] customerOrderId: {}, scanCode: {}", customerOrderCode, scanCode);
                throw new CustomException(HttpStatus.NOT_FOUND, "일반분류할 배송 정보가 없습니다.", false);
            }

            final Long projectId = delivery.getProjectId();
            if (projectBasicService.isProjectTerminated(projectId)) {
                log.error("[getProductDispatchInfo] projectId: {}, 종료된 프로젝트는 일반분류 불가", projectId);
                throw new CustomException(HttpStatus.NOT_FOUND, "일반분류할 정보가 없습니다.", false);
            }

            final Long deliveryId = delivery.getId();
//            final Integer whGubn = delivery.getDetail().getWhGubn(); // 1: 본부(물류코드), 2: 일반(전표번호)
//            if (Objects.isNull(whGubn)) {
//                throw new CustomException(HttpStatus.BAD_REQUEST, "본부/일반 구분이 없습니다.", false);
//            }
//            if (!TheHyundaiConstant.WHGUBN_G.equals(whGubn)) {
//                throw new CustomException(HttpStatus.BAD_REQUEST, "일반분류 대상이 아닙니다.", false);
//            }

            final DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocation(deliveryId).orElse(null);
            if (Objects.isNull(deliveryAllocation)) {
                throw new CustomException(HttpStatus.NOT_FOUND, "일반분류할 배차 정보가 없습니다.", false);
            }

            final Rider deliveryRider = riderService.getRider(deliveryAllocation.getRiderId());
            if (Objects.nonNull(deliveryRider)) {
                dispatchNo = deliveryRider.getDispatchNumber();
            }
            orderNo = deliveryAllocation.getOrderNum();
            log.info("[getProductDispatchInfo] 전표번호: {}, 호차번호: {}, 배송순번: {}, deliveryId: {}, projectId: {}", orderId, dispatchNo, orderNo, deliveryId, projectId);
        } else {
            if (!isTheHyundaiOrg) {
                // 투홈은 일반분류만 제공
                throw new CustomException(HttpStatus.BAD_REQUEST, "잘못된 요청입니다.", false);
            }

            // 본부분류 : 호차별 배차 갯수 표출
            // 본부 상품의 배차 목록 조회 (D+1 이내 본부분류 완료한다고 함)
            final Rider scanRider = riderService.getRider(scanRiderId);
            final RiderDepartment scanRiderDepartment = riderDepartmentRepository.findTop1ByRiderOrderByUpdatedAtDesc(scanRider);
            if (Objects.isNull(scanRiderDepartment)) {
                throw new CustomException(HttpStatus.NOT_FOUND, "담당자의 부서 정보가 없습니다.", false);
            }

            final String centerName = scanRiderDepartment.getDepartment().getDepartmentName(); // 스캔업무 담당자의 부서는 물류센터이므로 센터명과 동일. 예) "경인센터", "광역센터", "충청물류", ...
            if (!centerName.contains("센터") && !centerName.contains("물류")) {
                log.info("[getProductDispatchInfo] departmentName: {}", centerName);
                throw new CustomException(HttpStatus.BAD_REQUEST, "담당자의 부서 정보를 확인하세요.", false);
            }

            List<Long> projectIdListOfCenter = new ArrayList<>();
            final String projectDate = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")); // 전일 생성된 프로젝트를 익일 본부분류한다.

            final List<String> storageTypeList = TheHyundaiCenter.getStorageTypeListByCenterName(centerName);
            for (final String storageType : storageTypeList) {
                // 본부상품은 명절에만 발생. 원거리는 전표번호를 이용
                // 해당 물류센터의 이관구분별 프로젝트 목록 조회
                final String projectName = TheHyundaiConstant.PROJECT_PREFIX + DeliveryCategory.HOLIDAY.toString() + "_" + centerName + "_" + storageType + "_" + projectDate; // 예) "TheHyundai_명절_경인센터_1-1 상온_20240729", ..., "TheHyundai_명절_경인센터_7-2 VIP_20240729"
                log.info("[getProductDispatchInfo] projectName: {}", projectName);
                final List<Project> projectListOfCenter = projectBasicService.getProjectListByNameAndOrganizationId(projectName, scanRiderOrg.getId());
                if (CollectionUtils.isNotEmpty(projectListOfCenter)) {
                    projectIdListOfCenter.addAll(projectListOfCenter.stream().map(Project::getId).collect(Collectors.toList()));
                }
            }
            if (CollectionUtils.isEmpty(projectIdListOfCenter)) {
                throw new CustomException(HttpStatus.NOT_FOUND, "본부분류할 프로젝트가 없습니다.", false);
            }

            for (final Long projectId : projectIdListOfCenter) {
                // 물류코드, 이관구분에 해당하는 배차 목록 조회
                final List<DeliveryAllocation> deliveryAllocationList = deliveryAllocationRepository.findByDeliveryIdInAndStatusAndProjectIdIn(scanCode, DeliveryStatus.toEnglishFromDeliveryStatus(DeliveryStatus.READY), Arrays.asList(projectId));
                if (CollectionUtils.isEmpty(deliveryAllocationList)) {
                    continue;
                }

                // 배차된 호차번호 목록
                final List<Long> dispatchNoList = deliveryAllocationList.stream()
                        .map(da -> riderService.getRider(da.getRiderId()).getDispatchNumber())
                        .filter(Objects::nonNull)
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(dispatchNoList)) {
                    continue;
                }

                // 호차별 배차 갯수
                for (final Long dn : dispatchNoList) {
                    final long dispatchCnt = deliveryAllocationList.stream()
                            .filter(da -> {
                                final Long riderDispatchNo = riderService.getRider(da.getRiderId()).getDispatchNumber();
                                final Integer whGubn = da.getDelivery().getDetail().getWhGubn();
                                final boolean isHq = Objects.nonNull(whGubn) && TheHyundaiConstant.WHGUBN_HQ.equals(whGubn);
                                return Objects.nonNull(riderDispatchNo) && dn.equals(riderDispatchNo) && isHq;
                            })
                            .mapToLong(da -> da.getDelivery().getDeliveryProducts().get(0).getOrderQuantity())
                            .sum();

                    productDispatchList.add(ProductDispatchDTO.of(dn, Long.valueOf(dispatchCnt)));
                }
            }

            if (CollectionUtils.isEmpty(productDispatchList)) {
                throw new CustomException(HttpStatus.NOT_FOUND, "본부분류 결과가 없습니다.", false);
            } else {
                productDispatchList.forEach(pd -> log.info("[getProductDispatchInfo] 물류코드: {}, 호차번호: {}, 갯수: {}", scanCode, pd.getDispatchNo(), pd.getDispatchCnt()));
            }
        }

        return ProductDispatchRespDTO.of(dispatchNo, orderNo, productDispatchList);
    }

    /**
     * 분류검증
     * 전표번호: 한 번의 스캔으로 검증 처리
     * 물류코드: 상품수량만큼 스캔해야 검증 처리
     *
     * @param scanRiderId
     * @param pickupVerificationDTO
     * @return
     */
    @Transactional
    public PickupVerificationRespDTO verifyPickupClassification(final Long scanRiderId,
                                                                final PickupVerificationDTO pickupVerificationDTO) {

        final String scanCode = pickupVerificationDTO.getScanCode();
        final Long dispatchNo = pickupVerificationDTO.getDispatchNo();
        if (StringUtils.isBlank(scanCode) || Objects.isNull(dispatchNo)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "필수 요청이 없습니다.", false);
        }

        String codeName = "";
        final RiderOrgStatus scanRiderOrgStatus = riderOrgStatusRepository.findTop1ByRiderIdAndIsDeletedOrderByIdDesc(scanRiderId, false);
        final Organization scanRiderOrg = organizationService.getOrganizationById(Optional.ofNullable(scanRiderOrgStatus).map(RiderOrgStatus::getOrgId).orElse(null));
        if (Objects.isNull(scanRiderOrg)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "해당하는 조직이 없습니다.", false);
        } else {
            codeName = scanRiderOrg.getCodeName();
            log.info("[verifyPickupClassification] organization: {}", codeName);
        }

        Delivery delivery = null;
        DeliveryDetail deliveryDetail = null;
        List<DeliveryProduct> deliveryProductList = new ArrayList<>();
        DeliveryProduct deliveryProduct = null;
        DeliveryAllocation newDeliveryAllocation = null;
        Long orderQuantity = 1L;
        Long asIsVerificationQuantity = null;
        Long toBeVerificationQuantity = null;
        PickupVerificationStatus pickupVerificationStatus = PickupVerificationStatus.VERIFIED; // 검증에서 미검증으로 변경하는 경우는 지원하지 않는다.

        // delivery_detail.qr_bar_code 값은 근거리/원거리/명절-일반:전표번호로, 원거리/명절-본부:물류코드로 구성된다.
        final Boolean isTheHyundaiHqProduct = StringUtils.equals(TheHyundaiConstant.THEHYUNDAI_ORG_CODE_NAME, codeName)
                && (scanCode.length() <= TheHyundaiConstant.MAX_PRODUCT_BARCODE_LENGTH);

        if (!isTheHyundaiHqProduct) {
            // 전표번호: 근거리, 원거리, 명절-일반
            String customerOrderCode = "";
            try {
                customerOrderCode = CustomerUtil.appendOrganizationCode(codeName, scanCode.substring(0, scanCode.length() - 2));
            } catch (Exception e) {
                log.error("[verifyPickupClassification] Exception: " + e.getMessage());
                throw new CustomException(HttpStatus.INTERNAL_SERVER_ERROR, "실행 오류입니다.", e, false);
            }

            delivery = deliveryBasicService.getDeliveryByCustomerOrderIdOrderByUpdateAtDesc(customerOrderCode);
            if (Objects.isNull(delivery)) {
                log.info("[verifyPickupClassification] customerOrderId: {}, scanCode: {}", customerOrderCode, scanCode);
                throw new CustomException(HttpStatus.NOT_FOUND, "분류검증할 배송 정보가 없습니다.", false);
            }

            deliveryDetail = delivery.getDetail();
            if (!StringUtils.equals(customerOrderCode, deliveryDetail.getCustomerOrderId())) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "전표번호가 올바르지 않습니다.", false);
            }

            deliveryProductList = delivery.getDeliveryProducts();
            if (CollectionUtils.isEmpty(deliveryProductList)) {
                throw new CustomException(HttpStatus.NOT_FOUND, "일반상품 정보가 없습니다.", false);
            }

            final DeliveryAllocation deliveryAllocation = deliveryBasicService.getDeliveryAllocation(delivery.getId()).orElse(null);
            if (Objects.isNull(deliveryAllocation)) {
                throw new CustomException(HttpStatus.NOT_FOUND, "분류검증할 배차 정보가 없습니다.", false);
            }

            final PickupVerificationStatus prevStatus = deliveryAllocation.getPickupVerificationStatus();
            if (Objects.nonNull(prevStatus) && PickupVerificationStatus.VERIFIED.equals(prevStatus)) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "이미 분류검증된 전표입니다.", false);
            }

            orderQuantity = Long.valueOf(deliveryDetail.getProductQuantity().longValue());
            if (Objects.isNull(orderQuantity) || orderQuantity < 1L) {
                throw new CustomException(HttpStatus.NOT_FOUND, "상품수량 정보가 없습니다.", false);
            }

            log.info("[verifyPickupClassification] 전표번호바코드: {}, 현재수량: {}/{}, projectId: {}, riderId: {}, deliveryId: {}, deliveryProductId: {}",
                    scanCode, deliveryAllocation.getVerificationQuantity(), orderQuantity, deliveryAllocation.getProjectId(), deliveryAllocation.getRiderId(), delivery.getId(), deliveryProductList.get(0).getId());
            asIsVerificationQuantity = Optional.ofNullable(deliveryAllocation.getVerificationQuantity()).orElse(0L);
            if (asIsVerificationQuantity < orderQuantity) {
                if (!asIsVerificationQuantity.equals((orderQuantity - 1L))) {
                    pickupVerificationStatus = PickupVerificationStatus.UNVERIFIED;
                }
                toBeVerificationQuantity = asIsVerificationQuantity + 1L;
                deliveryAllocation.setPickupVerificationStatus(pickupVerificationStatus); // UNVERIFIED --> UNVERIFIED --> VERIFIED
                deliveryAllocation.setVerificationQuantity(toBeVerificationQuantity);
            } else {
                throw new CustomException(HttpStatus.BAD_REQUEST, "더 이상 분류검증할 수 없습니다.", false);
            }

            String storeCenterCd = deliveryDetail.getWarehouseCode();
            final DeliveryCategory deliveryCategory = deliveryDetail.getDeliveryCategory();
            if (DeliveryCategory.SHORT_DIST.equals(deliveryCategory)) {
                storeCenterCd = deliveryDetail.getStoreCd();
            }

            log.info("[verifyPickupClassification] 분류검증 업데이트 --- projectId: {}, deliveryId: {}, 점/센터명: {}, 전표번호바코드: {}, 호차: {}, 분류검증상태: {}, 검증수량: {}/{}",
                    deliveryAllocation.getProjectId(), delivery.getId(), storeCenterCd, scanCode, dispatchNo, pickupVerificationStatus, toBeVerificationQuantity, orderQuantity);

            newDeliveryAllocation = deliveryAllocationRepository.save(deliveryAllocation);
        } else {
            // 물류코드: 명절-본부
            final Rider scanRider = riderService.getRider(scanRiderId);
            final RiderDepartment scanRiderDepartment = riderDepartmentRepository.findTop1ByRiderOrderByUpdatedAtDesc(scanRider);
            if (Objects.isNull(scanRiderDepartment)) {
                throw new CustomException(HttpStatus.NOT_FOUND, "담당자의 부서 정보가 없습니다.", false);
            }
            String centerName = scanRiderDepartment.getDepartment().getDepartmentName(); // 스캔업무 담당자의 부서는 물류센터이므로 센터명과 동일. 예) "경인센터", "광역센터", "충청물류", ...
            if (!centerName.contains("센터") && !centerName.contains("물류")) {
                log.info("[verifyPickupClassification] departmentName: {}", centerName);
                throw new CustomException(HttpStatus.BAD_REQUEST, "담당자의 부서 정보를 확인하세요.", false);
            }

            final String projectDate = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")); // 전일 생성된 명절 프로젝트를 익일 분류검증한다.
            final String storageType = TheHyundaiCenter.getStorageTypeByDispatchNo(String.valueOf(dispatchNo));
            final String projectName = TheHyundaiConstant.PROJECT_PREFIX + DeliveryCategory.HOLIDAY.toString() + "_" + centerName + "_" + storageType + "_" + projectDate; // 예) "TheHyundai_명절_경인센터_1-1 상온_20240729"
            log.info("[verifyPickupClassification] projectName: {}", projectName);
            final List<Project> projectListOfCenter = projectBasicService.getProjectListByNameAndOrganizationId(projectName, scanRiderOrg.getId()); // 명절 프로젝트 1개만 조회될 것이다.
            if (CollectionUtils.isEmpty(projectListOfCenter)) {
                throw new CustomException(HttpStatus.NOT_FOUND, "분류검증할 프로젝트가 없습니다.", false);
            }

            // 프로젝트의 호차별 물류코드의 배송 목록중 배송순서 빠른순대로 인식하여 개별 처리
            final Long projectId = projectListOfCenter.get(0).getId();
            final List<DeliveryAllocation> deliveryAllocationList = deliveryAllocationRepository.findByDispatchNumberAndProductBarcode(dispatchNo, scanCode, projectId, DeliveryStatus.toEnglishFromDeliveryStatus(DeliveryStatus.READY));
            if (CollectionUtils.isEmpty(deliveryAllocationList)) {
                throw new CustomException(HttpStatus.NOT_FOUND, "분류검증할 배차 정보가 없습니다.", false);
            }

            final DeliveryAllocation deliveryAllocation = deliveryAllocationList.stream()
                    .filter(da -> PickupVerificationStatus.UNVERIFIED.equals(da.getPickupVerificationStatus()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(deliveryAllocation)) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "이미 분류검증된 코드입니다.", false);
            }

            delivery = deliveryAllocation.getDelivery();
            deliveryProductList = delivery.getDeliveryProducts();
            if (CollectionUtils.isEmpty(deliveryProductList)) {
                throw new CustomException(HttpStatus.NOT_FOUND, "본부상품 정보가 없습니다.", false);
            }

            deliveryDetail = delivery.getDetail();
            deliveryProduct = deliveryProductList.get(0);
            asIsVerificationQuantity = Optional.ofNullable(deliveryAllocation.getVerificationQuantity()).orElse(0L);
            log.info("[verifyPickupClassification] 물류코드: {}, 현재수량: {}/{}, projectId: {}, riderId: {}, deliveryId: {}, deliveryProductId: {}",
                    scanCode, asIsVerificationQuantity, deliveryDetail.getProductQuantity(), projectId, deliveryAllocation.getRiderId(), delivery.getId(), deliveryProduct.getId());

            final List<String> productBarcodeList = deliveryProductList.stream()
                    .map(DeliveryProduct::getProductBarcode)
                    .collect(Collectors.toList());
            if (!productBarcodeList.contains(scanCode) || !StringUtils.equals(deliveryProduct.getProductBarcode(), scanCode)) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "물류코드가 올바르지 않습니다.", false);
            }

            orderQuantity = deliveryProduct.getOrderQuantity();
            if (Objects.isNull(orderQuantity) || orderQuantity < 1L) {
                throw new CustomException(HttpStatus.NOT_FOUND, "상품수량 정보가 없습니다.", false);
            }

            if (asIsVerificationQuantity < orderQuantity) {
                if (!asIsVerificationQuantity.equals((orderQuantity - 1L))) {
                    pickupVerificationStatus = PickupVerificationStatus.UNVERIFIED;
                }
                toBeVerificationQuantity = asIsVerificationQuantity + 1L;
                deliveryAllocation.setPickupVerificationStatus(pickupVerificationStatus); // UNVERIFIED --> UNVERIFIED --> VERIFIED
                deliveryAllocation.setVerificationQuantity(toBeVerificationQuantity);
            } else {
                throw new CustomException(HttpStatus.BAD_REQUEST, "더 이상 분류검증할 수 없습니다.", false);
            }

            log.info("[verifyPickupClassification] 분류검증 업데이트 --- projectId: {}, deliveryId: {}, 센터명: {}, 물류코드: {}, 호차: {}, 분류검증상태: {}, 검증수량: {}/{}",
                    projectId, deliveryAllocation.getDelivery().getId(), centerName, scanCode, dispatchNo, pickupVerificationStatus, toBeVerificationQuantity, orderQuantity);

            newDeliveryAllocation = deliveryAllocationRepository.save(deliveryAllocation);
        }

        return PickupVerificationRespDTO.of(scanCode, dispatchNo, pickupVerificationStatus, orderQuantity, toBeVerificationQuantity, newDeliveryAllocation);
    }

    /**
     * [스캔업무] 간선정보 목록 조회
     *
     * @param riderId
     * @param dateFrom
     * @param dateTo
     * @return
     */
    public List<TrunkLineDTO> getTrunkLineInfoList(@NotNull final Long riderId,
                                                   @NotNull final LocalDateTime dateFrom,
                                                   @NotNull final LocalDateTime dateTo) {

        final RiderOrgStatus riderOrgStatus = riderOrgStatusRepository.findTop1ByRiderIdAndIsDeletedOrderByIdDesc(riderId, false);
        if (Objects.isNull(riderOrgStatus)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "담당자 조직 정보를 찾을 수 없습니다.", false);
        }
        final Organization organization = organizationService.getOrganizationById(riderOrgStatus.getOrgId());
        if (Objects.isNull(organization)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "조직 정보를 찾을 수 없습니다.", false);
        }

        final List<TrunkLine> trunkLineList = trunkLineRepository.findByRiderIdAndOrganizationAndTrunkLineStatusInAndScanDtBetween(riderId, organization, Arrays.asList(TrunkLineStatus.PICKUP), dateFrom, dateTo);
        if (CollectionUtils.isEmpty(trunkLineList)) {
            return Collections.emptyList();
        } else {
            return trunkLineList.stream()
                    .map(t -> {
                        final Rider rider = riderService.getRiderById(riderId).orElse(null);
                        return TrunkLineDTO.builder()
                                .scanCode(t.getScanCode())
                                .riderName(Optional.ofNullable(rider).map(Rider::getName).orElse(""))
                                .riderPhoneNo(Optional.ofNullable(rider).map(Rider::getMobile).orElse(""))
                                .scanDt(t.getScanDt())
                                .trunkLineStatus(t.getTrunkLineStatus())
                                .build();
                    })
                    .collect(Collectors.toList());
        }
    }

    /**
     * 여러 프로젝트의 배송정보 조회
     *
     * @param fromDate
     * @param toDate
     * @param warehouseCode
     * @return
     */
    public List<WebProjectDeliveryDTO> getMultipleProjectDeliveries(final LocalDate fromDate,
                                                                    final LocalDate toDate,
                                                                    final String warehouseCode) {

        final DeliveryCategory deliveryCategory = getDeliveryCategory(warehouseCode);
        if (Objects.isNull(deliveryCategory)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "배송카테고리를 찾을 수 없습니다.", false);
        }

        List<WebProjectDeliveryDTO> webProjectDeliveryDTOList = new ArrayList<>();

        // fromDate, toDate를 yyyyMMdd 형태의 delivery_time 문자열로 변환
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        final String deliveryTimeFrom = fromDate.format(formatter);
        final String deliveryTimeTo = toDate.format(formatter);

        final String secretKey = transformerColumnKeyLoader.getSecretKey();
        final List<DeliveryMultipleProjectDAO> deliveryMultipleProjectDAOList = deliveryRepository.findDeliveryList4MultipleProjects(deliveryTimeFrom, deliveryTimeTo, warehouseCode, deliveryCategory.getCode(), secretKey);

        if (CollectionUtils.isNotEmpty(deliveryMultipleProjectDAOList)) {
            webProjectDeliveryDTOList = deliveryMultipleProjectDAOList.stream()
                    .filter(Objects::nonNull)
                    .map(dao -> {
                        final Long deliveryAllocationId = dao.getDeliveryAllocationId();
                        if (Objects.nonNull(deliveryAllocationId)) {
                            final DeliveryAllocation deliveryAllocation = deliveryAllocationRepository.findById(deliveryAllocationId).orElse(null);
                            final LocalDateTime startDt = Optional.ofNullable(deliveryAllocation.getStatusHistoryTime(DeliveryStatus.GOING))
                                    .orElse(deliveryAllocation.getStatusHistoryTime(DeliveryStatus.READY));
                            final LocalDateTime endDt = deliveryAllocation.getStatusHistoryTime(DeliveryStatus.SERVICING);
                            return WebProjectDeliveryDTO.of(dao,
                                    Optional.ofNullable(startDt).map(dt -> dt.toLocalTime().truncatedTo(ChronoUnit.SECONDS)).orElse(null),
                                    Optional.ofNullable(endDt).map(dt -> dt.toLocalTime().truncatedTo(ChronoUnit.SECONDS)).orElse(null));
                        } else {
                            return WebProjectDeliveryDTO.of(dao, null, null);
                        }
                    })
                    .collect(Collectors.toList());
        }

        // 구분 컬럼에 표시할 일련번호 추가
        if (CollectionUtils.isNotEmpty(webProjectDeliveryDTOList)) {
            for (int i = 0; i < webProjectDeliveryDTOList.size(); i++) {
                webProjectDeliveryDTOList.get(i).setSerialNo(i + 1);
            }
        }

        return webProjectDeliveryDTOList;
    }

    /**
     * 센터코드로 배송카테고리 조회
     *
     * @param warehouseCode
     * @return
     */
    private DeliveryCategory getDeliveryCategory(final String warehouseCode) {

        DeliveryCategory deliveryCategory;
        if (TheHyundaiCenter.isTheHyundaiCenter(warehouseCode)) {
            deliveryCategory = DeliveryCategory.HOLIDAY;
        } else  if (TheHyundaiStore.isTheHyundaiStore(warehouseCode)) {
            deliveryCategory = DeliveryCategory.SHORT_DIST;
        } else {
            deliveryCategory = null;
        }

        return deliveryCategory;
    }

    /**
     * 첫번째 배송출발이 있었는지 여부 확인
     *
     * @param riderId
     * @param projectId
     * @return
     */
    public boolean hasEverDeliveryDeparted(final Long riderId,
                                           final Long projectId) {

        final List<DeliveryAllocation> riderDeliveryAllocationList = deliveryBasicService.getRiderDeliveryAllocations(riderId, projectId);
        final List<Long> targetDeliveryAllocationIdList = riderDeliveryAllocationList.stream()
                .map(DeliveryAllocation::getId)
                .collect(Collectors.toList());
        final List<DeliveryStatusHistory> goingHistoryList = deliveryStatusHistoryService.getDeliveryStatusHistoryList(targetDeliveryAllocationIdList, DeliveryStatus.GOING, Boolean.FALSE);

        return CollectionUtils.isNotEmpty(goingHistoryList);
    }


        /**
     * 같은 좌표를 가진 배송지들의 orderNum을 연속되도록 보정하는 함수
     * 
     * @param allocations 보정할 DeliveryAllocation 리스트
     * @return 보정된 DeliveryAllocation 리스트
     */
    @Transactional
    public List<DeliveryAllocation> correctOrderNumForSameCoordinates(List<DeliveryAllocation> allocations) {

        if (CollectionUtils.isEmpty(allocations)) {
            return allocations;
        }

        // 기사별로 그룹화하여 처리
        allocations.stream()
                .collect(Collectors.groupingBy(DeliveryAllocation::getRiderId))
                .entrySet().stream()
                .forEach(entry -> {
                    this.correctRiderOrderNumForSameCoordinates(entry.getValue());
                });

        // 변경된 allocations 저장
        deliveryAllocationRepository.saveAll(allocations);

        return allocations;
    }

    /**
     * 특정 기사의 배송지들에 대해 같은 좌표를 가진 배송지들의 orderNum을 연속되도록 보정
     * 
     * @param riderAllocations 특정 기사의 DeliveryAllocation 리스트
     */
    private void correctRiderOrderNumForSameCoordinates(List<DeliveryAllocation> riderAllocations) {

        if (CollectionUtils.isEmpty(riderAllocations)) {
            return;
        }

        // orderNum 기준으로 정렬
        List<DeliveryAllocation> sortedAllocations = riderAllocations.stream()
                .filter(allocation -> Objects.nonNull(allocation.getOrderNum()))
                .sorted(Comparator.comparing(DeliveryAllocation::getOrderNum))
                .collect(Collectors.toList());

        if (sortedAllocations.isEmpty()) {
            return;
        }

        // 좌표별로 그룹화 (x, y 좌표가 같은 배송지들을 묶음)
        Map<String, List<DeliveryAllocation>> coordinateGroups = sortedAllocations.stream()
                .filter(this::hasValidCoordinates)
                .collect(Collectors.groupingBy(this::getCoordinateKey, LinkedHashMap::new, Collectors.toList()));

        // 1부터 시작하는 orderNum 카운터
        int currentOrderNum = 1;

        // 각 그룹별로 순차적으로 orderNum 할당
        for (Map.Entry<String, List<DeliveryAllocation>> entry : coordinateGroups.entrySet()) {
            String coordinateKey = entry.getKey();
            List<DeliveryAllocation> group = entry.getValue();

            // 그룹 내에서 현재 orderNum 순으로 정렬
            List<DeliveryAllocation> sortedGroup = group.stream()
                    .sorted(Comparator.comparing(DeliveryAllocation::getOrderNum))
                    .collect(Collectors.toList());

            // 그룹 내 배송지들에 순차적으로 연속된 orderNum 할당
            for (int i = 0; i < sortedGroup.size(); i++) {
                DeliveryAllocation allocation = sortedGroup.get(i);
                Integer oldOrderNum = allocation.getOrderNum();
                Integer newOrderNum = currentOrderNum++;

                allocation.setOrderNum(newOrderNum);

                log.info("[correctRiderOrderNumForSameCoordinates] 좌표: {}, 배송지 ID: {}, orderNum 변경: {} -> {}",
                        coordinateKey, allocation.getDelivery().getId(), oldOrderNum, newOrderNum);
            }

            log.info("[correctRiderOrderNumForSameCoordinates] 좌표: {}, 그룹 크기: {}, 할당된 orderNum 범위: {} ~ {}",
                    coordinateKey, sortedGroup.size(), currentOrderNum - sortedGroup.size(), currentOrderNum - 1);
        }

        log.info("[correctRiderOrderNumForSameCoordinates] 기사 ID: {}, 총 좌표 그룹 수: {}, 총 보정된 배송지 수: {}, 최종 orderNum: {}",
                riderAllocations.get(0).getRiderId(), coordinateGroups.size(), sortedAllocations.size(),
                currentOrderNum - 1);
    }

    /**
     * 배송지의 좌표가 유효한지 확인
     * 
     * @param allocation DeliveryAllocation
     * @return 좌표 유효성 여부
     */
    private boolean hasValidCoordinates(DeliveryAllocation allocation) {
        try {
            if (Objects.isNull(allocation.getDelivery()) ||
                    Objects.isNull(allocation.getDelivery().getDetail()) ||
                    Objects.isNull(allocation.getDelivery().getDetail().getDestinationAddress()) ||
                    Objects.isNull(allocation.getDelivery().getDetail().getDestinationAddress().getLocation())) {
                return false;
            }

            Point location = allocation.getDelivery().getDetail().getDestinationAddress().getLocation();
            return Objects.nonNull(location) &&
                    Objects.nonNull(location.getX()) &&
                    Objects.nonNull(location.getY()) &&
                    location.getX() != 0.0 &&
                    location.getY() != 0.0;
        } catch (Exception e) {
            log.warn("[hasValidCoordinates] 좌표 확인 중 오류 발생: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 배송지의 좌표를 키로 변환 (x,y 좌표를 문자열로 결합)
     * 
     * @param allocation DeliveryAllocation
     * @return 좌표 키 문자열
     */
    private String getCoordinateKey(DeliveryAllocation allocation) {
        try {
            Point location = allocation.getDelivery().getDetail().getDestinationAddress().getLocation();
            // 정밀도 체크 없이 정확한 좌표값으로 비교
            return location.getX() + "," + location.getY();
        } catch (Exception e) {
            log.warn("[getCoordinateKey] 좌표 키 생성 중 오류 발생: {}", e.getMessage());
            return "invalid";
        }
    }

}
