package com.logisteq.tms.delivery.service;

import com.logisteq.tms.delivery.domain.Receiver;
import com.logisteq.tms.delivery.repository.ReceiverRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.persistence.NonUniqueResultException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Validated
@Service
public class ReceiverService {

    private final ReceiverRepository receiverRepository;

    @Autowired
    public ReceiverService(final ReceiverRepository receiverRepository) {
        this.receiverRepository = receiverRepository;
    }

    public Receiver getReceiver(final Long receiverId) {
        return receiverRepository.findById(receiverId).orElse(null);
    }

    @Transactional
    public Receiver saveReceiver(final Receiver receiver) {
        return receiverRepository.save(receiver);
    }

    /**
     * Receiver가 이미 존재하면 그곳에 매핑 하고 존재 하지 않으면 receiver를 추가 한다...
     *
     * @param receiverName        :방문지명 (센터명)
     * @param receiverOwner       :방문지오너-수신인이름 (센터장)
     * @param receiverPhoneNumber :받는사람 전화번호 (센터 전화번호)
     * @return
     */
    @Deprecated
    @Transactional
    public Receiver ifExistReceiverThenMappingElseAdd(final String receiverName,
                                                      final String receiverOwner,
                                                      final String receiverPhoneNumber) {

        Receiver foundReceiver = null;
        try {
            foundReceiver = receiverRepository.findByReceiverNameAndReceiverOwnerAndReceiverPhoneNumber(receiverName, receiverOwner, receiverPhoneNumber);
        } catch (IncorrectResultSizeDataAccessException | NonUniqueResultException e) {
            // 동일한 내용의 엔티티가 들어가는 경우가 있어서 마지막 엔티티를 사용하도록 한다.
            foundReceiver = receiverRepository.findTopByReceiverNameAndReceiverOwnerAndReceiverPhoneNumberOrderByCreateAtDesc(receiverName, receiverOwner, receiverPhoneNumber);
        }

        //receiver가 없다면 새롭게 생성
        if (Objects.isNull(foundReceiver)) {
            final Receiver receiver = Receiver.builder()
                    .receiverName(receiverName)
                    .receiverOwner(receiverOwner)
                    .receiverPhoneNumber(receiverPhoneNumber)
                    .build();
            foundReceiver = receiverRepository.save(receiver);
        }

        return foundReceiver;
    }

    @Transactional
    public Receiver createReceiver(final String receiverName,
                                   final String receiverOwner,
                                   final String receiverPhoneNumber,
                                   final String receiverRealPhoneNumber) {

        final Receiver receiver = Receiver.builder()
                .receiverName(receiverName)
                .receiverOwner(receiverOwner)
                .receiverPhoneNumber(receiverPhoneNumber)
                .receiverRealPhoneNumber(receiverRealPhoneNumber)
                .build();

        return receiverRepository.save(receiver);
    }

    public void setPrivacyDataToNull(final Receiver receiver) {

        if (Objects.nonNull(receiver.getReceiverName())) {
            receiver.setReceiverName(null);
        }
        if (Objects.nonNull(receiver.getReceiverOwner())) {
            receiver.setReceiverOwner(null);
        }
        if (Objects.nonNull(receiver.getReceiverPhoneNumber())) {
            receiver.setReceiverPhoneNumber(null);
        }
        if (Objects.nonNull(receiver.getReceiverRealPhoneNumber())) {
            receiver.setReceiverRealPhoneNumber(null);
        }
    }

    /**
     * 수신인 개인정보 파기
     *
     * @param fromDt
     * @param toDt
     */
    @Transactional
    public void destroyReceiver(final LocalDateTime fromDt,
                                final LocalDateTime toDt) {

        List<Receiver> receiverList = receiverRepository.findByCreateAtBetween(fromDt, toDt);
        if (CollectionUtils.isNotEmpty(receiverList)) {
            for (Receiver receiver : receiverList) {
                setPrivacyDataToNull( receiver );
            }

            receiverRepository.saveAll(receiverList);
            log.info("[destroyReceiver] {}~{}, {}건의 수신인 개인정보 파기", fromDt, toDt, receiverList.size());
        } else {
            log.info("[destroyReceiver] {}~{}, 파기할 수신인 개인정보가 없습니다.", fromDt, toDt);
        }
    }

    public List<String> getReceiverNameListJoinDeliveryDetail(final List<Long> deliveryDetailIds) {

        if (CollectionUtils.isEmpty(deliveryDetailIds)) {
            return new ArrayList<>();
        }
        return receiverRepository.findReceiverNameJoinDeliveryDetail(deliveryDetailIds);
    }

}
