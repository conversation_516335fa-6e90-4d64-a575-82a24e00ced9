package com.logisteq.tms.delivery.repository;

import com.logisteq.tms.delivery.domain.DeliveryInspectionHistory;
import com.logisteq.tms.delivery.domain.suppl.InspectionStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DeliveryInspectionHistoryRepository extends JpaRepository<DeliveryInspectionHistory, Long> {

    DeliveryInspectionHistory findTop1ByDeliveryAllocationIdAndInspectionStatusOrderByStatusUpdateDtDesc(Long deliveryAllocationId, InspectionStatus inspectionStatus);

    List<DeliveryInspectionHistory> findByDeliveryAllocationId(Long deliveryAllocationId);

    DeliveryInspectionHistory findTop1ByDeliveryAllocationIdOrderByStatusUpdateDtDesc(Long deliveryAllocationId);
}
