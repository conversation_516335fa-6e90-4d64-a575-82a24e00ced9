package com.logisteq.tms.delivery.repository.dao;

/**
 * <AUTHOR>
 * @created 2024-04-26
 * @project tms-service
 */
public interface ReportDeliveryWeeklyCountDAO {

    String getWeek();           // 기준주

    Long getTotal();            // 총 건수

    Long getWaiting();          // 배차대기 건수

    Long getReady();            // 배송전 건수

    Long getGoing();            // 배송중 건수

    Long getServicing();        // 서비스중 건수

    Long getRejected();         // 거절 건수

    Long getFailure();          // 배송실패 건수

    Long getCompleted();        // 배송완료 건수

    Long getUndelivered();      // 미배송 건수

}
