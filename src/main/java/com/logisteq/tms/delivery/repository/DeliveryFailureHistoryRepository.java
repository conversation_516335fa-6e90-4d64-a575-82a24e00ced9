package com.logisteq.tms.delivery.repository;

import com.logisteq.tms.delivery.domain.DeliveryFailureHistory;
import com.logisteq.tms.delivery.domain.id.DeliveryFailureHistoryId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DeliveryFailureHistoryRepository extends JpaRepository<DeliveryFailureHistory, DeliveryFailureHistoryId>, JpaSpecificationExecutor<DeliveryFailureHistory> {

    List<DeliveryFailureHistory> findByDeliveryAllocationId(final Long deliveryAllocationId);

    DeliveryFailureHistory findTopByDeliveryAllocationId(final Long deliveryAllocationId);

}
