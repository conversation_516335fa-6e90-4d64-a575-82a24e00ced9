package com.logisteq.tms.delivery.dto.excel;

import com.logisteq.common.component.excel.annotation.PxlColumn;
import com.logisteq.tms.delivery.domain.DeliveryAllocation;
import com.logisteq.tms.delivery.domain.DeliveryDetail;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.domain.suppl.ProductSizeType;
import lombok.*;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

@Getter
@Setter
@AllArgsConstructor
@RequiredArgsConstructor
@Builder
@ToString
/*@JsonInclude(JsonInclude.Include.NON_NULL) */
public final class DeliveryPastOrderExcelExportDTO {
    @PxlColumn(name = "주문ID")
    private String customerOrderId;

    @PxlColumn(name = "운송장번호")
    private String trackingNumber;

    @PxlColumn(name = "방문지명")
    private String receiverName;

    @PxlColumn(name = "고객예약일시")
    private String customerDeliveryTime;

    @PxlColumn(name = "배송시작일시", pattern = "yyyy.MM.dd HH:mm")//"yyyy/MM/dd HH:mm:ss")
    private LocalDateTime realStartDateTime;

    //@NotNull(message = "'상태변경(완료)일시'가 비어있습니다.")
    @PxlColumn(name = "배송완료일시", pattern = "yyyy.MM.dd HH:mm")//"yyyy/MM/dd HH:mm:ss")
    private LocalDateTime realEndDateTime;

    //@NotNull(message = "'업무결과(결과)'가 비어있습니다.")
    @PxlColumn(name = "업무결과(결과)")
    private DeliveryStatus deliveryStatus;

    @PxlColumn(name = "물품명")
    private String productName;

    @PxlColumn(name = "물품크기")
    private ProductSizeType productSize;

    @PxlColumn(name = "물품무게")
    private Float productWeight;

    @PxlColumn(name = "물품수량")
    private Integer productQuantity;

    //@NotBlank(message = "'담당기사'가 비어있습니다.")
    @PxlColumn(name = "담당기사")
    private String riderName;

    @PxlColumn(name = "기사전화번호")
    private String riderMobile;

    @PxlColumn(name = "기사 보고사항")
    private String deliveryMsg;

    public static DeliveryPastOrderExcelExportDTO parseFromDelivery(
            DeliveryAllocation dA,
            DeliveryDetail dD,
            String riderName,
            String riderMobile,
            String deliveryMsg) {
        return DeliveryPastOrderExcelExportDTO.builder()
                .customerOrderId(dD != null ? dD.getCustomerOrderId() : null)
                .trackingNumber(dD != null ? dD.getTrackingNumber() : null)
                .receiverName((dD != null) && (dD.getReceiver() != null) ? dD.getReceiver().getReceiverName() : null)
                .customerDeliveryTime(getCustomerDetailsTime(dD))
                .realStartDateTime(dA != null ? dA.getStatusHistoryTime(DeliveryStatus.GOING) : null)
                .realEndDateTime(dA != null ? dA.getStatusHistoryTime(DeliveryStatus.COMPLETED) : null)
                .deliveryStatus(dA != null ? dA.getStatus() : DeliveryStatus.WAITING)
                .productName(dD != null ? dD.getProductName() : null)
                .productSize(dD != null ? dD.getProductSize() : null)
                .productWeight(dD != null ? dD.getProductWeight() : null)
                .productQuantity(dD != null ? dD.getProductQuantity() : null)
                .riderName(riderName)
                .riderMobile(riderMobile)
                .deliveryMsg(deliveryMsg)
                .build();
    }

    public static String getCustomerDetailsTime(DeliveryDetail dD) {

        String customerDeliveryTime = null;
        LocalTime deliveryStartTime = dD.getDeliveryStartTime();
        LocalTime deliveryEndTime = dD.getDeliveryEndTime();

        if (deliveryStartTime != null) {
            LocalDateTime createAt = dD.getCreateAt();
            customerDeliveryTime = createAt.format(DateTimeFormatter.ofPattern("yyyy.MM.dd")) + " "
                    + deliveryStartTime.format(DateTimeFormatter.ofPattern("HH:mm")) + "-";
        }
        else if (deliveryEndTime != null) {
            customerDeliveryTime += deliveryEndTime.format(DateTimeFormatter.ofPattern("HH:mm"));
        }

        return customerDeliveryTime;
    }
}