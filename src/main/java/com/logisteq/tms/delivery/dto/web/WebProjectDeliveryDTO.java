package com.logisteq.tms.delivery.dto.web;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.delivery.repository.dao.DeliveryMultipleProjectDAO;
import com.logisteq.tms.external.etc.utils.CustomerUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalTime;
import java.util.Objects;
import java.util.Optional;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.ALWAYS)
@Schema(description = "웹 프로젝트 배송 정보 DTO")
public class WebProjectDeliveryDTO {

    @Schema(description = "배송ID", example = "134345")
    private Long deliveryId;

    @Schema(description = "프로젝트ID", example = "1495")
    private Long projectId;

    @Schema(description = "기사ID", example = "1495")
    private Long riderId;

    @Schema(description = "배송할당ID", example = "135782")
    private Long deliveryAllocationId;

    @Schema(description = "구분", example = "1")
    private Integer serialNo;

    @Schema(description = "센터코드", example = "경인센터")
    private String warehouseCode;

    @Schema(description = "배송일자", example = "20241213")
    private String deliveryTime;

    @Schema(description = "호차번호", example = "1201")
    private Long dispatchNo;

    @Schema(description = "배송순서", example = "11")
    private Integer orderNo;

    @Schema(description = "기사명", example = "홍길동")
    private String driverName;

    @Schema(description = "주문번호", example = "1025562883001")
    private String customerOrderId;

    @Schema(description = "배송상태", example = "배송완료")
    private String deliveryStatusKor;

    @Schema(description = "배송시작시간", example = "06:45")
    private LocalTime deliveryStartTime;

    @Schema(description = "배송종료시간", example = "06:54")
    private LocalTime deliveryEndTime;

    @Schema(description = "법정동", example = "도곡동")
    private String eupMyeonDong;

    @Schema(description = "주소", example = "서울 강남구 테헤란로98길 12")
    private String address;

    @Schema(description = "프로젝트명", example = "TheHyundai_명절_경인센터_2-1 상온_20240904")
    private String projectName;

    public static WebProjectDeliveryDTO of(final DeliveryMultipleProjectDAO dao,
                                           final LocalTime deliveryStartTime,
                                           final LocalTime deliveryEndTime) {

        if (Objects.isNull(dao)) {
            return null;
        }

        return WebProjectDeliveryDTO.builder()
                .deliveryId(dao.getDeliveryId())
                .projectId(dao.getProjectId())
                .riderId(dao.getRiderId())
                .deliveryAllocationId(dao.getDeliveryAllocationId())
                .warehouseCode(dao.getWarehouseCode())
                .deliveryTime(dao.getDeliveryTime())
                .dispatchNo(dao.getDispatchNumber())
                .orderNo(dao.getOrderNum())
                .driverName(dao.getRiderName())
                .customerOrderId(CustomerUtil.extractOrganizationCodeAndUniqueCode(dao.getCustomerOrderId()).getRight())
                .deliveryStatusKor(Optional.ofNullable(dao.getDeliveryStatus()).map(DeliveryStatus::getDescription).orElse(""))
                .deliveryStartTime(deliveryStartTime)
                .deliveryEndTime(deliveryEndTime)
                .eupMyeonDong(dao.getEupMyeonDong())
                .address(StringUtils.trim(dao.getBaseAddress() + " " + dao.getDetailAddress()))
                .projectName(dao.getProjectName())
                .build();
    }

}
