package com.logisteq.tms.delivery.dto.excel;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.common.component.excel.annotation.PxlSheet;
import com.logisteq.common.component.excel.annotation.PxlWorkbookName;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public final class DeliveryPastOrderExcelExportOverallDTO {

    @NotBlank
    @PxlWorkbookName
    private String name; // 이름

    @NotEmpty(message = "'방문지' 시트가 비어있습니다.")
    @Valid
    @PxlSheet(name = "방문지")
    private List<DeliveryPastOrderExcelExportDTO> deliveryPastOrderExcelExportDTOList; // 배송 리스트

}
