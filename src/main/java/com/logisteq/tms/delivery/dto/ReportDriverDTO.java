package com.logisteq.tms.delivery.dto;

import com.logisteq.common.component.excel.annotation.PxlColumn;
import com.logisteq.common.component.excel.styler.data.PxlDataHorizontalCenterTextStyler;
import com.logisteq.tms.delivery.constant.DeliveryConstant;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReportDriverDTO {

    // 점포명
    @PxlColumn(name = "점포명", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private String branchName;

    // 배송담당자
    @PxlColumn(name = "배송담당자", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private String driverName;

    // 수행프로젝트건수
    @PxlColumn(name = "수행프로젝트건수", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private Long projectCount;

    // 누적주행거리 (km)
    @PxlColumn(name = "주행거리(km)", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT)
    private Long mileage;

    // 전화주문건수
    @PxlColumn(name = "전화주문건수", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private Long sisOrderCount;

    // 온라인주문건수
    @PxlColumn(name = "온라인주문건수", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private Long mosOrderCount;

    // 전체주문건수
    @PxlColumn(name = "전체주문건수", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private Long totalOrderCount;

    // 배차완료건수
    // 점포명과 기사ID로 조회한 주문건수는 배차완료건수와 동일
    @PxlColumn(name = "배차완료건수", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private Long dispatchCount;

    // 미배차건수
    @PxlColumn(name = "미배차건수", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private Long undispatchCount;

    // 배차율 (소수점 한자리까지 표현)
    @PxlColumn(name = "배차율(%)", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT)
    private Double dispatchRate;

    // 배송완료건수
    @PxlColumn(name = "배송완료건수", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private Long completedCount;

    // 미배송건수
    @PxlColumn(name = "미배송건수", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private Long undeliveredCount;

    // 배송실패건수
    @PxlColumn(name = "배송실패건수", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private Long failureCount;

    // 배송완료율 (소수점 한자리까지 표현)
    @PxlColumn(name = "배송완료율(%)", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT)
    private Double completedRate;

    // 전화주문금액
    @PxlColumn(name = "전화주문금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long sisOrderAmount;

    // 온라인주문금액
    @PxlColumn(name = "온라인주문금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long mosOrderAmount;

    // 전체주문금액
    @PxlColumn(name = "전체주문금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long totalOrderAmount;

    // 객단가
    @PxlColumn(name = "객단가", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long customerTransaction;

    // 배차완료금액
    @PxlColumn(name = "배차완료금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long dispatchAmount;

    // 미배차금액
    @PxlColumn(name = "미배차금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long undispatchAmount;

    // 배송완료금액
    @PxlColumn(name = "배송완료금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long completedAmount;

    // 미배송금액
    @PxlColumn(name = "미배송금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long undeliveredAmount;

    // 배송실패금액
    @PxlColumn(name = "배송실패금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long failureAmount;

}
