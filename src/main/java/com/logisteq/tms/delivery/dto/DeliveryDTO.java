package com.logisteq.tms.delivery.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.logisteq.common.dto.CoordinateDTO;
import com.logisteq.common.util.Aes256Util;
import com.logisteq.common.util.GeometryUtil;
import com.logisteq.tms.address.dto.AddressDTO;
import com.logisteq.tms.delivery.domain.*;
import com.logisteq.tms.delivery.domain.suppl.DeliveryCategory;
import com.logisteq.tms.delivery.domain.suppl.*;
import com.logisteq.tms.external.etc.utils.CustomerUtil;
import com.logisteq.tms.external.welstory.domain.WelstoryDeliveryInfo;
import com.logisteq.tms.product.domain.Product;
import com.logisteq.tms.product.domain.ProductAllocation;
import com.logisteq.tms.product.dto.OrderItemDTO;
import com.logisteq.tms.product.service.ProductService;
import com.logisteq.tms.project.dto.excel.leftpanel.ProjectDestinationSheetDTO;
import com.logisteq.tms.route.constant.RouteConstant;
import com.logisteq.tms.route.domain.RouteAppPlan;
import com.logisteq.tms.route.domain.RouteRun;
import com.logisteq.tms.web.dto.WebDeliveryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeliveryDTO {

    @Schema(description = "배송 ID", example = "1411651")
    private Long deliveryId;

    @Schema(description = "방문지명", example = "서울시청")
    private String receiverName;

    @Schema(description = "수신인 전화번호")
    @JsonSerialize(using = Aes256Util.PersonalInfoSerializer.class)
    private String receiverPhoneNumber;

    @Schema(description = "수신인 실전화번호")
    @JsonSerialize(using = Aes256Util.PersonalInfoSerializer.class)
    private String receiverRealPhoneNumber;

    @Schema(description = "수신인")
    @JsonSerialize(using = Aes256Util.PersonalInfoSerializer.class)
    private String receiverOwner;

    @Schema(description = "물품명", example = "테이퍼드 크롭 데님 팬츠")
    private String productName;

    @Schema(description = "물품 너비", example = "10")
    private Integer productWidth;

    @Schema(description = "물품 길이", example = "10")
    private Integer productLength;

    @Schema(description = "물품 높이", example = "10")
    private Integer productHeight;

    @Schema(description = "물품 무게", example = "3.14")
    private Float productWeight;

    @Schema(description = "물품 크기", example = "중형")
    private ProductSizeType productSize;

    @Schema(description = "물품 수량", example = "1")
    private Integer productQuantity;

    @Schema(description = "목적지 정보")
    private AddressDTO destinationAddress;

    @Schema(description = "방문예약일시", example = "2024-01-02T14:30:00")
    private LocalDateTime visitReservationDateTime;

    @Schema(description = "배송출발 시각", example = "10:30:00")
    @JsonInclude(JsonInclude.Include.ALWAYS)
    private LocalTime deliveryStartTime;

    @Schema(description = "배송마감 시각", example = "23:00:00")
    @JsonInclude(JsonInclude.Include.ALWAYS)
    private LocalTime deliveryEndTime;

    @Schema(description = "상차 시각", example = "23:00:00")
    private LocalTime pickUpTime; //상차 검수 시간

    @Schema(description = "하차 시각", example = "23:00:00")
    private LocalTime dropOffTime; //하차 검수 시간

    @Schema(description = "체류시간(min)", example = "3")
    private Long duration;

    @Schema(description = "배송 메시지", example = "파손주의")
    private String deliveryMessage;

    @Schema(description = "배송 상태", example = "READY")
    private DeliveryStatus deliveryStatus;

    @Schema(description = "배송 유형", example = "방문지")
    private DeliveryType deliveryType;

    @Schema(description = "업무 유형", example = "배송")
    private VisitType visitType;

    @Schema(description = "배송완료 유형", example = "NORMAL_DELIVERY")
    private DeliveryCompletedType deliveryCompletedType;

    @Schema(description = "배송완료 메시지", example = "3층문앞에 두었습니다 확인바랍니다")
    private String deliveryCompletedMessage;

    @Schema(description = "배송실패 유형", example = "WRONG_ADDR")
    private DeliveryFailureType deliveryFailureType;

    @Schema(description = "배송실패 메시지", example = "영업사원이 이미 수거해 감")
    private String deliveryFailureMessage;

    @Schema(description = "고객사에서 관리하는 주문 ID", example = "3710000055812")
    private String customerOrderId; //고객사에서 관리하는 주문Id

    @Schema(description = "운송장 번호", example = "3710000055812")
    private String trackingNumber; // 운송장 번호

    @Schema(description = "고객 ID", example = "A01")
    private String clientId; //고객사에서 관리하는 고객Id

    @Schema(description = "상품이미지 URL 목록", example = "['https://cdn.pixabay.com/photo/2021/01/01/13/21/guggenheim-museum-2707258_1280.jpg']")
    private String[] customerProductImageUrls; //고객사에서 관리하는 상품 이미지 URL

    @Schema(description = "배송요청일시", example = "2024-01-02T12:27:55")
    private LocalDateTime deliveryRequestAt;

    @Schema(description = "배송완료일시", example = "2024-01-02T13:16:42.92")
    private LocalDateTime deliveryDoneAt;

    @Schema(description = "기사 ID", example = "6433")
    private Long riderId;

    @Schema(description = "권역명", example = "강남1")
    private String groupName;

    @Schema(description = "배송순서", example = "1")
    private Integer orderNum;

    @Schema(description = "박스번호", example = "123")
    private Integer boxNum; // 물품 번호

    @Schema(description = "실제 이동거리(m)", example = "46349.7706334733")
    private Double realDistance;

    @Schema(description = "실제 소요시간(sec)", example = "2580")
    private Long realTime;

    @Schema(description = "실제 출발시각", example = "21:35:50")
    private LocalTime realStartTime;

    @Schema(description = "실제 도착시각", example = "22:14:53")
    private LocalTime realArrivalTime;

    @Schema(description = "실제 완료시각", example = "22:14:58")
    private LocalTime realEndTime;

    @Schema(description = "실제 서비스 소요시간(sec)", example = "10")
    private Long realServiceDuration;

    @Schema(description = "경로")
    private List<CoordinateDTO> routePath;

    @Schema(description = "예상 이동거리(m)", example = "50.00883156932372")
    private Double predictionDistance;      // TODO: estimatedMeters로 바꾸고 싶으나, 앱에서도 사용하므로 나중에 바꾸자.

    @Schema(description = "예상 소요시간(sec)", example = "30")
    private Long predictionTime;            // TODO: estimatedSeconds로 바꾸고 싶으나, 앱에서도 사용하므로 나중에 바꾸자.

    @Schema(description = "실제 다중경로")
    private List<CoordinateDTO> routePathReal;

    @Schema(description = "실제 다중경로 목록")
    private List<List<CoordinateDTO>> multiRoutePathReal;

    @Schema(description = "실제 이동거리(m)", example = "24.31569323729123")
    private Double estimatedMetersReal;

    @Schema(description = "실제 소요시간(sec)", example = "30")
    private Long estimatedSecondsReal;

    @Schema(description = "실제 예상도착일시", example = "2023-02-27T17:05:19")
    private LocalDateTime etaDateTimeReal;

    @Schema(description = "배송이미지 갯수", example = "1")
    private Long fileCountForCompleted;

    @Schema(description = "배송할당 목록")
    private List<DeliveryAllocationDTO> allocations;

    @Schema(description = "픽업장소 정보")
    private PickupPlaceDTO pickupPlace;

    @Schema(description = "프로젝트 ID", example = "13342")
    private Long projectId;

    @Schema(description = "콜앱 사용자 ID", example = "8375")
    private Long callUserId;

    @Schema(description = "신규배송 여부", example = "false")
    private Boolean isNewDelivery;

    @Schema(description = "헤일링 유형 (1:pickup, 2:drop)", example = "1")
    private Integer hailingType; // 1: pickup, 2: drop

    @Schema(description = "콜앱에 전송 여부", example = "false")
    private Boolean isSendingToCall;

    @Schema(description = "사용자정의 배송순서", example = "1")
    private Integer userDefinedOrderNum;        // 사용자 정의 배송 순서

    @Schema(description = "검수상태", example = "PICKUP")
    private InspectionStatus inspectionStatus; // 상품 검수상태

    @Schema(description = "상차수량", example = "1")
    private Long pickupQuantity;

    @Schema(description = "하차수량", example = "1")
    private Long dropoffQuantity;

    @Schema(description = "검수마감 여부", example = "false")
    private Boolean isInspectionEnded; // 검수마감 여부

    @Schema(description = "QR/바코드", example = "702430068959")
    private String qrBarCode; // QR코드, 바코드

    @Schema(hidden = true)
    private String developerField;  // 개발자 용도로 다양하게 사용

    @Schema(description = "상품 주문 목록")
    private List<OrderItemDTO> orderItemList; // 상품 주문 목록

    @Schema(description = "실시간배송 여부", example = "false")
    private Boolean isOnDemand; // 실시간배송 여부

    @Schema(description = "즉시배송 여부", example = "false")
    private Boolean isImmediate; //즉시 배송 플래그

    @Schema(description = "분할배송 번호", example = "1")
    private Integer splitNumber;

    @Schema(description = "발신인", example = "김발신")
    private String senderName;

    @Schema(description = "발신인 전화번호", example = "01098765432")
    private String senderPhoneNumber;

    @Schema(description = "발신인 회사명", example = "로지스텍")
    private String senderCompanyName;

    @Schema(description = "발신인 이미지 URL", example = "https://file-examples.com/wp-content/uploads/2018/03/file_example_TIFF_1MB.tiff")
    private String senderImageUrl;

    @Schema(description = "발신인 메모", example = "새해 복 많이 받으세요")
    private String senderMemo;

    @Schema(description = "발신인 기본주소", example = "서울 노원구 상계로 193-14")
    private String senderBaseAddress;

    @Schema(description = "발신인 상세주소", example = "123동 1234호")
    private String senderDetailAddress;

    @Schema(description = "주문금액", example = "32000")
    private Integer orderAmount; // 주문 금액

    @Schema(description = "주문정보 고유코드", example = "DO47369")
    private String uniqueCode;//고유값 위해 만듬

    @Schema(description = "창고코드", example = "F01")
    private String warehouseCode;//창고 코드

    @Schema(description = "기사의 배송 메모", example = "파손주의")
    private String memo; // 기사의 배송 메모

    @Schema(description = "배송 카테고리", example = "근거리배송")
    private DeliveryCategory deliveryCategory;

    @Schema(description = "배송일자", example = "20240521")
    private String deliveryTime;

    @Schema(description = "조코드", example = "001")
    private String joCd;

    @Schema(description = "본부/일반 구분", example = "1")
    private Integer whGubn;

    @Schema(description = "점코드", example = "무역센터점")
    private String storeCd;

    @Schema(description = "호차구분", example = "1-1 일반상온")
    private String storageType;

    @Schema(description = "분류검증상태", example = "VERIFIED")
    private PickupVerificationStatus pickupVerificationStatus;

    @Schema(description = "검증수량", example = "1")
    private Long verificationQuantity;

    @Schema(description = "고객 서비스 연락처", example = "02-2662-2234")
    private String customerServiceContact;

    @Schema(description = "상품QR", example = "M9999999999999")
    private String productQr;

    @Schema(description = "명함QR", example = "M999999999")
    private String businessCardQr;

    @Schema(description = "명함 유무", example = "Y")
    private String businessCard;

    @Schema(description = "관리자 배송 확인 유무", example = "false")
    private boolean isCsChecking;

    @Schema(description = "관리자 이름", example = "logisteq")
    private String csCheckingName;

    @Schema(description = "예상도착일시", example = "2024-08-20 09:48:54")
    private LocalDateTime estimatedDtOfArrival;

    @Schema(description = "고정순서", example = "1")
    private Integer fixedOrderNum;

    public DeliveryDTO(final Delivery delivery,
                       final Long riderId,
                       final ProductService productService) {

        this.deliveryId = delivery.getId();
        this.deliveryType = delivery.getType();

        this.deliveryRequestAt = delivery.getCreateAt();
        this.projectId = delivery.getProjectId();
        this.groupName = delivery.getGroupName();
        this.userDefinedOrderNum = delivery.getUserDefinedOrderNum();
        this.visitType = delivery.getVisitType();
        this.isOnDemand = delivery.getIsOnDemand();
        this.splitNumber = delivery.getSplitNumber();

        DeliveryDetail deliveryDetail = delivery.getDetail();
        Receiver rcv = deliveryDetail.getReceiver();
        if (Objects.nonNull(rcv)) {
            this.receiverName = rcv.getReceiverName();
            this.receiverPhoneNumber = rcv.getReceiverPhoneNumber();
            this.receiverOwner = rcv.getReceiverOwner();
            this.receiverRealPhoneNumber = rcv.getReceiverRealPhoneNumber();
        }
        if (Objects.nonNull(deliveryDetail.getCustomerOrderId())) {
            String cOrderId = deliveryDetail.getCustomerOrderId();
            final Pair<String, String> customerInfo = CustomerUtil.extractOrganizationCodeAndUniqueCode(cOrderId);
            this.customerOrderId = customerInfo.getRight();
        }

        if (Objects.nonNull(deliveryDetail.getTrackingNumber())) {
            String cTrackingNumber = deliveryDetail.getTrackingNumber();
            final Pair<String, String> trackingNumInfo = CustomerUtil.extractOrganizationCodeAndUniqueCode(cTrackingNumber);
            this.trackingNumber = trackingNumInfo.getRight();
        }

        if (Objects.nonNull(deliveryDetail.getClientId())) {
            String clientId = deliveryDetail.getClientId();
            final Pair<String, String> customerInfo = CustomerUtil.extractOrganizationCodeAndUniqueCode(clientId);
            this.clientId = customerInfo.getRight();
        }
        this.customerProductImageUrls = DeliveryDTO.extractProductImageUrlsFromParentheses(deliveryDetail.getCustomerProductImageUrl());

        this.productName = deliveryDetail.getProductName();
        this.productWeight = deliveryDetail.getProductWeight();
        this.productSize = deliveryDetail.getProductSize();
        this.productQuantity = deliveryDetail.getProductQuantity();
        this.visitReservationDateTime = deliveryDetail.getVisitReservationDateTime();
        this.deliveryStartTime = deliveryDetail.getDeliveryStartTime();
        this.deliveryEndTime = deliveryDetail.getDeliveryEndTime();
        this.duration = deliveryDetail.getEstimatedStayTime();
        this.deliveryMessage = deliveryDetail.getDeliveryMessage();
        this.qrBarCode = deliveryDetail.getQrBarCode();
        this.orderAmount = deliveryDetail.getOrderAmount();
        this.uniqueCode = deliveryDetail.getUniqueCode();
        this.warehouseCode = deliveryDetail.getWarehouseCode();
        this.deliveryCategory = deliveryDetail.getDeliveryCategory();

        // 목적지
        this.destinationAddress = AddressDTO.builder()
                .base(deliveryDetail.getDestinationAddress().getBase())
                .detail(deliveryDetail.getDestinationAddress().getDetail())
                .zipCode(deliveryDetail.getDestinationAddress().getZipCode())
                .locality(deliveryDetail.getDestinationAddress().getLocality())
                .location(CoordinateDTO.of(deliveryDetail.getDestinationAddress().getLocation()))
                .entrance(CoordinateDTO.of(deliveryDetail.getDestinationAddress().getEntrance()))
                .entranceVehicle(CoordinateDTO.of(deliveryDetail.getDestinationAddress().getEntranceVehicle()))
                .areaPolygon(Optional.ofNullable(deliveryDetail.getDestinationAddress().getAreaPolygon()).map(GeometryUtil::convertPointListFromPolygon).orElse(null))
                .build();

        // send info
        SenderInfo senderInfo = deliveryDetail.getSenderInfo();

        if (Objects.nonNull(senderInfo)) {
            this.senderName = senderInfo.getSenderName();
            this.senderCompanyName = senderInfo.getSenderCompanyName();
            this.senderPhoneNumber = senderInfo.getSenderPhoneNumber();
        }

        // 배송 할당
        List<DeliveryAllocation> allocations = delivery.getAllocations();
        if (CollectionUtils.isNotEmpty(allocations)) {
            DeliveryAllocation allocation = allocations.get(0);
            if (Objects.nonNull(riderId)) {
                allocation = allocations.stream()
                        .filter(a -> a.getRiderId().longValue() == riderId.longValue())
                        .findAny()
                        .orElse(allocation);
            }

            final RouteAppPlan routeAppPlan = allocation.getRouteAppPlan();
            final RouteRun routeRun = allocation.getRouteRun();

            this.riderId = allocation.getRiderId();
            this.orderNum = allocation.getOrderNum();
            this.boxNum = allocation.getBoxNum();
            this.realDistance = allocation.getRealMeters();
            this.realTime = allocation.getRealSeconds();
            this.deliveryStatus = allocation.getStatus();
            this.routePath = GeometryUtil.convertPointListFromLineString(routeAppPlan.getRoutePath());
            this.predictionDistance = routeAppPlan.getEstimatedMeters();
            this.predictionTime = routeAppPlan.getEstimatedSeconds();
            this.estimatedMetersReal = routeRun.getEstimatedMeters();
            this.estimatedSecondsReal = routeRun.getEstimatedSeconds();
            this.etaDateTimeReal = routeRun.getEtaDateTime();
            this.routePathReal = GeometryUtil.convertPointListFromLineString(routeRun.getRoutePath());
            if (RouteConstant.ENABLE_MULTILINESTRING_FEATURE) {
                this.multiRoutePathReal = GeometryUtil.convertPointListFromMultiLineString(routeRun.getMultiRoutePath());
            }
            this.fileCountForCompleted = allocation.getFileCountForCompleted();
            this.inspectionStatus = allocation.getInspectionStatus();
            this.pickupQuantity = Optional.ofNullable(allocation.getPickupQuantity()).orElse(0L);
            this.dropoffQuantity = Optional.ofNullable(allocation.getDropoffQuantity()).orElse(0L);
            this.isInspectionEnded = allocation.getIsInspectionEnded();
            this.memo = allocation.getMemo();
            this.pickupVerificationStatus = allocation.getPickupVerificationStatus();
            this.verificationQuantity = Optional.ofNullable(allocation.getVerificationQuantity()).orElse(0L);
            this.estimatedDtOfArrival = allocation.getEstimatedDtOfArrival();
            this.fixedOrderNum = allocation.getFixedOrderNum();

            // 물류센터/거점 같은 경우 여러 기사에게 할당될 수 있다.
            this.allocations = allocations.stream().map(DeliveryAllocationDTO::createSimpleDto).collect(Collectors.toList());

            // 배송시작/완료 시간
            LocalDateTime start = allocation.getStatusHistoryTime(DeliveryStatus.GOING);
            // TODO : CHECK_PROD
            if (Objects.isNull(start)) {
                start = allocation.getStatusHistoryTime(DeliveryStatus.READY);
            }

            if (Objects.nonNull(start)) {
                this.realStartTime = LocalTime.of(start.getHour(), start.getMinute(), start.getSecond());
            }

            LocalDateTime servicing = allocation.getStatusHistoryTime(DeliveryStatus.SERVICING);
            if (Objects.nonNull(servicing)) {
                this.realArrivalTime = LocalTime.of(servicing.getHour(), servicing.getMinute(), servicing.getSecond());
            }

            final DeliveryStatus deliveryStatus = allocation.getStatus();
            if (deliveryStatus.isDeliveryEnded()) {
                LocalDateTime end = allocation.getStatusHistoryTime(deliveryStatus);

                if (Objects.nonNull(end)) {
                    this.realEndTime = LocalTime.of(end.getHour(), end.getMinute(), end.getSecond());
                    if (Objects.nonNull(servicing)) {
                        this.realServiceDuration = servicing.until(end, ChronoUnit.SECONDS);
                    }
                    this.deliveryDoneAt = end;
                }

                if (DeliveryStatus.FAILURE.equals(deliveryStatus)) {
                    //failure 타입과 메세지를 가져오는데 deliveryFailureDt로 가장 나중에 data로 가져오게 한다.... (가장 최종 fail메세지.. )
                    allocation.getFailureHistories()
                            .stream()
                            .max(Comparator.comparing(DeliveryFailureHistory::getDeliveryFailureDt))
                            .ifPresent(deliveryFailureHistory -> {
                                this.deliveryFailureType = deliveryFailureHistory.getDeliveryFailureType();
                                this.deliveryFailureMessage = deliveryFailureHistory.getDeliveryFailureMessage();
                            });
                }

                if (DeliveryStatus.COMPLETED.equals(deliveryStatus)) {
                    // 배송완료타입, 메시지
                    allocation.getCompletedHistories().stream()
                            .max(Comparator.comparing(DeliveryCompletedHistory::getDeliveryCompletedDt))
                            .ifPresent(deliveryCompletedHistory -> {
                                this.deliveryCompletedType = deliveryCompletedHistory.getDeliveryCompletedType();
                                this.deliveryCompletedMessage = deliveryCompletedHistory.getDeliveryCompletedMessage();
                            });
                }
            }

            //상 하차 검수 있는지 확인함.
            final List<DeliveryInspectionHistory> deliveryInspectionHistories = allocation.getInspectionHistories();
            if (CollectionUtils.isNotEmpty(deliveryInspectionHistories)) {
                deliveryInspectionHistories.forEach(inspectionHistory -> {
                    if (inspectionHistory.getInspectionStatus().equals(InspectionStatus.PICKUP)) {
                        LocalDateTime time = inspectionHistory.getStatusUpdateDt();
                        this.pickUpTime = LocalTime.of(time.getHour(), time.getMinute(), time.getSecond());
                    }
                    if (inspectionHistory.getInspectionStatus().equals(InspectionStatus.DROPOFF)) {
                        LocalDateTime time = inspectionHistory.getStatusUpdateDt();
                        this.dropOffTime = LocalTime.of(time.getHour(), time.getMinute(), time.getSecond());
                    }
                });
            }

            /**
             * 배송 화물 관리자 확인 기능 구현
             */
            final List<DeliveryCsHistory> deliveryCsHistories  = allocation.getCsHistories();
            if(CollectionUtils.isNotEmpty(deliveryCsHistories)){
                this.isCsChecking = true;
                this.csCheckingName = deliveryCsHistories.get(0).getCsName();
            }

        } else {
            final String qrBarCode = delivery.getDetail().getQrBarCode();

            this.deliveryStatus = DeliveryStatus.WAITING;

            // 기본 검수상태 설정
            this.inspectionStatus = DeliveryDTO.setDefaultInspectionStatus(qrBarCode, delivery.getIsOnDemand());
            this.pickupQuantity = 0L;
            this.dropoffQuantity = 0L;
            this.isInspectionEnded = false;

            // 기본 분류검증상태 설정
            this.pickupVerificationStatus = DeliveryDTO.setDefaultPickupVerificationStatus(qrBarCode);
            this.verificationQuantity = 0L;
        }

        // 픽업 장소
        Delivery pickupPlace = delivery.getPickupPlace();
        if (Objects.nonNull(pickupPlace)) {
            this.pickupPlace = PickupPlaceDTO.createDefaultVO(pickupPlace);
        }

        //welstory 추가 정보
        final WelstoryDeliveryInfo welstoryDeliveryInfo = delivery.getWelstoryDeliveryInfo();
        if(Objects.nonNull(welstoryDeliveryInfo)){
            if(Objects.nonNull(welstoryDeliveryInfo.getProductQr()))
                this.productQr = welstoryDeliveryInfo.getProductQr();

            if(Objects.nonNull(welstoryDeliveryInfo.getBusinessCardQr()))
                this.businessCardQr = welstoryDeliveryInfo.getBusinessCardQr();

            if(Objects.nonNull(welstoryDeliveryInfo.getBusinessCard()))
                this.businessCard = welstoryDeliveryInfo.getBusinessCard();
        }

        // 상품 주문 리스트
        List<OrderItemDTO> orderList = new ArrayList<>();
        final List<DeliveryProduct> deliveryProducts = delivery.getDeliveryProducts();
        if (CollectionUtils.isNotEmpty(deliveryProducts)) {
            deliveryProducts.forEach(dp -> {
                        final Product product = productService.getProductByProductBarcodeAndItemCode(dp.getProductBarcode(), dp.getItemCode(), "").orElse(null);
                        ProductAllocation productAllocation = null;

                        if (Objects.nonNull(product)) {
                            productAllocation = productService.getLastProductAllocation(delivery.getProjectId(), riderId, product.getProductId());
                        }

                        final OrderItemDTO order = OrderItemDTO.builder()
                                .itemCode(dp.getItemCode())
                                .productBarcode(dp.getProductBarcode())
                                .itemName(dp.getItemName())
                                .orderQuantity(dp.getOrderQuantity())
                                .storageType((Objects.nonNull(product) && Objects.nonNull(product.getStorageType())) ? product.getStorageType().toString() : null)
                                .boxNumber(Objects.nonNull(productAllocation) ? productAllocation.getBoxNumber() : null)
                                .build();

                        orderList.add(order);
                    });

            this.orderItemList = CollectionUtils.isNotEmpty(orderList) ? orderList : null;
        }
    }

    public static DeliveryDTO of(final ProjectDestinationSheetDTO projectDestinationSheetDTO,
                                 final ProductService productService) {

        boolean isOnDemand = false;
        List<OrderItemDTO> orderItemList = projectDestinationSheetDTO.getOrderItemList();
        if (CollectionUtils.isNotEmpty(orderItemList)) {
            orderItemList.forEach(order -> {
                final Product product = productService.getProductByProductBarcodeAndItemCode(order.getProductBarcode(), order.getItemCode(), "").orElse(null);
                if (Objects.nonNull(product)) {
                    order.setStorageType(Objects.nonNull(product.getStorageType()) ? product.getStorageType().toString() : null);
                }
            });

            isOnDemand = true;
        }

        final String qrBarCode = projectDestinationSheetDTO.getQrBarCode();
        final InspectionStatus inspectionStatus = DeliveryDTO.setDefaultInspectionStatus(qrBarCode, isOnDemand);
        final PickupVerificationStatus pickupVerificationStatus = DeliveryDTO.setDefaultPickupVerificationStatus(qrBarCode);
        final String productName = ProjectDestinationSheetDTO.convertProductName(orderItemList, projectDestinationSheetDTO.getProductName());

        return DeliveryDTO.builder()
                .deliveryMessage(projectDestinationSheetDTO.getViaNote())
                .deliveryType(projectDestinationSheetDTO.getViaType())
                .visitType(projectDestinationSheetDTO.getVisitType())
                .productQuantity(Optional.ofNullable(projectDestinationSheetDTO.getProductQuantity()).orElse(1))
                .productName(productName)
                .productWidth(projectDestinationSheetDTO.getProductWidth())
                .productLength(projectDestinationSheetDTO.getProductLength())
                .productHeight(projectDestinationSheetDTO.getProductHeight())
                .productWeight(projectDestinationSheetDTO.getProductWeight())
                .productSize(projectDestinationSheetDTO.getProductSize())
                .receiverName(projectDestinationSheetDTO.getViaName())
                .receiverPhoneNumber(projectDestinationSheetDTO.getViaPhone())
                .receiverRealPhoneNumber(projectDestinationSheetDTO.getRealPhoneNumber())
                .receiverOwner(projectDestinationSheetDTO.getViaOwner())
                .destinationAddress(AddressDTO.builder()
                    .base(projectDestinationSheetDTO.getViaAddress())
                    .detail(projectDestinationSheetDTO.getViaDetailAddress())
//                      .location(null)
//                          .zipCode(null)
                    .build())
//              .projectId(null)
                .userDefinedOrderNum(projectDestinationSheetDTO.getViaOrder())
                .deliveryStartTime(projectDestinationSheetDTO.getStartTime())
                .deliveryEndTime(projectDestinationSheetDTO.getEndTime())
                .duration(projectDestinationSheetDTO.getDuration())
                .groupName(projectDestinationSheetDTO.getGroupName())
                .customerOrderId(projectDestinationSheetDTO.getCustomerOrderId())
                .trackingNumber(projectDestinationSheetDTO.getTrackingNumber())
                .clientId(projectDestinationSheetDTO.getClientId())
                .customerProductImageUrls(DeliveryDTO.extractProductImageUrlsFromParentheses(projectDestinationSheetDTO.getCustomerProductImageUrl()))
                .productSize(projectDestinationSheetDTO.getProductSize())
                .inspectionStatus(inspectionStatus)
                .pickupQuantity(0L)
                .dropoffQuantity(0L)
                .isInspectionEnded(false)
                .pickupVerificationStatus(pickupVerificationStatus)
                .verificationQuantity(0L)
                .qrBarCode(qrBarCode)
                .developerField(projectDestinationSheetDTO.getDeveloperField())
                .orderItemList(orderItemList)
                .isOnDemand(false/*CollectionUtils.isNotEmpty(orderItemList)*/)
                .senderName(projectDestinationSheetDTO.getSenderName())
                .senderPhoneNumber(projectDestinationSheetDTO.getSenderPhoneNumber())
                .senderCompanyName(projectDestinationSheetDTO.getSenderCompanyName())
                .senderImageUrl(projectDestinationSheetDTO.getSenderImageUrl())
                .senderMemo(projectDestinationSheetDTO.getSenderMemo())
                .senderBaseAddress(projectDestinationSheetDTO.getSenderBaseAddress())
                .senderDetailAddress(projectDestinationSheetDTO.getSenderDetailAddress())
                .orderAmount(projectDestinationSheetDTO.getOrderAmount())
                .uniqueCode(projectDestinationSheetDTO.getUniqueCode())
                .warehouseCode(projectDestinationSheetDTO.getWarehouseCode())
                .deliveryCategory(projectDestinationSheetDTO.getDeliveryCategory())
                .deliveryTime(projectDestinationSheetDTO.getDeliveryTime())
                .joCd(projectDestinationSheetDTO.getJoCd())
                .whGubn(projectDestinationSheetDTO.getWhGubn())
                .storeCd(projectDestinationSheetDTO.getStoreCd())
                .storageType(projectDestinationSheetDTO.getStorageType())
                .customerServiceContact(projectDestinationSheetDTO.getCustomerServiceContact())
                .build();
    }

    public static DeliveryDTO of(final WebDeliveryDTO webDeliveryDTO,
                                 final ProductService productService) {

        //TODO 문제가 있는데 엑셀은 스트링인데 저장할때는 배열 형식으로 내려와서 뭔가 안맞는다. 그래서 1개일때만 처리하고 배열 일때는 생각해보자.
        String imageUrl = null;
        String[]  imageUrlList = webDeliveryDTO.getCustomerProductImageUrl();
        if (Objects.nonNull(imageUrlList) && imageUrlList.length == 1) {//1개일 때만 처리한다. 1개 이상일때는 null처리 된다. null일때 updateDelivery 되지 않아서 괜찮을 듯 하다.
            imageUrl = imageUrlList[0];
        }

        final List<OrderItemDTO> orderItemList = webDeliveryDTO.getOrderItemList();
        final String productName = ProjectDestinationSheetDTO.convertProductName(orderItemList, webDeliveryDTO.getProductName());

        boolean isOnDemand = false;
        if (CollectionUtils.isNotEmpty(orderItemList)) {
            orderItemList
                    .forEach(order -> {
                        final Product product = productService.getProductByProductBarcodeAndItemCode(order.getProductBarcode(), order.getItemCode(), "").orElse(null);
                        if (Objects.nonNull(product)) {
                            order.setStorageType(Objects.nonNull(product.getStorageType()) ? product.getStorageType().toString() : null);
                        }
                    });

            isOnDemand = true;
        }

        final String qrBarCode = webDeliveryDTO.getQrBarCode();
        final InspectionStatus inspectionStatus = DeliveryDTO.setDefaultInspectionStatus(qrBarCode, isOnDemand);
        final PickupVerificationStatus pickupVerificationStatus = DeliveryDTO.setDefaultPickupVerificationStatus(qrBarCode);

        return DeliveryDTO.builder()
                .deliveryMessage(webDeliveryDTO.getNote())
                .deliveryType(Optional.ofNullable(webDeliveryDTO.getDeliveryType()).orElse(DeliveryType.FINAL_DESTINATION))
                .visitType(webDeliveryDTO.getVisitType())
                .productQuantity(Optional.ofNullable(webDeliveryDTO.getProductQuantity()).orElse(1))
                .productName(productName)
                .productWidth(webDeliveryDTO.getProductWidth())
                .productLength(webDeliveryDTO.getProductLength())
                .productHeight(webDeliveryDTO.getProductHeight())
                .productWeight(webDeliveryDTO.getProductWeight())
                .productSize(webDeliveryDTO.getProductSize())
                .receiverName(webDeliveryDTO.getReceiverName())
                .receiverPhoneNumber(webDeliveryDTO.getReceiverPhoneNumber())
                .receiverOwner(webDeliveryDTO.getReceiverOwner())
                .destinationAddress(AddressDTO.builder().base(webDeliveryDTO.getBaseAddr())
                        .detail(webDeliveryDTO.getDetailAddr())
//                      .location(null)
//                          .zipCode(null)
                        .build())
//              .projectId(null)
                //.userDefinedOrderNum(webDeliveryDTO.getUserDefinedOrderNum())
                .deliveryStartTime(webDeliveryDTO.getStartTime())
                .deliveryEndTime(webDeliveryDTO.getEndTime())
                .duration(webDeliveryDTO.getDuration())
                .groupName(webDeliveryDTO.getGroupName())
                .customerOrderId(webDeliveryDTO.getCustomerOrderId() /* getOrgCode(userId, webDeliveryDTO.getCustomerOrderId()) */)
                .trackingNumber(webDeliveryDTO.getTrackingNumber())
                //.clientId(webDeliveryDTO.getClientId())
                .customerProductImageUrls(DeliveryDTO.extractProductImageUrlsFromParentheses(imageUrl))
                .productSize(webDeliveryDTO.getProductSize())
                .inspectionStatus(inspectionStatus)
                .pickupQuantity(0L)
                .dropoffQuantity(0L)
                .isInspectionEnded(false)
                .pickupVerificationStatus(pickupVerificationStatus)
                .verificationQuantity(0L)
                .qrBarCode(qrBarCode)
                //.developerField(webDeliveryDTO.getDeveloperField())
                .orderItemList(orderItemList)
                .isOnDemand(false/*CollectionUtils.isNotEmpty(orderItemList)*/)
                .splitNumber(webDeliveryDTO.getSplitNumber())
                .orderAmount(webDeliveryDTO.getOrderAmount())
                .uniqueCode(webDeliveryDTO.getUniqueCode())
                .warehouseCode(webDeliveryDTO.getWarehouseCode())
                .build();
    }

    public Long getRiderId() {

        return Objects.isNull(this.riderId) ? -1 : this.riderId;
    }

    /**
     * []으로 감싸져 있는 URL을 목록으로 추출한다.
     *
     * @param urls
     * @return
     */
    private static String[] extractProductImageUrlsFromParentheses(String urls) {

        if (Objects.isNull(urls)) {
            return null;
        }

        return String.join("", urls.split("\\s*\\[\\s*")).split("\\s*]\\s*");
    }

    /**
     * URL 문자열 배열을 하나의 문자열로 변환한다. (DB 저장용: AttributeConverter를 쓰면 더 보기 좋았을 것 같다.)
     *
     * @param urls
     * @return
     */
    public static String convertProductImageUrlsToSingleList(String[] urls) {

        if (Objects.isNull(urls)) {
            return null;
        }

        return Arrays.asList(urls)
                .stream()
                .map(url -> "[" + url + "]")
                .collect(Collectors.joining());
    }

    /**
     * QR/바코드에 따른 기본 검수상태 설정
     *
     * @param qrBarCode
     * @param isOnDemand
     * @return
     */
    public static InspectionStatus setDefaultInspectionStatus(final String qrBarCode,
                                                              final Boolean isOnDemand) {

        InspectionStatus inspectionStatus = InspectionStatus.UNINSPECTED;
        if (StringUtils.isBlank(qrBarCode) || isOnDemand) {
            inspectionStatus = InspectionStatus.NA;
        }

        return inspectionStatus;
    }

    /**
     * QR/바코드에 따른 기본 분류검증상태 설정
     *
     * @param qrBarCode
     * @return
     */
    public static PickupVerificationStatus setDefaultPickupVerificationStatus(final String qrBarCode) {

        PickupVerificationStatus pickupVerificationStatus = PickupVerificationStatus.UNVERIFIED;
        if (StringUtils.isBlank(qrBarCode)) {
            pickupVerificationStatus = PickupVerificationStatus.NA;
        }

        return pickupVerificationStatus;
    }

    @Override
    public String toString() {

        Map<String, Object> map = new HashMap<>();

        map.put("projectId", projectId);
        map.put("riderId", riderId);
        map.put("deliveryId", deliveryId);
        map.put("customerOrderId", customerOrderId);
        map.put("orderNum", orderNum);
        map.put("deliveryStatus", Optional.ofNullable(deliveryStatus).map(Enum::name).orElse(null));
        map.put("receiverName", receiverName);

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            log.error("DeliveryDTO: convert object to string error", e);
            return null;
        }
    }

}
