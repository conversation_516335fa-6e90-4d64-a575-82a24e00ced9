package com.logisteq.tms.delivery.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryCancellationDTO {

    @Schema(description = "배송 ID", example = "1411651")
    @NotNull
    @Positive
    private Long deliveryId;

}
