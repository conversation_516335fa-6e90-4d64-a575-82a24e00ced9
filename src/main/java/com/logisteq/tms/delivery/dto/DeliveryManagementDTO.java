package com.logisteq.tms.delivery.dto;

import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.Receiver;
import com.logisteq.tms.delivery.domain.suppl.DeliveryType;
import lombok.Getter;

import javax.validation.constraints.NotNull;

/**
 * 방문지 목록 (방문지 관리에 사용)
 *
 * <AUTHOR>
 */
@Getter
public class DeliveryManagementDTO {

    // 아이디
    private Long id;

    // 사용자 아이디
    private Long userId;

    // 받는 사람 이름
    private String receiverName;

    // 주소
    private String baseAddress;

    // 상세주소
    private String detailAddress;

    // 배송 타입
    private DeliveryType type;

    // 받는 사람 전화번호
    private String receiverPhoneNumber;

    public DeliveryManagementDTO(@NotNull final Delivery delivery) {
        this.id = delivery.getId();
        this.userId = delivery.getUserId();
        Receiver rcv = delivery.getDetail().getReceiver();
		if (rcv != null) {
			this.receiverName = rcv.getReceiverName();
			this.receiverPhoneNumber = rcv.getReceiverPhoneNumber();
		}
        this.baseAddress = delivery.getDetail().getDestinationAddress().getBase();
        this.detailAddress = delivery.getDetail().getDestinationAddress().getDetail();
        this.type = delivery.getType();
    }

}