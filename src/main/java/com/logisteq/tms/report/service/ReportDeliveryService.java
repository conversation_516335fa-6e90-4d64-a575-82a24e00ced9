package com.logisteq.tms.report.service;

import com.logisteq.tms.delivery.repository.DeliveryRepository;
import com.logisteq.tms.delivery.repository.dao.ReportDeliveryDailyCountDAO;
import com.logisteq.tms.delivery.repository.dao.ReportDeliveryMonthlyCountDAO;
import com.logisteq.tms.delivery.repository.dao.ReportDeliveryWeeklyCountDAO;
import com.logisteq.tms.delivery.repository.dao.ReportDeliveryYearlyCountDAO;
import com.logisteq.tms.delivery.service.DeliveryService;
import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.threeten.extra.YearWeek;

import javax.validation.constraints.NotNull;
import java.time.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 2024-04-26
 * @project tms-service
 */
@Slf4j
@Validated
@Service
public class ReportDeliveryService {

    private final UserService userService;
    private final DeliveryService deliveryService;
    private final DeliveryRepository deliveryRepository;

    @Autowired
    public ReportDeliveryService(final UserService userService,
                                 final DeliveryService deliveryService,
                                 final DeliveryRepository deliveryRepository) {

        this.userService = userService;
        this.deliveryService = deliveryService;
        this.deliveryRepository = deliveryRepository;
    }

    public Slice<ReportDeliveryDailyCountDAO> getDeliveryDailyCountPage(
            @NotNull final User user,
            final List<Long> filterDepartmentIdList,
            @NotNull final LocalDate fromDate,
            @NotNull final LocalDate toDate,
            @NotNull final Pageable pageable) {

        final Long userId = user.getUserId();
        final RoleType userRole = userService.getUserRoleByUser(user);

        final LocalDateTime fromDateTime = fromDate.atStartOfDay();
        final LocalDateTime toDateTime = toDate.atTime(LocalTime.MAX);
        if (fromDateTime.isAfter(toDateTime)) {
            return new SliceImpl<>(Collections.emptyList(), pageable, false);
        }

        Slice<ReportDeliveryDailyCountDAO> reportDeliveryDailyCountDTOSlice;

        if (RoleType.ROLE_ADMIN.equals(userRole)) {
            reportDeliveryDailyCountDTOSlice = deliveryRepository.findAdminDeliveryDailyCountPage(pageable, fromDateTime, toDateTime);
        } else if (RoleType.ROLE_ORG_ADMIN.equals(userRole) || RoleType.ROLE_COMMON.equals(userRole)) {
            final List<Project> authorizedProjectList = deliveryService.getAuthorizedProjectList(user, filterDepartmentIdList);
            if (CollectionUtils.isEmpty(authorizedProjectList)) {
                return new SliceImpl<>(Collections.emptyList(), pageable, false);
            }
            final List<Long> authorizedProjectIdList = authorizedProjectList.stream().map(Project::getId).collect(Collectors.toList());

            reportDeliveryDailyCountDTOSlice = deliveryRepository.findProjectDeliveryDailyCountPage(pageable, authorizedProjectIdList, fromDateTime, toDateTime);
        } else {
            reportDeliveryDailyCountDTOSlice = deliveryRepository.findUserDeliveryDailyCountPage(pageable, Arrays.asList(userId), fromDateTime, toDateTime);
        }

        return reportDeliveryDailyCountDTOSlice;
    }

    public Slice<ReportDeliveryWeeklyCountDAO> getDeliveryWeeklyCountPage(
            @NotNull final User user,
            final List<Long> filterDepartmentIdList,
            @NotNull final YearWeek fromWeek,
            @NotNull final YearWeek toWeek,
            @NotNull final Pageable pageable) {

        final Long userId = user.getUserId();
        final RoleType userRole = userService.getUserRoleByUser(user);

        final DayOfWeek startDayOfWeek = DayOfWeek.MONDAY;
        final DayOfWeek endDayOfWeek = DayOfWeek.SUNDAY;
        final LocalDateTime fromDateTime = fromWeek.atDay(startDayOfWeek).atStartOfDay();
        final LocalDateTime toDateTime = toWeek.atDay(endDayOfWeek).atTime(LocalTime.MAX);
        if (fromDateTime.isAfter(toDateTime)) {
            return new SliceImpl<>(Collections.emptyList(), pageable, false);
        }

        Slice<ReportDeliveryWeeklyCountDAO> reportDeliveryWeeklyCountDTOSlice;

        if (RoleType.ROLE_ADMIN.equals(userRole)) {
            reportDeliveryWeeklyCountDTOSlice = deliveryRepository.findAdminDeliveryWeeklyCountPage(pageable, fromDateTime, toDateTime);
        } else if (RoleType.ROLE_ORG_ADMIN.equals(userRole) || RoleType.ROLE_COMMON.equals(userRole)) {
            final List<Project> authorizedProjectList = deliveryService.getAuthorizedProjectList(user, filterDepartmentIdList);
            if (CollectionUtils.isEmpty(authorizedProjectList)) {
                return new SliceImpl<>(Collections.emptyList(), pageable, false);
            }
            final List<Long> authorizedProjectIdList = authorizedProjectList.stream().map(Project::getId).collect(Collectors.toList());

            reportDeliveryWeeklyCountDTOSlice = deliveryRepository.findProjectDeliveryWeeklyCountPage(pageable, authorizedProjectIdList, fromDateTime, toDateTime);
        } else {
            reportDeliveryWeeklyCountDTOSlice = deliveryRepository.findUserDeliveryWeeklyCountPage(pageable, Arrays.asList(userId), fromDateTime, toDateTime);
        }

        return reportDeliveryWeeklyCountDTOSlice;
    }

    public Slice<ReportDeliveryMonthlyCountDAO> getDeliveryMonthlyCountPage(
            @NotNull final User user,
            final List<Long> filterDepartmentIdList,
            @NotNull final YearMonth fromMonth,
            @NotNull final YearMonth toMonth,
            @NotNull final Pageable pageable) {

        final Long userId = user.getUserId();
        final RoleType userRole = userService.getUserRoleByUser(user);

        final LocalDateTime fromDateTime = fromMonth.atDay(1).atStartOfDay();
        final LocalDateTime toDateTime = toMonth.atEndOfMonth().atTime(LocalTime.MAX);
        if (fromDateTime.isAfter(toDateTime)) {
            return new SliceImpl<>(Collections.emptyList(), pageable, false);
        }

        Slice<ReportDeliveryMonthlyCountDAO> reportDeliveryMonthlyCountDTOSlice;

        if (RoleType.ROLE_ADMIN.equals(userRole)) {
            reportDeliveryMonthlyCountDTOSlice = deliveryRepository.findAdminDeliveryMonthlyCountPage(pageable, fromDateTime, toDateTime);
        } else if (RoleType.ROLE_ORG_ADMIN.equals(userRole) || RoleType.ROLE_COMMON.equals(userRole)) {
            final List<Project> authorizedProjectList = deliveryService.getAuthorizedProjectList(user, filterDepartmentIdList);
            if (CollectionUtils.isEmpty(authorizedProjectList)) {
                return new SliceImpl<>(Collections.emptyList(), pageable, false);
            }
            final List<Long> authorizedProjectIdList = authorizedProjectList.stream().map(Project::getId).collect(Collectors.toList());

            reportDeliveryMonthlyCountDTOSlice = deliveryRepository.findProjectDeliveryMonthlyCountPage(pageable, authorizedProjectIdList, fromDateTime, toDateTime);
        } else {
            reportDeliveryMonthlyCountDTOSlice = deliveryRepository.findUserDeliveryMonthlyCountPage(pageable, Arrays.asList(userId), fromDateTime, toDateTime);
        }

        return reportDeliveryMonthlyCountDTOSlice;
    }

    public Slice<ReportDeliveryYearlyCountDAO> getDeliveryYearlyCountPage(
            @NotNull final User user,
            final List<Long> filterDepartmentIdList,
            @NotNull final Year fromYear,
            @NotNull final Year toYear,
            @NotNull final Pageable pageable) {

        final Long userId = user.getUserId();
        final RoleType userRole = userService.getUserRoleByUser(user);

        final LocalDateTime fromDateTime = fromYear.atMonth(1).atDay(1).atStartOfDay();
        final LocalDateTime toDateTime = toYear.atMonth(12).atEndOfMonth().atTime(LocalTime.MAX);
        if (fromDateTime.isAfter(toDateTime)) {
            return new SliceImpl<>(Collections.emptyList(), pageable, false);
        }

        Slice<ReportDeliveryYearlyCountDAO> reportDeliveryYearlyCountDTOSlice;

        if (RoleType.ROLE_ADMIN.equals(userRole)) {
            reportDeliveryYearlyCountDTOSlice = deliveryRepository.findAdminDeliveryYearlyCountPage(pageable, fromDateTime, toDateTime);
        } else if (RoleType.ROLE_ORG_ADMIN.equals(userRole) || RoleType.ROLE_COMMON.equals(userRole)) {
            final List<Project> authorizedProjectList = deliveryService.getAuthorizedProjectList(user, filterDepartmentIdList);
            if (CollectionUtils.isEmpty(authorizedProjectList)) {
                return new SliceImpl<>(Collections.emptyList(), pageable, false);
            }
            final List<Long> authorizedProjectIdList = authorizedProjectList.stream().map(Project::getId).collect(Collectors.toList());

            reportDeliveryYearlyCountDTOSlice = deliveryRepository.findProjectDeliveryYearlyCountPage(pageable, authorizedProjectIdList, fromDateTime, toDateTime);
        } else {
            reportDeliveryYearlyCountDTOSlice = deliveryRepository.findUserDeliveryYearlyCountPage(pageable, Arrays.asList(userId), fromDateTime, toDateTime);
        }

        return reportDeliveryYearlyCountDTOSlice;
    }

}
