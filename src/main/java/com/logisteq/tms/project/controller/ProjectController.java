package com.logisteq.tms.project.controller;

import com.logisteq.common.exception.CustomException;
import com.logisteq.common.exception.InvalidParameterException;
import com.logisteq.common.service.TrackAsyncService;
import com.logisteq.common.util.DateTimeUtil;
import com.logisteq.tms.address.domain.Address;
import com.logisteq.tms.address.service.AddressService;
import com.logisteq.tms.common.security.WebUserDetails;
import com.logisteq.tms.delivery.dto.DeliveryDTO;
import com.logisteq.tms.delivery.service.DeliveryService;
import com.logisteq.tms.external.efoodist.constant.EfoodistConstant;
import com.logisteq.tms.privacy.dto.PrivacyRecordDto;
import com.logisteq.tms.privacy.service.PrivacyRecordService;
import com.logisteq.tms.privacy.suppl.PrivacyDataType;
import com.logisteq.tms.privacy.suppl.PrivacyRecordType;
import com.logisteq.tms.privacy.suppl.PrivacyUsageType;
import com.logisteq.tms.product.service.ProductService;
import com.logisteq.tms.project.constant.ApiUrl;
import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.domain.suppl.ProjectAttribute;
import com.logisteq.tms.project.domain.suppl.ProjectCreateFrom;
import com.logisteq.tms.project.domain.suppl.ProjectStatus;
import com.logisteq.tms.project.dto.*;
import com.logisteq.tms.project.dto.excel.leftpanel.ProjectDestinationSheetDTO;
import com.logisteq.tms.project.dto.excel.leftpanel.ProjectWorkbookDTO;
import com.logisteq.tms.project.dto.web.WebProjectDTO;
import com.logisteq.tms.project.dto.web.WebRiderDTO;
import com.logisteq.tms.project.service.ProjectBasicService;
import com.logisteq.tms.project.service.ProjectExcelService;
import com.logisteq.tms.project.service.ProjectService;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.domain.RiderOrgStatus;
import com.logisteq.tms.rider.domain.RiderProjectSetting;
import com.logisteq.tms.rider.domain.RiderRecentSetting;
import com.logisteq.tms.rider.dto.RiderDTO;
import com.logisteq.tms.rider.service.RiderProjectSettingService;
import com.logisteq.tms.rider.service.RiderRecentSettingService;
import com.logisteq.tms.rider.service.RiderService;
import com.logisteq.tms.rider.service.RiderWorkCompletionService;
import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.service.DepartmentService;
import com.logisteq.tms.user.service.GroupService;
import com.logisteq.tms.user.service.OrganizationService;
import com.logisteq.tms.user.service.UserService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Tag(name = "Project")
@RestController
@Validated
@Slf4j
public class ProjectController {

    private static final String REQ_DATE_FORMAT = "yyyy-MM-dd";

    private final ProjectBasicService projectBasicService;
    private final ProjectExcelService projectExcelService;
    private final ProjectService projectService;
    private final RiderService riderService;
    private final UserService userService;
    private final OrganizationService organizationService;
    private final DepartmentService departmentService;
    private final DeliveryService deliveryService;
    private final RiderWorkCompletionService riderWorkCompletionService;
    private final AddressService addressService;
    private final ProductService productService;
    private final RiderProjectSettingService riderProjectSettingService;
    private final RiderRecentSettingService riderRecentSettingService;
    private final GroupService groupService;
    private final TrackAsyncService trackAsyncService;
    private final PrivacyRecordService privacyRecordService;

    private final boolean projectAutoCreateSchedulerEnabled;
    private final boolean departmentProjectAutoCreateSchedulerEnabled;

    @Autowired
    public ProjectController(final ProjectBasicService projectBasicService,
                             final ProjectExcelService projectExcelService,
                             final ProjectService projectService,
                             final RiderService riderService,
                             final UserService userService,
                             final OrganizationService organizationService,
                             final DepartmentService departmentService,
                             final DeliveryService deliveryService,
                             final RiderWorkCompletionService riderWorkCompletionService,
                             final AddressService addressService,
                             final ProductService productService,
                             final RiderProjectSettingService riderProjectSettingService,
                             final RiderRecentSettingService riderRecentSettingService,
                             final GroupService groupService,
                             final TrackAsyncService trackAsyncService,
                             final PrivacyRecordService privacyRecordService,
                             @Value("${scheduler.project-auto-create.enabled: true}") final boolean projectAutoCreateSchedulerEnabled,
                             @Value("${scheduler.department-project-auto-create.enabled: true}") final boolean departmentProjectAutoCreateSchedulerEnabled) {

        this.projectBasicService = projectBasicService;
        this.projectExcelService = projectExcelService;
        this.projectService = projectService;
        this.riderService = riderService;
        this.userService = userService;
        this.organizationService = organizationService;
        this.departmentService = departmentService;
        this.deliveryService = deliveryService;
        this.riderWorkCompletionService = riderWorkCompletionService;
        this.addressService = addressService;
        this.productService = productService;
        this.riderProjectSettingService = riderProjectSettingService;
        this.riderRecentSettingService = riderRecentSettingService;
        this.groupService = groupService;
        this.trackAsyncService = trackAsyncService;
        this.privacyRecordService = privacyRecordService;

        this.projectAutoCreateSchedulerEnabled = projectAutoCreateSchedulerEnabled;
        if (projectAutoCreateSchedulerEnabled) {
            log.info("autoCreateProjectScheduler is enabled.");
        } else {
            log.warn("autoCreateProjectScheduler is not enabled.");
        }

        this.departmentProjectAutoCreateSchedulerEnabled = departmentProjectAutoCreateSchedulerEnabled;
        if (departmentProjectAutoCreateSchedulerEnabled) {
            log.info("autoCreateDepartmentProjectScheduler is enabled.");
        } else {
            log.warn("autoCreateDepartmentProjectScheduler is not enabled.");
        }
    }

    /**
     * @param projectDTO
     * @return
     */
    @Hidden
    @PostMapping(path = ApiUrl.PROJECT)//[WMS-API]
    public ResponseEntity<ProjectDTO> createProjectFromDTO(@Valid @RequestBody final ProjectCustomDTO projectDTO) {

        final String projectName = projectDTO.getProjectName();
        log.info("[WMS] createProjectFromDTO {}", projectName);

        Optional<User> user = Optional.<User>empty(); //빈 객체로 초기화

        try {
            user = userService.getUserByEmail(projectDTO.getEmail());
            final Long userId = user.get().getUserId();

            // [푸디스트] 배송지없이 프로젝트 생성시 이미 존재하는 프로젝트면 중복 생성 방지
            if (organizationService.isCodeNameUser(userId, EfoodistConstant.EFOODIST_ORG_CODE_NAME) && CollectionUtils.isEmpty(projectDTO.getDestinations())) {
                final List<Project> projects = projectBasicService.getProjectsByNameAndUserIdAndDeleted(projectName, userId, false);
                if (CollectionUtils.isNotEmpty(projects)) {
                    final Project existingProject = projects.stream()
                            .filter(p -> projectName.equals(p.getName()))
                            .findAny()
                            .orElse(null);

                    if (Objects.nonNull(existingProject)) {
                        throw new CustomException(HttpStatus.BAD_REQUEST, "이미 존재하는 프로젝트입니다. (projectName: " + projectName + ")");
                    }
                }
            }

            //cutoffTime (배송 요청 마감 시간)
            LocalDateTime cutOffTime = null;
            if (Objects.nonNull(projectDTO.getCutoffTime())) {
                cutOffTime = DateTimeUtil.toLocalDateTimeFromDateTimeStr(projectDTO.getCutoffTime());
                if (Objects.isNull(cutOffTime)) {
                    throw new CustomException(HttpStatus.BAD_REQUEST, "잘못된 날짜 형식입니다. (cutoffTime: " + projectDTO.getCutoffTime() + ")");
                }
            }
            List<ProjectDestinationSheetDTO> reqDeliveries = projectDTO.getDestinations();
            projectDTO.setDestinations(null);
            Project project = projectService.createProjectFromDTO(userId,
                    projectDTO,
                    cutOffTime,
                    true);
            ProjectDTO response = ProjectDTO.parseFromProject(project);

            List<ProjectDestinationSheetDTO> geoCodingFails = new ArrayList<>();
            List<ProjectDestinationSheetDTO> geoCodingSuccess = new ArrayList<>();
            final List<Address> addressList = projectService.getAddressesList(reqDeliveries, geoCodingSuccess, geoCodingFails, null, null);
            addressService.saveAddressListAndReturnFailedList(addressList);//나온 결과를 DB에 저장한다

            if (CollectionUtils.isNotEmpty(reqDeliveries)) {
                for (int i = 0; i < reqDeliveries.size(); i++) {
                    final Address address = addressList.get(i);
                    final ProjectDestinationSheetDTO destination = reqDeliveries.get(i);
                    if (AddressService.isValidAddress(address)) {
                        final DeliveryDTO deliveryDto = DeliveryDTO.of(destination, this.productService);
                        deliveryService.saveDeliveryWithAddress(project.getUser().getUserId(), project.getId(), deliveryDto, address);
                    }
                }

                //다시 attribute 조정함
                if (reqDeliveries.size() > 0) {
                    projectBasicService.updateProjectAttribute(project, ProjectAttribute.builder()
                            .isClusterDone(false)
                            .isFirstRoutingDone(false)
                            .build());
                }
            }

            if (CollectionUtils.isNotEmpty(geoCodingFails)) {
                response.setGeoCodingFails(geoCodingFails);
                return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT).body(response);
            } else {
                return ResponseEntity.ok().body(response);
            }
        } catch (NoSuchElementException e) {
            log.error("user(" + projectDTO.getEmail() + ")가 존재 하지 않습니다..");
            throw new CustomException(HttpStatus.BAD_REQUEST, "user(" + projectDTO.getEmail() + ")가 존재 하지 않습니다.", false);
        }
    }

    /**
     * @param project
     * @return
     */
    @Hidden
    @PostMapping(path = ApiUrl.PROJECT_ONDEMAND)//[WMS-API]
    public ResponseEntity<ProjectDTO> createProjectFromDTO(@Valid @RequestBody final ProjectCustomOndemandDTO project) {

        log.info("[WMS] createProjectFromDTO OnDemand  {} ", project.getProjectName());

        Optional<User> user = Optional.<User>empty(); //빈 객체로 초기화

        try {
            user = userService.getUserByEmail(project.getEmail());

            //cutoffTime (배송 요청 마감 시간)
            LocalDateTime cutOffTime = null;
            if (project.getCutoffTime() != null) {
                cutOffTime = DateTimeUtil.toLocalDateTimeFromDateTimeStr(project.getCutoffTime());
                if (cutOffTime == null) {
                    throw new CustomException(HttpStatus.BAD_REQUEST, "잘못된 날짜 형식 입니다. ( cutoffTime : " + project.getCutoffTime() + ")");
                }
            }

//			Map<String, Object> result = deliveryService.geoCodingValidationCheck( project.getDestinations() );
//			List<ProjectDestinationDetailsDTO> geoCodingFails = (List<ProjectDestinationDetailsDTO>) result.get("responseError");
//			List<ProjectDestinationDetailsDTO> responseSuccess = (List<ProjectDestinationDetailsDTO>) result.get("responseSuccess");

//			project.setDestinations(responseSuccess);
            Project prj = projectService.createProjectFromDTO(user.get().getUserId(),
                    project,
                    cutOffTime);

            ProjectDTO response = ProjectDTO.parseFromProject(prj);

            return ResponseEntity.ok().body(response);

//			if (CollectionUtils.isNotEmpty(geoCodingFails)) {
//				response.setGeoCodingFails(geoCodingFails);
//				return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT).body(  response );
//			} else  {
//				return ResponseEntity.ok().body(response);
//			}
        } catch (NoSuchElementException e) {

            log.error("user(" + project.getEmail() + ")가 존재 하지 않습니다..");
            throw new CustomException(HttpStatus.BAD_REQUEST, "user(" + project.getEmail() + ")가 존재 하지 않습니다.", false);
        }
    }

    private static String getFullAddress(@NotBlank final String base,
                                         @Nullable final String detail) {

        final String baseAddr = Optional.ofNullable(base).map(String::trim).orElse("");
        final String detailAddr = Optional.ofNullable(detail).map(String::trim).orElse("");
        final String fullAddr = (baseAddr + " " + detailAddr).trim();

        return fullAddr;
    }

    /**
     * 빈 프로젝트 로딩
     *
     * @param userInfo
     * @return
     */
    @Hidden
    @GetMapping(path = ApiUrl.PROJECT_EMPTY_PROJECT)
    public ProjectDTO loadEmptyProjectOrCreateIfNotExists(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo) {

        return ProjectDTO.parseFromProject(projectService.loadEmptyProjectOrCreateIfNotExists(userInfo.getId(), true));
    }

    /**
     * CSV 파일들로 프로젝트 생성하거나 생성된 프로젝트에 기사/방문지 추가
     *
     * @param userInfo
     * @param projectId   null이나 0이면 프로젝트 생성, 그렇지 않으면 기존 프로젝트에 추가
     * @param selectedTab 기사탭(TAB_RIDER) or 방문지탭(TAB_DELIVERY)
     * @param projectName 프로젝트를 생성할때 사용할 이름
     * @param csvFiles
     * @return
     * @throws Exception
     */
    @Hidden
    @PostMapping(value = ApiUrl.PROJECT_CSV, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ProjectDTO createProjectFromCsv(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                           @RequestParam(required = false) @Nullable @Positive final Long projectId,
                                           @RequestParam(required = false) @NotNull final Integer selectedTab,
                                           @RequestParam(required = false) @Nullable final String projectName,
                                           @RequestPart("csvFiles") @NotEmpty final List<MultipartFile> csvFiles) throws Exception {

        final Project project = projectService.createProjectFromCsv(userInfo.getId(),
                userInfo.getName(),
                projectId,
                selectedTab,
                projectName,
                csvFiles);

        return ProjectDTO.parseFromProject(project);
    }

    /**
     * 엑셀 파일로 프로젝트 생성하거나 생성된 프로젝트에 기사/방문지 추가
     *
     * @param userInfo
     * @param projectId   null이나 0이면 프로젝트 생성, 그렇지 않으면 기존 프로젝트에 추가
     * @param selectedTab 기사탭(TAB_RIDER) or 방문지탭(TAB_DELIVERY)
     * @param projectName 프로젝트를 생성할때 사용할 이름
     * @param excelFile
     * @return
     * @throws Exception
     */
    @Hidden
    @PostMapping(value = ApiUrl.PROJECT_EXCEL, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ProjectDTO createProjectFromExcel(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                             @RequestParam(required = false) @Nullable @Positive final Long projectId,
                                             @RequestParam(required = false) @NotNull final Integer selectedTab,
                                             @RequestParam(required = false) @Nullable final String projectName,
                                             @RequestParam("excelFile") @NotNull final MultipartFile excelFile) throws Exception {

        final Project project = projectService.createProjectFromExcel(userInfo.getId(),
                projectId,
                selectedTab,
                projectName,
                excelFile);

        return ProjectDTO.parseFromProject(project);
    }

    /**
     * 프로젝트 복사
     */
    @Hidden
    @GetMapping(value = ApiUrl.PROJECT_COPY_PROJECT)
    public void createCopyNormalProject(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                        @RequestParam @Positive final Long projectId) {

        // ROLE_ORG_ADMIN 와 ROLE_COMMON
        final RoleType role = userService.getUserRoleByUserId(userInfo.getId());
        Project project;

        if (RoleType.ROLE_ORG_ADMIN.equals(role)) {
            project = projectBasicService.getProjectByIdOrThrowException(projectId);
        } else {
            project = projectBasicService.getProjectByIdAndUserId(projectId, userInfo.getId());
        }

        ProjectWorkbookDTO projectWorkbookDTO = projectExcelService.makeProjectWorkbookDTO(projectId, role, userInfo.getId(), false, false, true);
        log.info("createCopyNormalProject: {} riders, {} deliveries",
                CollectionUtils.size(projectWorkbookDTO.getRiders()),
                CollectionUtils.size(projectWorkbookDTO.getDestinations()));

        String copyProject = projectWorkbookDTO.getProjectName() + "_copy";

        projectWorkbookDTO.setProjectName(copyProject);

        projectService.createCopyNormalProject(userInfo.getId(), project.getCallbackUri(), projectWorkbookDTO, ProjectCreateFrom.COPY);
    }

    /**
     * 프로젝트 이관 : 기사 전송이전 배송지와 기사를 새로운 프로젝트 만듬
     */
    @Hidden
    @GetMapping(value = ApiUrl.PROJECT_TRANSFER_PROJECT)
    public void createTransferNormalProject(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                            @RequestParam @Positive final Long projectId) {

        // ROLE_ORG_ADMIN 와 ROLE_COMMON
        final RoleType role = userService.getUserRoleByUserId(userInfo.getId());

        Project preProject;

        if (RoleType.ROLE_ORG_ADMIN.equals(role)) {
            preProject = projectBasicService.getProjectByIdOrThrowException(projectId);
        } else {
            preProject = projectBasicService.getProjectByIdAndUserId(projectId, userInfo.getId());
        }

        if (preProject == null)
            throw new CustomException(HttpStatus.NOT_FOUND, "프로젝트아이디 " + projectId + "에 해당하는 프로젝트를 찾을 수 없습니다.", false);

        final String copyProject = preProject.getName() + "_transfer";

        // 전송 되지 않은 기사의 정보를 가져옴
        final List<Rider> transferRiders = riderService.getTransferRiders(preProject.getId(), preProject, false);

        if (transferRiders.size() == 0)
            throw new CustomException(HttpStatus.NOT_FOUND, "이관할 riders 배송지가 없습니다.", false);

        //이관할 배송지가 있을 경우에만 프로젝트 만듬
        final Project newProject = projectService.saveTempProject(userInfo.getId(), ProjectDTO.builder().name(copyProject).build(), false);

        //신규 프로젝트의 attribute를 저장함.
        projectBasicService.updateProjectAttribute(newProject.getId(), ProjectAttribute.builder()
                .clusterRule(preProject.getProjectAttribute().getClusterRule())
                .createFrom(ProjectCreateFrom.TRANSFER)
                .isClusterDone(true)
                .isFirstRoutingDone(true)
                .isRouteEnabled(false)
                .isReadOnly(false)
                .isSendingRiderEnabled(true)
                .isRouteExecute(true)
                .projectLoadingMode(preProject.getProjectAttribute().getProjectLoadingMode())
                .build());

        // rider setting , delivery 값 변경
        if (transferRiders.size() > 0) {
            transferRiders.forEach(rider -> {
                //rider project setting 변경함
                riderProjectSettingService.changedRiderProjectSetting(rider.getId(), preProject.getId(), newProject.getId());

                //rider recent setting 변경함
                final RiderRecentSetting rrs = riderRecentSettingService.updateRiderRecentSettingWithProjectId(rider, newProject.getId());

                //rider project ids 변경함
                List<Long> projectIds = rider.getProjectIds();
                int indexOf = projectIds.indexOf(preProject.getId());
                projectIds.set(indexOf, newProject.getId());
                riderService.updateRiderInfo(rider);

                //rider group project 변경
                groupService.changedProjectIdGroup(rider.getId(), preProject.getId(), newProject.getId());

                //delivery 변경
                deliveryService.changedProjectIdDeliveries(rider.getId(), preProject.getId(), newProject.getId());
            });
        }
    }

    /**
     * 프로젝트 이름 변경
     */
    @Hidden
    @PutMapping(value = ApiUrl.UPDATE_PROJECT_NAME)
    public boolean updateProjectName(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                     @PathVariable final Long projectId,
                                     @RequestBody final ProjectDTO projectDTO) {

        return projectService.updateProjectName(userInfo.getId(), projectId, projectDTO);
    }

    /**
     * 프로젝트 실행일 설정
     *
     * @param userInfo
     * @param projectId
     * @param projectDTO
     */
    @Hidden
    @PutMapping(value = ApiUrl.PROJECT_INFO)
    public boolean updateProjectItem(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                     @PathVariable @Positive final Long projectId,
                                     @RequestBody final ProjectDTO projectDTO) {

        // 실행시 OrderNumber가 없을 경우에 대비하여 추가한 기능
        // 경로 생성 없이 바로 기사에게 전송할때 필요함.
        if (projectDTO.getEffectiveDateTime() != null) {
            deliveryService.setAutoOrderNumIndexDeliveryAllocation(projectId);
        }

        return projectService.updateProjectItem(userInfo.getId(), projectId, projectDTO);
    }

    @Hidden
    @GetMapping(value = ApiUrl.PROJECT)
    public Page<ProjectManagementInfoDTO> getProjectsForManagement(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                                   @PageableDefault(size = 10, sort = "createAt", direction = Sort.Direction.DESC) final Pageable pageable,
                                                                   @RequestParam(required = false) final List<ProjectStatus> projectStatusList,
                                                                   @RequestParam(required = false) final List<Long> departmentIdList,
                                                                   @RequestParam(required = false) final String keyword,
                                                                   @RequestParam(required = false) @DateTimeFormat(pattern = REQ_DATE_FORMAT) final LocalDateTime fromDateTime,
                                                                   @RequestParam(required = false) @DateTimeFormat(pattern = REQ_DATE_FORMAT) final LocalDateTime toDateTime) {

        if (userInfo == null) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "인증되지 않는 사용자 입니다.", false);
        }

        final RoleType userRole = userService.getUserRoleByUserId(userInfo.getId());

        return projectService.getProjectsForManagement(userInfo.getId(), projectStatusList, departmentIdList, keyword, userRole, pageable, fromDateTime, toDateTime);
    }

    /**
     * 프로젝트들 삭제 API (프로젝트 관리에 사용)
     *
     * @param userInfo
     * @param projectIdList
     */
    @Hidden
    @DeleteMapping(value = ApiUrl.PROJECT, params = {"type=manage"})
    public void deleteProjectsForManagement(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                            @RequestParam @NotEmpty final List<Long> projectIdList) {

        final RoleType userRole = userService.getUserRoleByUserId(userInfo.getId());

        final List<Project> deletedProjectList = projectService.deleteProjectsForManagement(userInfo.getId(), userRole, projectIdList);

        final List<Long> deletedProjectIdList = deletedProjectList.stream()
                .map(Project::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(deletedProjectIdList)) {
            // #3294 Async하게 수정
            trackAsyncService.deleteByProjectIdAsync(deletedProjectIdList);
//            try {
//                trackServiceClient.deleteByProjectId(deletedProjectIdList);
//            } catch (Exception e) {
//                log.error("프로젝트아이디 {}의 위치정보 삭제 요청 시 오류 발생: {}", deletedProjectIdList, e.toString());
//            }
        }
    }

    /*
     * 기사의 마지막 프로젝트가 진행중일때 다시 프로젝트에 할당 되는 API
     */
    @Hidden
    @PostMapping(path = ApiUrl.PROJECT_REINSERT, params = {"riderId"})
    public RiderProjectDTO setReInsertProjectByRiderId(@RequestParam final Long riderId) {
        final Optional<Rider> rd = riderService.getRiderById(riderId);

        if (!rd.isPresent()) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "배송기사가 존재하지 않습니다.", false);
        }

        final Rider rider = rd.get();

        RiderProjectSetting riderProjectSetting = riderProjectSettingService.getLastProjectSettingByRiderId(riderId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "참여한 프로젝트가 없습니다.", false));

        Project project = projectBasicService.getProjectById(riderProjectSetting.getProjectId());

        if (project.getStatus() == ProjectStatus.IN_PROGRESS || project.getStatus() == ProjectStatus.SCHEDULED) {
            final Long projectId = project.getId();
            riderService.deleteByProjectStatus(rider, projectId);
            return RiderProjectDTO.of(project, projectService.getRiderProjectSetting(projectId, riderId), organizationService.getOrganizationByProject(project));
        } else
            throw new CustomException(HttpStatus.FORBIDDEN, "최신 프로젝트가 완료 되었습니다.", false);
    }

    /**
     * 기사의 프로젝트 목록 조회
     *
     * @param riderId 기사 아이디
     * @return 프로젝트 목록
     */
    @Operation(summary = "기사의 프로젝트 목록 조회", description = "기사의 프로젝트 목록을 조회한다.")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "OK")
    })
    @GetMapping(path = ApiUrl.PROJECT, params = {"riderId"})
    public List<RiderProjectDTO> getProjectListByRiderId(@RequestParam final Long riderId) {

        // 기사 & 프로젝트 IDs 조회
        final Rider rider = riderService.getRider(riderId);
        final List<Long> projectIds = rider.getProjectIds();

        // 기사의 업무완료된 projectId 목록
        final List<Long> workCompletedProjectIds = riderWorkCompletionService.getProjectIdListByRiderIdAndIsWorkCompleted(riderId, true);

        // 기사의 업무완료되지 않은 projectId 목록
        final List<Long> workNotCompletedProjectIds = ListUtils.subtract(ListUtils.emptyIfNull(projectIds),
                ListUtils.emptyIfNull(workCompletedProjectIds));

        // 종료(완료)되지 않고 관제중 프로젝트 목록 조회
        final List<Project> projects = projectService.getActiveProjects(workNotCompletedProjectIds);

        final List<RiderProjectSetting> riderProjectSettingList = riderProjectSettingService.loadProjectSettings(riderId, workNotCompletedProjectIds);

        // 응답
        final List<RiderProjectDTO> riderProjectDTOList = projects.stream()
                .map(p -> Pair.of(p, IterableUtils.find(riderProjectSettingList, rps -> p.getId().equals(rps.getProjectId()))))
                .filter(p -> Objects.nonNull(p.getRight()) && Objects.nonNull(p.getRight().getProjectPushedAt()))
                .map(p -> RiderProjectDTO.of(p.getLeft(), p.getRight(), organizationService.getOrganizationByProject(p.getLeft())))
                .sorted(Comparator.comparing(RiderProjectDTO::getProjectPushedAt).reversed())
                .collect(Collectors.toList());

        log.info("getProjectListByRiderId - riderId: {}, projectIdList: {}",
                riderId, riderProjectDTOList.stream().map(RiderProjectDTO::getId).collect(Collectors.toList()));


        final List<RiderOrgStatus> riderOrgStatusList = rider.getOrgStatusList();
        for ( RiderOrgStatus status : riderOrgStatusList) {
            if(!status.getIsDeleted()) {
                privacyRecordService.saveRecord(
                        PrivacyRecordDto.builder()
                                .recordType(PrivacyRecordType.RIDER)
                                .orgId(status.getOrgId())
                                .type(PrivacyUsageType.ACCESS)
                                .dataType(PrivacyDataType.NAME_MOBILE)
                                .acquisitionChannel("MOBILE")
                                .func("getProjectListByRiderId")
                                .riderId(riderId)
                                .build());
            }
        }

        return riderProjectDTOList;
    }

    @Hidden
    @GetMapping(value = ApiUrl.PROJECT_INFO)
    public WebProjectDTO getProjectInfo(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                        @PathVariable final Long projectId) {

        RoleType userRole = userService.getUserRoleByUserId(userInfo.getId());
//		return projectService.getProjectInfo(userInfo.getId(), userRole, projectId);
        WebProjectDTO wpd = projectService.getProjectInfo(userInfo.getId(), userRole, projectId);

        return wpd;
    }

    //	@Scheduled(cron = "0 0 5 * * *")    // at five everyday
    public void deleteProjectPeriodically() {

        final List<Organization> organizationList = organizationService.getListOrganization();
        final LocalDate now = LocalDate.now();

        for (final Organization organization : organizationList) {

            final Integer preservationDate = organization.getPreservationDate();

            if (Objects.nonNull(preservationDate) && preservationDate > 0) {

                final List<User> userList = userService.getUserByOrganization(organization.getId());

                if (CollectionUtils.isNotEmpty(userList)) {

                    final LocalDateTime baseDateTime = now.minusDays(preservationDate).atStartOfDay();
                    final List<Project> projectList = projectService.getProjectsByCreateAtBeforeAndUserIn(baseDateTime, userList);

                    for (final Project project : projectList) {

                        log.info("{}에 생성된 '{}' 고객사의 '{}' 프로젝트(아이디 {})가 보존일수 {}일을 넘어 영구삭제합니다.",
                                project.getCreateAt(),
                                organization.getOrganizationName(),
                                project.getName(),
                                project.getId(),
                                preservationDate);

                        try {
                            projectService.deleteByProjectId(project.getId());
                        } catch (Exception e) {
                            log.error("영구삭제 중에 예외 발생했습니다. {}", e.getMessage());
                            e.printStackTrace();
                        }
                    }

                    final List<Long> deletedProjectIdList = projectList.stream()
                            .map(Project::getId)
                            .collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(deletedProjectIdList)) {
                        // #3294 Async하게 수정
                        trackAsyncService.deleteByProjectIdAsync(deletedProjectIdList);
//                        try {
//                          trackServiceClient.deleteByProjectId(deletedProjectIdList);
//                        } catch (Exception e) {
//                          log.error("프로젝트아이디 {}의 위치정보 삭제 요청 시 오류 발생: {}", deletedProjectIdList, e.toString());
//                        }
                    }
                }
            }
        }
    }

    /**
     * WARNING!!! 프로젝트를 영구 삭제한다
     *
     * @param projectId
     */
    @Hidden
    @DeleteMapping(value = ApiUrl.PROJECT_INFO)
    public void deleteByProjectId(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                  @PathVariable @NotNull @Positive final Long projectId) {

        final Project project = projectBasicService.getProjectById(projectId);
        if (Objects.isNull(project)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "삭제하려는 프로젝트아이디 " + projectId + "가 존재하지 않습니다.", false);
        }

        final Long authUserId = userInfo.getId();
        final User user = userService.getUser(authUserId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, authUserId + " 사용자 정보가 없습니다.", false));
        final RoleType roleType = userService.getUserRoleByUser(user);

        final Set<Long> userIdList;
        if (RoleType.ROLE_ADMIN.equals(roleType)) {
            userIdList = null;
        } else if (RoleType.ROLE_ORG_ADMIN.equals(roleType)) {
            final Long orgId = user.getOrganizationId();
            final List<User> userList = userService.getUserByOrganization(orgId);
            userIdList = userList.stream().map(User::getUserId).collect(Collectors.toSet());
        } else {
            userIdList = Stream.of(authUserId).collect(Collectors.toSet());
        }

        if (CollectionUtils.isNotEmpty(userIdList) && !userIdList.contains(project.getUser().getUserId())) {
            throw new CustomException(HttpStatus.NOT_FOUND, "삭제하려는 프로젝트아이디 " + projectId + "에 대한 권한이 없습니다.", false);
        }

        projectService.deleteByProjectId(projectId);

        // #3294 Async하게 수정
        trackAsyncService.deleteByProjectIdAsync(Arrays.asList(projectId));
//        try {
//            trackServiceClient.deleteByProjectId(Arrays.asList(projectId));
//        } catch (Exception e) {
//            log.error("프로젝트아이디 {}의 위치정보 삭제 요청 시 오류 발생: {}", Arrays.asList(projectId), e.toString());
//        }
    }

    @Hidden
    @GetMapping(value = ApiUrl.PROJECT_RIDER_INFO)
    public Map<String, Object> getProjectRiderList(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                   @PathVariable final Long projectId) {

        Map<String, Object> result = new HashMap<>();
        List<WebRiderDTO> riderList = projectService.getProjectRiderList(userInfo.getId(), projectId, null, false);

        if (riderList != null) {
            result.put("riders", riderList);
        }

        return result;
    }

    /**
     * 프로젝트 종료
     *
     * @param userInfo
     * @param projectId
     */
    @Hidden
    @PutMapping(value = ApiUrl.PROJECT_TERMINATE)
    public void terminateProject(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                 @PathVariable final Long projectId) {

        final Project project = projectBasicService.getProjectById(projectId);
        if (Objects.isNull(project)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "종료하려는 프로젝트아이디 " + projectId + "가 존재하지 않습니다.", false);
        }

        final Long authUserId = userInfo.getId();
        final User user = userService.getUser(authUserId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, authUserId + " 사용자 정보가 없습니다.", false));
        final RoleType roleType = userService.getUserRoleByUser(user);

        final Set<Long> userIdList;
        if (RoleType.ROLE_ADMIN.equals(roleType)) {
            userIdList = null;
        } else if (RoleType.ROLE_ORG_ADMIN.equals(roleType)) {
            final Long orgId = user.getOrganizationId();
            final List<User> userList = userService.getUserByOrganization(orgId);
            userIdList = userList.stream().map(User::getUserId).collect(Collectors.toSet());
        } else {
            userIdList = Stream.of(authUserId).collect(Collectors.toSet());
        }

        if (CollectionUtils.isNotEmpty(userIdList) && !userIdList.contains(project.getUser().getUserId())) {
            throw new CustomException(HttpStatus.NOT_FOUND, "종료하려는 프로젝트아이디 " + projectId + "에 대한 권한이 없습니다.", false);
        }

        projectService.terminateProject(projectId, true);
    }

    @Deprecated
    private String getUserRole(final WebUserDetails userInfo) {

        String role = RoleType.ROLE_COMMON.getId();
        if (userInfo.getAuthorities().contains(new SimpleGrantedAuthority(RoleType.ROLE_ADMIN.getId()))) {
            role = RoleType.ROLE_ADMIN.getId();
        }
        return role;
    }

    /**
     * 신규 프로젝트 푸시 메시지 전송
     *
     * @param projectId 프로젝트 아이디
     * @param riderId   기사 아이디
     */
    @Hidden
    @Deprecated
    @PostMapping(path = ApiUrl.NEW_PROJECT_PUSH)
    public void sendNewProjectPushMessage(@RequestParam final Long projectId,
                                          @RequestParam final Long riderId) {

        projectService.sendNewProjectAssignedMessageToRider(projectId, riderId);
    }

    /**
     * 프로젝트를 기사들에게 전송
     *
     * @param projectId
     * @param projectPushDTOList
     */
    @Hidden
    @PostMapping(value = ApiUrl.PROJECT_PUSH_RIDER)
    public List<@Valid ProjectPushDTO> pushProjectToRiders(@PathVariable @Positive final Long projectId,
                                                           @RequestBody(required = false) final List<@Valid ProjectPushDTO> projectPushDTOList) {

        return projectService.pushProjectToRiders(projectId, projectPushDTOList, null);
    }

    @Hidden
    @GetMapping(value = ApiUrl.PROJECT_FILES_ZIP_DOWNLOAD)
    public void downloadProjectAttachedPhotos(@NotNull final HttpServletResponse response,
                                              @PathVariable @Positive final Long projectId) {

        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);
        if (Objects.isNull(project.getDoneDateTime())) {
            throw new InvalidParameterException("완료된 프로젝트가 아닙니다.");
        }

        projectService.downloadProjectAttachedPhotos(response, project);
    }

    @Hidden
    @GetMapping(value = ApiUrl.PROJECT_PROPERTY)
    public ProjectPropertyDTO getProjectAttributes(@RequestParam(required = false) @Min(1) Long projectId,
                                                   @RequestParam(required = false) @Email String email,
                                                   @RequestParam(required = false) String date) {

        if (Objects.isNull(projectId)) {//openApi에서 넘어왔으면 항상 projectId는 존재한다. 테스트로 넘어왔을 경우에 해당된다
            projectId = Optional.ofNullable(this.projectService.getAutoCreateProjectIdByOrgCodeOrEmail(null, email, date))
                    .orElseThrow(() -> new CustomException(HttpStatus.NO_CONTENT, "해당 프로젝트가 존재하지 않습니다."));
        }

        Project project = this.projectBasicService.getProjectById(projectId);

        Boolean isCutoff = false;
        if (Objects.nonNull(project.getCutoffTime()) && LocalDateTime.now().isAfter(project.getCutoffTime())) {
            isCutoff = true;
        }

        return ProjectPropertyDTO.builder()
                .projectId(project.getId())
                .projectName(project.getName())
                .isCutoff(isCutoff)
                .email(project.getUser().getEmail())
                .build();
    }

    @Hidden
    @GetMapping(value = ApiUrl.PROJECT_CREATE)
    public ProjectDTO createAutoProject(@RequestParam(required = false) @Email String email,
                                        @RequestParam(required = false) String date) {

        final User user = userService.getUserByEmail(email)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, email + " 사용자 정보가 없습니다.", false));

        final LocalDateTime localDateTime = Objects.isNull(date) ? LocalDateTime.now() : DateTimeUtil.toLocalDateTimeFromDateStr(date);
        if (Objects.isNull(localDateTime)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "잘못된 날짜 형식입니다. (" + date + ")");
        }

        Project project = this.createAutoProjectByUserIdAndDateTime(user.getUserId(), localDateTime);

        return ProjectDTO.parseFromProject(project);
    }

    @Hidden
    private Project createAutoProjectByUserIdAndDateTime(Long userId,
                                                         LocalDateTime localDateTime) {

        Project project = projectService.findAutoCreatedProjectByUserIdAndDateTime(userId, localDateTime);
        if (Objects.isNull(project)) {
            project = projectService.createAutoCreateProject(userId, localDateTime, null);
        } else {
            log.warn("createAutoProjectByUserIdAndDateTime 프로젝트가 있습니다.");
        }

        return project;
    }

    @Hidden
    @Scheduled(cron = "${scheduler.project-auto-create.cron}")
    public void batchCreatedProjectAutoScheduled() {

        if (!projectAutoCreateSchedulerEnabled) {
            return;
        }

        final List<Organization> listOrg = organizationService.getListOrganizationByAutoScheduleProject(true);

        if (CollectionUtils.isNotEmpty(listOrg)) {
            log.info("[Auto Scheduled] batchCreatedProjectAutoScheduled: org count is {}", listOrg.size());
            listOrg.forEach(org -> {
                final Long adminUserId = userService.getAdminUserIdOfOrganization(org);

                log.info("[Auto Scheduled] batchCreatedProjectAutoScheduled: createAutoProjectByUserIdAndDateTime userId: {}", adminUserId);
                this.createAutoProjectByUserIdAndDateTime(adminUserId, LocalDateTime.now());
            });
        }
    }

    @Hidden
    @Scheduled(cron = "${scheduler.department-project-auto-create.cron}")
    public void batchCreateDepartmentProjectScheduled() {

        if (!departmentProjectAutoCreateSchedulerEnabled) {
            return;
        }

        final List<Organization> organizationList = organizationService.getListOrganizationByAutoScheduleDepartmentProject();

        log.info("[Auto Scheduled] begin batchCreateDepartmentProjectScheduled: org count is {}", organizationList.size());

        organizationList.forEach(projectService::createDepartmentProjectOfOrganization);

        log.info("[Auto Scheduled] end batchCreateDepartmentProjectScheduled: org count is {}", organizationList.size());
    }

    //POC용 스케줄 제거
    //@Scheduled(cron = "0 50 23 * * *")   //(cron = "초 분 시 * * *") 매일 저녁 23시 50분에 Auto 프로젝트 종료 확인 후 프로젝트 종료 안되어 있으면 강제 종료 시킴
    public void batchSetFinishProjectAutoScheduled() {

        log.info("[Auto Scheduled] batchSetFinishProjectAutoScheduled");
        projectService.setFinishProjectAutoScheduled();
    }

    /**
     * 이전 프로젝트를 신규 프로젝트로 이동하는 기능
     *
     * @param userInfo
     * @param oldProjectId 완료 되지 않은 이전 프로젝트
     * @param newProjectId 신규 프로젝트 Id
     * @return
     * @throws Exception
     */
    @Hidden
    @GetMapping(value = ApiUrl.PROJECT_DELIVERY_CHANGED)
    public ProjectDTO changeProjectDeliveryItems(@AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                 @RequestParam(required = false) @Nullable @Positive final Long oldProjectId,
                                                 @RequestParam(required = false) @Nullable @Positive final Long newProjectId) throws Exception {

        return ProjectDTO.parseFromProject(projectService.saveNewProjectDestination(userInfo.getId(), oldProjectId, newProjectId));
    }

    /**
     * 자동으로 프로젝트에  자동 생성 프로젝트에 기사가 추가되도록 함
     */
    @GetMapping(value = ApiUrl.PROJECT_AUTO_ADD_RIDER)
    public List<RiderDTO> addAutoRegionRiderToProject(@RequestParam @Nullable @Positive final Long userId, @RequestParam @Nullable @Positive final Long projectId) {
        return projectService.addAutoRegionRiderToProject(userId, projectId);
    }

}