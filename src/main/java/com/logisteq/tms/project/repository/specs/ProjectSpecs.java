package com.logisteq.tms.project.repository.specs;

import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.domain.suppl.ProjectStatus;
import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.service.DepartmentService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

public final class ProjectSpecs {
    public static final String KEY_USER = "user";
    public static final String KEY_ORGANIZATION = "organization";
    private static final String KEY_ORGANIZATION_ID = "id";
    public static final String KEY_DEPARTMENT = "department";
    private static final String KEY_DEPARTMENT_ID = "departmentId";
    public static final String KEY_PROJECT_ID = "id";
    private static final String KEY_PROJECT_STATUS = "status";
    private static final String KEY_PROJECT_NAME = "name";
    private static final String KEY_DELETED = "deleted";
    private static final String KEY_ANONYMOUS = "isAnonymous";
    private static final String KEY_CREATE_AT = "createAt";
    private static final String KEY_IS_CLUSTER_DONE = "isClusterDone";
    private static final String KEY_IS_FIRST_ROUTING_DONE = "isFirstRoutingDone";
    private static final String KEY_IS_ROUTE_ENABLED = "isRouteEnabled";
    private static final String KEY_IS_SENDING_RIDER_ENABLED = "isSendingRiderEnabled";
    private static final String KEY_IS_AUTO_CREATED_PROJECT = "isAutoCreated";
    private static final String KEY_COMPLETE_DATE_TIME = "completeDateTime";
    private static final String KEY_DONE_DATE_TIME = "doneDateTime";
    private static final String KEY_EFFECTIVE_DATE_TIME = "effectiveDateTime";

    public static Specification<Project> eqUser(User user) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get(KEY_USER), user);
        };
    }

    public static Specification<Project> inUser(List<User> userList) {
        return (root, query, criteriaBuilder) -> {
            return root.get(KEY_USER).in(userList);
        };
    }

    public static Specification<Project> eqOrganization(final Organization organization) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get(KEY_ORGANIZATION), organization);
        };
    }

    public static Specification<Project> isNullOrganization() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.isNull(root.get(KEY_ORGANIZATION));
        };
    }

    public static Specification<Project> inDepartmentList(final List<Department> departmentList) {
        return (root, query, criteriaBuilder) -> {
            return root.get(KEY_DEPARTMENT).in(departmentList);
        };
    }

    public static Specification<Project> isNullDepartment() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.isNull(root.get(KEY_DEPARTMENT));
        };
    }

    public static Specification<Project> hasAuthority(final RoleType userRole,
                                                      final User user,
                                                      final Organization organization,
                                                      final boolean orgHasDepartment,
                                                      final List<User> sameOrgUserList,
                                                      final List<Department> authorizedDepartmentList,
                                                      final List<Department> filterDepartmentList) {
        if (RoleType.ROLE_ADMIN.equals(userRole)) {
            return (root, query, criteriaBuilder) -> criteriaBuilder.conjunction();
        } else if (RoleType.ROLE_ORG_ADMIN.equals(userRole) || RoleType.ROLE_COMMON.equals(userRole)) {
            if (Objects.isNull(organization)) {
                // 소속조직이 없는 관제자인 경우
                // -> 프로젝트의 생성조직이 설정되어있지 않고, 프로젝트의 생성자가 관제자와 동일한 프로젝트 조회
                return isNullOrganization().and(eqUser(user));
            } else if (orgHasDepartment) {
                // 소속조직이 있고 부서가 있는 조직이라면 부서에 따른 권한 조건 추가
                if (CollectionUtils.isNotEmpty(authorizedDepartmentList)) {
                    // 소속조직과 소속부서가 모두 있는 관제자인 경우
                    // -> 프로젝트의 생성조직이 설정되어있지 않고, 프로젝트의 생성자가 관제자와 동일 조직에 소속된 관제자들 중의 한명이거나
                    // -> 프로젝트의 생성조직이 관제자의 소속조직과 동일하고, 프로젝트의 생성부서가 관제자의 소속부서(하위부서 포함) 목록 중 하나인 프로젝트 조회
                    if (CollectionUtils.isNotEmpty(filterDepartmentList)) {
                        final Specification<Project> spec1 = isNullOrganization().and(inUser(sameOrgUserList));
                        final Specification<Project> spec2 = eqOrganization(organization).and(isNullDepartment().or(inDepartmentList(DepartmentService.intersection(authorizedDepartmentList, filterDepartmentList))));
                        return spec1.or(spec2);
                    } else {
                        final Specification<Project> spec1 = isNullOrganization().and(inUser(sameOrgUserList));
                        final Specification<Project> spec2 = eqOrganization(organization).and(isNullDepartment().or(inDepartmentList(authorizedDepartmentList)));
                        return spec1.or(spec2);
                    }
                } else {
                    // 소속조직은 있으나 소속부서가 없는 관제자인 경우
                    // -> 프로젝트의 생성조직이 설정되어있지 않고, 프로젝트의 생성자가 관제자와 동일 조직에 소속된 관제자들 중의 한명이거나
                    // -> 프로젝트의 생성조직이 관제자의 소속조직과 동일하고, 프로젝트의 생성부서가 설정되어있지 않은 프로젝트 조회
                    final Specification<Project> spec1 = isNullOrganization().and(inUser(sameOrgUserList));
                    final Specification<Project> spec2 = eqOrganization(organization).and(isNullDepartment());
                    return spec1.or(spec2);
                }
            } else {
                // 소속조직이 있고 부서가 없는 조직인 경우
                // -> 프로젝트의 생성조직이 설정되어있지 않고, 프로젝트의 생성자가 관제자와 동일 조직에 소속된 관제자들 중의 한명이거나
                // -> 프로젝트의 생성조직이 동일한 프로젝트 조회
                final Specification<Project> spec1 = isNullOrganization().and(inUser(sameOrgUserList));
                final Specification<Project> spec2 = eqOrganization(organization);
                return spec1.or(spec2);
            }
        } else {    // RoleType.ROLE_ANONYMOUS
            return eqUser(user);
        }
    }

    public static Specification<Project> eqProjectId(Long projectId) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get(KEY_PROJECT_ID), projectId);
        };
    }

    public static Specification<Project> inProjectIdList(List<Long> projectIdList) {
        return (root, query, criteriaBuilder) -> {
            return root.get(KEY_PROJECT_ID).in(projectIdList);
        };
    }

    public static Specification<Project> eqProjectStatus(ProjectStatus projectStatus) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get(KEY_PROJECT_STATUS), projectStatus);
        };
    }

    public static Specification<Project> inProjectStatusList(List<ProjectStatus> projectStatusList) {
        return (root, query, criteriaBuilder) -> {
            return root.get(KEY_PROJECT_STATUS).in(projectStatusList);
        };
    }

    public static Specification<Project> eqProjectName(String projectName) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get(KEY_PROJECT_NAME), projectName);
        };
    }

    public static Specification<Project> projectNameLike(String projectName) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.like(root.get(KEY_PROJECT_NAME), "%" + projectName + "%");
        };
    }

    public static Specification<Project> eqDeleted(boolean deleted) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get(KEY_DELETED), deleted);
        };
    }

    public static Specification<Project> eqAnonymous(boolean anonymous) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get(KEY_ANONYMOUS), anonymous);
        };
    }

    public static Specification<Project> eqAutoCreate(boolean isCheck) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.equal(root.get(KEY_IS_AUTO_CREATED_PROJECT), isCheck);
        };
    }

    public static Specification<Project> isNullCompleteDateTime(boolean isNullCheck) {
        return (root, query, criteriaBuilder) -> {
            if (isNullCheck)
                return criteriaBuilder.isNull(root.get(KEY_COMPLETE_DATE_TIME));
            else
                return criteriaBuilder.isNotNull(root.get(KEY_COMPLETE_DATE_TIME));
        };
    }

    public static Specification<Project> isNullDoneDateTime(boolean isNullCheck) {
        return (root, query, criteriaBuilder) -> {
            if (isNullCheck)
                return criteriaBuilder.isNull(root.get(KEY_DONE_DATE_TIME));
            else
                return criteriaBuilder.isNotNull(root.get(KEY_DONE_DATE_TIME));
        };
    }

    public static Specification<Project> isNullEffectiveDateTime(boolean isNullCheck) {
        return (root, query, criteriaBuilder) -> {
            if (isNullCheck)
                return criteriaBuilder.isNull(root.get(KEY_EFFECTIVE_DATE_TIME));
            else
                return criteriaBuilder.isNotNull(root.get(KEY_EFFECTIVE_DATE_TIME));
        };
    }

    public static Specification<Project> betweenCreateAt(LocalDateTime fromDateTime, LocalDateTime toDateTime) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.and(
                    criteriaBuilder.greaterThanOrEqualTo(root.get(KEY_CREATE_AT), fromDateTime),
                    criteriaBuilder.lessThanOrEqualTo(root.get(KEY_CREATE_AT), toDateTime));
        };
    }

	public static Specification<Project> gteCreateAt(LocalDateTime fromDateTime) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.greaterThanOrEqualTo(root.get(KEY_CREATE_AT), fromDateTime);
		};
	}

	public static Specification<Project> lteCreateAt(LocalDateTime toDateTime) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.lessThanOrEqualTo(root.get(KEY_CREATE_AT), toDateTime);
		};
	}

	////////////////////////////////////////////////////////////////
    // 빈 프로젝트를 확인하기 위해서 만들어 둠
    public static Specification<Project> isNotNullClusterDone() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.isNotNull(root.get(KEY_IS_CLUSTER_DONE));
        };
    }

    public static Specification<Project> isNotNullFirstRoutingDone() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.isNotNull(root.get(KEY_IS_FIRST_ROUTING_DONE));
        };
    }

    public static Specification<Project> isNotNullRouteEnabled() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.isNotNull(root.get(KEY_IS_ROUTE_ENABLED));
        };
    }

    public static Specification<Project> isNotNullSendingRiderEnabled() {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.isNotNull(root.get(KEY_IS_SENDING_RIDER_ENABLED));
        };
    }

    public static Specification<Project> composeSpecs(List<ProjectStatus> projectStatusList,
													  String projectName,
													  LocalDateTime fromDateTime,
													  LocalDateTime toDateTime) {

        Specification<Project> specs = (root, query, criteriaBuilder) -> {
            return null;
        };

        if (StringUtils.isNotBlank(projectName)) {
            specs = specs.and(projectNameLike(projectName));
        }

        if (CollectionUtils.isNotEmpty(projectStatusList)) {
            specs = specs.and(inProjectStatusList(projectStatusList));
        }

        if (Objects.nonNull(fromDateTime)) {
			specs = specs.and(gteCreateAt(fromDateTime));
		}

		if (Objects.nonNull(toDateTime)) {
			specs = specs.and(lteCreateAt(toDateTime));
		}

        return specs;
    }

}
