package com.logisteq.tms.project.repository;

import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.domain.suppl.ProjectStatus;
import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ProjectRepository extends JpaRepository<Project, Long>, JpaSpecificationExecutor<Project> {

    interface Id {
        Long getId();
    }

    /**
     * 프로젝트 갯수 조회 (by UserId)
     *
     * @param userId
     * @return
     */
    Long countByUserUserId(Long userId);

    Optional<Project> findById(Long projectId);

    List<Project> findByNameAndDeleted(String name, Boolean deleted);

    // #3270 : 동시성 제어를 위해 Pessimistic Lock 적용
    @Transactional
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    Optional<Project> findTop1ByUserUserIdAndNameAndDeleted(final Long userId, final String name, final Boolean deleted);

    // #3270 : 동시성 제어를 위해 Pessimistic Lock 적용
    @Transactional
    @Lock(LockModeType.PESSIMISTIC_WRITE)
//    @QueryHints({@QueryHint(name = "javax.persistence.lock.timeout", value = "3000")}) // MariaDB에서는 Transaction Lock Timeout 옵션이 동작 안하는 것 같다.
    List<Project> findByNameAndIsAutoCreatedAndUserUserIdAndDeleted(String name, Boolean isAutoCreated, Long userId, Boolean deleted);

    // #3270 : 동시성 제어를 위해 Pessimistic Lock 적용
    @Transactional
    @Lock(LockModeType.PESSIMISTIC_WRITE)
//    @QueryHints({@QueryHint(name = "javax.persistence.lock.timeout", value = "3000")}) // MariaDB에서는 Transaction Lock Timeout 옵션이 동작 안하는 것 같다.
    List<Project> findByNameAndIsAutoCreatedAndUserUserIdAndDeletedOrderByCreateAtDesc(String name, Boolean isAutoCreated, Long userId, Boolean deleted);

    List<Project> findByNameAndUserUserIdAndDeleted(final String name, final Long userId, final Boolean deleted);

    /**
     * 프로젝트 목록 조회 (by UserId)
     *
     * @param userId
     * @param pageable
     * @return
     */
    Page<Project> findByUserUserId(Long userId, Pageable pageable);

    /**
     * 예정 상태(관제 이전 상태) 프로젝트 찾기
     *
     * @param userId
     * @return
     */
    // #3270 : 동시성 제어를 위해 Pessimistic Lock 적용
    @Transactional
    @Lock(LockModeType.PESSIMISTIC_WRITE)
//    @QueryHints({@QueryHint(name = "javax.persistence.lock.timeout", value = "3000")}) // MariaDB에서는 Transaction Lock Timeout 옵션이 동작 안하는 것 같다.
    List<Project> findByUserUserIdAndEffectiveDateTimeIsNullOrderByCreateAtDesc(Long userId);

    /**
     * 수행해야할 프로젝트 목록 조회
     *
     * @param projectIdList
     * @return
     */
    @Deprecated
    List<Project> findByIsAnonymousFalseAndDeletedFalseAndCompleteDateTimeNullAndDoneDateTimeNullAndEffectiveDateTimeNotNullAndIdIn(final List<Long> projectIdList);

    /**
     * 사용자 아이디 목록에 따라 그 사용자가 소유한 프로젝트 아이디 목록을 반환한다.
     *
     * @param userIdList
     * @return
     */
    @Query(value = "SELECT DISTINCT id FROM project WHERE user_id IN :userIdList", nativeQuery = true)
    Set<Long> findProjectIdListByUserIdIn(@Param("userIdList") @NotEmpty final Set<Long> userIdList);

    /**
     * 특정 상태와 특정 실행일의 프로젝트 아이디 목록 반환
     *
     * @param projectStatus
     * @param effectiveDate
     * @return
     */
    @Query(value = "SELECT id FROM Project WHERE status = :projectStatus AND cast(effectiveDateTime AS LocalDate) = :effectiveDate")
    List<Long> findProjectIdByStatusAndEffectiveDateIs(@NotNull @Positive @Param("projectStatus") final ProjectStatus projectStatus,
                                                       @NotNull @Param("effectiveDate") final LocalDate effectiveDate);

    /**
     * Org code_name 프로젝트의 상태와 실행일에 대한 프로젝트 아이디 목록 반환
     *
     * @param projectStatus
     * @param codeName
     * @param effectiveDate
     * @return
     */
    @Query(value = "SELECT P.id FROM Project P WHERE P.user.userId IN (SELECT U.userId  FROM Organization O INNER JOIN User U ON O.id = U.organizationId WHERE O.codeName =:codeName) AND P.status =:projectStatus AND cast(P.effectiveDateTime AS LocalDate) = :effectiveDate")
    List<Long> findJoinsProjectIdByStatusAndEffectiveDateIs(@NotNull @Positive @Param("projectStatus") final ProjectStatus projectStatus,
                                                            @NotNull @Positive @Param("codeName") final String codeName,
                                                            @NotNull @Param("effectiveDate") final LocalDate effectiveDate);

    /**
     * 프로젝트의 사용자아이디를 반환
     *
     * @param projectId
     * @return
     */
    @Query(nativeQuery = true, value = "SELECT user_id FROM project WHERE id = :projectId")
    Long findUserIdByProjectId(@NotNull @Positive @Param("projectId") final Long projectId);

    /**
     * 부서별 최신 프로젝트아이디 목록 반환
     *
     * @param departmentIdList
     * @return
     */
    @Query(nativeQuery = true, value = "SELECT MAX(id) FROM project WHERE department_id IN :departmentIdList GROUP BY department_id")
    List<Long> findTopProjectIdListGroupByDepartmentId(final Collection<Long> departmentIdList);

    /**
     * 부서의 최신 프로젝트아이디 반환
     *
     * @param departmentId
     * @return
     */
    Project findTop1ByDepartment_DepartmentIdOrderByIdDesc(final Long departmentId);

    /**
     * 부서의 최신 프로젝트아이디 반환
     *
     * @param department
     * @return
     */
    Project findTop1ByDepartmentOrderByIdDesc(final Department department);

    /**
     * 특정 사용자가 기준일시보다 이전에 생성된 프로젝트들 반환
     *
     * @param baseDateTime
     * @param userList
     * @return
     */
    List<Project> findByCreateAtBeforeAndUserIn(@NotNull final LocalDateTime baseDateTime, List<User> userList);


    /**
     * 업체별 기간으로 프로젝트들 반환
     *
     * @param startTime
     * @param endTime
     * @param userList
     * @param projectStatus
     * @return
     */
    List<Id> findByCreateAtBetweenAndUserInAndStatus(final LocalDateTime startTime,
                                                     final LocalDateTime endTime,
                                                     final List<User> userList,
                                                     final ProjectStatus projectStatus);

    /**
     * 프로젝트 목록 조회 (by UserId)
     *
     * @param userId
     * @return
     */
    List<Project> findByUserUserId(Long userId);

    /**
     * 특정 조직의 사용자가 생성한 기간별 프로젝트 목록 조회
     *
     * @param codeName
     * @param fromDt
     * @param toDt
     * @return
     */
    @Query(value = "SELECT p.* FROM project p " +
        "WHERE 1=1 " +
        "AND p.user_id IN (SELECT u.user_id FROM user u " +
        "    INNER JOIN (SELECT o.id FROM organization o WHERE o.code_name = :codeName) o2 " +
        "    ON u.organization_id = o2.id) " +
        "AND p.create_at >= :fromDt " +
        "AND p.create_at < :toDt " +
        "AND p.deleted = 0 " +
        "AND (p.name NOT LIKE '20%' OR p.name NOT LIKE '%T%')",
        nativeQuery = true)
    List<Project> findByUserUserIdAndCreateAtBetween(@Param("codeName") final String codeName,
                                                     @Param("fromDt") final LocalDateTime fromDt,
                                                     @Param("toDt") final LocalDateTime toDt);

    /**
     * 특정 점포명에 소속된 배송담당자의 프로젝트 목록 조회
     *
     * @param groupName
     * @param riderId
     * @param fromDt
     * @param toDt
     * @return
     */
    @Query(value = "SELECT p.* FROM project p " +
        "WHERE p.id IN (SELECT da.project_id FROM delivery_allocation da " +
        "WHERE 1=1 " +
        "AND da.group_name = :groupName " +
        "AND da.rider_id = :riderId " +
        "AND da.create_at >= :fromDt " +
        "AND da.create_at < :toDt " +
        "GROUP BY da.project_id)",
        nativeQuery = true)
    List<Project> findByIdInDeliveryAllocationProjectIds(@Param("groupName") final String groupName,
                                                         @Param("riderId") final Long riderId,
                                                         @Param("fromDt") final LocalDateTime fromDt,
                                                         @Param("toDt") final LocalDateTime toDt);

    /**
     * 특정 조직의 프로젝트명에 해당하는 프로젝트 목록 조회
     *
     * @param name
     * @param orgId
     * @return
     */
    @Query(value = "SELECT p.* FROM project p " +
        "WHERE p.name = :name AND p.organization_id = :orgId AND p.deleted = 0",
        nativeQuery = true)
    List<Project> findByNameAndOrganizationId(@Param("name") final String name,
                                              @Param("orgId") final Long orgId);

    /**
     * 특정 조직의 해당하는 완료 안된 프로젝트 이름을 넘겨줌.
     *
     * @param orgId
     * @param projectIds
     * @param projectStatus
     * @return
     */
    @Query(value = "SELECT GROUP_CONCAT(p.name) AS project_names FROM project p " +
        "WHERE p.organization_id = :orgId AND p.deleted = 0 AND p.id IN :projectIds AND p.status != :projectStatus",
        nativeQuery = true)
    String findByOrganizationIdAndProjectIds(@Param("orgId") final Long orgId,
                                                    @Param("projectIds") final List<Long> projectIds,
                                                    @Param("projectStatus") final String projectStatus);

    /**
     * 프로젝트 ID와 상태 목록으로 프로젝트 목록 조회
     *
     * @param projectIdList
     * @param projectStatusList
     * @param deleted
     * @return
     */
    List<Project> findByIdInAndStatusInAndDeleted(final List<Long> projectIdList, final List<ProjectStatus> projectStatusList, final Boolean deleted);

}
