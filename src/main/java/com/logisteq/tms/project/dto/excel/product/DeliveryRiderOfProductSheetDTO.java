package com.logisteq.tms.project.dto.excel.product;

import com.logisteq.tms.delivery.repository.dao.DeliveryReceiverProductDAO;
import lombok.*;

import java.time.LocalDate;
import java.util.List;

@Getter
@Builder
@ToString(of = {"deliveryDate", "deliveryProductDTO",})
public class DeliveryRiderOfProductSheetDTO {

    // 배송일
    private LocalDate deliveryDate;

    private DeliveryProductDTO deliveryProductDTO;

    private List<DeliveryRiderDTO> deliveryRiderOfProductDTOList;

    @Getter
    @Builder
    @EqualsAndHashCode(of = {"warehouseCode", "warehouseCategory", "productBarcode",})
    @ToString(of = {"warehouseCode", "warehouseCategory", "productBarcode", "itemName",})
    public static class DeliveryProductDTO {

        // 센터명
        private String warehouseCode;

        // 유형 (본부: WHGUBN_HQ, 일반: WHGUBN_G)
        private Integer warehouseCategory;

        // 물류코드
        private String productBarcode;

        // 상품명
        private String itemName;
    }

    @Getter
    @Builder
    @ToString(of = {"riderId", "riderName", "itemQuantity",})
    public static class DeliveryRiderDTO {

        // 기사아이디
        private Long riderId;

        // 기사명
        private String riderName;

        // 수량
        @Setter
        private Long itemQuantity;

        public static DeliveryRiderDTO of(final DeliveryReceiverProductDAO deliveryReceiverProductDAO,
                                          final String riderName) {

            return DeliveryRiderDTO.builder()
                    .riderId(deliveryReceiverProductDAO.getRiderId())
                    .riderName(riderName)
                    .itemQuantity(deliveryReceiverProductDAO.getOrderQuantity())
                    .build();
        }

    }

}
