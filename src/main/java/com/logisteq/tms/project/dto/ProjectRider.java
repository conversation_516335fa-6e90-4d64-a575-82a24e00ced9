package com.logisteq.tms.project.dto;

import com.logisteq.tms.address.domain.suppl.DispatchableItem;
import com.logisteq.tms.rider.domain.Rider;
import lombok.*;
import org.locationtech.jts.geom.Point;

@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ProjectRider extends DispatchableItem {

    private Rider rider;

    @Deprecated
    private Point startPoint;    // 현재 사용하는 곳이 없다.

    @Builder
    protected ProjectRider(Rider rider, String groupName, Point startPoint) {
        super(groupName, null);
        this.rider = rider;
        this.startPoint = startPoint;
    }

}
