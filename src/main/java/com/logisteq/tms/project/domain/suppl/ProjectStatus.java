package com.logisteq.tms.project.domain.suppl;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nonnull;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ProjectStatus {

    SCHEDULED(
            0,
            "SCHEDULED",
            "예정"
    ),
    IN_PROGRESS(
            1,
            "IN_PROGRESS",
            "관제중"
    ),
    DONE(
            2,
            "DONE",
            "완료"
    ),
    UNKNOWN(
            3,
            "UNKNOWN",
            "알수없음"
    ),
    ;

    public static final String COLUMN_DEFINITION =
            "enum(" +
                    "'SCHEDULED'" +
                    "," +
                    "'IN_PROGRESS'" +
                    "," +
                    "'DONE'" +
                    "," +
                    "'UNKNOWN'" +
                    ")";

    private Integer index;
    private String english;
    private String korean;

    private static final Map<Integer, ProjectStatus> indexToEnum = Stream
            .of(values())
            .collect(Collectors.toMap(e -> e.index, e -> e));

    private static final Map<String, ProjectStatus> englishToEnum = Stream
            .of(values())
            .collect(Collectors.toMap(e -> StringUtils.deleteWhitespace(e.getEnglish()).toUpperCase(), e -> e));

    private static final Map<String, ProjectStatus> koreanToEnum = Stream
            .of(values())
            .collect(Collectors.toMap(e -> StringUtils.deleteWhitespace(e.getKorean()), e -> e));

    public static ProjectStatus fromEnglish(final String str) {

        return englishToEnum.get(StringUtils.deleteWhitespace(str).toUpperCase());
    }

    public static ProjectStatus fromKorean(final String str) {

        return koreanToEnum.get(StringUtils.deleteWhitespace(str));
    }

    @JsonCreator
    public static ProjectStatus getProjectStatusFromJson(final String str) {
        return fromKorean(str);
    }

    @JsonValue
    public String getJsonFromProjectStatus() {
        return korean;
    }

    @Override
    public String toString() {
        return korean;
    }

    public static ProjectStatus getProjectStatusFromText(final String text) {

        if (Objects.isNull(text)) {
            return null;
        }

        return Optional.ofNullable(fromKorean(text))
                .orElseGet(() -> Optional.ofNullable(fromEnglish(text))
                        .orElseThrow(() -> new IllegalArgumentException("ProjectStatus is {" + text + "} but not supported.")));
    }

    public static ProjectStatus getProjectStatusFromIndex(final Integer index) {

        if (Objects.isNull(index)) {
            return null;
        }

        return Optional.ofNullable(ProjectStatus.indexToEnum.get(index))
                .orElseThrow(() -> new IllegalArgumentException("ProjectStatus index is {" + index + "} but not supported."));
    }

    @Converter(autoApply = true)
    public static class ProjectStatusConverter implements AttributeConverter<ProjectStatus, Integer> {

        @Override
        public Integer convertToDatabaseColumn(ProjectStatus attribute) {
            if (attribute == null) {
                return null;
            }
            return attribute.getIndex();
        }

        @Override
        public ProjectStatus convertToEntityAttribute(Integer dbData) {
            if (dbData == null) {
                return null;
            }
            return ProjectStatus.getProjectStatusFromIndex(dbData);
        }

    }

    public static class ApiParamToProjectStatusConverter
            implements org.springframework.core.convert.converter.Converter<String, ProjectStatus> {

        @Override
        public ProjectStatus convert(@Nonnull String source) {
            return ProjectStatus.getProjectStatusFromText(source);
        }
    }

}
