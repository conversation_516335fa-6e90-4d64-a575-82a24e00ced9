package com.logisteq.tms.privacy.suppl;

public enum PrivacyDataType {

    ADDRESS("주소"),
    NAME("이름"),
    MOBILE("전화번호"),
    NAME_MOBILE("이름,전화번호"),
    MEMO("메모"),
    ETC("기타"),
    NAME_MOBILE_ADDRESS("이름,전화번호,주소"),
    NAME_ADDRESS("이름,주소");

    private final String value;

    PrivacyDataType(final String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static final String COLUMN_DEFINITION =
            "enum("
                    + "'ADDRESS'"
                    + ","
                    + "'NAME'"
                    + ","
                    + "'MOBILE'"
                    + ","
                    + "'NAME_MOBILE'"
                    + ","
                    + "'MEMO'"
                    + ","
                    + "'ETC'"
                    + ","
                    + "'NAME_MOBILE_ADDRESS'"
                    + ","
                    + "'NAME_ADDRESS'"
                    + ")";

}
