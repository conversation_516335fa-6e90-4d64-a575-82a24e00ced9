package com.logisteq.tms.privacy.feign;

import com.logisteq.tms.privacy.dto.PrivacyRecordDto;
import com.logisteq.tms.privacy.dto.PrivacyRecordResponseDto;
import com.logisteq.tms.privacy.suppl.PrivacyRecordType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "privacy-record-service", url = "${aloa.system.privacy.external.url:}")
public interface PrivacyRecordServiceClient {

    @PostMapping("/api/privacy-access-record")
    void createRecord(@RequestBody PrivacyRecordDto recordData);

    @PostMapping("/api/privacy-access-record/array")
    void createRecordArray(@RequestBody List<PrivacyRecordDto> recordDataArray);

    @GetMapping("/api/privacy-access-record")
    PrivacyRecordResponseDto getRecords(
            @RequestParam(value = "recordType") PrivacyRecordType recordType,
            @RequestParam(value = "vendor") String vendor,
            @RequestParam(value = "orgId", required = false) Long orgId,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "perPage", defaultValue = "30") int perPage,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate
    );

}
