package com.logisteq.tms.vehicle.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logisteq.tms.common.component.ProfileManager;
import com.logisteq.tms.vehicle.domain.VehicleCharacteristics;
import com.logisteq.tms.vehicle.domain.VehicleModel;
import com.logisteq.tms.vehicle.dto.*;
import com.logisteq.tms.vehicle.service.VehicleCharacteristicsService;
import com.logisteq.tms.vehicle.service.VehicleModelService;
import com.logisteq.tms.vehicle.service.VehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.util.List;

@Slf4j
@DependsOn(value = {"defaultDbObjectConfig"})
@Component
public class DefaultVehicleDataConfig {

    private final VehicleService vehicleService;

    private final VehicleModelService vehicleModelService;

    private final VehicleCharacteristicsService vehicleCharacteristicsService;

    private final ProfileManager profileManager;

    private final ObjectMapper mapper;

    @Autowired
    public DefaultVehicleDataConfig(final VehicleService vehicleService,
                                    final VehicleModelService vehicleModelService,
                                    final VehicleCharacteristicsService vehicleCharacteristicsService,
                                    final ProfileManager profileManager,
                                    final ObjectMapper mapper) {

        this.vehicleService = vehicleService;
        this.vehicleModelService = vehicleModelService;
        this.vehicleCharacteristicsService = vehicleCharacteristicsService;
        this.profileManager = profileManager;
        this.mapper = mapper;
    }

    @Value("${vehicle.modelFile}")
    private String vehicleModelFile;

    @Value("${vehicle.characteristicsFile}")
    private String vehicleCharacteristicsFile;

    @PostConstruct
    private void init() {

        if (profileManager.isDefaultDataConfigProfiles()) {

            try {
                final Pageable page = PageRequest.of(0, 10);

                // 기 등록된 VehicleCharacteristics이 존재하는지 검색
                final VehicleCharacteristicsListDTO vehicleCharacteristicsListDTO = vehicleCharacteristicsService.getVehicleCharacteristicsPage(VehicleCharacteristicsDTO.builder().build(), page);
                if (vehicleCharacteristicsListDTO.getTotalCount() == 0) {
                    try (final InputStream is = new ClassPathResource(vehicleCharacteristicsFile).getInputStream()) {
                        final List<VehicleCharacteristicsDTO> vehicleCharacteristicsDTOList = mapper.readValue(is, new TypeReference<List<VehicleCharacteristicsDTO>>() {
                        });

                        for (final VehicleCharacteristicsDTO vc : vehicleCharacteristicsDTOList) {
                            vehicleCharacteristicsService.registerVehicleCharacteristics(vc);
                        }
                    } catch (Exception e) {
                        log.error("initializeSampleData(): " + e.getMessage());
                    }
                }

                // 기 등록된 VehicleModel와 Vehicle이 존재하는지 검색
                final VehicleModelListDTO vehicleModelList = vehicleModelService.getVehicleModelList(VehicleModelDTO.builder().build(), page);
                final VehicleListDTO vehicleList = vehicleService.getVehicleList(VehicleDTO.builder().build(), page);

                if (vehicleModelList.getTotalCount() == 0 && vehicleList.getTotalCount() == 0) {
                    try (final InputStream is = new ClassPathResource(vehicleModelFile).getInputStream()) {
                        final List<VehicleModelDTO> vehicleModelDTOList = mapper.readValue(is, new TypeReference<List<VehicleModelDTO>>() {
                        });

                        for (final VehicleModelDTO vm : vehicleModelDTOList) {
                            final VehicleCharacteristics vehicleCharacteristics = getVehicleCharacteristicsDTO(vm);

                            if (vehicleCharacteristics != null) {
                                setVehicleModelDTOFromVehicleCharacteristics(vm, vehicleCharacteristics);
                            }

                            final VehicleModel savedVehicleModel = vehicleModelService.registerVehicleModel(vm);

                            for (int i = 0; i < 5; i++) {
                                final VehicleDTO vehicle = VehicleDTO.builder()
                                        .licensePlate(String.format("%02d가00%02d", savedVehicleModel.getVehicleModelId(), i))
                                        //													   .milesType(i % 2 == 0 ? MilesType.FIRSTMILE : MilesType.LASTMILE)
                                        .vehicleModelId(savedVehicleModel.getVehicleModelId())
                                        .build();

                                if (vehicleCharacteristics != null) {
                                    setVehicleDTOFromVehicleCharacteristics(vehicle, vehicleCharacteristics);
                                }

                                vehicleService.registerVehicle(vehicle);
                            }
                        }
                    } catch (Exception e) {
                        log.error("initializeSampleData(): " + e.getMessage());
                    }
                }

                log.info("Default vehicle information initialized");
            } catch (Exception e) {
                log.error("Default vehicle information not initialized: {}", e.getMessage());
//            throw e;
            }
        }
    }

    private VehicleCharacteristics getVehicleCharacteristicsDTO(final VehicleModelDTO vehicleModelDTO) {

        final VehicleCharacteristicsDTO vehicleCharacteristicsDTO = VehicleCharacteristicsDTO.builder()
                .vehicleType(vehicleModelDTO.getVehicleType())
                .fuelType(vehicleModelDTO.getFuelType())
                .sizeType(vehicleModelDTO.getSizeType())
                .wheelDrvType(vehicleModelDTO.getWheelDrvType())
                .build();

        final List<VehicleCharacteristics> vehicleCharacteristicsList = vehicleCharacteristicsService.getVehicleCharacteristicsList(vehicleCharacteristicsDTO);

        if (vehicleCharacteristicsList.size() > 0) {
            return vehicleCharacteristicsList.get(0);
        }

        return null;
    }

    private void setVehicleModelDTOFromVehicleCharacteristics(final VehicleModelDTO vm, final VehicleCharacteristics vehicleCharacteristics) {

        vm.setFrontalAreaSQFT(vehicleCharacteristics.getFrontalAreaSQFT());
        vm.setFrontalAreaSQM(vehicleCharacteristics.getFrontalAreaSQM());
    }

    private void setVehicleDTOFromVehicleCharacteristics(final VehicleDTO vehicle, final VehicleCharacteristics vehicleCharacteristics) {

        vehicle.setDrivetrainLoss(vehicleCharacteristics.getDrivetrainLoss());
        vehicle.setDrivetrainEfficiency(vehicleCharacteristics.getDrivetrainEfficiency());
        vehicle.setRollingResistance(vehicleCharacteristics.getRollingResistance());
        vehicle.setDragCoefficient(vehicleCharacteristics.getDragCoefficient());
        vehicle.setEngineEfficiency(vehicleCharacteristics.getEngineEfficiency());
    }

}
