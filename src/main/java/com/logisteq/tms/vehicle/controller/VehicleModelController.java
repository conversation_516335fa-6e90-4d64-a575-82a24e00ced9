package com.logisteq.tms.vehicle.controller;

import com.logisteq.tms.vehicle.dto.VehicleModelDTO;
import com.logisteq.tms.vehicle.dto.VehicleModelListDTO;
import com.logisteq.tms.vehicle.service.VehicleModelService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "VehicleModel")
@Validated
@RestController
public class VehicleModelController {

	private final VehicleModelService vehicleModelService;

	@Autowired
	public VehicleModelController(final VehicleModelService vehicleModelService) {

		this.vehicleModelService = vehicleModelService;
	}

	/**
	 * Vehicle Model 등록
	 * 
	 * @param vehicleModelDTO
	 */
	@Hidden
	@PostMapping(value = ApiUrl.VEHICLE_MODEL, consumes = MediaType.APPLICATION_JSON_VALUE)
	@ResponseStatus(HttpStatus.CREATED)
	public void registerVehicleModel(@RequestBody(required = true) VehicleModelDTO vehicleModelDTO) {

		vehicleModelService.registerVehicleModel(vehicleModelDTO);
	}

	/**
	 * Vehicle Model 리스트 조회
	 * 
	 * @param vehicleModelDTO
	 * @param pageable
	 * @return
	 */
	@Hidden
	@GetMapping(value = ApiUrl.VEHICLE_MODEL)
	public VehicleModelListDTO getVehicleModelList(@ModelAttribute VehicleModelDTO vehicleModelDTO,
			@PageableDefault(size = 100/*10*/, sort = "vehicleModelId", direction = Sort.Direction.DESC) Pageable pageable) {

		return vehicleModelService.getVehicleModelList(vehicleModelDTO, pageable);
	}

	/**
	 * Vehicle Model 단일 조회
	 * 
	 * @param vehicleModelId
	 * @return
	 */
	@Hidden
	@GetMapping(ApiUrl.VEHICLE_MODEL_ITEM)
	public VehicleModelDTO getVehicleModel(@PathVariable(required = true) Long vehicleModelId) {

		return VehicleModelDTO.parseFromVehicleModel(vehicleModelService.getVehicleModel(vehicleModelId));
	}

	/**
	 * Vehicle Model 수정
	 * 
	 * @param vehicleModelId
	 * @param vehicleModelDTO
	 * @return
	 */
	@Hidden
	@PutMapping(ApiUrl.VEHICLE_MODEL_ITEM)
	public VehicleModelDTO updateVehicleModel(@PathVariable(required = true) Long vehicleModelId,
			@RequestBody(required = true) VehicleModelDTO vehicleModelDTO) {

		return VehicleModelDTO.parseFromVehicleModel(vehicleModelService.updateVehicleModel(vehicleModelId, vehicleModelDTO));
	}

	/**
	 * Vehicle Model 삭제
	 * 
	 * @param vehicleModelId
	 */
	@Hidden
	@DeleteMapping(ApiUrl.VEHICLE_MODEL_ITEM)
	public void deleteVehicleModel(@PathVariable(required = true) Long vehicleModelId) {

		vehicleModelService.deleteVehicleModel(vehicleModelId);
	}

}
