package com.logisteq.tms.vehicle.domain;

import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity@Table
@Getter@Setter
@NoArgsConstructor@AllArgsConstructor
@Builder
public class VehicleDriveInfo {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long vehicleDriveInfoId;

	@Column(nullable = false, 	columnDefinition = "BIGINT(20) NOT NULL comment 'Vehicle-FK'")
	private Long vehicleId;
	@Column(nullable = false, 	columnDefinition = "BIGINT(20) NOT NULL comment 'Rider-FK'")
	private Long riderId; //현재 운전자

	//delivery 단위로
	@Column(nullable = false)
	private long deliveryId; // not null(DeliveryStatus가  GOING("배송중"), REJECTED("거절")	, FAILURE("배송실패"), COMPLETED("배송완료") 일때 만들어져야 한다. 아마도?)

	private LocalDateTime driveVehicleStartDt; // 운행시작일시
	private LocalDateTime driveVehicleEndDt; // 운행종료일시
	private Float beforeKm; //운행시작전 주행거리 km
	private Float afterKm; //운행 종료후 주행거리 km

	private long startTracksInfoId;
	private long endTracksInfoId;

	//특정 이벤트 단위로 할때 조아 보임
	private Float grossWeight; // 현재 총중량(짐+사람+차)
	private Float loadWeight;  // 현재적재중량(짐)
	private Float passengerWeight; //승차인원의 중량(사람+드라이버)
	private Integer numberOfCurrentPassengers; //현재 탑승한 인원
}