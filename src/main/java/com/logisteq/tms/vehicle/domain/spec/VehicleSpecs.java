package com.logisteq.tms.vehicle.domain.spec;

import com.logisteq.tms.vehicle.domain.Vehicle;
import com.logisteq.tms.vehicle.dto.VehicleDTO;
import com.logisteq.tms.vehicle.types.FuelType;
import com.logisteq.tms.vehicle.types.MilesType;
import com.logisteq.tms.vehicle.types.VehicleType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Objects;

public final class VehicleSpecs {
	private static final String KEY_VEHICLE_ID = "vehicleId";
	private static final String KEY_VEHICLE_LICENSE_PLATE = "licensePlate";

	// Vehicle 배차 특성
	private static final String KEY_MILES_TYPE = "milesType";
	private static final String KEY_IS_DISPATCHING = "isDispatching";
	private static final String KEY_CURRENT_PASSENGER_CAPACITY = "currentPassengerCapacity";
	private static final String KEY_CURRENT_WEIGHT_CAPACITY = "currentWeightCapacity";
	private static final String KEY_CURRENT_DISTANCE_CAPACITY = "currentDistanceCapacity";

	// Vehicle Model 특성
	private static final String KEY_VEHICLE_MODEL = "vehicleModel";
	private static final String KEY_VEHICLE_TYPE = "vehicleType";
	private static final String KEY_FUEL_TYPE = "fuelType";

	public static Specification<Vehicle> eqLicensePlate(String licensePlate) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.equal(root.get(KEY_VEHICLE_LICENSE_PLATE), licensePlate);
		};
	}

	public static Specification<Vehicle> containLicensePlate(String licensePlate) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.like(root.get(KEY_VEHICLE_LICENSE_PLATE), "%" + licensePlate + "%");
		};
	}

	private static Specification<Vehicle> eqIsDispatching(Boolean isDispatching) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.equal(root.get(KEY_IS_DISPATCHING), isDispatching);
		};
	}

	private static Specification<Vehicle> eqMilesType(MilesType milesType) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.equal(root.get(KEY_MILES_TYPE), milesType);
		};
	}

	private static Specification<Vehicle> geCurrentPassengerCapacity(Integer passengerCapacity) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.ge(root.get(KEY_CURRENT_PASSENGER_CAPACITY), passengerCapacity);
		};
	}

	private static Specification<Vehicle> geCurrentWeightCapacity(Integer weightCapacity) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.ge(root.get(KEY_CURRENT_WEIGHT_CAPACITY), weightCapacity);
		};
	}

	private static Specification<Vehicle> geCurrentDistanceCapacity(Integer distanceCapacity) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.ge(root.get(KEY_CURRENT_DISTANCE_CAPACITY), distanceCapacity);
		};
	}

	public static Specification<Vehicle> inVehicleIdList(List<Long> vehicleIdList) {
		return (root, query, criteriaBuilder) -> {
			if (vehicleIdList != null && !vehicleIdList.isEmpty()) {
				return root.get(KEY_VEHICLE_ID).in(vehicleIdList);
			} else {
				return criteriaBuilder.and();
			}
		};
	}

	public static Specification<Vehicle> notInVehicleIdList(List<Long> vehicleIdList) {
		return (root, query, criteriaBuilder) -> {
			if (vehicleIdList != null && !vehicleIdList.isEmpty()) {
				return root.get(KEY_VEHICLE_ID).in(vehicleIdList).not();
			} else {
				return criteriaBuilder.and();
			}
		};
	}

	private static Specification<Vehicle> eqVehicleType(VehicleType vehicleType) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.equal(root.get(KEY_VEHICLE_MODEL).get(KEY_VEHICLE_TYPE), vehicleType.getIndex());
		};
	}

	private static Specification<Vehicle> eqFuelType(FuelType fuelType) {
		return (root, query, criteriaBuilder) -> {
			return criteriaBuilder.equal(root.get(KEY_VEHICLE_MODEL).get(KEY_FUEL_TYPE), fuelType.getIndex());
		};
	}

	/**
	 * Vehicle Model 검색을 위한 specs 작성
	 * 
	 * @param vehicleDto
	 * @return
	 */
	public static Specification<Vehicle> composeSpecs(VehicleDTO vehicleDto) {
		Specification<Vehicle> specs = Specification.where(null);

		// licensePlate 포함하는지 검색
		if (StringUtils.isNotBlank(vehicleDto.getLicensePlate())) {
			specs = specs.and(containLicensePlate(vehicleDto.getLicensePlate()));
		}

		// isDispatching 일치 검색
		if (Objects.nonNull(vehicleDto.getIsDispatching())) {
			specs = specs.and(eqIsDispatching(vehicleDto.getIsDispatching()));
		}

		// CurrentPassengerCapacity가 충분한 것만 검색
		if (Objects.nonNull(vehicleDto.getPassengerCapacity())) {
			specs = specs.and(geCurrentPassengerCapacity(vehicleDto.getPassengerCapacity()));
		}

		// CurrentWeightCapacity가 충분한 것만 검색
		if (Objects.nonNull(vehicleDto.getWeightCapacity())) {
			specs = specs.and(geCurrentWeightCapacity(vehicleDto.getWeightCapacity()));
		}

		// CurrentDistanceCapacity가 충분한 것만 검색
		if (Objects.nonNull(vehicleDto.getDistanceCapacity())) {
			specs = specs.and(geCurrentDistanceCapacity(vehicleDto.getDistanceCapacity()));
		}

		// Vehicle Model 검색(vehicle type)
		if (Objects.nonNull(vehicleDto.getVehicleType())) {
			specs = specs.and(eqVehicleType(vehicleDto.getVehicleType()));
		}

		// Vehicle Model 검색(fuel type)
		if (Objects.nonNull(vehicleDto.getFuelType())) {
			specs = specs.and(eqFuelType(vehicleDto.getFuelType()));
		}
		return specs;
	}

	/**
	 * 등록시 입력받은 정보가 존재하는지 검색을 위한 specs 작성
	 * 
	 * @param vehicleDto
	 * @return
	 */
	public static Specification<Vehicle> composeKeySpec(VehicleDTO vehicleDto) {
		Specification<Vehicle> specs = Specification.where(null);

		if (StringUtils.isNotBlank(vehicleDto.getLicensePlate())) {
			specs = specs.and(eqLicensePlate(vehicleDto.getLicensePlate()));
		}

		return specs;
	}
}