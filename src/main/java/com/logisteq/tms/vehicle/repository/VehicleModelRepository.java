package com.logisteq.tms.vehicle.repository;

import com.logisteq.tms.vehicle.domain.VehicleModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface VehicleModelRepository extends JpaRepository<VehicleModel, Long>,  JpaSpecificationExecutor<VehicleModel>{
//    Optional<VehicleModel> findOneByModelName( String modelName );
        //가장 최근에 생성된 ModelName 이 0 번에 위치하도록 한다.
        List<VehicleModel> findByModelNameOrderByCreateAtDesc(String modelName );
}
