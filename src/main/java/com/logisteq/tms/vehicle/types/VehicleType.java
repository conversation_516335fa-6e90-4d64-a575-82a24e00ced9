package com.logisteq.tms.vehicle.types;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.logisteq.common.component.excel.annotation.PxlExportValue;
import com.logisteq.common.component.excel.annotation.PxlImportValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum VehicleType {

    TRUCK_CARGO(
            0,
            Arrays.asList("Truck_Cargo", "SUV/Truck"),
            Arrays.asList("트럭_카고", "SUV/트럭", "1종(승용차/소형승합차/소형화물차)")
    ),
    TRUCK_TOP(
            1,
            Arrays.asList("Truck_Top"),
            Arrays.asList("트럭_탑")
    ),
    TRUCK_WINGBODY(
            2,
            Arrays.asList("Truck_Wingbody"),
            Arrays.asList("트럭_윙바디")
    ),
    TRUCK_COLD_STORAGE(
            3,
            Arrays.asList("Truck_ColdStorage"),
            Arrays.asList("트럭_냉장")
    ),
    TRUCK_REFRIGERATED(
            4,
            Arrays.asList("Truck_Regrigerated"),
            Arrays.asList("트럭_냉동")
    ),
    TRUCK_LIFT(
            5,
            Arrays.asList("Truck_Lift"),
            Arrays.asList("트럭_리프트")
    ),
    TRUCK_NONVIBRATION(
            6,
            Arrays.asList("Truck_Nonvibration"),
            Arrays.asList("트럭_무진동")
    ),
    VAN(
            7,
            Arrays.asList("Van"),
            Arrays.asList("승합차")
    ),
    SEDAN(
            8,
            Arrays.asList("Sedan", "Sedan/Coupe", "Hatchback/Wagon"),
            Arrays.asList("승용차", "세단/쿠페", "해치백/웨건")
    ),
    DAMAS(
            9,
            Arrays.asList("Damas"),
            Arrays.asList("다마스")
    ),
    BIKE(
            10,
            Arrays.asList("Bike", "Motorbike"),
            Arrays.asList("이륜차", "오토바이")
    ),
    PEDESTRIAN(
            11,
            Arrays.asList("Pedestrian"),
            Arrays.asList("도보")
    ),
    ARMROLL(
            12,
            Arrays.asList("Armroll"),
            Arrays.asList("암롤차량")
    ),
    COMPACTOR(
            13,
            Arrays.asList("Compactor"),
            Arrays.asList("압축진개차량")
    ),
    NORMAL(
            14,
            Arrays.asList("Normal"),
            Arrays.asList("일반차량")
    ),
    ;

    public static final String COLUMN_DEFINITION = "TINYINT NULL DEFAULT NULL"
            + " COMMENT '0:트럭_카고, 1:트럭_탑, 2:트럭_윙바디, 3:트럭_냉장, 4:트럭_냉동, 5:트럭_리프트, 6:트럭_무진동, 7:승합차, 8:승용차, 9:다마스, 10:이륜차, 11:도보, 12:암롤차량, 13:압축진개차량, 14:일반차량'";

    private final Integer index;
    private final List<String> english;
    private final List<String> korean;

    private static final Map<Integer, VehicleType> indexToEnum = Stream
            .of(values())
            .collect(Collectors.toMap(e -> e.index, e -> e));

    private static final Map<String, VehicleType> englishToEnum = Stream
            .of(values())
            .map(e ->
                    e.getEnglish()
                            .stream()
                            .collect(Collectors.toMap(str -> StringUtils.deleteWhitespace(str).toUpperCase(), str -> e)))
            .flatMap(map -> map.entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    private static final Map<String, VehicleType> koreanToEnum = Stream
            .of(values())
            .map(e ->
                    e.getKorean()
                            .stream()
                            .collect(Collectors.toMap(str -> StringUtils.deleteWhitespace(str), str -> e)))
            .flatMap(map -> map.entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    public static VehicleType fromEnglish(final String str) {
        final VehicleType value = englishToEnum.get(StringUtils.deleteWhitespace(str).toUpperCase());

        return value;
    }

    public static VehicleType fromKorean(final String str) {
        final VehicleType value = koreanToEnum.get(StringUtils.deleteWhitespace(str));

        return value;
    }

    @JsonCreator
    public static VehicleType getVehicleTypeFromJson(final String str) {
        return fromKorean(str);
    }

    @JsonValue
    public String getJsonFromVehicleType() {
        return korean.get(0);
    }

    public static class ApiParamToVehicleTypeConverter
            implements org.springframework.core.convert.converter.Converter<String, VehicleType> {

        @Override
        public VehicleType convert(String source) {
            return VehicleType.fromKorean(source);
        }
    }

    @PxlImportValue
    public static VehicleType toPxlImportValue(final String str) {
        return fromKorean(str);
    }

    @PxlExportValue
    public String toPxlExportValue() {
        return korean.get(0);
    }

    @Override
    public String toString() {
        return korean.get(0);
    }

    @Converter(autoApply = true)
    public static class VehicleTypeConverter implements AttributeConverter<VehicleType, Integer> {

        @Override
        public Integer convertToDatabaseColumn(VehicleType vehicleType) {
            if (vehicleType == null) {
                return null;
            }

            return vehicleType.getIndex();
        }

        @Override
        public VehicleType convertToEntityAttribute(Integer vehicleIndex) {
            if (vehicleIndex == null) {
                return null;
            }

            return VehicleType.indexToEnum.get(vehicleIndex);
        }
    }

}
