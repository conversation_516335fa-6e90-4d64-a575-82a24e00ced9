package com.logisteq.tms.vehicle.service;

import com.logisteq.common.exception.InvalidParameterException;
import com.logisteq.common.exception.ItemNotFoundException;
import com.logisteq.tms.vehicle.domain.VehicleDriveInfo;
import com.logisteq.tms.vehicle.domain.spec.VehicleDriveInfoSpecs;
import com.logisteq.tms.vehicle.dto.VehicleDriveInfoDTO;
import com.logisteq.tms.vehicle.dto.VehicleDriveInfoListDTO;
import com.logisteq.tms.vehicle.repository.VehicleDriveInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Optional;

@Slf4j
@Validated
@Service
public class VehicleDriveInfoService {

	private final VehicleDriveInfoRepository vehicleDriveInfoRepository;

	@Autowired
	public VehicleDriveInfoService(final VehicleDriveInfoRepository vehicleDriveInfoRepository) {

		this.vehicleDriveInfoRepository = vehicleDriveInfoRepository;
	}

	/**
	 * Vehicle Drive Info 등록
	 * 
	 * @param vehicleDriveInfoDTO
	 * @return
	 */
	@Transactional
	public VehicleDriveInfo registerVehicleDriveInfo(VehicleDriveInfoDTO vehicleDriveInfoDTO) {
		try {
			VehicleDriveInfo vehicleDriveInfo = VehicleDriveInfo.builder()
												 .vehicleDriveInfoId(vehicleDriveInfoDTO.getVehicleDriveInfoId())
												 .vehicleId(vehicleDriveInfoDTO.getVehicleId())
												 .riderId(vehicleDriveInfoDTO.getRiderId())
												 .driveVehicleStartDt(vehicleDriveInfoDTO.getDriveVehicleStartDt())
												 .driveVehicleEndDt(vehicleDriveInfoDTO.getDriveVehicleEndDt())
												 .beforeKm(vehicleDriveInfoDTO.getBeforeKm())
												 .afterKm(vehicleDriveInfoDTO.getAfterKm())
												 .grossWeight(vehicleDriveInfoDTO.getGrossWeight())
												 .loadWeight(vehicleDriveInfoDTO.getLoadWeight())
												 .passengerWeight(vehicleDriveInfoDTO.getPassengerWeight())
												 .numberOfCurrentPassengers(vehicleDriveInfoDTO.getNumberOfCurrentPassengers())
												 .build();

			return vehicleDriveInfoRepository.save(vehicleDriveInfo);
		} catch (Exception e) {
			log.error("registerVehicleDriceInfo() " + e.getMessage());
		}

		throw new InvalidParameterException("차량 등록에 실패하였습니다");
	}

	/**
	 * Vehicle Drive Info 리스트 조회
	 * 
	 * @param vehicleDriveInfoDTO
	 * @param pageable
	 * @return
	 */
	public VehicleDriveInfoListDTO getVehicleDriveInfoList(VehicleDriveInfoDTO vehicleDriveInfoDTO, Pageable pageable) {
		Specification<VehicleDriveInfo> specs = VehicleDriveInfoSpecs.composeSpecs(vehicleDriveInfoDTO);
		Page<VehicleDriveInfo> result = vehicleDriveInfoRepository.findAll(specs, pageable);

		return VehicleDriveInfoListDTO.parseFromVehicleDriveInfoList(result);
	}

	/**
	 * Vehicle Drive Info 단일 조회
	 * 
	 * @param vehicleDriveInfoId
	 * @return
	 */
	public VehicleDriveInfo getVehicleDriveInfo(Long vehicleDriveInfoId) {
		return findByVehicleDriveInfoId(vehicleDriveInfoId);
	}

	/**
	 * Vehicle Drive Info 수정
	 * 
	 * @param vehicleDriveInfoId
	 * @param vehicleDriveInfoDTO
	 * @return
	 */
	@Transactional
	public VehicleDriveInfo updateVehicleDriveInfo(Long vehicleDriveInfoId, VehicleDriveInfoDTO vehicleDriveInfoDTO) {
		VehicleDriveInfo vehicleDriveInfo = findByVehicleDriveInfoId(vehicleDriveInfoId);

		vehicleDriveInfo.setVehicleId(vehicleDriveInfoDTO.getVehicleId());
		vehicleDriveInfo.setRiderId(vehicleDriveInfoDTO.getRiderId());
		vehicleDriveInfo.setDriveVehicleStartDt(vehicleDriveInfoDTO.getDriveVehicleStartDt());
		vehicleDriveInfo.setDriveVehicleEndDt(vehicleDriveInfoDTO.getDriveVehicleEndDt());
		vehicleDriveInfo.setBeforeKm(vehicleDriveInfoDTO.getBeforeKm());
		vehicleDriveInfo.setAfterKm(vehicleDriveInfoDTO.getAfterKm());
		vehicleDriveInfo.setStartTracksInfoId(vehicleDriveInfoDTO.getStartTracksInfoId());
		vehicleDriveInfo.setEndTracksInfoId(vehicleDriveInfoDTO.getEndTracksInfoId());
		vehicleDriveInfo.setGrossWeight(vehicleDriveInfoDTO.getGrossWeight());
		vehicleDriveInfo.setLoadWeight(vehicleDriveInfoDTO.getLoadWeight());
		vehicleDriveInfo.setPassengerWeight(vehicleDriveInfoDTO.getPassengerWeight());
		vehicleDriveInfo.setNumberOfCurrentPassengers(vehicleDriveInfoDTO.getNumberOfCurrentPassengers());
		
		return vehicleDriveInfoRepository.save(vehicleDriveInfo);
	}

	/**
	 * Vehicle Drive Info 삭제
	 * 
	 * @param vehicleDriveInfoId
	 */
	@Transactional
	public void deleteVehicleDriveInfo(Long vehicleDriveInfoId) {
		VehicleDriveInfo vehicleDriveInfo = findByVehicleDriveInfoId(vehicleDriveInfoId);
		vehicleDriveInfoRepository.delete(vehicleDriveInfo);
	}

	/**
	 * vehicleDriveInfoId로 VehicleDriceInfo 조회
	 * 
	 * @param vehicleDriveInfoId
	 * @return
	 */
	public VehicleDriveInfo findByVehicleDriveInfoId(Long vehicleDriveInfoId) {
		Optional<VehicleDriveInfo> vehicleDriveInfo = vehicleDriveInfoRepository.findById(vehicleDriveInfoId);

		if (vehicleDriveInfo.isPresent()) {
			return vehicleDriveInfo.get();
		} else {
			throw new ItemNotFoundException();
		}
	}
}