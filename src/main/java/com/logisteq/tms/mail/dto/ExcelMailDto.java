package com.logisteq.tms.mail.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
public class ExcelMailDto {

    private String toEmail;
    private String excelFileName;
    private File excelFile;
    private List<MultipartFile> multipartFile;
}