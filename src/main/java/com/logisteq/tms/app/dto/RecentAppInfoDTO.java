package com.logisteq.tms.app.dto;

import com.logisteq.tms.app.domain.suppl.AppResourceType;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecentAppInfoDTO {

    private Long id;

    private String name;

    private String binaryVersion;

    private String resourceVersion;

    private String binaryDownloadUrl;

    private String resourceDownloadUrl;

    private Long binaryFileSize;

    private Long resourceFileSize;

    private LocalDateTime binaryUpdateAt;

    private LocalDateTime resourceUpdateAt;

    private AppResourceType resourceType;
}
