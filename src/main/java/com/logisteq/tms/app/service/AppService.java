package com.logisteq.tms.app.service;

import com.logisteq.common.exception.CustomException;
import com.logisteq.tms.app.domain.*;
import com.logisteq.tms.app.domain.suppl.AppResourceType;
import com.logisteq.tms.app.repository.AppBinaryResourceRepository;
import com.logisteq.tms.app.repository.AppBinaryVersionRepository;
import com.logisteq.tms.app.repository.AppRepository;
import com.logisteq.tms.app.repository.AppResourceVersionRepository;
import org.apache.maven.artifact.versioning.ComparableVersion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.transaction.Transactional;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 앱 서비스
 */
@Service
@Validated
public class AppService {

    private final AppRepository appRepository;
    private final AppBinaryVersionRepository appBinaryVersionRepository;
    private final AppResourceVersionRepository appResourceVersionRepository;
    private final AppBinaryResourceRepository appBinaryResourceRepository;

    private final CustomException notExistBinaryException = new CustomException(HttpStatus.BAD_REQUEST, "앱 바이너리 버전 정보를 찾을수 없습니다.", false);
    private final CustomException notExistResourceException = new CustomException(HttpStatus.BAD_REQUEST, "앱 리소스 버전 정보를 찾을수 없습니다.", false);

    // 버전 비교 구현체
    private static Comparator<AppFileVersion> versionComparator = (a1, a2) -> {
        final ComparableVersion v1 = new ComparableVersion(a1.getId());
        final ComparableVersion v2 = new ComparableVersion(a2.getId());
        return v1.compareTo(v2);
    };


    @Autowired
    public AppService(final AppRepository appRepository,
                      final AppBinaryVersionRepository appBinaryVersionRepository,
                      final AppResourceVersionRepository appResourceVersionRepository,
                      final AppBinaryResourceRepository appBinaryResourceRepository) {

        this.appRepository = appRepository;
        this.appBinaryVersionRepository = appBinaryVersionRepository;
        this.appResourceVersionRepository = appResourceVersionRepository;
        this.appBinaryResourceRepository = appBinaryResourceRepository;
    }

    /**
     * 최근 앱 버전 조회 (by AppId)
     *
     * @param appId 앱 아이디
     * @return App
     */
    public App getRecentAppVersion(@NotNull Long appId) {
        Optional<App> appOpt = appRepository.findById(appId);
        return getRecentAppVersion(appOpt);
    }

    /**
     * 최신 앱 버전 정보 조회
     *
     * @param appOpt 앱 정보
     * @return App
     */
    private App getRecentAppVersion(final Optional<App> appOpt) {
        App app = appOpt.orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "앱 정보가 없습니다.", false));

        // 최신 앱 바이너리 버전 조회
        Optional<AppBinaryVersion> appBinaryVersionOpt = app.getBinaryVersions().stream().max(versionComparator);
        AppBinaryVersion appBinaryVersion = appBinaryVersionOpt.orElseThrow(() -> notExistBinaryException);
        app.setBinaryVersions(Collections.singletonList(appBinaryVersion));

        // 최신 앱 리소스 버전 조회
        Optional<AppResourceVersion> appResourceVersionOpt = appBinaryVersion.getAppBinaryResourceMapper().stream()
                .map(AppBinaryResource::getAppResourceVersion)
                .filter(rv -> rv.getResourceType() == AppResourceType.FULL)
                .max(versionComparator);
        AppResourceVersion appResourceVersion = appResourceVersionOpt.orElseThrow(() -> notExistResourceException);
        app.setResourceVersions(Collections.singletonList(appResourceVersion));

        // TODO : 패치 타입 리소스 처리는 어떻게 할지?

        return app;
    }

    public String getAppBinaryUrl(@NotNull Long appId, @NotBlank String version) {

        final App app = appRepository.findById(appId).orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "앱 정보가 없습니다.", false));
        final List<AppBinaryVersion> appBinaryVersionList = app.getBinaryVersions();
        AppBinaryVersion appBinaryVersion;

        if ("latest".equals(version) || "recent".equals(version)) {
            appBinaryVersion = appBinaryVersionList.stream()
                    .max(versionComparator)
                    .orElseThrow(() -> notExistBinaryException);
        } else {
            appBinaryVersion = appBinaryVersionList.stream()
                    .filter(v -> version.equals(v.getId()))
                    .findFirst()
                    .orElseThrow(() -> notExistBinaryException);
        }
        return appBinaryVersion.getFileDownloadUrl();
    }

    public String getAppResourceUrl(@NotNull Long appId, @NotBlank String version) {

        final App app = appRepository.findById(appId).orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "앱 정보가 없습니다.", false));
        final List<AppResourceVersion> appResourceVersionList = app.getResourceVersions();
        AppResourceVersion appResourceVersion;

        if ("latest".equals(version) || "recent".equals(version)) {
            appResourceVersion = appResourceVersionList.stream()
                    .max(versionComparator)
                    .orElseThrow(() -> notExistResourceException);
        } else {
            appResourceVersion = appResourceVersionList.stream()
                    .filter(v -> version.equals(v.getId()))
                    .findFirst()
                    .orElseThrow(() -> notExistResourceException);
        }
        return appResourceVersion.getFileDownloadUrl();
    }

    /**
     * 앱 등록
     *
     * @param app 앱 정보
     */
    @Transactional
    public void registerApp(@NotNull App app) {
        appRepository.save(app);
    }

    /**
     * 앱 조회
     *
     * @param appId
     * @return
     */
    public Optional<App> getApp(@NotNull Long appId) {
        return appRepository.findById(appId);
    }

    /**
     * 앱 조회 with 유효성 검사
     *
     * @param appId 앱 아이디
     * @param fileVersion 파일 버전
     * @param mappingVersion 맵핑 파일 버전
     * @param isBinary 바이너리 여부
     * @return 앱 정보
     */
    public App getAppWithCheckValidation(@NotNull Long appId, @NotNull String fileVersion,
                                         String mappingVersion, @NotNull Boolean isBinary) {
        final App foundApp = getApp(appId)
                .orElseThrow(() -> new CustomException(HttpStatus.BAD_REQUEST, "존재하지 않는 앱 아이디 입니다.", false));

        List<AppFileVersion> appFileVersion;
        List<AppFileVersion> appRelationVersion;
        if (isBinary) {
            appFileVersion = foundApp.getBinaryVersions().stream().map(bv -> (AppFileVersion)bv).collect(Collectors.toList());
            appRelationVersion = foundApp.getResourceVersions().stream().map(bv -> (AppFileVersion)bv).collect(Collectors.toList());
        } else {
            appFileVersion = foundApp.getResourceVersions().stream().map(bv -> (AppFileVersion)bv).collect(Collectors.toList());
            appRelationVersion = foundApp.getBinaryVersions().stream().map(bv -> (AppFileVersion)bv).collect(Collectors.toList());
        }

        // 버전 중복 체크
//        boolean isSameVersion = appFileVersion.stream()
//                .anyMatch(resourceVersion -> resourceVersion.getId().equals(fileVersion));
//        if (isSameVersion) {
//            throw new CustomException(HttpStatus.BAD_REQUEST, "이미 등록된 버전("+ fileVersion +") 입니다.", false);
//        }

        // 맵핑 버전 존재 여부 체크
        boolean existResourceVersion = appRelationVersion.stream()
                .anyMatch(resourceVersion -> resourceVersion.getId().equals(mappingVersion));
        if (mappingVersion != null && !existResourceVersion) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "존재하지 않는 버전(" + mappingVersion + ") 입니다.", false);
        }

        return foundApp;
    }

    /**
     * 앱 바이너리 버전 등록
     *
     * @param appFileVersion 앱 파일 버전 정보
     */
    @Transactional
    public AppBinaryVersion registerAppBinaryVersion(@NotNull AppFileVersion appFileVersion) {
        AppBinaryVersion appBinaryVersion = new AppBinaryVersion(appFileVersion);
        return appBinaryVersionRepository.save(appBinaryVersion);
    }

    /**
     * 앱 바이너리 버전 조회
     *
     * @param appId 앱 아이디
     * @param binaryVersion 바이너리 버전
     * @return
     */
    public Optional<AppBinaryVersion> getAppBinaryVersion(Long appId, String binaryVersion) {
        return appBinaryVersionRepository.findById(new AppFileVersionId(binaryVersion, appId));
    }

    /**
     * 앱 리소스 버전 조회
     *
     * @param appId 앱 아이디
     * @param resourceVersion 리소스 버전 문자열
     * @return
     */
    public Optional<AppResourceVersion> getAppResourceVersion(Long appId, String resourceVersion) {
        return appResourceVersionRepository.findById(new AppFileVersionId(resourceVersion, appId));
    }

    /**
     * 앱 바이너리 & 리소스 맵핑 등록
     *
     * @param appBinaryVersion 앱 바이너리 버전 객체
     * @param appResourceVersion 앱 리소스 버전 객체
     */
    public void registerAppBinaryResource(@NotNull AppBinaryVersion appBinaryVersion, @NotNull AppResourceVersion appResourceVersion) {
        appBinaryResourceRepository.save(AppBinaryResource.builder()
                .appBinaryVersion(appBinaryVersion)
                .appResourceVersion(appResourceVersion)
                .build());
    }

    /**
     * 앱 리소스 버전 등록
     *
     * @param appFileVersion 앱 리소스 버전 객체
     * @param appResourceType 앱 리소스 타입
     * @return 저장된 앱 리소스 버전
     */
    @Transactional
    public AppResourceVersion registerAppResourceVersion(@NotNull AppFileVersion appFileVersion,
                                                         @NotNull AppResourceType appResourceType) {
        AppResourceVersion appResourceVersion = new AppResourceVersion(appFileVersion, appResourceType);
        return appResourceVersionRepository.save(appResourceVersion);
    }

    /**
     * 앱 파일 버전 조회
     *
     * @param appId 앱 아이디
     * @param version 버전
     * @param isBinary 바이너리 여부
     * @return
     */
    public AppFileVersion getAppFileVersion(@NotNull Long appId, @NotNull String version, @NotNull Boolean isBinary) {
        if (isBinary) {
            return getAppBinaryVersion(appId, version)
                    .orElseThrow(() -> notExistBinaryException);
        } else {
            return getAppResourceVersion(appId, version)
                    .orElseThrow(() -> notExistResourceException);
        }
    }

    /**
     * 앱 파일 버전 등록
     *
     * @param appFileVersion 앱 파일 버전
     * @param mappingFileVersion 맵핑 파일 버전
     * @param appResourceType 앱 리소스 타입
     * @param isBinary 바이너리 여부
     */
    @Transactional
    public void registerAppFileVersion(@NotNull AppFileVersion appFileVersion, String mappingFileVersion, AppResourceType appResourceType, @NotNull Boolean isBinary) {
        AppBinaryVersion appBinaryVersion;
        AppResourceVersion appResourceVersion;

        // 앱 파일 버전 등록
        if (isBinary) {
            appBinaryVersion = registerAppBinaryVersion(appFileVersion);
            appResourceVersion = getAppResourceVersion(appBinaryVersion.getApp().getId(), mappingFileVersion).orElse(null);
        } else {
            appResourceVersion = registerAppResourceVersion(appFileVersion, appResourceType);
            appBinaryVersion = getAppBinaryVersion(appResourceVersion.getApp().getId(), mappingFileVersion).orElse(null);
        }

        // 앱 바이너리 & 리소스 맵핑 등록
        if (appBinaryVersion != null && appResourceVersion != null) {
            registerAppBinaryResource(appBinaryVersion, appResourceVersion);
        }
    }

    /**
     * 앱 버전 맵핑
     *
     * @param appId 앱 아이디
     * @param binaryVersion 앱 바이너리 버전
     * @param resourceVersion 앱 리소스 버전
     */
    public void mappingAppVersion(Long appId, String binaryVersion, String resourceVersion) {

        // 바이너리 버전 조회
        AppBinaryVersion appBinaryVersion = getAppBinaryVersion(appId, binaryVersion).orElseThrow(() -> notExistBinaryException);

        // 리소스 버전 조회
        AppResourceVersion appResourceVersion = getAppResourceVersion(appId, resourceVersion).orElseThrow(() -> notExistResourceException);

        // 앱 바이너리 & 리소스 맵핑 등록
        registerAppBinaryResource(appBinaryVersion, appResourceVersion);
    }
}
