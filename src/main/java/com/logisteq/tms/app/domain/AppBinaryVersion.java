package com.logisteq.tms.app.domain;

import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Entity;
import javax.persistence.OneToMany;
import java.util.ArrayList;
import java.util.List;

/**
 * 앱 바이너리 버전 엔티티
 */
@Entity
@Getter @Setter
@NoArgsConstructor
@SuperBuilder
public class AppBinaryVersion extends AppFileVersion {

    /*
     * 앱 바이너리 & 리소스 맵퍼
     */
    @OneToMany(mappedBy = "appBinaryVersion")
    @Builder.Default
    private List<AppBinaryResource> appBinaryResourceMapper = new ArrayList<>();

    public AppBinaryVersion(AppFileVersion appFileVersion) {
        super(appFileVersion.getId(), appFileVersion.getApp(), appFileVersion.getFileDownloadUrl(),
                appFileVersion.getFileName(), appFileVersion.getFileSize());
    }
}
