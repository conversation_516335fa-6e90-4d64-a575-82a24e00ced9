class ProdService extends CommonService {
  constructor(mapModule, project, fields) {
    // super( project, fields );
    /* private fields */
    super();
    this.project = project;
    this.fields = fields;

    this.mapViewController = this.createMapViewController(mapModule, fields);
    this.profile = "dev";

    this.tempRiderCount = 0;
    this.tempDestCount = 0;

    this.modifyCommandList = [];

    //for Context Menu
    this.multipleSelectContextMenuList = [];
    this.contextMenuEvent = null;
    this.contextMenu = null;
    // this.createManager(this.mapViewController);

    //for tool tip
    this.toolTip = null;

    this.workingStartAddress = null;
    this.groupNameList = [];

    //순서 변경이 이루어 졌을때 값을 저장 하고 있다가 순서를 맞춰주는 데이터를 저장.
    this.storedSequenceData = [];


    //mouse-dragging
    this.isMouseDragging = false; //mouse drag 이벤트
    this.mouseDraggingMode = Constant.MOUSE_DRAGGING_MODE.DRAWING_RECTANGLE;
    this.mouseDraggingCatchTimer = -1;
    this.mouseDownPlace = null;
    this.mouseMoveOnRider = null;

    this.ctrlMouseDownEvent = {x: null, y: null, isPressed: false};
    this.shiftMouseDownEvent = {x: null, y: null, isPressed: false};
    this.mouseDownEvent = {x: null, y: null, isPressed: false};
    this.drawingmouseMoveEvent = {x: null, y: null};

    this.delayDrawingTimer = -1;

    this.mapSpeedUpMode = false; // 맵의 속도 우선 모드 . 불필요한 그리기 동작등은 없앰
    this.mapParamSetImageMask = false;

    this.locationEventStack = [];
    this.timelastUpdateLocation = 0;

    this.isRouteLineDisplay = false; // 경로선 보이게 하는 Flag

  }

  createMapViewController(mapModule, fields) {
    return new MapViewController(mapModule, fields);
  }

  getMapViewController() {
    return this.mapViewController;
  }

  loadImageFiles() {
    this.mapViewController.loadImageFiles();
  }

  /**
   * Service Common functions
   */

  changedZoomLevel(newValue, oldValue) {
    // 차량 Tag 처리 & 방문지 Pin 처리
    let needUpdateScreen = false;
    let show = (newValue <= 5);

    if (this.fields.savedShowRiderTimestamp) {
      this.mapViewController.clearRiderTimestamp().then();
      this.fields.savedShowRiderTimestamp = false;
      needUpdateScreen = true;
    }

    if (show != this.fields.savedShowScreenRiderPin) {
      for (let rider of this.project.riders) {
        // 차량 Tag 처리
        this.callSetRiderTagVisible(rider.riderId, show);

        if (rider.isSelected) {
          for (let destination of this.project.destinations) { // 방문자 Pin 처리
            this.callSetDestinationPinTagVisible(destination.deliveryId, show);
          }
        }
      }
      this.fields.savedShowScreenRiderPin = show;
      needUpdateScreen = true;
    }


    let labelLevel = this.callGetMapLevel(newValue);
    if (labelLevel != this.fields.savedScreenUpdatedMapLevel) { //Map Level 의 값이 변경되었을때만 처리한다.
      // 방문지 Label 처리

      for (let destination of this.project.destinations) {
        for (let i = 0; i < 2; i++) {
          const LABEL_TAG_ID = MapTagUtil.getDestinationLabelTagId(destination.deliveryId, i);
          this.mapViewController.setShowLabelByUserTag(LABEL_TAG_ID, i == labelLevel);
        }
      }

      this.fields.savedScreenUpdatedMapLevel = labelLevel;
      needUpdateScreen = true;
    }

    this.closeAllPopup();

    if (needUpdateScreen)
      this.callUpdateMapViewSync();
  }

  startTracking() {
    this.stopTracking();

    const _this = this;
    const intervalSeconds = 2 * 1000; // 너무 움직임이 듬성인 듯 해서 일단 2초로 변경 30 * 1000;

    try {
      console.log("[TRACKING] startTracking()");
      this.fields.pollingCurrentLocations = setInterval(function () {
        if (_this.fields.locationWorker) {
          _this.fields.locationWorkCompleted = false;
          _this.fields.locationWorker.postMessage({
            riders: _this.project.riders,
            reqUrl: Url.WEB.TRACKS_RIDER_LAST_LOCATIONS
          });
        }
        //불필요한 update 제거
        // _this.updateMapView();
      }, intervalSeconds);
    } catch (e) {
      console.error("[TRACKING] startTracking() ERROR: " + e);
      _this.stopTracking();
    }
  }

  stopTracking() {
    console.log("[TRACKING] stopTracking()");
    clearInterval(this.fields.pollingCurrentLocations);
    this.fields.pollingCurrentLocations = undefined;
  }

  showRiderTimeline() {

    //breadCrums 의 startTime,endTime을 계산한다.
    // const DEFAULT_WORKING_START_TIME = "09:00:00";
    const DEFAULT_WORKING_START_TIME = TimeUtil.getCurrentTimeStr();
    const DEFAULT_WORKING_TIME = 12;

    let earlyWorkingStartDateStr = null;

    this.project.riders.forEach(r => {
      let workingStartTime = r.workingStartTime != null ? r.workingStartTime : DEFAULT_WORKING_START_TIME;
      if (r.destinations.length > 0)
        if (r.destinations[0].deliveryStatus == Constant.DELIVERY_STATUS.SERVICING || r.destinations[0].deliveryStatus == Constant.DELIVERY_STATUS.COMPLETED ||
          r.destinations[0].deliveryStatus == Constant.DELIVERY_STATUS.REJECTED || r.destinations[0].deliveryStatus == Constant.DELIVERY_STATUS.FAILURE) {
          if (r.destinations[0].realStartTime != null && TimeUtil.compareTimeSecondsStr(r.destinations[0].realStartTime, workingStartTime) < 0) { //가장 이른 기사 시작시간을 구한다
            workingStartTime = r.destinations[0].realStartTime;
          }
        }
      if (earlyWorkingStartDateStr == null || TimeUtil.compareTimeSecondsStr(workingStartTime, earlyWorkingStartDateStr) < 0) { //가장 이른 기사 시작시간을 구한다
        earlyWorkingStartDateStr = workingStartTime;
      }
    });

    if (earlyWorkingStartDateStr == null)
      earlyWorkingStartDateStr = DEFAULT_WORKING_START_TIME;

    //breacrums 표시 시간 30분 시간 단위 맞추기 - startTime,endTime 을 정수가 아닌 dataTime의 문자열로 준다.(다음날 시간이 넘어가는 경우 대비한다)
    let startTime = TimeUtil.toDateFromTimeSecondsStr(earlyWorkingStartDateStr);
    startTime.setMinutes(startTime.getMinutes() > 30 ? 30 : 0);//30 분 단위로 시간을 맞춘다
    this.fields.breadCrums.startTime = startTime;

    let endTime = new Date(startTime);
    endTime.setMinutes(startTime.getMinutes() + (DEFAULT_WORKING_TIME * 60));// 기본 시작 종료는 DEFAULT_WORKING_TIME 시간으로 잡는다. 나중에 배송시간이나 기사 근무 종료 시간을 계산해서 넣는것도 고려해 보자
    this.fields.breadCrums.endTime = endTime;
    this.fields.breadCrums.isShow = true;

  }

  dismissRiderTimeline() {
    this.fields.breadCrums.isShow = false;
  }

  toggleMapViewMode() {
    this.fields.fullMapMode = !this.fields.fullMapMode;
    this.saveLocalStorage("setMapFullScreen", this.fields.fullMapMode);
    this.updateMapMapFullScreen();
  }

  updateMapMapFullScreen() {
    this.fields.fullMapMode = this.storageGetters().getMapFullScreen;

    if (this.fields.fullMapMode) {
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.MINIMIZE_TOOLBAR);
      window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.HIDE);
    } else {
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.NORMALIZE_TOOLBAR);
      window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.SHOW);
    }
  }


  mouseMove(event) {
    if (this.fields.isMouseDown) {
      if (Math.abs(this.fields.lastMousePoint.x - event.clientX) > 3 || Math.abs(this.fields.lastMousePoint.y - event.clientY) > 3) {
        this.fields.isMouseMove = true;
      }

      if (this.fields.isMouseMove) {
        let centerPos = this.mapViewController.screen2world(window.innerWidth / 2, window.innerHeight / 2);

        const mapBoundByCountry = MapUtil.getMapBoundByCountry(this.fields.selectedCountry);

        if (centerPos.x >= mapBoundByCountry.xMin && centerPos.x <= mapBoundByCountry.xMax
          && centerPos.y >= mapBoundByCountry.yMin && centerPos.y <= mapBoundByCountry.yMax) {
          this.fields.lastMousePoint.x = event.clientX;
          this.fields.lastMousePoint.y = event.clientY;

          this.fields.centerPos = centerPos;
        } else {
          this.setCenterFocus(this.fields.centerPos);
          this.fields.isMouseMove = false;
        }
        // Mobile Map Touch 주석처리
        //MapMove(event.clientX, event.clientY);
      }
      this.closeAllPopup();
    } else {
      if (Constant.SUPPORT_REAL_ROUTE_PATH) {
        if (this.fields.timerRiderTimestamp) {
          clearTimeout(this.fields.timerRiderTimestamp);
          this.fields.timerRiderTimestamp = null;
        }

        // const _this = this;
        // this.fields.timerRiderTimestamp = setTimeout(function() {
        // 	_this.displayRiderTimestamp(event);
        // }, 200);

        this.fields.timerRiderTimestamp = setTimeout(this.displayRiderTimestamp.bind(null, this, event), 500);
      }
    }

    this.processMouseDraggingOnMouseMove(event);

  }

  doubleClick(event) {
    const nextLocation = this.mapViewController.screen2world(event.clientX, event.clientY);
    this.mapViewController.setCenterFocus(nextLocation.x, nextLocation.y, -1, true);
    const mapBoundByCountry = MapUtil.getMapBoundByCountry(this.fields.selectedCountry);

    let _this = this;
    setTimeout(() => {
      if (nextLocation.x >= mapBoundByCountry.xMin && nextLocation.x <= mapBoundByCountry.xMax
        && nextLocation.y >= mapBoundByCountry.yMin && nextLocation.y <= mapBoundByCountry.yMax) {

        let centerPos = this.mapViewController.screen2world(window.innerWidth / 2, window.innerHeight / 2);
        if (centerPos.x >= mapBoundByCountry.xMin && centerPos.x <= mapBoundByCountry.xMax
          && centerPos.y >= mapBoundByCountry.yMin && centerPos.y <= mapBoundByCountry.yMax) {
          _this.fields.lastMousePoint.x = event.clientX;
          _this.fields.lastMousePoint.y = event.clientY;
          this.setCenterPosition(centerPos);
        }
      } else {
        _this.setCenterFocus(_this.fields.centerPos);
      }
    }, 700);
  }


  mouseDown(event) {
    this.fields.isMouseDown = true;
    this.fields.lastMousePoint.x = event.clientX;
    this.fields.lastMousePoint.y = event.clientY;
    document.getElementById("canvas").focus();

    //mouse-dragging
    this.processMouseDraggingOnMouseDown(event);

  }


  onAddDestinationBySearch(delivery) {

    window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.INIT_TOOLBARMODE);//Left-panel 서치 모드 닫기
    this.addDestinationByClick(delivery.x, delivery.y, Constant.PINMODE.DESTINATION, delivery.baseAddr);
  }

  //그룹 리스트를 리턴한다. 지워지더라도 기존에 있던 그룹은 없어지지 않도록 한다
  updateGroupNameList() {

    //매번 초기화 한다.
    this.groupNameList = MapUtil.getGroupNameList(this.project);
    return [Constant.NO_GROUP_KEYWORD_NAME, ...this.groupNameList];
  }

  //그룹 리스트를 리턴한다. 지워지더라도 기존에 있던 그룹은 없어지지 않도록 한다
  getRiderInfoList() {
    let riderList = [];

    for (const rider of this.project.riders) {
      riderList.push({riderId: rider.riderId, riderName: rider.name, groupName: rider.groupName})
    }

    return riderList;
  }

  /**
   * 지도 상의 클릭으로 방문지 추가
   */
  async addDestinationByClick(x, y, pinMode, addr) {
    if (!addr) {
      addr = await this.getSearchAddressByPoint(x, y);
    }

    //await this.getSearchEntrance(x, y, destination);

    this.tempDestCount++;
    let data = {};
    data.dest = {};

    data.dest.delivery = {
      'projectId': this.project.id,
      // 'deliveryId': Constant.ADDED_TEMP_FLAG_STRING + this.tempDestCount++,
      // 'deliveryId': DemoUtil.getLargestIdNumber(this.project.destinations) + 1,//[demo] 정수가 아닌 경우 cluster등에서 오류가 발생한다. 현재 가지고 있는 값 중에 가장 큰 값 +1 을 아이디로 설정한다
      'x': x, 'y': y,
      // 'type' : PlaceUtil.getDestinationType(pinMode),
      'type': PlaceUtil.getDestinationTypeByPinMode(pinMode),//[demo]
      'baseAddr': addr,
      'detailAddr': '',
      'isSelected': true,
    };

    data.viewMode = 'a';

    this.showDeliveryDetailPopup(data);
    return data.delivery;
  }

  handleSimulationWorker(event) {
    console.log("no used function handleSimulationWorker");
  }

  setRiderConnectionStateToMobileApp(rider, location) {

    const DISCONNECTION_PERMIT = 60;//60초 동안 위치 정보가 올라오지 않으면 끊긴것으로 간주한다

    if (location.timestamp) {
      let pastTimeUpdated = TimeUtil.getTimeStampFromNowByDateTimeSeconds(location.timestamp);

      if (rider.isConnectedToMobileApp === undefined) {
        rider.isConnectedToMobileApp = false;
      }

      if (pastTimeUpdated > DISCONNECTION_PERMIT) {
        if (rider.isConnectedToMobileApp) {
          console.log("[tracking] 기사( " + rider.name + " ) 가 연결이 끊겼습니다. ");
          rider.isConnectedToMobileApp = false;
          this.project.updateCount++;//Left panel 업데이트 - watch가 호출되기 위함
          window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.SET_CHANGE_RIDER_STATUS, rider);
        }
      } else {
        if (!rider.isConnectedToMobileApp) {
          console.log("[tracking] 기사( " + rider.name + " ) 가 연결되었습니다. ");
          rider.isConnectedToMobileApp = true;
          this.project.updateCount++;//Left panel 업데이트 - watch가 호출되기 위함
          window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.SET_CHANGE_RIDER_STATUS, rider);
        }
      }


      if (pastTimeUpdated % 60 === 0)
        console.debug(" 기사( " + rider.name + " ) 가 " + pastTimeUpdated + " 초 전에 위치 정보를 올렸습니다. ");
    }
  }


  isTrackingRiderEnable(rider) {
    if (this.isDispatchDone()) {//관제 중이고  기사가 출발을 할때만  트래킹 가능하다 / 관제 중이더라도 배차 완료 상태이면  트래킹하지 않는다
      if (rider.status === Constant.RIDER_STATUS.GOING || rider.status === Constant.RIDER_STATUS.SERVICING || rider.status === Constant.RIDER_STATUS.COMPLETED) {
        return true;
      }
    }

    return rider.isConnectedToMobileApp === undefined ? false : rider.isConnectedToMobileApp;
  }

  async updateRiderLocations(locationsInfo) {
    let startTime = new Date().getTime();

    locationsInfo.locations.forEach(location => {
      startTime = new Date().getTime();
      let rider = RiderUtil.findRiderById(this.project.riders, location.riderId);

      startTime = new Date().getTime();
      if (rider) {

        startTime = new Date().getTime();
        this.setRiderConnectionStateToMobileApp(rider, location);
        if (this.isTrackingRiderEnable(rider)) {
          rider.x = location.location.x;
          rider.y = location.location.y;
          rider.speed = location.speed;
          rider.angle = location.angle;
          this.mapViewController.moveRider(rider);
        }

        let destination = rider.drivingDest;
        if (this.getRouteLineDisplay()) {
          if (rider.isSelected && destination && Util.isPassingDestination(destination)) {
            startTime = new Date().getTime();
            this.mapViewController.drawTrackingRoutePath(rider, destination);
          }
        }
      }
    });

    console.debug("[locations] updateRiderLocations  Done!!!  => 위치 데이터 업데이트 걸린 시간 : " + (new Date().getTime() - startTime) + "ms");
    this.updateMapView();
  }


  /**
   * 1초마다 위치정보를 업데이트 하는 함수. 업데이트 시간이 1초가 넘게 걸리면 lock이 걸리기 때문에 이것을 고려햐여 setTimeout으로 그리는 시간 제외한 1초마다 호출을 보장한다.
   */
  startUpdateLocationTimer() {
    if (this.locationEventStack.length === 0) return;

    const lastUpdateLocationGap = new Date().getTime() - this.timelastUpdateLocation;
    if (lastUpdateLocationGap < 1000) {
      console.debug('[locations] startUpdateLocationTimer()  이벤트 건너 뜀. 이전 업데이트 시간  : ' + lastUpdateLocationGap + ' ms');
      return;
    }

    console.debug('[locations] startUpdateLocationTimer()  => 누적된 이벤트 : ' + this.locationEventStack.length);

    //여러개의 이벤트가 쌓였을 경우 가장 최신 이벤트만 업데이트하고 나머지 이벤트는 버린다
    const locationsInfo = this.locationEventStack[this.locationEventStack.length - 1]; //this.locationEventStack.pop()
    this.locationEventStack = [];

    const _this = this;
    setTimeout(async () => {
      await _this.updateRiderLocations(locationsInfo);
      _this.timelastUpdateLocation = new Date().getTime();
      _this.startUpdateLocationTimer();
    }, 500);

  }

  async handleLocationWorker(event) {
    if (!event.data) {
      return;
    }

    if (this.project.status == Constant.PROJECT_STATUS.DONE) {
      return;
    }

    if (event.data.type == "COMPLETED") {
      const CURRENT_LOCATIONS_INFO = event.data.locations;

      if (!CURRENT_LOCATIONS_INFO.locations || CURRENT_LOCATIONS_INFO.locations.length === 0)
        return;

      this.locationEventStack.push(CURRENT_LOCATIONS_INFO);
      this.startUpdateLocationTimer();

    } else if (event.data.type == "FAILED") {
      console.error("Getting current Locations is failed!!!");
    } else if (event.data.type == "CANCELED") {
      console.error("Getting current Locations is canceled!!!");
      this.stopTracking();
    }
  }

  updateRiderWorkStatus(data) {
    let rider = RiderUtil.findRiderById(this.project.riders, data.riderId);
    if (rider) {
      rider.workStatus = data.workStatus;
      // top tool bar noti 갯수 세팅
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.GET_NOTI_COUNT, this.project.id);
      window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.SET_CHANGE_RIDER_STATUS, rider);
    }
  }

  updateRiderDispatchStatus(data) {
    let rider = RiderUtil.findRiderById(this.project.riders, data.riderId);
    if (rider) {
      rider.dispatchStatus = data.dispatchStatus;
      // top tool bar noti 갯수 세팅
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.GET_NOTI_COUNT, this.project.id);
      window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.SET_CHANGE_RIDER_STATUS, rider);
    }
  }

  updateRiderWorkCompletion(data) {
    let rider = RiderUtil.findRiderById(this.project.riders, data.riderId);
    if (rider) {
      rider.isWorkCompleted = data.isWorkCompleted;
      if (rider.isWorkCompleted) {
        this.updateRiderStatus(rider);
        this.updateNewRoutePath(data);

        // top tool bar noti 갯수 세팅
        window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.GET_NOTI_COUNT, this.project.id);
      }
    }
  }

  UpdateProductLoadProject(data) {
    window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.UPDATE_PRODUCT_LOADING_STATUS, data.isLoadProduct);
  }

  UpdateProductPage(data) {
    if (window.parent.app.$refs.productpage !== undefined) {
      window.parent.app.$refs.productpage.$emit(EVENT.PRODUCT_PAGE.REFRESH_PRODUCT_PAGE, data.projectId);
    }
  }

  onProjectRefresh(data) {
    let _this = this;

    PROJECT_API.getProjectInfo(data.projectId, {
      onSuccess: (response) => {
        console.log('[action] [project] loadProject  projectId : ' + response.data.id);
        PopupUtil.showNotificationPopup("배송지를 추가하였습니다");
        this.responseDataMakeProject(response.data, false, false, null);
      },
      onError: (error) => {
        console.error('프로젝트 정보 조회 실패');
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showErrorPopup(error);
        _this.setTopToolbarClusteringButtonEnabled();
      }
    });
  }

  loadOtherUserWatchProject(data) {
    const userId = this.storageGetters().getLoginUserInfo.id;
    if ((data.userId != userId) && (this.project.userId == data.userId)) {
      console.log("여기에서 project 다시 로딩하게 만들면 될까요?");
      this.refreshProject(data.projectId);//팝업창이 나타나게 하기 위함
    }
  }

  updateDeliveryStatusByEvent(rider, foundDestination, delivery) {
    console.log("[updateDeliveryStatusByEvent] : " + PlaceUtil.getDestinationDisplayId(foundDestination) + " =>  foundDestination.deliveryStatus : " + foundDestination.deliveryStatus + ", delivery.deliveryStatus : " + delivery.deliveryStatus);

    if (foundDestination.deliveryStatus === delivery.deliveryStatus) {
      // deliveryStatus가 변경없이 Push된 경우인데,
      // 현재는 배송완료 후에 사진 추가/삭제된 경우에만 해당된다.
      foundDestination.fileCountForCompleted = delivery.fileCountForCompleted;

      //관제자 배송 완료 체크
      if (!foundDestination.isCsChecking && delivery.isCsChecking) {
        foundDestination.isCsChecking = delivery.isCsChecking;
        foundDestination.csCheckingName = delivery.csCheckingName;

        let foundDestinations = [];
        foundDestinations.push(foundDestination);
        this.refreshSectionDestinationsList(rider, foundDestinations);
      }

      //현대 백화점(관제웹에 고객에게 알림톡으로 안내한 ETA를 표출)
      if (_.isNil(foundDestination.estimatedDtOfArrival) && !_.isNil(delivery.estimatedDtOfArrival)) {
        foundDestination.estimatedDtOfArrival = delivery.estimatedDtOfArrival;

        let foundDestinations = [];
        foundDestinations.push(foundDestination);
        this.refreshSectionDestinationsList(rider, foundDestinations);
      }

      return;
    }
    foundDestination.deliveryStatus = delivery.deliveryStatus;
    if (foundDestination.deliveryStatus === Constant.DELIVERY_STATUS.READY
      || foundDestination.deliveryStatus === Constant.DELIVERY_STATUS.GOING
      || Util.isPassedDestination(foundDestination)) {


      if (this.hasReservationTimeDestination(foundDestination)) {
        foundDestination.isArrivedOnTime = PlaceUtil.isArrivedOnReservationTime(foundDestination);
      }

      if (rider.isSelected)
        this.addDestinationPin(foundDestination);

      foundDestination.estimatedMetersReal = delivery.estimatedMetersReal;
      foundDestination.estimatedSecondsReal = delivery.estimatedSecondsReal;

      if (delivery.routePathReal) {
        foundDestination.routePathReal = delivery.routePathReal;
      } else {
        foundDestination.routePathReal = null;
      }
      if (Constant.SUPPORT_REAL_MULTI_ROUTE_PATH) {
        if (delivery.multiRoutePathReal) {
          foundDestination.multiRoutePathReal = delivery.multiRoutePathReal;
        } else {
          foundDestination.multiRoutePathReal = null;
        }
      }
      if (Util.isPassedDestination(foundDestination) && foundDestination.routePathReal && foundDestination.routePathReal.length > 0) {
        //foundDestination.passedRoutePathReal = Util.getBufferedRoutePath(foundDestination.routePathReal);
        //#2534 turf.buffer()가 오래걸리는 이슈가 있어 web worker로 처리한다.
        this.fields.geoBufferWorker.postMessage({
          riderId: rider.riderId,
          deliveryId: foundDestination.deliveryId,
          routePathReal: foundDestination.routePathReal
        });
      } else {
        foundDestination.passedRoutePathReal = null;
      }

      if (Util.isPassingDestination(foundDestination)) {
        rider.drivingDest = foundDestination;
      }

      if (rider.isSelected && this.getRouteLineDisplay()) {
        this.mapViewController.drawTrackingRoutePath(rider, foundDestination);
      }

      this.updateMapView();
    } else if (foundDestination.deliveryStatus === Constant.DELIVERY_STATUS.SERVICING) {    // SERVING으로 변경되었을때 경로만 다시 그린다.
      if (delivery.routePathReal) {
        foundDestination.routePathReal = delivery.routePathReal;
      } else {
        foundDestination.routePathReal = null;
      }
      if (Constant.SUPPORT_REAL_MULTI_ROUTE_PATH) {
        if (delivery.multiRoutePathReal) {
          foundDestination.multiRoutePathReal = delivery.multiRoutePathReal;
        } else {
          foundDestination.multiRoutePathReal = null;
        }
      }
      if (Util.isPassedDestination(foundDestination) && foundDestination.routePathReal && foundDestination.routePathReal.length > 0) {
        //foundDestination.passedRoutePathReal = Util.getBufferedRoutePath(foundDestination.routePathReal);
        //#2534 turf.buffer()가 오래걸리는 이슈가 있어 web worker로 처리한다.
        this.fields.geoBufferWorker.postMessage({
          riderId: rider.riderId,
          deliveryId: foundDestination.deliveryId,
          routePathReal: foundDestination.routePathReal
        });
      } else {
        foundDestination.passedRoutePathReal = null;
      }

      if (rider.isSelected && this.getRouteLineDisplay()) {
        this.mapViewController.drawTrackingRoutePath(rider, foundDestination);
      }

      this.updateMapView();
    }

    if (delivery.realStartTime) {
      foundDestination.realStartTime = delivery.realStartTime;
    }

    if (delivery.realTime) {
      foundDestination.realSeconds = delivery.realTime;
    }

    if (delivery.realServiceDuration) {
      foundDestination.realServiceDuration = delivery.realServiceDuration;
    }

    if (delivery.realArrivalTime) {
      foundDestination.realArrivalTime = delivery.realArrivalTime;
    }

    if (delivery.realEndTime) {
      foundDestination.realEndTime = delivery.realEndTime;
    }

    if (delivery.etaDateTimeReal) {
      foundDestination.etaDateTimeReal = delivery.etaDateTimeReal;
    }
    if (delivery.pickUpTime) {
      foundDestination.pickUpTime = delivery.pickUpTime;
    }

    if (delivery.dropOffTime) {
      foundDestination.dropOffTime = delivery.dropOffTime;
    }

    foundDestination.fileCountForCompleted = delivery.fileCountForCompleted;

    if (foundDestination.deliveryStatus === Constant.DELIVERY_STATUS.COMPLETED) {
      rider.completeDeliveryCount = rider.destinations.filter(destination => destination.deliveryStatus === Constant.DELIVERY_STATUS.COMPLETED).length;
      console.log("[updateDeliveryStatusByEvent] riderId: " + rider.riderId + ", completeDeliveryCount: " + rider.completeDeliveryCount);
      // PopupUtil.showNotificationPopup(`The Delivery is completed to ${foundDestination.deliveryId}`);
      PopupUtil.showNotificationPopup(` 배송(픽업)이 완료되었습니다. (${PlaceUtil.getDestinationDisplayId(foundDestination, 15)})`);
    } else if (foundDestination.deliveryStatus === Constant.DELIVERY_STATUS.FAILURE) {
      rider.failureDeliveryCount = rider.destinations.filter(destination => destination.deliveryStatus === Constant.DELIVERY_STATUS.FAILURE).length;
      console.log("[updateDeliveryStatusByEvent] riderId: " + rider.riderId + ", failureDeliveryCount: " + rider.failureDeliveryCount);
      // PopupUtil.showNotificationPopup(`The Delivery is completed to ${foundDestination.deliveryId}`);
      PopupUtil.showNotificationPopup(` 배송(픽업)이 실패 되었습니다. (${PlaceUtil.getDestinationDisplayId(foundDestination, 15)})`);
    }

    // 기사 상태 설정
    this.updateRiderStatus(rider, foundDestination, this.project.attribute.isAutoCreated);


    window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.SET_CHANGE_DELIVERY_STATUS, {
      rider: rider,
      destination: foundDestination
    });

    let foundDestinations = [];
    foundDestinations.push(foundDestination);
    this.refreshSectionDestinationsList(rider, foundDestinations);
  }

  async handleDeliveryStatusWorker(event) {

    const _this = this;

    if (!event.data) {
      return;
    }

    if (event.data.result == "FAILED") {
      PopupUtil.alertPopup("배송상태 변경에 의한 배송 목록 API 요청 실패!");
      return;
    }

    const deliveries = event.data.data.content;
    if (!deliveries || deliveries.length == 0) {
      return;
    }

    const rider = RiderUtil.findRiderById(this.project.riders, event.data.riderId);
    if (!rider || rider.destinations.length == 0) {
      return;
    }

    deliveries.forEach(delivery => {
      const foundDestination = PlaceUtil.findDestinationByDeliveryId(rider.destinations, delivery.deliveryId);
      if (foundDestination) {
        _this.updateDeliveryStatusByEvent(rider, foundDestination, delivery);
      }
    });

    DELIVERY_API.getDeliveryETA(rider.riderId, this.project.id, {
      onSuccess: async (response) => {
        let deliveryETAs = response.data;
        if (!deliveryETAs || deliveryETAs.length === 0) {
          return;
        }

        deliveryETAs = deliveryETAs.sort(function (a, b) {
          return a.order > b.order ? 1 : a.order < b.order ? -1 : 0;
        });

        for (let index = 0; index < deliveryETAs.length; index++) {
          let deliveryETA = deliveryETAs[index];
          let foundDestination = PlaceUtil.findDestinationByDeliveryId(rider.destinations, deliveryETA.deliveryId);
          if (foundDestination && !Util.isPassedDestination(foundDestination)) {
            foundDestination.etaDateTimeReal = deliveryETA.etaDateTimeReal;
          }
        }
      },
      onError: (error) => {
        PopupUtil.alertPopup("배송상태 변경에 의한 배송 ETA API 요청 실패!");
      }
    });

    this.updateMapView();
  }

  async handleGeoBufferWorker(event) {

    if (!event || !event.data) {
      return;
    }

    const riderId = event.data.riderId;
    const deliveryId = event.data.deliveryId;
    const bufferedRoutePathReal = event.data.bufferedRoutePathReal;
    if (!riderId || !deliveryId || !bufferedRoutePathReal) {
      return;
    }

    const rider = RiderUtil.findRiderById(this.project.riders, riderId);
    if (!rider || rider.destinations.length === 0) {
      return;
    }

    const destination = PlaceUtil.findDestinationByDeliveryId(rider.destinations, deliveryId);
    if (!destination) {
      return;
    }

    //console.log(`${riderId}/${deliveryId}: ${JSON.stringify(bufferedRoutePathReal)}`)
    destination.passedRoutePathReal = bufferedRoutePathReal;
  }

  async onPerformSending(silentMode = false) {
    console.log("[onPerformSending] silentMode: " + silentMode + ", projectStatus: " + this.project.status);
    let updateRiderIds = this.getModifiedRiderIdList();

    if (silentMode) {// 실시간 배차 옵션의 전송 여부가 '기사에게 바로전송' 일 경우 이미 MQTT 전송된 기사에 한해 MQTT를 전송한다.
      updateRiderIds = this.filterMqttPushedRiderIds(updateRiderIds);
    }

    let selectedRiderIds = this.getSelectedRiderIdList();
    let allRiderIds = this.getAllRiderIdList();

    let updateRiders = [];
    let reasonStr = "배송 정보를 ";

    // 프로젝트가 예정일 경우 선택된 기사만 확인
    if (this.project.status === Constant.PROJECT_STATUS.SCHEDULED) {
      if (!silentMode) {
        updateRiders = this.getSelectedRiderList();
      } else {
        console.log("[onPerformSending] silentMode에서 전송할 기사가 없습니다. - 체크 요망");
        return;
      }
    }
    // 프로젝트가 관제중일 경우 기사 배송 변경에 따른 기능 구현
    else if (this.project.status === Constant.PROJECT_STATUS.IN_PROGRESS) {
      //기사에게 전송하고 나서 배송지가 변경되었을 경우
      if (updateRiderIds.length > 0) {
        // 모든 기사가 선택되어 있을 경우
        if (_.xor(selectedRiderIds, allRiderIds).length === 0) {
          updateRiders = RiderUtil.getRidersByRiderIds(this.project.riders, updateRiderIds);
          reasonStr = " 업데이트된 배송 정보를 ";
        }
        // 변경된 기사와 선택된 기사에 포함되어 있지 않을 경우 select 된 기사에게만 전송함.
        else {
          if (!silentMode) {
            updateRiders = this.getSelectedRiderList();
          } else {
            console.log("[onPerformSending] silentMode에서 전송할 기사가 없습니다. - 체크 요망");
            return;
          }
        }
      }
      //기사에게 전송하고 변경된 부분이 없을 경우 selected 된 기사에게 전송함.
      else {
        if (!silentMode) {
          updateRiders = this.getSelectedRiderList();
        } else {
          console.log("[onPerformSending] 다른 로직에서 보내기 때문에 기사에게 전송하지 않습니다.");
          return;
        }
      }
    }
    console.log("[onPerformSending] updateRiders.length: " + updateRiders.length);

    //isShowReSendingButton 참고
    if (this.isDispatchDone() && !this.project.attribute.isSendingRiderEnabled) {
      let riderStr;
      if (updateRiders.length <= 0) {
        PopupUtil.alertPopup(
          Util.getSeparateEngString("선택된 기사가 없습니다. 배송 정보를 보낼 기사를 선택해 주세요.", "선택된 기사가 없습니다."));
        return;
      } else if (updateRiders.length === this.project.riders.length) {
        riderStr = _t("모든 기사")
      } else {
        riderStr = "( ";
        for (let i = 0; i < updateRiders.length; i++) {
          riderStr += updateRiders[i].name;
          if (i < updateRiders.length - 1)
            riderStr += ", ";
        }
        riderStr += " )";
      }

      if (silentMode) {
        await this.performSending(updateRiders, false);
        PopupUtil.showNotificationPopup(
          Util.getSeparateEngString(reasonStr + riderStr + "의 모바일 앱으로 전송 완료하였습니다.",
            _t("${name}의 모바일 앱으로 전송 완료하였습니다.", [riderStr])
          )
        );
      } else {
        PopupUtil.confirmPopup(
          Util.getSeparateEngString(reasonStr + riderStr + "의 모바일 앱으로 전송합니다. 실행하시겠습니까?",
            _t("${name}의 모바일 앱으로 전송합니다. 실행하시겠습니까?", [riderStr])
          ), {
            onConfirm: () => {
              this.performSending(updateRiders);
            },
          });
      }
    } else {
      if (updateRiders.length <= 0) {
        PopupUtil.alertPopup(Util.getSeparateEngString("선택된 기사가 없습니다. 배송 정보를 보낼 기사를 선택해 주세요.", "선택된 기사가 없습니다."));
        return;
      } else {
        await this.performSendingWithCheckWithoutRouting(updateRiders);
        // await this.performSendingWithConfirm(updateRiders);
      }
    }
  }

  async onPerformRiderSending(rider) {
    let reasonStr = "배송 정보를 ";

    const riderList = [];
    riderList.push(rider);

    PopupUtil.confirmPopup(
      Util.getSeparateEngString(reasonStr + rider.name + "의 모바일 앱으로 전송합니다. <br> 실행하시겠습니까?",
        _t("${name}의 모바일 앱으로 전송합니다. 실행하시겠습니까?", rider.name)
      ), {
        onConfirm: () => {
          this.performSending(riderList);
        },
      });
  }

  async performSendingWithCheckWithoutRouting(updateRiders) {

    //배송지가 모두 HUB인 경우에는 입고 프로젝트로 간주 되므로 굳이 경고 팝업을 띄우지 않는다
    const allHubs = PlaceUtil.isAllDestinationsHub(this.project.destinations);

    if (!allHubs && !this.project.attribute.isSendingRiderEnabled) {//경로 탐색 없이 보낼시에 한번더 팝업 보냄
      const _this = this;
      PopupUtil.confirmPopup("경로 탐색이 수행되지 않았습니다. 경로 탐색없이 기사에게 전송을 하면 기사앱에서 수동으로 경로 설정을 해야 하고 기능이 제대로 동작하지 않을수 있습니다. 계속 진행하시겠습니까?"
        , {
          onConfirm: async () => setTimeout(async () => await _this.performSendingWithConfirm(updateRiders), 100)
        }
      );
    } else if (!allHubs && !PlaceUtil.isAllDestinationsClustered(this.project.destinations)) {//배차가 되지 않은 배송지가 존재할 때
      const _this = this;
      PopupUtil.confirmPopup("배차되지 않은 배송지가 존재합니다. 계속 진행하시겠습니까?"
        , {
          onConfirm: async () => setTimeout(async () => await _this.performSendingWithConfirm(updateRiders), 100)
        }
      );
    } else {
      await this.performSendingWithConfirm(updateRiders);
    }
  }

  async performSendingWithConfirm(updatedRiders) {
    const _this = this;
    let messageText;

    //경로 생성 실행 및 미 실행시 조건에 따른 message 분리
    if (this.project.attribute.isSendingRiderEnabled) {
      if (updatedRiders.length === this.project.riders.length)
        messageText = _t("배차 및 배송 순서를 확정하고 모든 기사의 모바일 앱에 배송 정보를 전송합니다.");
      else
        messageText = _t("배차 및 배송 순서를 확정하고 ${name} 의 모바일  앱에 배송 정보를 전송합니다.", this.performSendingDisplayText(updatedRiders));
      // messageText = "배차 및 배송 순서를 확정하고 " + this.performSendingDisplayText(updatedRiders) + "의 모바일  앱에 배송 정보를 전송합니다.";
    } else {
      if (updatedRiders.length === this.project.riders.length)
        messageText = _t("경로생성 절차 없이 배송 정보를 모든 기사에게 전송합니다.");
      else
        messageText = _t("경로생성 절차 없이 배송 정보를 ${name} 기사에게 전송합니다.", this.performSendingDisplayText(updatedRiders));
      // messageText = "경로생성 절차 없이 배송 정보를" + this.performSendingDisplayText(updatedRiders) + "기사에게 전송합니다.";
    }

    messageText += "</br>";
    messageText += _t("계속 진행하시겠습니까?");

    if (this.project.status !== Constant.PROJECT_STATUS.IN_PROGRESS) {
      PopupUtil.confirmPopup(messageText, {
        onConfirm: async () => {
          await _this.performSending(updatedRiders);
        },
      });
    } else {
      await this.performSending(updatedRiders);
    }
  }

  performSendingDisplayText(updateRiders) {
    let riderStr;
    if (updateRiders.length <= 0) {
      PopupUtil.alertPopup("선택된 기사가 없습니다. 배송 정보를 보낼 기사를 선택해 주세요");
      return;
    } else if (updateRiders.length === this.project.riders.length) {
      riderStr = "모든 기사"
    } else {
      riderStr = "일부 기사 ( ";
      for (let i = 0; i < updateRiders.length; i++) {
        riderStr += updateRiders[i].name;
        if (i < updateRiders.length - 1)
          riderStr += ", ";
      }
      riderStr += " )";
    }
    return riderStr;
  }

  async performSending(updatedRiders, showPopup = true) {
    const _this = this;
    if (showPopup) {
      PopupUtil.showSendingPopup();
    }
    this.updateMapView();
    setTimeout(async () => _this.executePerformSending(updatedRiders), 10);
  }

  /**
   * Dispatch 버튼 클릭
   */
  async executePerformSending(updatedRiders) {
    console.log("[executePerformSending] projectStatus: " + this.project.status);

    // TODO : 근무시간 체크 주석처리
    // 프로젝트 내의 모든 기사의 근무 시간이 현재 시간보다 나중에 있는지 검사
//			let response = await TMS_WEB_API.checkRiderWorkingTime(this.project.id);

//			if(!response.data || response.data.data == false) {
//				PopupUtil.alertPopup("근무시간 오류", "엑셀에 설정된 기사 근무시간이 올바르지 않습니다.<br />시간 확인 후 다시 입력해주세요.", () => {});
//				return;
//			}


    // "관제 중" 상태로 설정하기 위해 프로젝트 실행일 설정. 이미 관제 중일 경우에는 속성만 바꾼다
    const updateAttributes = {isRouteEnabled: false, isSendingRiderEnabled: false};
    if (this.project.status !== Constant.PROJECT_STATUS.IN_PROGRESS) {
      let response = await PROJECT_API.setEffectiveDateTime(this.project.id, TimeUtil.getCurrentDateTimeStr(), updateAttributes);
      if (!response || !response.data || response.data.isSuccess == false) {
        return; // 실패했기 때문에 더이상 로직 수행 하지 않는다.
      }

      // 경로 생성 안하고 기사에게 전송히 배차 구역 삭제 해야함.
      if (this.project.attribute.isFirstRoutingDone == false) {
        this.mapViewController.cleanCluster(this.project.riders, false);
      }
    } else {
      await PROJECT_API.setProjectAttribute(this.project.id, updateAttributes);
    }

    this.clearModifyCommandList();

    this.project.attribute.isRouteEnabled = false;
    this.project.attribute.isSendingRiderEnabled = false;

    // 기사에게 전송 팝업
    // PopupUtil.showSendingPopup();
    // this.updateMapView();

    let _this = this;

    if (!isAnonymous()) {
      this.publicMQTTMessageToRiderApps(updatedRiders, this.project.status);
      // PopupUtil.showNotificationPopup("기사 모바일 앱으로 전송을 완료하였습니다.")
      PROJECT_API.sendDispatchToOMS(this.project.id, {
        onSuccess: (response) => {
          console.log("sendDispatchToOMS : " + JSON.stringify(response.data));
        },
        onError: (error) => {
          console.log(`failed : $error`);
        }
      });
    } else
      this.updateDispatchData();
  }

  updateDispatchData() {
    this.project.status = Constant.PROJECT_STATUS.IN_PROGRESS;
    const _this = this;

    setTimeout(async () => {
      console.log("[updateDispatchData] 경로 업데이트 시작");
      for (const rider of this.project.riders) {
        // 할당된 목적지가 없으면 스킵
        if (!rider.destinations || rider.destinations.length == 0) {
          continue;
        }

        rider.drivingDest = rider.destinations[0];

        // 방문지 아이콘, 경로선색 변경
        if (rider.isSelected === true) {
          for (const destination of rider.destinations) {
            _this.changeDestinationPinToSetMode(destination);

            //the line 옵션으로 정리함.
            //기사 트랙 표시하는 부분
            if (rider.isSelected && _this.getRouteLineDisplay()) {
              _this.mapViewController.drawTrackingRoutePath(rider, destination);
            }
          }
        }
      }
      _this.updateMapView();
      console.log("[updateDispatchData] 경로 업데이트 완료");
    }, 100);

    PopupUtil.dismissSendingPopup();

    this.updateTreeViewOnLeftPanel();
    window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.MAP_DATA_CHANGED, this.project);
  }

  getRouteLineDisplay() {
    return this.isRouteLineDisplay;
  }

  updateTreeViewOnLeftPanel() {
    console.log("updateTreeViewOnLeftPanel");
    window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.INIT_LEFT_PANEL_TREE_SOURCES, {project: this.project});// LeftPanel의 Tree source init
  }

  changeDestinationPinToSetMode(destination) {
    if (destination.mode === Constant.DESTINATION_PIN_MODE.EDIT || destination.mode === Constant.DESTINATION_PIN_MODE.IMPOSSIBLE_ARRIVAL_IN_TIME) {
      this.addDestinationPin(destination);
    }
  }

  publicMQTTMessageToRiderApps(riders, projectStatus) {

    let projectMqttList = [];

    riders.forEach(rider => {
      let data = {};

      console.log("[MQTT-publish] MQTT 메시지를 " + rider.name + " (" + rider.riderId + ") 에게 전송합니다.");

      data.riderId = rider.riderId;
      data.vehicleId = rider.vehicle.vehicleId;

      let isRiderFirstProjectSending;
      //주행중일때만 업데이트한다. 항상 isRiderFirstProjectSending = true 로 설정해야 될듯 하다. UPDATE_DELIVERIES 일 경우 모바일앱이 다른 프로젝트를 관제중일때 업데이트를 하지 못한다.
      if (rider.status === Constant.RIDER_STATUS.GOING || rider.status === Constant.RIDER_STATUS.SERVICING) {
        isRiderFirstProjectSending = false;
      } else {
        isRiderFirstProjectSending = true;
      }
      data.pushEventType = isRiderFirstProjectSending ? Constant.MQTT_EVENT_STRING_TYPE.NEW_PROJECT : Constant.MQTT_EVENT_STRING_TYPE.UPDATE_DELIVERIES;

      projectMqttList.push(data);

    });


    const _this = this;
    PROJECT_API.pushProjectToRiders(this.project.id, projectMqttList, {
      onSuccess: (response) => {
        console.log(response.data);
        //////////////////////////////////////////////////////
        //성공 여부를 확인하여 rider 별로 sendMqtt flag 를 셋팅함.
        //filter로 pushedAt 값이 있는지 확인함. (정상 전송)
        response.data.filter(
          function (rider) {
            if (rider.pushedAt !== null && rider.pushEventType === Constant.MQTT_EVENT_STRING_TYPE.NEW_PROJECT)
              return rider;
          }).forEach(updatedRider => {
          _this.project.riders.filter(rider => {
            if (rider.riderId == updatedRider.riderId) {
              rider.projectPushedAt = updatedRider.pushedAt;
            }
          })
        });
        //////////////////////////////////////////////////////
        _this.updateDispatchData()
      },
      onError: (error) => {
        console.log(error);
        PopupUtil.dismissSendingPopup();
        PopupUtil.showErrorPopup(error);
      }
    });
  }

  async onPerformClusterAll() {
    this.clearModifyCommandList();

    if (this.isClusteringStatus()) {
      PopupUtil.confirmPopup("현재 배차를 취소하고 현재 배차 옵션으로 배차를 수행합니다. 실행하시겠습니까?", {
        onConfirm: () => {
          window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.PERFORM_ROUTING_BUTTON_DISABLED, true);
          this.performClusterAll();
        },
        onCancel: () => this.setTopToolbarClusteringButtonEnabled()
      });
    } else if (this.isRoutingDoneStatus()) {
      PopupUtil.confirmPopup("경로탐색 결과를 취소하고 다시 배차 모드로 전환합니다. 실행하시겠습니까? ( 현재 배차는 변경되지 않습니다 )", {
        onConfirm: () => {
          window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.PERFORM_ROUTING_BUTTON_DISABLED, true);
          this.moveBackToClusteringMode();
        },
        onCancel: () => this.setTopToolbarClusteringButtonEnabled()
      });
    } else {
      await this.performClusterAll();
    }
  }

  async performClusterAll(callback) {
    console.log("[action] performClusterAll()");

    if (PlaceUtil.isAllDestinationsHub(this.project.destinations)) {
      PopupUtil.alertPopup('입고 프로젝트에서는 배차를 수행할수 없습니다 . 기사에게 전송을 수행하세요 ');
      return;
    }

    let projectId = this.project.id;

    const _this = this;
    PopupUtil.showLoadingPopup("탐색 중...", "잠시만 기다려 주세요.");

    const clusterRuleOption = MapUtil.convertClusterRuleFromIndex(this.storageGetters().getClusterRuleOption);

    PROJECT_API.getDeliveryClustering(projectId, clusterRuleOption, {
      onSuccess: (response) => {
        PopupUtil.dismissLoadingPopup();
        // _this.loadProjectData( projectId );
        _this.refreshProject(projectId);//팝업창이 나타나게 하기 위함
        if (callback && callback.onSuccess) callback.onSuccess();
      },
      onError: (error) => {
        PopupUtil.dismissLoadingPopup();
        _this.setTopToolbarClusteringButtonEnabled();
        if (callback && callback.onError) {
          callback.onError(error);
        } else {
          PopupUtil.showErrorPopup(error);
        }

      }
    });
  }

  setTopToolbarClusteringButtonEnabled() {
    // 배차 완료 후
    // 기사에게 전송 안된 상태 및 경로 탐색 완료시에는 배차 버튼 활성화 시킴
    if (this.project.attribute.isAutoCreated == true)
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.PERFORM_CLUSTERING_BUTTON_DISABLED, true);
    else if (this.project.attribute.isClusterDone == true && (this.project.attribute.isSendingRiderEnabled == true || this.project.attribute.isRouteEnabled == true))
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.PERFORM_CLUSTERING_BUTTON_DISABLED, false);
    //배차 버튼 클릭시 문제 발생시 다시 버튼 원복 시킴
    else if (this.project.attribute.isClusterDone == false)
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.PERFORM_CLUSTERING_BUTTON_DISABLED, false);
    else if (!PlaceUtil.isAllDestinationsClustered(this.project.destinations)) //배차안된 배송지가 존재할때는 배차 버튼 활성화
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.PERFORM_CLUSTERING_BUTTON_DISABLED, false);
  }

  //마우스 드래그로 선택된 배송지를 이전한다
  changeClusteringMouseDragging(mouseMoveOnRider) {

    let willChangeDestIds = [];
    let fromRiderIds = [];
    for (const dest of this.project.destinations) {
      if (dest.onCheck && dest.riderId !== mouseMoveOnRider.riderId) {
        let isChangeAbleDest = PlaceUtil.isDestinationChangeClusteringEnabled(dest);
        if (isChangeAbleDest) {
          willChangeDestIds.push(dest.deliveryId);
          if (dest.riderId && fromRiderIds.indexOf(dest.riderId) < 0) {
            fromRiderIds.push(dest.riderId);
          }
        } else {
          const errorMessage = PopupUtil.getUnEditableDestinationErrorMessage(dest)
          PopupUtil.showNotificationPopup(errorMessage);
          console.log("changeClusteringMouseDragging Can't change clustering =>  " + dest.deliveryId + "  , status : " + dest.deliveryStatus);
        }
      }
    }

    const _this = this;
    if (willChangeDestIds.length > 0) {
      // const firstDest = PlaceUtil.findDestinationById(this.project.destinations, willChangeDestIds[0]);
      let popupStr = _t("${num} 개의 배송지를 '${name}' 에게 이전합니다. 진행하시겠습니까?", [willChangeDestIds.length, mouseMoveOnRider.name]);
      if (this.isDispatchDone()) {
        popupStr += _t("(주의: 기사가  편집한 배송순서는 초기화 됩니다)");
      }

      PopupUtil.confirmPopup(popupStr, {
        onConfirm: () => {
          _this.cancelDestinationChecked();

          const fromRiders = RiderUtil.getRidersByRiderIds(_this.project.riders, fromRiderIds);
          _this.setChangeDeliveriesClusteringCommand(willChangeDestIds, fromRiders, mouseMoveOnRider);
        }
      });

    }

  }

  changeClustering(data) {

    if (!this.isEditableProject()) {
      PopupUtil.alertPopup(Util.getSeparateEngString("클러스터링 변경이 허용되지 않았습니다", "프로젝트에 권한이 없습니다."));
      return;
    }


    //해당 목적지를 해당 riderId에 배차를 할당한다.
    console.log("[changeClustering] dragDestId: " + data.dragDestId + ", dropoffDestId: " + data.dropoffDestId + ", isolate: " + data.isolate);
    let dragDest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, data.dragDestId);//드래깅 아이템
    let dropoffDest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, data.dropoffDestId);//드래깅한 아이템이 떨어진 아이템

    if (!PlaceUtil.isDestinationChangeClusteringEnabled(dragDest)) {
      PopupUtil.showModifyDestinationFailedPopup(dragDest);
      return;
    }

    //기사에게 배송지 할당시
    if (data.dropoffRiderId) {
      //미 배송지 할당시
      if (data.dropoffRiderId == "unassigned") {
        this.requestChangeNotAssignedCluster(dragDest);
      } else {
        let dropoffRider = RiderUtil.findRiderById(this.project.riders, data.dropoffRiderId);

        const _this = this;
        this.requestChangeCluster(dragDest, dropoffRider, data.isolate);
      }
    }
    //배송지에게 배송지 할당시
    else {
      //1.기사에서 기사로 할당
      //2.미배차 배송지 기사로 할당
      //3.가사 배송지를 미배차로 할당
      if (data.isDragUnassigned == false && data.isDropOffUnassigned == false) {
        let dropoffDest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, data.dropoffDestId);//드래깅한 아이템이 떨어진 아이템

        let dropoffRider = RiderUtil.findRiderById(this.project.riders, dropoffDest.riderId);
        let dragRider = RiderUtil.findRiderById(this.project.riders, dragDest.riderId);

        //Dropoff 할때  손가락 위치를 기준으로 하기때문에 원래 아래 위치에 있는  dropoff아이템을 선택하는게 좀더 맞는다
        let findDropOffIndex = dropoffRider.destinations.findIndex(d => d.deliveryId == dropoffDest.deliveryId);
        findDropOffIndex++;
        if (findDropOffIndex >= dropoffRider.destinations.length) {
          findDropOffIndex = dropoffRider.destinations.length - 1;
        }
        dropoffDest = dropoffRider.destinations[findDropOffIndex];

        if (dropoffRider && dragRider) {//dragDest 를 dropoffDest로 이동한다
          //위치 변경
          // this.switchSequenceDestinationInRider(dragRider, dropoffRider, dragDest, dropoffDest);
          // this.saveSequenceOrderRiders();//현재 기사의 배송지 위치를 저장함
          // this.reArrangeSequenceOrderRiders();

          if (dragRider.riderId !== dropoffRider.riderId) {// 찾지 못하였을때만 배차를 바꾼다
            this.requestChangeCluster(dragDest, dropoffRider);
          }
        }
      } else if (data.isDragUnassigned) {
        let dropoffDest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, data.dropoffDestId);//드래깅한 아이템이 떨어진 아이템

        let dropoffRider = RiderUtil.findRiderById(this.project.riders, dropoffDest.riderId);
        let dragDest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, data.dragDestId);

        let findDropOffIndex = dropoffRider.destinations.findIndex(d => d.deliveryId == dropoffDest.deliveryId);
        findDropOffIndex++;
        if (findDropOffIndex >= dropoffRider.destinations.length) {
          findDropOffIndex = dropoffRider.destinations.length - 1;
        }
        dropoffDest = dropoffRider.destinations[findDropOffIndex];

        this.requestChangeAssignedCluster(dragDest, dropoffRider);
      } else if (data.isDropOffUnassigned) {
        this.requestChangeNotAssignedCluster(dragDest);
      }
    }
  }


  //Left-panel에서 기사끼리 Drag&Drop시에 배송지 이전 또는 배송지 교환이 이루어 지도록 한다
  ridersSwitchClustering(data) {
    if (!this.isEditableProject()) {
      PopupUtil.alertPopup("클러스터링 변경이 허용되지 않았습니다");
      return;
    }

    if (data.dragDestRiderId === "unassigned" || data.dropItemRiderId === "unassigned") {
      console.log("미배송 배차에게 배송지 전체 교환/이전을 금지 합니다");
      return;
    }

    const _this = this;
    //관제중일때는 기사의 남은 배송지가 다른 기사에게 할당된다

    PopupUtil.confirmPopup(
      "기사의 모든 배송지를 다른 기사에게 모두 이전 또는 두 기사의 배송지를 서로 교환 합니다. ( [ 예 ] : 다른 기사에게 배송지 모두 이전, [ 아니오 ] : 두 기사의 배송지 서로 교환 )"
      , {
        onConfirm: () => {//배송지 서로 교환
          setTimeout(() => _this.transferAllReadyDestinationsToRider(data), 500);
        },
        onCancel: () => {//배송지 서로 이전
          setTimeout(() => _this.callRidersSwitchClustering(data), 500);
        }
      }
    );

  }

  //모든 완료되지 않은 방문지를 모두 특정 기사에게 할당한다.
  transferAllReadyDestinationsToRider(data) {
    const _this = this;

    const dragDestRider = RiderUtil.findRiderById(this.project.riders, data.dragDestRiderId);
    const dropItemRider = RiderUtil.findRiderById(this.project.riders, data.dropItemRiderId);

    let deliveryIds = [];
    for (const dest of dragDestRider.destinations) {
      if (!PlaceUtil.isDeliveryCompleted(dest)) {
        deliveryIds.push(dest.deliveryId);
      }
    }
    if (deliveryIds.length === 0) return;

    let popupStr = "'" + dragDestRider.name + "' 님의 완료되지 않은 배송지" + deliveryIds.length + "개를 '" + dropItemRider.name + "'에게 모두 할당합니다. 진행하시겠습니까? ";
    if (this.isDispatchDone()) {
      popupStr += "( 주의: 기사가 편집한 배송순서는 초기화 됩니다 ) ";
    }

    PopupUtil.confirmPopup(
      Util.getSeparateEngString(popupStr
        , _t("${name} 기사의 완료되지 않은 배송지를  ${name}에게 모두 이전합니다. 진행하시겠습니까?", [dragDestRider.name, dropItemRider.name])
      )
      , {
        onConfirm: () => {
          _this.setChangeDeliveriesClusteringCommand(deliveryIds, [dragDestRider], dropItemRider);
        }
      });
  }


  callRidersSwitchClustering(data) {
    const _this = this;

    const dragDestRider = RiderUtil.findRiderById(this.project.riders, data.dragDestRiderId);
    const dropItemRider = RiderUtil.findRiderById(this.project.riders, data.dropItemRiderId);

    const dragDestCount = dragDestRider.destinations ? dragDestRider.destinations.length : 0;
    const dropDestCount = dropItemRider.destinations ? dropItemRider.destinations.length : 0;

    if (dragDestCount === 0 && dropDestCount === 0) {
      return;
    }
    let popupStr = "'" + dragDestRider.name + "' 의 배송지 " + dragDestCount + "개와  '" + dropItemRider.name + "' 의 배송지 " + dropDestCount + "개를 서로 교환합니다. 계속 진행하시겠습니까?";
    if (this.isDispatchDone()) {
      popupStr += "( 주의: 기사가 편집한 배송순서는 초기화 됩니다 ) ";
    }
    PopupUtil.confirmPopup(
      Util.getSeparateEngString(popupStr
        , _t("'${name1}' 와 '${name2}' 의 배송지를 서로 서로 교환합니다. 계속 진행하시겠습니까?", [dragDestRider.name, dropItemRider.name])
      ), {
        onConfirm: () => {
          PROJECT_API.putSwitchRidersClustering(_this.project.id, data.dragDestRiderId, data.dropItemRiderId, {
            // PROJECT_API.changeDeliveryClustering(  destination.deliveryId, { riderId : toRider.riderId }, {
            onSuccess: (response) => {
              console.log("putSwitchRidersClustering success ");
              PopupUtil.alertPopup("'" + dragDestRider.name + "' 와  '" + dropItemRider.name + "' 의 배차를 서로 교환 완료 하였습니다");
              _this.cleanRidersClusterAndRoutePath([dragDestRider, dropItemRider]);
              const cmd = {type: Constant.RESERVED_COMMAND.SWITCH_RIDER_CLUSTERING};
              _this.performRefreshActionAfterModified(cmd);
            },

            onError: (error) => {
              PopupUtil.showErrorPopup(error);
              _this.refreshProject(_this.project.id);
            }
          });
        }
      });
  }

  requestChangeAssignedCluster(destination, toRider) {
    this.requestChangeClusterWithCheckSamePosition(destination, null, toRider);
  }

  requestChangeNotAssignedCluster(destination) {
    let fromRider = RiderUtil.findRiderById(this.project.riders, destination.riderId);
    if (!fromRider) {
      return;
    }
    this.requestChangeClusterWithCheckSamePosition(destination, fromRider, null);
  }

  requestChangeCluster(destination, toRider, isolate = false) {
    const _this = this;
    let fromRider = RiderUtil.findRiderById(this.project.riders, destination.riderId);
    if (fromRider && fromRider.riderId === toRider.riderId) {
      return;
    }

    this.requestChangeClusterWithCheckSamePosition(destination, fromRider, toRider, isolate);
  }

  requestChangeClusterWithCheckSamePosition(destination, fromRider, toRider, isolate = false) {
    console.log("[requestChangeClusterWithCheckSamePosition] deliveryId: " + destination.deliveryId);
    let deliveryIds = [];
    deliveryIds.push(destination.deliveryId);
    let samePosDeliveryIds = [];

    if (!isolate) {//배송지 팝업창에서 이전했을 경우에는 해당 배송지만 이동한다
      if (fromRider) {
        console.log("[requestChangeClusterWithCheckSamePosition] fromRiderId: " + fromRider.riderId);
        samePosDeliveryIds = PlaceUtil.getSamePositionDestinationIdsNotDelivered(fromRider.destinations, destination);
        console.log("[requestChangeClusterWithCheckSamePosition] samePosDeliveryIds: " + samePosDeliveryIds);
      } else {
        samePosDeliveryIds = PlaceUtil.getSamePositionDestinationIdsNoClustering(this.project.destinations, destination);
        console.log("[requestChangeClusterWithCheckSamePosition] samePosDeliveryIds: " + samePosDeliveryIds);
      }
    }

    let popupText = "";
    if (samePosDeliveryIds.length > 0) {
      deliveryIds = deliveryIds.concat(samePosDeliveryIds);
      // popupText = "동일 주소(" + destination.baseAddr + ") 배송지가 " + deliveryIds.length + " 개 존재 합니다. 모든 배송지를 '" + toRider.name + "' 에게 할당하시겠습니까?";
      popupText = _t("동일 주소 배송지가 ${num}개 존재합니다.(${addr}) ", [deliveryIds.length, destination.baseAddr]);
    }
    if (toRider) {
      console.log("[requestChangeClusterWithCheckSamePosition] toRiderId: " + toRider.riderId);
      popupText += _t("배송지를 '${name}' 에게 할당하시겠습니까? ", [toRider.name]);
    } else {
      popupText += _t("배송지를 미배차 배송지로 할당하시겠습니까? ");
    }

    if (this.isDispatchDone()) {
      popupText += _t("(주의: 기사가 편집한 배송순서는 초기화됩니다.)");
    }

    const _this = this;
    PopupUtil.confirmPopup(popupText, {
        onConfirm: () => {
          const dests = PlaceUtil.getDestinationsByIds(deliveryIds, this.project.destinations);
          if (!PlaceUtil.checkDestinationsEditEnabled(dests)) {
            PopupUtil.alertPopup(Util.getSeparateEngString("현재 배송진행중인 배송지가 있어 배차 변경이 불가합니다.", "배송중인 목적지는 삭제/수정할 수 없습니다."));
            return;
          }

          _this.setChangeDeliveriesClusteringCommand(deliveryIds, fromRider ? [fromRider] : [], toRider ? toRider : null);
        }
      }
    );
  }

  sortDestinationsByLocalityAndZipCode(destinations) {
    let sortedDestinations = PlaceUtil.sortDestinationsByLocalityZipCode(destinations);

    for (let i = 0; i < sortedDestinations.length; i++) {
      sortedDestinations[i].label = i + 1;
    }

    this.checkOverlappingDestinations(sortedDestinations);
    return sortedDestinations;
  }


  //맵에서 배송지의 클러스터링을 이동함
  changeClusteringDestinations(fromRiders, toRider, deliveryIds) {

    let riders = [];

    //미배차배송지로 이동할 경우 기사가 없음 이때 조건들이 추가 되어야 함.
    if (toRider != undefined) {
      riders.push(toRider);
    }

    if (fromRiders) {
      riders = riders.concat(fromRiders);
    }

    if (this.isRoutingDone()) {
      this.cleanRidersClusterAndRoutePath(riders);
    }

    for (const deliveryId of deliveryIds) {
      let dest = PlaceUtil.findDestinationById(this.project.destinations, deliveryId);
      if (dest.riderId) {
        const oldRider = RiderUtil.findRiderById(this.project.riders, dest.riderId);
        dest = PlaceUtil.removeDestinationByDeliveryId(oldRider.destinations, deliveryId);
      }

      if (toRider != undefined) {
        dest.riderId = toRider.riderId;
        toRider.destinations.push(dest);
      } else if (toRider == undefined) {//미배송에 이전시
        PlaceUtil.cancelClusterDestinationData(dest);
        this.addDestinationPin(dest, null);
      }
    }

    for (const rider of riders) {
      rider.destinations = this.sortDestinationsByLocalityAndZipCode(rider.destinations);
      rider.totalDeliveryCount = rider.destinations.length;
      if (!this.isRoutingDone()) {//경로탐색이 완료된 이후에는  이후에 경로탐색을 새로 하면서 배송지핀이 업데이트 되므로 속도빠르게 하기 위해서 불필요한 배송지 핀 업데이트 하지 않는다
        this.drawRiderDestinationPin(rider);
      }
    }

    this.processShowCluster(riders);

    const _this = this;
    setTimeout(() => {
      _this.updateTreeViewOnLeftPanel();
    }, 100);
    console.log("changeClusteringDestinations5");
  }

  setChangeDeliveriesClusteringCommand(deliveryIds, fromRiders, toRider) {

    const cmd = this.makeModifyCommand(Constant.RESERVED_COMMAND.CHANGE_CLUSTERING_DESTINATION, {
      deliveryIds: deliveryIds,
      fromRiders: fromRiders,
      toRider: toRider
    });
    this.modifyCommandList.push(cmd);

    //넘겨줘야 할 rider 의 all pin 삭제함.
    fromRiders.forEach(rider => {
      rider.destinations.forEach(destination => {
        this.mapViewController.removeDestinationPin(destination.deliveryId);
      })
    });

    const _this = this;
    const projectId = this.project.id;
    const updateCluster = {};

    if (!toRider) {
      updateCluster.projectId = this.project.id;
//            updateCluster.isNotAssigment = true;
    } else {
      updateCluster.riderId = toRider.riderId;
      updateCluster.projectId = this.project.id;
//            updateCluster.isNotAssigment = false;
    }


    PopupUtil.showLoadingPopup("로딩 중...", "배송지의 배차를 이동중입니다.");
    PROJECT_API.changeDeliveriesClustering(cmd.data.deliveryIds, updateCluster, {
      onSuccess: (response) => {
        console.log("[action] setChangeDeliveriesClusteringCommand success ");
        _this.changeClusteringDestinations(fromRiders, toRider, deliveryIds);
        if (_this.isClusteringStatus()) {
          _this.performRefreshActionAfterModified(cmd, {
            onSuccess: (response) => {
              PopupUtil.dismissLoadingPopup();
              const destination = PlaceUtil.findDestinationByDeliveryId(_this.project.destinations, deliveryIds[0]);
              let popupText = `배송지(${PlaceUtil.getDestinationDisplayId(destination, 20)})${deliveryIds.length > 1 ? " 포함 총 " + deliveryIds.length + "개" : ""}를 ${this.getRiderName(toRider)}에게 배차 변경했습니다.`;
              if (_this.isDispatchDone()) {
                PopupUtil.alertPopup(popupText);
              } else {
                PopupUtil.showNotificationPopup(popupText);
              }
              this.refreshDestinationsList();
            },
            onError: (error) => {
              PopupUtil.dismissLoadingPopup();
              PopupUtil.showErrorPopup(error);
            }
          });
        } else {
          PopupUtil.dismissLoadingPopup();

          //배차가  수행되지 않은 상태에서 배송지 변경이 이루어지면 clusterDone을 true로 설정한다
          if (!this.project.attribute.isClusterDone) {
            if (PlaceUtil.isDestinationsClustered(_this.project.destinations)) {
              // _this.project.attribute.isClusterDone = true;
              _this.syncProjectAttributeToProject({isClusterDone: true});
            }
          }

          _this.performRefreshActionAfterModified(cmd);
        }
      },
      onError: (error) => {
        console.log("[action]Failed to change cluster");
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showErrorPopup(error);
        _this.refreshProject(projectId);
      }
    });

  }

  getRiderName(rider) {
    let riderName = '';
    if (rider)
      riderName = rider.name;
    else
      riderName = '미배차';

    return riderName;
  }

  async onPerformRouting(data) {
    this.clearModifyCommandList();
    await this.performRouting(data);
  }

  async performRouting(data, callback) {

    let routeOption = (data && data.routeOption) ? data.routeOption : this.fields.routeOption;

    console.log("[performRouting] routeOption: " + routeOption);
    PopupUtil.showLoadingPopup("탐색 중...", "잠시만 기다려 주세요.");
    const projectId = this.project.id;
    const _this = this;

    PROJECT_API.getDeliveryOrdersAndRoutes(projectId, routeOption, {
      onSuccess: (response) => {
        PopupUtil.dismissLoadingPopup();
        console.log("[performRouting] loadProject(), projectId: " + projectId);
        _this.loadProject(projectId, callback);
        _this.setTopToolbarRountingButtonEnabled();
      },
      onError: (error) => {
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showErrorPopup(error);
        _this.setTopToolbarRountingButtonEnabled();
        if (callback && callback.onError) {
          callback.onError(error);
        }
      }
    });
  }

  setTopToolbarRountingButtonEnabled() {
    window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.PERFORM_CLUSTERING_BUTTON_DISABLED, false);
  }

  async mouseUp(event) {
    let _this = this;

    if (event.button === 0) { //FireFox에서는 ContextMenuSearch  나온 이후에 MouseUp이벤트가 발생하여 ContextMenuSearch가 바로 사라지는 이슈가 있다. 이때문에 left click일때만 popup을 삭제한다.
      this.closeAllPopup();
    }

    if ((this.fields.isMobile && (!Touch && event instanceof Touch) && !this.fields.isTouchMove) || //Touch 클래스는 Firefox에서 정의가 되지 않아서 오류가 발생함
      (!this.fields.isMouseMove && !this.fields.isShowContextMenu && event.button === 0)) {
      if (this.fields.topToolbarSelectionMode != 0) {
        const worldCoords = this.mapViewController.screen2world(Math.round(event.clientX), Math.round(event.clientY));


        if (PlaceUtil.isLocationPinMode(this.fields.pinMode)) {
          await this.addDestinationByClick(worldCoords.x, worldCoords.y, this.fields.pinMode);
        } else if (RiderUtil.isRiderPinMode(this.fields.pinMode)) {
          await this.addRiderByClick(worldCoords.x, worldCoords.y, this.fields.pinMode);
        } else {
          return;
        }

      }
    }

    this.processMouseDraggingOnMouseUp(event);

    this.fields.isMouseDown = false;
    this.fields.isMouseMove = false;
  }


  allRidersSelected(on, mapClicked = false) {
    const _this = this;

    this.closeAllPopup();

    if (on && !mapClicked)
      this.setCenterFocus({'x': this.project.riders[0].x, 'y': this.project.riders[0].y});

    this.project.riders.filter(rider => rider.isSelected != on).forEach(rider => {
      rider.isSelected = on;
      _this.riderSelected(rider, mapClicked, true);
    });

    //클러스터링 안된 배송지 핀 업데이트
    const destNotRiderIds = _.filter(_this.project.destinations, d => !d.riderId);
    if (destNotRiderIds.length)
      this.destinationsSelected({destinations: destNotRiderIds, isSelected: on});

    this.callUpdateMapViewSync();

  }

  calculateSeletedRidersBoundRect(selectedRider) {
    let updatedRiders = false;

    if (!selectedRider) {
      return false;
    }

    const selectedRiders = RiderUtil.findSelectedRiders(this.project.riders);

    if (!selectedRider.isSelected) {
      this.fields.vehiclesBoundRect.left = 180;
      this.fields.vehiclesBoundRect.top = 90;
      this.fields.vehiclesBoundRect.right = -180;
      this.fields.vehiclesBoundRect.bottom = -90;
    }

    if (selectedRiders && selectedRiders.length > 0) {
      for (const sr of selectedRiders) {
        if (this.fields.vehiclesBoundRect.left > sr.x) {
          this.fields.vehiclesBoundRect.left = sr.x;
        }

        if (this.fields.vehiclesBoundRect.top > sr.y) {
          this.fields.vehiclesBoundRect.top = sr.y;
        }

        if (this.fields.vehiclesBoundRect.right < sr.x) {
          this.fields.vehiclesBoundRect.right = sr.x;
        }

        if (this.fields.vehiclesBoundRect.bottom < sr.y) {
          this.fields.vehiclesBoundRect.bottom = sr.y;
        }

        for (const destination of sr.destinations) {
          if (this.fields.vehiclesBoundRect.left > destination.x) {
            this.fields.vehiclesBoundRect.left = destination.x;
          }

          if (this.fields.vehiclesBoundRect.top > destination.y) {
            this.fields.vehiclesBoundRect.top = destination.y;
          }

          if (this.fields.vehiclesBoundRect.right < destination.x) {
            this.fields.vehiclesBoundRect.right = destination.x;
          }

          if (this.fields.vehiclesBoundRect.bottom < destination.y) {
            this.fields.vehiclesBoundRect.bottom = destination.y;
          }
        }
      }
      updatedRiders = true;
    }
    return updatedRiders;
  }


  riderSelected(rider, mapClicked = false, allRiders = false) {
    if (rider === undefined) {
      return;
    }

    //기사 클릭시 기사의 위치로 이동하게 수정함.
    if (!mapClicked && !allRiders && rider.isSelected === true)
      this.setCenterFocus({'x': rider.x, 'y': rider.y});

    const selectedRider = RiderUtil.findRiderByGid(this.project.riders, rider.gid); // redmine-#1138 RiderUtil.findRiderById(this.project.riders, rider.riderId);

    if (selectedRider === undefined) {
      return;
    }

    selectedRider.isSelected = rider.isSelected;

    if (!allRiders) {
      this.closeAllPopup();
    }
    // 안하는게 나을 듯 this.mapViewController.showCluster(tempRider, rider.type === Constant.RiderType.TRUCK, tempRider.isSelected);
    if (selectedRider.isSelected) {

      //rider 차량 및 정보 보여주는 기능
      this.addRiderPinToMap(rider);
      //rider work location pin 보이는 기능
      this.mapViewController.addWorkLocationPin(rider);

      //경로선감춤 기능시 left panel에서 select 값이 this.project에 추가 안되는 이슈 수정함.
      if (this.getRouteLineDisplay()) {
        this.drawRiderRouteLine(rider);
      }

      //선택 해지시 pin 추가함.
      if (!this.mapSpeedUpMode) {//속도 업 모드에서는 핀을 항상 표시함
        rider.destinations.forEach(dest => {
          this.addDestinationPin(dest);
        });
      }

      this.activateRider(selectedRider);//[demo]

      if (this.isClusteringStatus()) {
        this.mapViewController.showCluster(rider, this.getRouteLineDisplay());
      }

    } else {

      //rider 차량 및 정보 지워지는 기능
      this.mapViewController.removeRiderPin(rider.riderId);
      this.mapViewController.clearDestinationPinTag(selectedRider);
      this.clearRiderAllRoutePath(selectedRider);

      //선택 해지시 pin 삭제함.
      if (!this.mapSpeedUpMode) {
        rider.destinations.forEach(dest => {
          this.mapViewController.removeDestinationPin(dest.deliveryId);
        });
      }

      if (selectedRider.riderId == this.fields.activatedRiderId) {//[demo]
        const selectedRiders = RiderUtil.findSelectedRiders(this.project.riders);
        if (selectedRiders && selectedRiders.length > 0) {
          this.fields.activatedRiderId = selectedRiders[0];
        }
      }

      if (this.isClusteringStatus()) {
        this.mapViewController.showCluster(rider, false);
      }
    }

    if (!allRiders) {
      this.callUpdateMapViewSync();
      this.project.updateCount++;//[브래드 크럼 업데이트 이슈]기사의 isSelected 바꿔도  watch가 호출이 되지 않기 때문에 강제로 이 값을 바꿔서 project에 해당한는 watch가 호출되도록 한다.
    }
  }

  riderSelectedItem(rider, isView = false) {
    if (rider === undefined) {
      return;
    }

    const selectedRider = RiderUtil.findRiderByGid(this.project.riders, rider.gid); // redmine-#1138 RiderUtil.findRiderById(this.project.riders, rider.riderId);

    if (selectedRider === undefined) {
      return;
    }

    // 안하는게 나을 듯 this.mapViewController.showCluster(tempRider, rider.type === Constant.RiderType.TRUCK, tempRider.isSelected);
    if (isView) {
      //rider 차량 및 정보 보여주는 기능
      this.addRiderPinToMap(rider);
      //rider work location pin 보이는 기능
      this.mapViewController.addWorkLocationPin(rider);

      if (this.getRouteLineDisplay()) {
        this.drawRiderRouteLine(rider, isView);
      }

      //선택 해지시 pin 추가함.
      if (!this.mapSpeedUpMode) {//속도 업 모드에서는 핀을 항상 표시함
        rider.destinations.forEach(dest => {
          this.addDestinationPin(dest);
        });
      }
      if (this.isClusteringStatus()) {
        this.mapViewController.showCluster(rider, this.getRouteLineDisplay());
      }

    } else {
      this.clearRiderAllRoutePath(selectedRider);
      if (this.isClusteringStatus()) {
        this.mapViewController.showCluster(rider, false);
      }
    }
  }

  destinationsSelected(dest, mapClicked = false) {
    if (dest == undefined)
      return;

    //할당 안된 배송지 pin 보여줌
    if (dest.isSelected) {
      dest.destinations.forEach(dest => {
        this.mapViewController.addDestinationPin(dest, Constant.DESTINATION_PIN_MODE.SET);
      })
    }
    //할당 안된 배송지 pin 삭제함.
    else {
      dest.destinations.forEach(dest => {
        this.mapViewController.removeDestinationPin(dest.deliveryId);
      });
    }
  }

  allDestinationsSelected(on, mapClicked = false) {
    const _this = this;


    this.closeAllPopup();
    this.project.destinations.filter(destination => destination.isSelected != on).forEach(destination => {
      destination.isSelected = on;
      // #2253 불필요한 루틴 comment out
      //_this.destinationSelected(destination, mapClicked, true);
    });
    if (on && !mapClicked) {
      const selectedDestinations = PlaceUtil.findSelectedDestinations(this.project.destinations);
      const seletedDestinationsBoundRect = this.calculateSelectedDestinationsBoundRect(selectedDestinations);

      if (seletedDestinationsBoundRect) {
        this.mapViewController.setZoomBound(this.fields.destinationsBoundRect);
        this.zoomLevel = this.mapViewController.getZoomLevel();
        this.setCenterPosition();
      } else {
        this.setCenterFocus({
          'x': selectedDestination.isSelected ? destination.x : selectedDestinations[0].x,
          'y': selectedDestination.isSelected ? destination.y : selectedDestinations[0].y
        });
      }
    }
    this.callUpdateMapViewSync();
  }


  destinationSelected(destination, mapClicked = false, allDestinations = false) {
    if (destination === undefined) {
      return;
    }

    const selectedDestination = PlaceUtil.findDestinationById(this.project.destinations, destination.deliveryId);

    if (selectedDestination === undefined) {
      return;
    }

    selectedDestination.isSelected = destination.isSelected;

    if (!allDestinations) {
      this.closeAllPopup();
    }

    if (selectedDestination.isSelected) {
      //console.log("destination select  : " + selectedDestination.deliveryId );
      if (!mapClicked && !allDestinations) {
        const selectedDestinations = PlaceUtil.findSelectedDestinations(this.project.destinations);

        if (selectedDestinations && selectedDestinations.length > 0) {
          const selectedDestinationsBoundRect = this.calculateSelectedDestinationsBoundRect(selectedDestination);

          if (selectedDestinationsBoundRect) {
            this.mapViewController.setZoomBound(this.fields.destinationsBoundRect);
            this.zoomLevel = this.mapViewController.getZoomLevel();
            this.setCenterPosition();
          } else {
            this.setCenterFocus({
              'x': selectedDestination.isSelected ? destination.x : selectedDestinations[0].x,
              'y': selectedDestination.isSelected ? destination.y : selectedDestinations[0].y
            });
          }
        }
      }

      this.activateDestination(destination);//[demo]
    }

    if (!allDestinations) {
      this.callUpdateMapViewSync();
    }
  }


  calculateSelectedDestinationsBoundRect(selectedDestination) {
    let updatedDestinations = false;

    if (!selectedDestination) {
      return false;
    }

    const selectedDestinations = PlaceUtil.findSelectedDestinations(this.project.destinations);

    if (!selectedDestination.isSelected) {
      this.fields.destinationsBoundRect.left = 180;
      this.fields.destinationsBoundRect.top = 90;
      this.fields.destinationsBoundRect.right = -180;
      this.fields.destinationsBoundRect.bottom = -90;
    }

    if (selectedDestinations && selectedDestinations.length == 1) {
      return false;
    }

    if (selectedDestinations && selectedDestinations.length > 0) {
      for (const destination of selectedDestinations) {
        if (this.fields.destinationsBoundRect.left > destination.x) {
          this.fields.destinationsBoundRect.left = destination.x;
        }

        if (this.fields.destinationsBoundRect.top > destination.y) {
          this.fields.destinationsBoundRect.top = destination.y;
        }

        if (this.fields.destinationsBoundRect.right < destination.x) {
          this.fields.destinationsBoundRect.right = destination.x;
        }

        if (this.fields.destinationsBoundRect.bottom < destination.y) {
          this.fields.destinationsBoundRect.bottom = destination.y;
        }
      }
      updatedDestinations = true;
    }
    return updatedDestinations;
  }

  projectTerminate(data) {
    if (this.project.id == data.projectId) {
      this.project.status = Constant.PROJECT_STATUS.DONE;
      this.project.attribute.isClusterDone = false;
      this.project.attribute.isRouteEnabled = false;
      this.project.attribute.isSendingRiderEnabled = false;
    }
  }


  /**
   * 실제 주행 경로 조회 및 그리기
   */
  async loadRealDrivingPath() {
    PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

    let projectId = this.project.id;
    let _this = this;
    let promises = []
    for (let rider of this.project.riders) {
      // const now = new Date();
      // now.setHours(0); now.setMinutes(0); now.setSeconds(0);		// 0시0분0초
      // const fromDate = Util.getDateFormattedString(now, 'yyyy-MM-ddTHH:mm:ss');
      // now.setHours(23); now.setMinutes(59); now.setSeconds(59);	// 23시59분59초
      // const toDate = Util.getDateFormattedString(now, 'yyyy-MM-ddTHH:mm:ss');
      const fromDate = null;
      const toDate = null;
      const promise = RIDER_API.getRiderLocations(rider.riderId, projectId, fromDate, toDate, {
        onSuccess: async (response) => {
          rider.realDrivingPath = response.data.locations.map(loc => loc.location);
          await _this.mapViewController.drawRealDrivingPath(rider);
        }
        , onError: (error) => {
          console.error(`실제 주행 경로 조회 실패 - 기사아이디(${rider.riderId}), 프로젝트 아이디(${projectId}), fromDate(${fromDate}), toDate(${toDate})`);
        }
      });

      promises.push(promise);
    }

    //한번에 호출한뒤 나중에 한번에 promises 해지 한다
    await Promise.all(promises);

    _this.updateMapView();
    PopupUtil.dismissLoadingPopup();
  }


  async clearRiderRoutingInfo(rider) {
    if (rider === undefined || rider.destinations.length == 0) {
      return;
    }

    rider.destinations.forEach(destination => {
      destination.order = 0;
      destination.drawingOrder = 0;
      destination.routePath = [];
      destination.routePathReal = [];
      if (Constant.SUPPORT_REAL_MULTI_ROUTE_PATH) {
        destination.multiRoutePathReal = [];
      }
      destination.label = null;
      destination.overlapLabel = null;

      if (rider.isSelected)
        this.addDestinationPin(destination);

    });
    rider.isRoutingDone = false;
  }

  //routepath와 mppRoutePath를 모두 지움
  clearRiderAllRoutePath(rider) {
    this.mapViewController.clearRiderAllRoutePath(rider);
    this.clearMppRoutePath(rider);
  }

  async clearAllRouteLines(riders, resetDestination) {
    for (let rider of riders) {
      this.clearRiderAllRoutePath(rider);
      this.clearRiderRoutingInfo(rider);

      if (resetDestination) {
        rider.destinations = [];
      }
    }
    //리셋시에 검색 결과도 초기화 되므로 이부분도 처리하여 준다.
    this.closeAllPopup();

    this.updateMapView();
  }

  getDataFromPosition(x, y) {
    const tagId = this.mapViewController.hitTestForPin(x, y);
    return this.getDataFromTagId(tagId);
  }

  getDataFromMultiPositions(x, y) {
    let tagObjs = [];
    const tagIds = this.mapViewController.hitTestForMultiPins(x, y);

    if (!tagIds)
      return tagObjs;

    for (const tagId of tagIds) {
      tagObjs.push(this.getDataFromTagId(tagId));
    }

    //tgsIds중에 가장 가까운 좌표만 구해서 리턴한다
    const mouseUpPos = this.mapViewController.screen2world(x, y);
    let closeTagObjs = [];
    let minDist = 99.9;
    let closeObj = null;

    for (const tagObj of tagObjs) {
      if (tagObj && tagObj.info && tagObj.info.x) {
        const dist = DemoUtil.getSimpleDistanceTwoPoint(mouseUpPos.x, mouseUpPos.y, tagObj.info.x, tagObj.info.y);
        if (dist <= minDist) {
          if (dist < minDist) {
            closeTagObjs = [];
          }
          minDist = dist;
          closeTagObjs.push(tagObj);
        }
      }
    }

    //중복 핀 제거 . 가끔 중복해서 맵사이즈별 정의된 핀이 한번에 올라오는 경우가 있다
    let menuMap = new Map;
    for (let i = 0; i < closeTagObjs.length; i++) {
      const o = closeTagObjs[i];
      if (!o) continue;
      const key = this.getObjectId(o);
      let value = menuMap.get(key);
      if (!value) {
        value = [];
        menuMap.set(key, value);
      }
      value.push(o);
    }

    let resultObjs = [];
    menuMap.forEach((value, key) => {
      resultObjs.push(value[0]);
    });

    return resultObjs;
  }

  getDataFromTagId(tagId) {
    if (tagId) {
      if (tagId.startsWith(Constant.TP.RIDER)) {
        const id = parseInt(tagId.substring(0, tagId.length - 2).replace(Constant.TP.RIDER, ""));
        return {type: Constant.TP.RIDER, tagId: tagId, info: RiderUtil.findRiderById(this.project.riders, id)};
      } else if (tagId.startsWith(Constant.TP.DESTINATION)) {
        const id = parseInt(tagId.substring(0, tagId.length - 2).replace(Constant.TP.DESTINATION, ""));
        const destData = PlaceUtil.findDestinationById(this.project.destinations, id);
        const riderData = RiderUtil.findRiderById(this.project.riders, destData.riderId);
        return {
          type: Constant.TP.DESTINATION,
          tagId: tagId,
          info: {
            ...destData,
            riderColor: riderData?.colorIndex,
            riderName: riderData?.name,
          }
        };
      } else if (tagId.startsWith(Constant.TP.ALL_STATION)) {
        const id = parseInt(tagId.substring(0, tagId.length - 2).replace(Constant.TP.ALL_STATION, ""));
        return {
          type: Constant.TP.ALL_STATION,
          tagId: tagId,
          station: this.evMgr.getEvStationFromAllEvStation(id)
        };
      } else if (tagId.startsWith(Constant.TP.STATION_ON_ROUTE)) {
        const params = tagId.replace(Constant.TP.STATION_ON_ROUTE, "").split('_');
        const riderId = params[0];
        const stationIndex = params[1];
        return {
          type: Constant.TP.STATION_ON_ROUTE,
          tagId: tagId,
          riderId: riderId,
          stationIndex: stationIndex,
          station: this.evMgr.getEvStationFromStationOnRoute(this.project.riders, riderId, stationIndex)
        };
      } else if (tagId.startsWith(Constant.TP.WAYPOINT_STATION)) {
        const params = tagId.replace(Constant.TP.WAYPOINT_STATION, "").split('_');
        const riderId = params[0];
        const stationIndex = params[1];
        return {
          type: Constant.TP.WAYPOINT_STATION,
          tagId: tagId,
          riderId: riderId,
          stationIndex: stationIndex,
          station: this.evMgr.getEvStationFromWaypointStation(this.project.riders, riderId, stationIndex)
        };
      }
    }
    return undefined;
  }


  addDestination(destination, data) {
    this.setAddDestinationCommand(destination, data);
    // this.addDestinationOnMap(destination);//기존 코드 직접 목적지 추가
  }


  addDestinationOnMap(destination) {
    this.initDestinationUiVariables(destination);
    this.project.destinations.push(destination);
    switch (destination.type) {
      case Constant.DestinationType.HUB:
        this.project.hubTotalCount += 1;
        break;
      case Constant.DestinationType.INTERMEDIATE:
        this.project.branchTotalCount += 1;
        break;
      case Constant.DestinationType.FINAL:
        this.project.lastDestinationTotalCount += 1;
        break;
    }
    this.project.attribute.isRouteEnabled = true;
    this.project.attribute.isSendingRiderEnabled = false;

    if (destination.type !== Constant.DestinationType.HUB && !destination.label) {//HUB만 아니면 항상 숫자가 표시됨.
      destination.seqLabel = (this.project.lastDestinationTotalCount + this.project.branchTotalCount);
    }

    this.addDestinationPin(destination);

    if (window.parent.app.$refs.leftPanel.$refs.addressSearch !== undefined) {
      window.parent.app.$refs.leftPanel.$refs.addressSearch.clearAddressList();
      window.parent.app.$refs.leftPanel.selectToolbar(0);
    }

    this.updateMapView();
  }


  /**
   * 지도 상의 클릭으로 Rider 추가
   */
  async addRiderByClick(x, y, pinMode, isRealTracking = false, uuid = null, riderId) {
    // const type = RiderUtil.getRiderType(pinMode);
    const type = Constant.PINMODE.TRUCK;

    //다른 기사의 근무 시작주소를 넣어준다
    let workingStartAddress = null;
    if (x && y) {
      workingStartAddress = await this.getSearchAddressByPoint(x, y);
    } else {
      if (!this.workingStartAddress) {
        for (const rider of this.project.riders) {
          if (rider.workingStartAddress) {
            this.workingStartAddress = rider.workingStartAddress;
            break;
          }
        }
      }
      workingStartAddress = this.workingStartAddress;
    }


    //그룹의 destinations의 groupName을 넣어준다.
    let rider = {
      'riderId': null,
      'name': null,
      'x': x,
      'y': y,
      'workingStartAddress': workingStartAddress,
      'totalDeliveryCount': 0,
      'projectId': this.project.id,
      'type': type,
    };

    rider.groupNameList = this.updateGroupNameList();
    rider.riderInfoList = this.getRiderInfoList();

    this.initRiderUiVariables(rider);
    window.parent.app.$refs.leftPanel.$emit('show-rider-detail-popup', rider, 'a');
    return rider;
  }


  async getSearchAddressByPoint(x, y) {
    let response = await SEARCH_API.getAddressByPosition(this.fields.selectedCountry, x, y);
    if (!response) //검색 실패 했을때 아래와 같이 검색하면 주소 검색에서 좌표값이 그대로 들어간다.
      return x + "," + y;

    let addr = response.data.searchcoord[0];
    if (addr) {
      //핀으로 주소지 추가시에 구주소 표시 문제.  addr.intersectinglocality 에 이미 신주소가 입력되어 있음. 이 주소가 존재 할경우 이 주소를 우선적으로 사용함.
      return (addr.intersectinglocality && addr.intersectinglocality.length > 0) ? addr.intersectinglocality : this.getDetailAddress(addr);
    }
    return null;
  }

  //addr로 구주소 형태로 변환
  getDetailAddress(addr) {
    let address = '';
    if (this.fields.selectedCountry == 'kr') {
      address = addr.adminarea + ' ' + addr.subadminarea + ' ' + addr.locality;
      if (addr.sublocality) {
        address += ' ' + addr.sublocality;
      }
      address += ' ' + addr.subno;
    } else {
      address = addr.subno + ' ';
      if (addr.sublocality) {
        address += ', ' + addr.sublocality;
      }
      address += addr.locality + ', ' + addr.subadminarea + ', ' + addr.adminarea;
    }

    return address;
  }


  async getSearchEntrance(x, y, destination) {
    let _this = this;
    return SEARCH_API.getEnteranceByPosition(this.fields.selectedCountry, x, y, {
      onSuccess: (response) => {
        try {
          let entrance = response.data;
          if (entrance) {
            destination.entrance = entrance;
          }
        } catch (e) {
          console.error("getSearchEntrance() " + e);
        }
      },
      onError: (error) => {
        PopupUtil.alertPopup("Search Entrance API 요청 실패!");
      }
    });
  }


  initRiderUiVariables(rider) {
    rider.isRoutingDone = (!(rider.status === Constant.RIDER_STATUS.WAITING || rider.status === Constant.RIDER_STATUS.DISPATCHING));
    rider.isClusteringDone = (!(rider.status === Constant.RIDER_STATUS.WAITING || rider.status === Constant.RIDER_STATUS.DISPATCHING));
    rider.isSelected = true;
    rider.isRealTracking = false;
    rider.angle = Util.getRandomNumber(360);
    rider.speed = 0;
    rider.waypoints = [];
    rider.isCompletedAllDriving = false;
    rider.drivingDestIndex = 0;
    rider.uuid = Util.uuidv4();
    rider.gid = rider.riderId + "_" + rider.groupName;          // redmine-#1138
    // TODO: rider.currentDrivableDistance = 5000;
    // TODO: rider.maxDrivableDistance = 10000;
  }

  async loadEmptyProject() {
    let _this = this;

    console.log("loadEmptyProject()");
    //굳이 재사용해서 버그 생성할 필요 없다. 매번 생성하자
    // PROJECT_API.loadEmptyProjectOrCreateIfNotExists({
    PROJECT_API.createEmptyProject({
      onSuccess: (response) => {
        // MapUtil.mergeProject(_this.project, response.data);
        _this.loadProject(response.data.id, /*{ isTempProject : true } 잘 안됨. */);
      },
      onError: (error) => {
        console.error('빈 프로젝트 열기 실패');
      }
    });
  }

  /**
   * 프로젝트를 로딩하되 화면 갱신을 최소화 하여 않고 업데이트가 이루어진다.
   */
  async loadProjectData(projectId, callback) {
    const MOVE_MAP = false;
    const SILENT_MODE = true;
    await this.loadProject(projectId, callback, {moveMap: MOVE_MAP, silentMode: SILENT_MODE});
  }

  /**
   *   프로젝트 다시 로딩. 데이터를 초기화 하고 다시 프로젝트를 세팅하는 방식임.
   */
  async refreshProject(projectId, callback) {
    const MOVE_MAP = false;
    const SILENT_MODE = false;
    await this.loadProject(projectId, callback, {moveMap: MOVE_MAP, silentMode: SILENT_MODE});
  }


  subscribeMQTTNewProject(projectId, userId, projectUserId) {
    if (!isAnonymous() && projectId) {
      window.parent.app.$emit(EVENT.MAIN.MQTT_SUBSCRIBE_NEW_PROJECT, {
        projectId: projectId,
        userId: userId,
        projectUserId: projectUserId,
      });
    }
  }

  subscribeMQTTProject(projectId, userId, projectUserId) {
    if (!isAnonymous() && projectId) {
      window.parent.app.$emit(EVENT.MAIN.MQTT_SUBSCRIBE_PROJECT, {
        projectId: projectId,
        userId: userId,
        projectUserId: projectUserId,
      });
    }
  }

  unsubscribeMQTTProject(projectId) {
    if (!isAnonymous()) {
      if (projectId) {
        const userId = this.storageGetters().getLoginUserInfo.id;

        if (userId) {
          window.parent.app.$emit(EVENT.MAIN.MQTT_UNSUBSCRIBE_PROJECT, {
            projectId: projectId,
            userId: userId,
          });
        }
      } else if (this.project.id) {
        const userId = this.storageGetters().getLoginUserInfo.id;
        const projectUserId = this.project.userId;

        if (userId || projectUserId) {
          window.parent.app.$emit(EVENT.MAIN.MQTT_UNSUBSCRIBE_PROJECT, {
            projectId: this.project.id,
            userId: userId,
            projectUserId: projectUserId,
          });
        }
      } else if (this.storageGetters().getWatchingProjectId) {
        const userId = this.storageGetters().getWatchingUserId;
        const projectUserId = this.storageGetters().getProjectUserId;

        if (userId || projectUserId) {
          window.parent.app.$emit(EVENT.MAIN.MQTT_UNSUBSCRIBE_PROJECT, {
            projectId: this.storageGetters().getWatchingProjectId,
            userId: userId,
            projectUserId: projectUserId,
          });
        }
      }
    }
  }

  logoutUser() {
    if (!isAnonymous()) {
      const userId = this.storageGetters().getLoginUserInfo.id || this.storageGetters().getWatchingUserId;
      window.parent.app.$emit(EVENT.MAIN.LOGOUT_REFRESH_USER, {
        userId: userId,
      });
    }
  }

  responseDataMakeProject(data, moveMap, silentMode, callback) {
    MapUtil.mergeProject(this.project, data);//[demo] parameter로 전달된 참조값 자체가 바뀐다. Object.assign을 이용하자.
    //this.isRouteLineDisplay = false;
    if (!this.project.attribute) {
      this.project.attribute = {
        "isClusterDone": false,
        "isRouteEnabled": false,
        "isSendingRiderEnabled": false,
        "isFirstRoutingDone": false,
        "isReadOnly": false,
        "isProductCheckEnabled": false,
        "isOnDemandEnabled": false,
        "isRouteExecute": false
      };
    }

    this.project.isTempProject = Util.isTempProject(this.project);//isTempProject 아직 사용하는곳이 없지만 혹시 모르니 설정한다

    if (this.project.status == Constant.PROJECT_STATUS.UNKNOWN) {
      PopupUtil.dismissLoadingPopup();
      PopupUtil.alertPopup("알 수 없는 프로젝트 형식 입니다.");
      return;
    }

    if (!silentMode) {
      // TOP TOOL BAR의 PROJECT NAME 세팅
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.SET_PROJECT_NAME, this.project.name);
      // top tool bar noti 갯수 세팅
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.GET_NOTI_COUNT, this.project.id);

      // LeftPanel의 Toolbar 선택 초기화
      window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.INIT_LEFT_PANEL_TAB);
    }

    this.setMapSpeedUpMode(); //로딩 이후 스피드 업 모드 체크
    this.setMapParametersByMapSpeedMode();

    this.project.riders.sort(function (a, b) {
      return a.name.localeCompare(b.name);
    });

    this.disableOverlapSetImageMask();

    // 기사 Pin 추가
    this.project.riders.forEach(rider => {

      if (!this.isTrackingRiderEnable(rider)) {//트래킹이 가능하지 않으면 시작 주소로 이동한다
        if (rider.workingStartLocation && rider.workingStartLocation.x) {
          rider.x = rider.workingStartLocation.x;
          rider.y = rider.workingStartLocation.y;
        }
      }

      // 기사에 할당된 방문지 Pin 추가
      let returnPlaceIndex = -1;
      rider.destinations.forEach((destination, index) => {
        this.initDestinationUiVariables(destination, false, rider.hasPickupPlace, index);

        if (Util.isPassingDestination(destination)) {
          rider.drivingDest = destination;
        }

        if (Util.isPassedDestination(destination) && destination.routePathReal && destination.routePathReal.length > 0) {
          //destination.passedRoutePathReal = Util.getBufferedRoutePath(destination.routePathReal);
          //#2534 turf.buffer()가 오래걸리는 이슈가 있어 web worker로 처리한다.
          this.fields.geoBufferWorker.postMessage({
            riderId: rider.riderId,
            deliveryId: destination.deliveryId,
            routePathReal: destination.routePathReal
          });
        } else {
          destination.passedRoutePathReal = null;
        }

        if (PlaceUtil.isReturnPlace(destination)) {
          returnPlaceIndex = index;
        }
      });

      //회차 지점 맨뒤로 보내기
      if (returnPlaceIndex >= 0) {
        let returnPlace = rider.destinations[returnPlaceIndex];
        rider.destinations.splice(returnPlaceIndex, 1);
        rider.destinations.push(returnPlace);
      }

      //drawDelayAllDestinations 로 이동
      // 절대 await 걸지 말것!! left-penel 문제 발생함!!
      // _this.mapViewController.drawAllPlannedWaypointsRoutePath(rider);

      this.initRiderUiVariables(rider);
      this.mapViewController.addRiderPin(rider);
      if (!this.workStartLocationNewConcept) {
        this.mapViewController.addWorkLocationPin(rider);
      }
      this.fields.currentDeliveryType = rider.deliveryType;// 모든 기사가 동일한 deliveryType을 가진다고 가정하자.

      this.checkOverlappingDestinations(rider.destinations);
    });


    let indexSeqLabel = 0;//미배차 배송지지의 경우 HUB가 아닐 경우에만 핀의 라벨을 표시한다

    let replaceDestinations = [];
    this.project.destinations.forEach(destination => {
      this.initDestinationUiVariables(destination, true);

      if (destination.type !== Constant.DestinationType.HUB && !destination.label) {
        destination.seqLabel = ++indexSeqLabel;
      }
      if (!destination.riderId) { // 기사가 할당되지 않은 방문지 Pin 추가
        this.addDestinationPin(destination);
        replaceDestinations.push(destination);//[demo] 추가
      } else {//project에만 delivery데이터가 있어서 sync를 맞춰준다. 깔끔하게 하는 방법을 생각해보자
        let findRider = RiderUtil.findRiderById(this.project.riders, destination.riderId);
        if (findRider) {
          let riderDestination = PlaceUtil.findDestinationByDeliveryId(findRider.destinations, destination.deliveryId);
          if (riderDestination && PlaceUtil.isFinalOrReturnPlace(riderDestination)) {
            MapUtil.mergeObject(riderDestination, destination);
            replaceDestinations.push(riderDestination);//[demo] 추가
          } else {
            replaceDestinations.push(destination);
          }
        } else {
          console.warn("loadProject warining Can't find destination from rider id " + destination.riderId);
          replaceDestinations.push(destination);
        }
      }
    });

    this.recoverOverlapSetImageMask();

    this.project.destinations = replaceDestinations;//[demo] rider의 목적지와 project 의 목적지가 동일한 참조값을 갖도록 함.

    if (!silentMode && this.project.status != Constant.PROJECT_STATUS.DONE) {
      this.startTracking();
    }

    // PopupUtil.dismissNotificationPopup();
    PopupUtil.dismissLoadingPopup();

    if (moveMap) {
      const RIDER_1 = this.project.riders[0];

      if (RIDER_1) {
        this.setCenterFocus({'x': RIDER_1.x, 'y': RIDER_1.y});
      }
    }

    this.subscribeMQTTNewProject(this.project.id, this.storageGetters().getLoginUserInfo.id, this.project.userId);

    if (callback && callback.onSuccess) callback.onSuccess();

    this.setRidersColorIndex(this.project.riders);
    this.processReservationTimeAllDestinations();


    //온디멘드 경우 order로 정렬 하면 오류가 생기기 때문에 배송 완료된 순서대로 정렬한다
    if (this.project.attribute.isAutoCreated) {
      this.setSequenceOrderDestinations();
    }

    this.drawDelayProject();
    this.updateMapView();

    this.syncProjectAttributeToStore();
    this.setTopToolbarClusteringButtonEnabled();
    console.log("loadProject completed  rider : " + this.project.riders.length + ", destinations : " + this.project.destinations.length + " isGeoCodingStatus : " + this.isGeoCodingStatus() + "  ,isClusteringStatus : " + this.isClusteringStatus() + "  ,isRoutingDoneStatus : "
      + this.isRoutingDoneStatus() + "  ,isDispatchDone : " + this.isClusteringStatus() + "  ,isProjectDone : " + this.isProjectDone() + " ,isEditableProject : " + this.isEditableProject());

    //기사 관리 페이지 UI 업데이트
    const riderList = window.parent.app.$root.popup.riderList;
    if (riderList && riderList.isShow) {
      const riderListPopup = window.parent.app.$refs.riderListPopup;
      if (riderListPopup) {
        riderListPopup.$emit(EVENT.RIDER_LIST_POPUP.UPDATE);
      }
    }
  }


  makeDropFileProject(data, callback = null, props = {}) {
    const moveMap = props.moveMap ? props.moveMap : false;
    const silentMode = props.silentMode ? props.silentMode : false;

    if (!silentMode) {//초기화를 할경우 초기화 된 화면이 잠깐 보이는 이슈가 있어서 이때는 삭제한다
      this.resetProject(true, true, moveMap, silentMode);
      PopupUtil.showLoadingPopup("로딩 중…", "잠시만 기다려 주세요.");
    }

    this.responseDataMakeProject(data, moveMap, silentMode, callback);
  }

  async loadProject(projectId, callback = null, props = {}) {
    const moveMap = props.moveMap ? props.moveMap : false;
    const silentMode = props.silentMode ? props.silentMode : false;

    if (!projectId) {
      projectId = this.project.id;
    }
    if (!projectId) {
      console.error("[loadProject] project is NULL");
      return;
    }

    if (!silentMode) {//초기화를 할경우 초기화 된 화면이 잠깐 보이는 이슈가 있어서 이때는 삭제한다
      this.resetProject(true, true, moveMap, silentMode);
      PopupUtil.showLoadingPopup("로딩 중…", "잠시만 기다려 주세요.");
    }

    let _this = this;

    this.subscribeMQTTProject(projectId);

    PROJECT_API.getProjectInfo(projectId, {
      onSuccess: (response) => {
        console.log('[loadProject] projectId: ' + response.data.id);
        this.responseDataMakeProject(response.data, moveMap, silentMode, callback);
      },
      onError: (error) => {
        console.error('[loadProject] 프로젝트 정보 조회 실패');
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showErrorPopup(error);
        _this.setTopToolbarClusteringButtonEnabled();
        if (callback && callback.onError) callback.onError();
      }
    });
  }

  //완료된 배송지가 앞에 완료되지 않은 배송지가 뒤에 나오도록 한다
  sortDestinationsByDeliveryEndTime(destinations) {
    if (destinations.length == 1) {
      return destinations;
    }

    let completedDests = destinations.filter(d => d.realEndTime);
    completedDests = completedDests.sort(function (a, b) {
      return TimeUtil.compareTimeFormat(a.realEndTime, b.realEndTime, Constant.TIME_FORMAT.TIME_SECONDS);
    });

    let notCompletedDest = destinations.filter(d => !d.realEndTime);
    notCompletedDest = PlaceUtil.generateSortedDestinationsByOrder(notCompletedDest);

    const sortedDests = completedDests.concat(notCompletedDest);
    return sortedDests;
  }


  setSequenceOrderDestinations() {
    for (let rider of this.project.riders) {
      rider.destinations = this.sortDestinationsByDeliveryEndTime(rider.destinations);

      //다시 순서대로 label 을 설정한다
      for (let i = 0; i < rider.destinations.length; i++) {
        rider.destinations[i].label = i + 1;
      }

      //label값이 바뀌었으므로 중복핀 처리
      this.checkOverlappingDestinations(rider.destinations);

    }
  }

  //기사의 배송지/경로를 그린다
  drawRiderDestinationPin(rider) {
    if (rider.isSelected) {
      for (const destination of rider.destinations) {
        this.addDestinationPin(destination, null);
      }
      console.log("this.drawRiderRouteLine : drawRiderDestinationPin")

      //the line 옵션으로 정리함.
      if (this.getRouteLineDisplay()) {
        this.drawRiderRouteLine(rider);
      }

      if (!rider.mpp) {
        this.performMpp(rider, false);
      }
    }
  }

  //새로운 맵을 업데이트 이후 이미지 추가 작업이 10배이상 오래 걸린다. 중간에 팝업을 보여줘야 하므로 timer를 사용해서 1개씩 배송지와 경로를 그리도록 한다
  drawDelayProject() {
    if (this.delayDrawingTimer) {
      clearTimeout(this.delayDrawingTimer);
    }

    let drawRider = null;
    for (const rider of this.project.riders) {
      if (!rider.isDraw) {
        drawRider = rider;
        break;
      }
    }

    if (drawRider) {
      if (Util.isPopupEnabledChecked() === false)
        PopupUtil.showLoadingPopup("로딩 중…", _t("기사의 배송지를 그리는 중입니다(${name})", [drawRider.name]));

      const _this = this;
      this.delayDrawingTimer = setTimeout(() => {
        drawRider.isDraw = true;
        _this.drawRiderDestinationPin(drawRider);
        _this.updateMapView();
        PopupUtil.dismissLoadingPopup();
        _this.drawDelayProject();
      }, 10);
    } else {
      //모든 기사 그리기가 종료 되었다
      if (!drawRider) {
        console.log("this.processShowCluster : drawDelayProject")
        //the line 옵션으로 정리함.
        if (this.getRouteLineDisplay()) {
          this.processShowCluster(this.project.riders);
          this.updateMapView();
        }
        this.updateTreeViewOnLeftPanel();
        PopupUtil.dismissLoadingPopup();
      }
    }
  }

  /**
   * 경로 설정에서 예약 시간 준수 여부 flag 업데이트
   */
  processReservationTimeAllDestinations() {
    if (this.isRoutingDone()) {
      for (const rider of this.project.riders) {
        this.setArrivedOnTimeFlagOnAllDestinations(rider.destinations);
      }
    } else {//경로 생성이 되어 있지 않으면 rider.destinations에 정보가 없다
      this.setArrivedOnTimeFlagOnAllDestinations(this.project.destinations);
    }
  }

  /**
   * 예약 시간을 맞춤 여부를 검사하여 저장한다
   */
  setArrivedOnTimeFlagOnAllDestinations(destinations) {
    destinations.forEach(d => {
      if (this.hasReservationTimeDestination(d)) {
        d.isArrivedOnTime = PlaceUtil.isArrivedOnReservationTime(d);
      }
    });
  }


  initDestinationUiVariables(destination, isNewDest = false, hasPickupPlace = false, index = -1) {

    if (isNewDest == true)
      destination.isSelected = true;

    if (destination.isPickupPlace && destination.type == Constant.DestinationType.HUB) {
      destination.label = "P";		// TODO: CHECK_PROD 적당한 이름이 필요함
    } else {
      if (index != -1) {
        destination.label = (hasPickupPlace && (!destination.isPickupPlace && destination.type == Constant.DestinationType.INTERMEDIATE)) ? index : index + 1;
      }
    }
  }


  deleteMapObject(data) {


    if (!data || !data.tagId || !data.info) {
      PopupUtil.alertPopup('데이터가 없어서 이벤트 처리가 안됩니다.');
      return;
    }

    console.log('삭제 : ' + JSON.stringify(data));
    const type = data.info.type;
    let riders = this.project.riders;
    // switch (type) {
    //     case Constant.DestinationType.HUB:
    //         riders = RiderUtil.getRiders(this.project.riders, PlaceUtil.isFirstMile(true));
    //         break;
    //     case Constant.DestinationType.INTERMEDIATE:
    //         riders = this.project.riders;
    //         break;
    //     case Constant.DestinationType.FINAL:
    //         riders = RiderUtil.getRiders(this.project.riders, PlaceUtil.isFirstMile(false));
    //         break;
    //     case Constant.RiderType.TRUCK:
    //     case Constant.RiderType.BIKE:
    //     case Constant.RiderType.PEDE:
    //     case Constant.RiderType.EV:
    //         riders = this.project.riders;
    //         break;
    // }

    // 그룹핑 영역 제거
    this.mapViewController.cleanCluster(riders);

    // 오브젝트 삭제
    if (type == Constant.DestinationType.HUB || type == Constant.DestinationType.INTERMEDIATE || type == Constant.DestinationType.FINAL) {
      this.removeDestination(data.info.deliveryId);
    } else {
      this.removeRider(data.info.riderId);
    }

    // 재그룹핑
    // this.performClusterAll();

    // 지도 화면 갱신
    this.updateMapView();
  }


  async updateNewRoutePath(data) {
    let _this = this;

    // 기사앱에서 재경로탐색결과를 받와서 화면을 그려야 한다.
    const rider = this.project.riders.find(rider => rider.riderId == data.riderId);
    if (rider) {
      await DELIVERY_API.getDeliveryRoutePath(rider.riderId, this.project.id, {
        onSuccess: async (response) => {
          let resDestinations = response.data;
          if (!resDestinations || resDestinations.length === 0) {
            return;
          }

          resDestinations = resDestinations.sort(function (a, b) {
            return a.order > b.order ? 1 : a.order < b.order ? -1 : 0;
          });

          // 기준 시간 설정
          let baseTime = new Date(0, 0, 0, 9, 0);
          if (rider.workingStartTime) {
            let timeArr = rider.workingStartTime.split(":");
            baseTime = new Date(0, 0, 0, parseInt(timeArr[0]), parseInt(timeArr[1]));
          }

          // 첫번째 배송 순서가 1 보다 큰 경우 - 기준 시간 증가
          if (resDestinations[0].order > 1) {
            for (let i = 1; i < resDestinations[0].order; i++) {
              let foundDestination = PlaceUtil.findDestinationByOrder(rider.destinations, i);
              if (foundDestination) {
                baseTime.setSeconds(baseTime.getSeconds() + foundDestination.estimatedSeconds + foundDestination.estimatedServiceDuration);
              }
            }
          }

          console.log('[action] updateNewRoutePath : 기사아이디(' + rider.riderId + '), 배송 경로 수(' + resDestinations.length + ')');


          for (let index = 0; index < resDestinations.length; index++) {
            let resDest = resDestinations[index];
            let foundDestination = PlaceUtil.findDestinationByDeliveryId(rider.destinations, resDest.deliveryId);
            if (foundDestination) {
              foundDestination.order = resDest.order;

              foundDestination.estimatedMeters = resDest.estimatedMeters;
              foundDestination.estimatedSeconds = resDest.estimatedSeconds;

              foundDestination.estimatedMetersReal = resDest.estimatedMetersReal;
              foundDestination.estimatedSecondsReal = resDest.estimatedSecondsReal;

              //배송 상태
              //foundDestination.deliveryStatus = resDest.deliveryStatus;

              //배송 예정시간 추가함.
              foundDestination.estimatedStartTime = resDest.estimatedStartTime;
              foundDestination.estimatedArrivalTime = resDest.estimatedArrivalTime;
              foundDestination.estimatedEndTime = resDest.estimatedEndTime;
              foundDestination.estimatedServiceDuration = resDest.estimatedServiceDuration;

              // 계획 시간 설정
              if (isNaN(baseTime.getHours()) && isNaN(baseTime.getMinutes())) {
                foundDestination.estimatedStartTime = Util.pad(baseTime.getHours(), 2) + ":" + Util.pad(baseTime.getMinutes(), 2);

                if (foundDestination.estimatedSeconds) {
                  baseTime.setSeconds(baseTime.getSeconds() + foundDestination.estimatedSeconds);
                  foundDestination.estimatedArrivalTime = Util.pad(baseTime.getHours(), 2) + ":" + Util.pad(baseTime.getMinutes(), 2);
                }

                if (foundDestination.estimatedServiceDuration) {
                  baseTime.setSeconds(baseTime.getSeconds() + foundDestination.estimatedServiceDuration);
                  foundDestination.estimatedEndTime = Util.pad(baseTime.getHours(), 2) + ":" + Util.pad(baseTime.getMinutes(), 2);
                }
              }

              foundDestination.routePath = resDest.routePath;
              foundDestination.routePathReal = resDest.routePathReal;
              if (Constant.SUPPORT_REAL_MULTI_ROUTE_PATH) {
                foundDestination.multiRoutePathReal = resDest.multiRoutePathReal;
              }

              foundDestination.etaDateTimeReal = resDest.etaDateTimeReal;

              if (Util.isPassingDestination(foundDestination)) {
                rider.drivingDest = foundDestination;
              }

              if (Util.isPassedDestination(foundDestination) && foundDestination.routePathReal && foundDestination.routePathReal.length > 0) {
                //foundDestination.passedRoutePathReal = Util.getBufferedRoutePath(foundDestination.routePathReal);
                //#2534 turf.buffer()가 오래걸리는 이슈가 있어 web worker로 처리한다.
                this.fields.geoBufferWorker.postMessage({
                  riderId: rider.riderId,
                  deliveryId: foundDestination.deliveryId,
                  routePathReal: foundDestination.routePathReal
                });
              } else {
                foundDestination.passedRoutePathReal = null;
              }

              // 진행중인 배송 경로인 경우 이어붙이기를 한다.
              // if (Util.isPassingDestination(foundDestination)) {
              //     let goingDestination = RiderUtil.getGoingDestination(rider);
              //     let tmpRoutePath = Util.copyRoutePath(foundDestination.routePath);
              //     let firstPoint = resDest.routePath[0];
              //     let closestIndex = Util.getClosetIndexOnRoutePath(tmpRoutePath, firstPoint.x, firstPoint.y);
              //     if (goingDestination && goingDestination.deliveryId === foundDestination.deliveryId && closestIndex > 0) {
              //         tmpRoutePath.splice(closestIndex + 1, tmpRoutePath.length - closestIndex);
              //         resDest.routePath.forEach(point => tmpRoutePath.push({x: point.x, y: point.y}));
              //         foundDestination.routePath = tmpRoutePath;
              //     } else {
              //         foundDestination.routePath = resDest.routePath;
              //     }
              // }

              // 핀 추가
              _this.initDestinationUiVariables(foundDestination, false, rider.hasPickupPlace, index);
              // _this.addDestinationPin(foundDestination);

              if (!Util.isPassedDestination(foundDestination)) {
                if (foundDestination.type === Constant.DestinationType.FINAL && !foundDestination.label) {//lastmile은 항상 숫자가 표시됨.
                  foundDestination.seqLabel = this.project.lastDestinationTotalCount;
                }
              }
            }
          }

          // 배송순서에 따라 정렬
          rider.destinations = PlaceUtil.generateSortedDestinationsByOrder(rider.destinations);

          //rider totalDeliveryCount 변경이 필요함
          rider.totalDeliveryCount = rider.destinations.length;

          _this.checkOverlappingDestinations(rider.destinations);

          if (rider.isSelected) {
            for (const dest of rider.destinations) {
              //rider의 selected를 넘겨 줘서 display 의 flag 로 사용함.
              _this.addDestinationPin(dest, null);
            }
          }

          // // LeftPanel 변경이 필요함.
          // _this.project.destinations.forEach((dest) => dest.isSelected = true);
          //
          // _this.project.riders.forEach((rider) => {
          //     rider.destinations.forEach((dest) => {
          //         dest.isSelected = true;
          //     })
          // });
          //
          // // _this.updateTreeViewOnLeftPanel();

          // 경로 그리기
          // await _this.mapViewController.drawRiderRoutePathInRealTracking(rider);
          if (_this.getRouteLineDisplay()) {
            await _this.drawRiderRouteLine(rider);
          }
          _this.updateMapView();

          //불필요한 코드로 인식됨.
          // //Left TreeView auto update
          // this.updateTreeViewOnLeftPanel();
          //
          // // destinationListPanel 실시간 상태알림 변경을 위해 업데이트
          // //this.refreshDestinationsList();
          //
          // //rider에 대한 정보를 넘겨줘서 추가하는 방식으로 변경함
          // this.refreshSectionDestinationsList(rider, rider.destinations);
        },
        onError: (error) => {
          PopupUtil.alertPopup("배송 경로 조회 API 요청 실패!");
        }
      });
    }
  }

  /**
   * 배송 목록 업데이트
   */
  updateDeliveries(data) {
    let _this = this;
    DELIVERY_API.getDeliveries(data.projectId, data.riderId, {
      onSuccess: (response) => {
        //console.log(JSON.stringify(response));
        let deliveries = response.data.content;

        if (!deliveries || deliveries.length === 0) {
          return;
        }

        deliveries = deliveries.sort(function (a, b) {
          return a.orderNum > b.orderNum ? 1 : a.orderNum < b.orderNum ? -1 : 0;
        });

        // 기사 찾기
        let rider = RiderUtil.findRiderById(this.project.riders, data.riderId);
        if (rider) {

          // 기준 시간 설정
          let baseTime = new Date(0, 0, 0, 9, 0);
          if (rider.workingStartTime) {
            let timeArr = rider.workingStartTime.split(":");
            baseTime = new Date(0, 0, 0, parseInt(timeArr[0]), parseInt(timeArr[1]));
          }

          // 첫번째 배송 순서가 1 보다 큰 경우 - 기준 시간 증가
          if (deliveries[0].orderNum > 1) {
            for (let i = 1; i < deliveries[0].orderNum; i++) {
              let foundDestination = PlaceUtil.findDestinationByOrder(rider.destinations, i);
              if (foundDestination) {
                baseTime.setSeconds(baseTime.getSeconds() + foundDestination.estimatedSeconds + foundDestination.estimatedServiceDuration);
              }
            }
          }

          for (let index = 0; index < deliveries.length; index++) {
            let delivery = deliveries[index];
            let foundDestination = PlaceUtil.findDestinationById(rider.destinations, delivery.deliveryId);
            if (foundDestination) {
              foundDestination.order = delivery.orderNum;

              foundDestination.estimatedMeters = delivery.predictionDistance;
              foundDestination.estimatedSeconds = delivery.predictionTime;

              foundDestination.estimatedMetersReal = delivery.estimatedMetersReal;
              foundDestination.estimatedSecondsReal = delivery.estimatedSecondsReal;

              // 계획 시간 설정
              if (isNaN(baseTime.getHours()) && isNaN(baseTime.getMinutes())) {
                foundDestination.estimatedStartTime = Util.pad(baseTime.getHours(), 2) + ":" + Util.pad(baseTime.getMinutes(), 2);

                if (foundDestination.estimatedSeconds) {
                  baseTime.setSeconds(baseTime.getSeconds() + foundDestination.estimatedSeconds);
                  foundDestination.estimatedArrivalTime = Util.pad(baseTime.getHours(), 2) + ":" + Util.pad(baseTime.getMinutes(), 2);
                }

                if (foundDestination.estimatedServiceDuration) {
                  baseTime.setSeconds(baseTime.getSeconds() + foundDestination.estimatedServiceDuration);
                  foundDestination.estimatedEndTime = Util.pad(baseTime.getHours(), 2) + ":" + Util.pad(baseTime.getMinutes(), 2);
                }
              }

              foundDestination.routePath = delivery.routePath;
              foundDestination.routePathReal = delivery.routePathReal;
              if (Constant.SUPPORT_REAL_MULTI_ROUTE_PATH) {
                foundDestination.multiRoutePathReal = delivery.multiRoutePathReal;
              }

              foundDestination.etaDateTimeReal = delivery.etaDateTimeReal;

              if (Util.isPassingDestination(foundDestination)) {
                rider.drivingDest = foundDestination;
              }

              if (Util.isPassedDestination(foundDestination) && foundDestination.routePathReal && foundDestination.routePathReal.length > 0) {
                //foundDestination.passedRoutePathReal = Util.getBufferedRoutePath(foundDestination.routePathReal);
                //#2534 turf.buffer()가 오래걸리는 이슈가 있어 web worker로 처리한다.
                this.fields.geoBufferWorker.postMessage({
                  riderId: rider.riderId,
                  deliveryId: foundDestination.deliveryId,
                  routePathReal: foundDestination.routePathReal
                });
              } else {
                foundDestination.passedRoutePathReal = null;
              }

              foundDestination.isArrivedOnTime = PlaceUtil.isArrivedOnReservationTime(foundDestination);//예약시간을 지키는지 검사

              foundDestination.inspectionStatus = delivery.inspectionStatus;
              foundDestination.pickupVerificationStatus = delivery.pickupVerificationStatus;

              if(delivery.realStartTime && !_.includes(Constant.DELIVERY_STATUS_READY, delivery.deliveryStatus))
                foundDestination.realStartTime = delivery.realStartTime;

              if(delivery.realEndTime)
                foundDestination.realEndTime = delivery.realEndTime;

              if(delivery.realArrivalTime)
                foundDestination.realArrivalTime = delivery.realArrivalTime;

              if(delivery.pickUpTime)
                foundDestination.pickUpTime = delivery.pickUpTime;

              if(delivery.dropOffTime)
                foundDestination.dropOffTime = delivery.dropOffTime;

              _this.initDestinationUiVariables(foundDestination, false, rider.hasPickupPlace, index);
              // _this.mapViewController.removeDestinationPin(foundDestination.deliveryId);//삭제하고 지워야 한다
              // _this.addDestinationPin(foundDestination);

              // 경로 다시 그리기 (because : 순서가 변경되면 drawingOrder 도 변경되어 TAG_ID가 달라짐)
              // _this.mapViewController.drawTrackingRoutePath(rider, foundDestination);
            }
          }

          // 배송 순서에 따라 정렬
          rider.destinations = PlaceUtil.generateSortedDestinationsByOrder(rider.destinations);

          _this.checkOverlappingDestinations(rider.destinations);

          if (rider.isSelected) {
            for (const dest of rider.destinations) {
              _this.addDestinationPin(dest);
              // _this.mapViewController.drawTrackingRoutePath(rider, dest);
            }
          }

          if (_this.getRouteLineDisplay()) {
            _this.drawRiderRouteLine(rider);
          }

          // 맵 화면 업데이트
          _this.updateMapView();

          //전체적으로 refresh 하는 방식임.
          //this.refreshDestinationsList();

          //rider에 대한 정보를 넘겨줘서 추가하는 방식으로 변경함
          this.refreshSectionDestinationsList(rider, rider.destinations);
        }
      },
      onError: (error) => {
        console.error("배송 목록 조회 실패");
      }
    });
  }


  riderDropExcel(data, userInfo) {
    let _this = this;
    PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

    const API_URL = Url.WEB.RIDERS_EXCEL_ADD;

    let params = {'projectId': data.projectId};
    let headers = {'Content-Type': 'multipart/form-data'};

    let body = new FormData();
    body.append('excelFile', data.file);
    let callback = {
      onSuccess: (response) => {
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showNotificationPopup("기사가 추가되었습니다");

        //기사 관리 페이지 UI 업데이트
        const riderList = window.parent.app.$root.popup.riderList;
        if (riderList && riderList.isShow) {
          const riderListPopup = window.parent.app.$refs.riderListPopup;
          if (riderListPopup) {
            riderListPopup.$emit(EVENT.RIDER_LIST_POPUP.UPDATE);
          }
        }

      },
      onError: (error) => {
        PopupUtil.dismissLoadingPopup();
        if (error.response.data.errorMessage) {
          PopupUtil.alertPopup(error.response.data.errorHeader, error.response.data.errorMessage, () => {
          });
        } else {
          PopupUtil.alertPopup(error.response.data);
        }

        //기사 관리 페이지 UI 업데이트
        const riderList = window.parent.app.$root.popup.riderList;
        if (riderList && riderList.isShow) {
          const riderListPopup = window.parent.app.$refs.riderListPopup;
          if (riderListPopup) {
            riderListPopup.$emit(EVENT.RIDER_LIST_POPUP.UPDATE);
          }
        }
      },
    };
    API.post(API_URL, params, body, headers, callback);
  }

  dropExcel(data, userInfo) {
    let _this = this;
    PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

    const isEmptyProject = data.riderCount === 0 && data.destinationCount === 0;
    const isTempProject = Util.isTempProjectName(data.name);
    const isNewProject = isEmptyProject && isTempProject;

    const API_URL = Url.WEB.PROJECT_EXCEL;

    let params = isNewProject ? {'userId': userInfo.id, 'selectedTab': data.tabMode}
      : {'userId': userInfo.id, 'projectId': data.projectId, 'selectedTab': data.tabMode};

    let headers = {'Content-Type': 'multipart/form-data'};

    let body = new FormData();
    body.append('excelFile', data.file);
    let callback = {
      onSuccess: (response) => {
        PopupUtil.dismissLoadingPopup();
        if (isNewProject) {
          _this.makeDropFileProject(response.data, null, {moveMap: true});
//                    _this.loadProject(response.data.id, null, { moveMap: true } );
        } else {
          const cmd = {type: Constant.RESERVED_COMMAND.DROP_EXCEL};
          _this.performRefreshActionAfterModified(cmd);
        }
      },
      onError: (error) => {
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showErrorPopup(error);
        //프로젝트 생성시 중간에 이슈가 발생하더라고 지금 까지 저장되어 있는 정보를 프로젝트에 생성 시킴.
        _this.loadProject(data.projectId, null, {moveMap: true});
      },
    };

    //this.subscribeMQTTProject(data.projectId, userInfo.id);
    API.post(API_URL, params, body, headers, callback);
  }

  dropCsv(data, userInfo) {
    let _this = this;
    PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

    const isEmptyProject = data.riderCount === 0 && data.destinationCount === 0;
    const isTempProject = Util.isTempProjectName(data.name);
    const isNewProject = isEmptyProject && isTempProject;

    const API_URL = Url.WEB.PROJECT_CSV;

    let params = isNewProject ? {'userId': userInfo.id, 'selectedTab': data.tabMode}
      : {'userId': userInfo.id, 'projectId': data.projectId, 'selectedTab': data.tabMode};

    let headers = {'Content-Type': 'multipart/form-data'};

    // const userInfo = this.$store.getters.getLoginUserInfo;

    let body = new FormData();
    for (let file of data.files) {
      body.append('csvFiles', file);
    }
    let callback = {
      onSuccess: (response) => {
        PopupUtil.dismissLoadingPopup();
        _this.makeDropFileProject(response.data, null, {moveMap: true});
        // _this.loadProject( response.data.id , null , { moveMap: true } );
      },
      onError: (error) => {
        PopupUtil.dismissLoadingPopup();
        if (error.response.data.errorMessage) {
          PopupUtil.alertPopup(error.response.data.errorHeader, error.response.data.errorMessage, () => {
          });
        } else {
          PopupUtil.alertPopup(error.response.data);
        }
      },
    };

    //this.subscribeMQTTProject(data.projectId, userInfo.id);
    API.post(API_URL, params, body, headers, callback);
  }


  updateRiderStatus(rider, destination = null, isAutoCreated = false) {
    let riderStatus;

    let prevRiderStatus = rider.status;
    // prod_service에서는 mapmode를 사용하지 않는다고 해서, demo_service의 updateRiderStatus()와 다르다.
    const COMPLETED_DESTINATIONS = rider.destinations.filter(destination => Util.isPassedDestination(destination));

    // 즉시배송은 기사의 업무종료시만 기사상태를 운행종료로 설정한다.
    if ((COMPLETED_DESTINATIONS.length === rider.destinations.length && isAutoCreated === false) || rider.isWorkCompleted) {
      riderStatus = Constant.RIDER_STATUS.COMPLETED;
    } else {
      const SERVICING_DESTINATIONS = rider.destinations.filter(destination => Util.isServicingDestination(destination));
      const GOING_DESTINATIONS = rider.destinations.filter(destination => Util.isGoingDestination(destination));

      if (SERVICING_DESTINATIONS.length > 0) {
        if (destination && destination.visitType === Constant.VISIT_TYPE.RETRIEVAL) {
          riderStatus = Constant.RIDER_STATUS.RETRIEVING;
        } else {
          riderStatus = Constant.RIDER_STATUS.SERVICING;
        }
      } else if (GOING_DESTINATIONS.length > 0) {
        riderStatus = Constant.RIDER_STATUS.GOING;
      } else {
        riderStatus = Constant.RIDER_STATUS.DISPATCHED;
      }
    }

    if (riderStatus !== prevRiderStatus) {//prev 상태 변경시 업데이트
      console.log("[updateRiderStatus] 기사명: " + rider.name + ", 기사상태: " + riderStatus + ", 이전 기사상태: " + prevRiderStatus);
      RiderUtil.setRiderStatus(rider, riderStatus);
      window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.SET_CHANGE_RIDER_STATUS, rider);
    }

  }

  async handlePollingWorker(event) {
    console.log("handlePollingWorker is demo function. Do nothing");
  }

  resetProjectValue() {
    this.project.id = 0;
    this.project.name = "Project";
    this.project.riders = [];
    this.project.destinations = [];
    this.project.hubTotalCount = 0;
    this.project.branchTotalCount = 0;
    this.project.lastDestinationTotalCount = 0;
    this.project.riderTotalCount = 0;
    this.project.lastWaypointId = -1;
    this.project.attribute = {
      "isRouteEnabled": false,
      "isSendingRiderEnabled": false,
      "isFirstRoutingDone": false,
      "isReadOnly": false,
    };
    this.project.cutoffTime = null;
    window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.SET_PROJECT_NAME, this.project.name);
  }


  //모든 기사의 clustering과 route정보를 없앤다. 데이터는 초기화하지 않는다
  cleanRidersClusterAndRoutePath(riders) {
    this.mapViewController.cleanCluster(riders, false);
    this.clearAllRouteLines(riders, false);
  }

  cleanDestinationPin(destination) {
    const rider = RiderUtil.findRiderById(this.project.riders, destination.riderId);
    if (rider) {
      this.cleanRidersClusterAndRoutePath([rider]);
    }

    this.mapViewController.removeDestinationPin(destination.deliveryId);
  }

  resetProject(reload = true, isDeleteServerData = true, moveMap = false) {
    let _this = this;
    const ALL = null;

    this.unsubscribeMQTTProject();

    this.stopTracking();
    this.closeAllPopup();
    this.fields.breadCrums.isShow = false;
    if (window.parent.app.$refs.leftPanel.$refs.addressSearch !== undefined) {
      window.parent.app.$refs.leftPanel.$refs.addressSearch.clearAddressList();
    }

    window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.INIT_LEFT_PANEL_TAB);
    window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.HIDE_DEST_LIST);

    window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.INIT_NO_READ_COUNT);

    if (this.project.riders) {
      let riders = this.project.riders;
      this.mapViewController.cleanCluster(riders);
      this.removeRider(ALL);
    }

    if (this.project.destinations) {
      this.removeDestinationAll();
    }

    this.updateMapView();
    this.resetProjectValue();//위 remove()에서 모두 초기화 되어 불필요하지만 명시적으로 호출함.
    this.tempRiderCount = 0;
    this.tempDestCount = 0;

    this.fields.localeTags.clear();
    this.fields.currentDeliveryType = null;

    if (moveMap) {
      this.setCenterFocus(MapUtil.getCountryCenterPosition(this.fields.selectedCountry));
    }
  }


  async updateDeliveriesStatus(data) {
    //handleDeliveryStatusWorker() 를 비동기로 호출
    this.fields.deliveryStatusWorker.postMessage({
      command: 1,
      riderId: data.riderId,
      deliveryIdList: data.deliveryIdList,
      reqUrl: Url.WEB.DELIVERIES + "?type=page&riderId={riderId}&deliveryIds={deliveryIdList}"
    });

    // top tool bar noti 갯수 세팅
    window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.GET_NOTI_COUNT, this.project.id);
  }

  /**
   * Context menu 표시
   */
  showContextMenu(event, contextMenu) {
    this.contextMenu = contextMenu;
    this.contextMenuEvent = event;
    this.multipleSelectContextMenuList = [];

    this.fields.contextMenuType = Constant.CONTEXT_MENU_ITEM.ITEM_NOTHING;
    let selectedItems = this.getDataFromMultiPositions(event.clientX, event.clientY);

    let selectItem = null
    if (selectedItems.length === 1) {

      if (selectedItems[0].type === Constant.TP.RIDER) {
        const rider = selectedItems[0].info;
        this.fields.contextMenuType = Constant.CONTEXT_MENU_ITEM.ITEM_SHOW_RIDER_DETAIL;
        if (rider.showRangeProjection) {
          this.fields.contextMenuType |= Constant.CONTEXT_MENU_ITEM.ITEM_HIDE_RANGE_PROJECTION;
        } else {
          this.fields.contextMenuType |= Constant.CONTEXT_MENU_ITEM.ITEM_SHOW_RANGE_PROJECTION;
        }

      } else if (selectedItems[0].type === Constant.TP.DESTINATION) {
        this.fields.contextMenuType = Constant.CONTEXT_MENU_ITEM.ITEM_SHOW_DEST_DETAIL;
      }
      selectItem = selectedItems[0];

    } else if (selectedItems.length > 1) {
      this.fields.contextMenuType = Constant.CONTEXT_MENU_ITEM.ITEM_MULTIPLE_SELECT;
      this.multipleSelectContextMenuList = selectedItems;
      selectItem = selectedItems;
    }

    this.setDisplayContextMenu(event, selectItem);

    this.cancelAllMouseDraggingAction();
  }

  /**
   * pin info 표시
   */
  showDeliveryInfo(selectedItem, toolTip) {

    if (this.toolTip)
      this.toolTip.close();

    this.toolTip = toolTip;

    if (selectedItem.type !== Constant.TP.RIDER) {
      let htmlColor = this.getToolTipHtml(selectedItem.info);
      console.log("htmlColor :::: " + htmlColor);
      this.toolTip.content = htmlColor;
      this.toolTip.open(event.clientX, event.clientY);
    }
  }

  getToolTipHtml(dest) {
    return `<div style="color : ${DeliveryUtil.getDestinationFontColor(dest)}; text-align: left; margin-left: 2px";>배송 상태 : ${DeliveryUtil.getDestinationStatusText(dest)}</div>
                <div style="color : ${DeliveryUtil.getDestinationFontColor(dest)}; text-align: left; margin-left: 2px";>${DeliveryUtil.getDestinationTimeText(dest)} ${TimeUtil.changedTimeHourMinString(dest.realEndTime ? dest.realEndTime : dest.estimatedStartTime)}</div>
                <div style="color : ${DeliveryUtil.getDestinationFontColor(dest)}";>${dest.baseAddr}&nbsp;${dest.detailAddr}</div>`;
  }

  setDisplayContextMenu(event, selectItem) {
    const isMultiPleSelectMode = this.fields.contextMenuType === Constant.CONTEXT_MENU_ITEM.ITEM_MULTIPLE_SELECT;
    this.contextMenu.showMenu(event, selectItem, false, isMultiPleSelectMode);
    this.fields.isShowContextMenu = true;
  }


  getObjectId(o) {
    let id = undefined;
    if (o.type === Constant.TP.DESTINATION) {
      id = "dest_" + o.info.deliveryId;
    } else if (o.type === Constant.TP.RIDER) {
      id = "rider_" + o.info.riderId;
    }
    return id;
  }

  getObjectNameOnContextMenu(o) {
    const userInfoData = this.storageGetters().getLoginUserInfo;
    let name = undefined;
    if (o.type === Constant.TP.DESTINATION) {
      if (this.isWelStoryUserCompany(userInfoData))
        name = PlaceUtil.getDestinationDisplayReceiverName(o.info, 20);
      else
        name = PlaceUtil.getDestinationDisplayId(o.info, 20);
    } else if (o.type === Constant.TP.RIDER) {
      name = o.info.name;
    }
    return name;
  }


  getCommonContextMenuItem() {
    let menuItems = [];
    const _this = this;
    let contextMenuType = this.fields.contextMenuType;

    if (contextMenuType & Constant.CONTEXT_MENU_ITEM.ITEM_MULTIPLE_SELECT) {
      for (const o of this.multipleSelectContextMenuList) {
        const name = this.getObjectNameOnContextMenu(o);
        if (name) {
          menuItems.push({
            name: name, handler: function (data) {
              _this.onMultiMenuItemSelected(data)
            }
          });
        }
      }
    }

    if (contextMenuType & Constant.CONTEXT_MENU_ITEM.ITEM_SHOW_DEST_DETAIL) {
      contextMenuType |= Constant.CONTEXT_MENU_ITEM.ITEM_DELETE_OBJECT_DEST;
    }

    if (contextMenuType & Constant.CONTEXT_MENU_ITEM.ITEM_SHOW_RIDER_DETAIL) {
      contextMenuType |= Constant.CONTEXT_MENU_ITEM.ITEM_DELETE_OBJECT_RIDER;
    }

    if (contextMenuType & Constant.CONTEXT_MENU_ITEM.ITEM_SHOW_RIDER_DETAIL) {
      menuItems.push({
        name: _t('기사 정보 보기'), handler: function (data) {
          _this.onShowRiderDetailInfo(data)
        }
      });
    }

    if (contextMenuType & Constant.CONTEXT_MENU_ITEM.ITEM_SHOW_DEST_DETAIL) {
      menuItems.push({
        name: _t('목적지 정보 보기'), handler: function (data) {
          _this.onShowDestDetailInfo(data)
        }
      });
    }

    if (contextMenuType & Constant.CONTEXT_MENU_ITEM.ITEM_DELETE_OBJECT_DEST) {
      menuItems.push({
        name: _t('방문지 삭제'), handler: function (data) {
          _this.onDeleteMapObject(data)
        }
      });
    }

    if (contextMenuType & Constant.CONTEXT_MENU_ITEM.ITEM_DELETE_OBJECT_RIDER) {
      menuItems.push({
        name: _t('기사업무 취소'), handler: function (data) {
          _this.onDeleteMapObject(data)
        }
      });
    }

    return menuItems;
  }

  getContextMenuItem() {
    let menuItems = this.getCommonContextMenuItem();

    if (menuItems.length > 0) {
      menuItems.push({
        name: _t('닫기'), handler: function (data) {
        }
      });
    }

    return menuItems;
  }


  onDeleteMapObject(data) {

    if (!this.isEditableProject()) {
      PopupUtil.alertPopup(Util.getSeparateEngString("편집이 허용되지 않았습니다", "프로젝트에 권한이 없습니다."));
      return;
    }

    if (data.type == Constant.TP.DESTINATION) {
      PopupUtil.confirmPopup(
        Util.getSeparateEngString("배송지 ( " + PlaceUtil.getDestinationDisplayId(data.info) + " ) 계획된 배송/픽업 업무를 취소하고 선택한 방문지를 삭제합니다."
          , "선택한 기사의 경로와 배차를 취소하고 현재 프로젝트에서 제외합니다.")
        , {
          onConfirm: () => {
            this.setRemoveDestinationsCommand([data.info.deliveryId]);
          },
        });
    } else if (data.type === Constant.TP.RIDER) {
      PopupUtil.confirmPopup("선택한 기사의 경로와 배차를 취소하고 현재 프로젝트에서 제외합니다.", {
        onConfirm: () => {
          this.setRemoveRidersCommand([data.info.riderId]);
        },
      });
    }
  }

  onMultiMenuItemSelected(data) {
    this.multipleSelectContextMenuList = [];

    const type = data.type;
    if (type === Constant.TP.RIDER) {
      this.fields.contextMenuType = Constant.CONTEXT_MENU_ITEM.ITEM_SHOW_RIDER_DETAIL;
    } else if (type === Constant.TP.DESTINATION) {
      this.fields.contextMenuType = Constant.CONTEXT_MENU_ITEM.ITEM_SHOW_DEST_DETAIL;
    } else {
      console.error("Never get here!!!");
      return;
    }

    this.setDisplayContextMenu(this.contextMenuEvent, data);
  }


  onShowRiderDetailInfo(data) {
    if (data) {
      this.closeAllPopup(this.fields);
      let rider = data.info;
      rider.projectId = this.project.id;
      this.showRiderDetailPopup({rider: rider, viewMode: 'r'});
    }
  }

  onShowDestDetailInfo(data) {
    if (data) {
      this.closeAllPopup();
      let dest = {};
      dest.delivery = data.info;
      dest.delivery.projectId = this.project.id;

      if (dest.delivery.riderId && !dest.rider) {
        dest.rider = RiderUtil.findRiderById(this.project.riders, dest.delivery.riderId);
      }

      this.showDeliveryDetailPopup({dest: dest, viewMode: 'r'});
    }
  }


  removeDestinationAll() {
    let _this = this;
    let destinations = [];

    // 기존 목적지 목록 초기화
    this.project.hubTotalCount = 0;
    this.project.branchTotalCount = 0;
    this.project.lastDestinationTotalCount = 0;
    this.project.destinations.forEach(destination => {
      _this.mapViewController.removeDestinationPin(destination.deliveryId ? destination.deliveryId : destination.tempDeliveryId);
    });
    this.project.destinations = [];

    this.project.riders.forEach(rider => {
      rider.destinations = [];
    });
  }


  removeRider(id) {
    let _this = this;
    let riders = [];
    if (id != null) {
      const foundRider = RiderUtil.findRiderById(this.project.riders, id);
      if (!foundRider) {
        PopupUtil.alertPopup(_t("Rider를 찾지 못하여 삭제하지 못합니다") + " : " + id);
        return;
      }

      // 삭제될 목적지를 제외한 목록 조회
      riders = this.project.riders.filter(function (rider) {
        return rider.riderId != id;
      });
    }

    // 기존 Rider 목록 초기화
    this.project.riderTotalCount = 0;
    this.project.riders.forEach(rider => {
      _this.mapViewController.removeRiderPin(rider.riderId);

      if (!_this.workStartLocationNewConcept) {
        _this.mapViewController.removeWorkLocationPin(rider.riderId, Constant.WORKLOCATION_PIN_MODE.START);
        _this.mapViewController.removeWorkLocationPin(rider.riderId, Constant.WORKLOCATION_PIN_MODE.END);
      }
    });
    this.project.riders = [];

    // Rider 재설정
    let count = 1;
    riders.forEach(rider => {
      rider.riderId = count++;
      _this.addRiderByClick(rider.x, rider.y, MapUtil.getPinModeByType(rider.type), rider.isRealTracking, rider.uuid);
    });
    this.updateMapView();
  }

  setCenterPosition(location) {
    if (location) {
      this.fields.centerPos = {'x': location.x, 'y': location.y};
    } else {
      this.fields.centerPos = this.mapViewController.screen2world(window.innerWidth / 2, window.innerHeight / 2);
    }

    this.fields.isShowContextMenu = false; //지도가 움직일때 지워 준다
  }

  setCenterFocus(location) {
    const bStepMove = this.mapSpeedUpMode ? false : true;
    this.mapViewController.setCenterFocus(location.x, location.y, -1, bStepMove);
    this.setCenterPosition(location)
  }


  setCenterFocusByObject(obj) {
    if (obj && obj.x && obj.y) {
      this.setCenterFocus({x: obj.x, y: obj.y});

      if (obj.deliveryId) {
        this.activateDestination(obj);
      } else if (obj.riderId) {
        this.activateRider(obj);
      }
    }
  }


  closeAllPopup() {
    this.fields.popUp = 0;
    this.fields.isShowContextMenu = false;
    if (app && app.$refs) {
      const aloaMap = app.$refs.aloaMap;
      aloaMap.$refs.addressSearchPopup.hideAddressSearchPopup();
    }
  }

  isShowPopup(popUp) {
    return (this.fields.popUp == popUp);
  }


  async displayRiderTimestamp(_this, event) {
    // const _this = this;
    // console.log("onMouseMove Screen Coordinate: " + event.clientX + ", " + event.clientY);

    if (!_this.fields.processingFlagRiderTimestamp) {

      _this.fields.processingFlagRiderTimestamp = true;

      try {
        let needUpdateScreen = false;

        if (_this.fields.savedShowRiderTimestamp) {
          // await _this.mapViewController.setRiderTimestampVisible(false);
          await _this.mapViewController.clearRiderTimestamp();
          _this.fields.savedShowRiderTimestamp = false;
          needUpdateScreen = true;
        }

        let riderId = null;
        const projectId = _this.project.id;
        const location = _this.mapViewController.screen2world(event.clientX, event.clientY);
        // console.log("onMouseMove World Coordinate: " + location.x + ", " + location.y);

        if (_this.fields.zoomLevel <= 5 && _this.project.status && (_this.project.status === Constant.PROJECT_STATUS.IN_PROGRESS || _this.project.status === Constant.PROJECT_STATUS.DONE)) {
          const point = turf.point([location.x, location.y]);

          for (let rider of _this.project.riders) {
            if (rider.isSelected) {
              for (let destination of rider.destinations) {
                if (Util.isPassedDestination(destination) || Util.isPassingDestination(destination)) {
                  if (destination.passedRoutePathReal && turf.booleanContains(destination.passedRoutePathReal, point)) {
                    riderId = rider.riderId;
                    break;
                  }
                }
              }

              if (riderId) {
                break;
              }
            }
          }
        }

        if (riderId) {
          RIDER_API.getRiderTimestamp(riderId, projectId, location.x, location.y, {
            onSuccess: (response) => {
              let timestamp = response.data.timestamp;
              console.log(`지나간 시간: ${timestamp}`);

              _this.mapViewController.drawRiderTimestamp(location, timestamp.substr(11, 5), true);
              _this.callUpdateMapViewSync();
              _this.fields.savedShowRiderTimestamp = true;
              _this.fields.processingFlagRiderTimestamp = false;
            },
            onError: (error) => {
              console.error(`지나간 시간 조회 실패 - 기사아이디(${riderId}), 프로젝트 아이디(${projectId}), x(${location.x}), y(${location.y})`);
              _this.fields.processingFlagRiderTimestamp = false;
            }
          });
        } else {
          if (needUpdateScreen) {
            _this.callUpdateMapViewSync();
          }
          _this.fields.processingFlagRiderTimestamp = false;
        }
      } catch (e) {
        _this.fields.processingFlagRiderTimestamp = false;
        console.error("displayRiderTimestamp() Exception: " + e);
      }
    }
  }


  contextMenuClose() {
    this.multipleSelectContextMenuList = [];
  }

  contextMenuOpened(event) {
    this.fields.isMouseDown = false;
  }

  settingChanged() {
    console.log("not used function settingChanged()");
  }

  // getGasStationInfoBoxStyle(station) {
  //     console.log("not used function getGasStationInfoBoxStyle()");
  // }
  //
  // getEnergyGaugeInfoBoxStyle(rider) {
  //     console.log("not used function getEnergyGaugeInfoBoxStyle()");
  // }


  activateDestination(destination) {
    // const rider = PlaceUtil.findRiderByDestinationId( this.project.riders, destination.deliveryId );
    // if( rider )
    //     this.activateRider( rider );
    // this.rearrangeDuplicatedDestinationPin(destination);//선택된 핀을 맨앞으로 나오게 한다
  }

  activateRider(rider) {//아직 사용은 안하지만 나중에 용도를 생각해보자
    this.fields.activatedRiderId = rider.riderId;
  }


  /**
   * 하나의 목적지를 지운다 . 지울때는 splice를 사용한다.
   */
  removeDestination(deleteId) {

    const _this = this;
    this.project.riders.forEach(rider => {
      const findIndex = rider.destinations.findIndex(d => d.deliveryId == deleteId);
      if (findIndex >= 0) {
        // _this.clearRiderAllRoutePath(rider);
        _this.cleanRidersClusterAndRoutePath([rider]);
        rider.destinations.splice(findIndex, 1);
        //rider 배송 갯수를 update 한다.
        rider.totalDeliveryCount = rider.destinations.length;
      }
    });

    const findIndex = this.project.destinations.findIndex(d => d.deliveryId == deleteId);
    if (findIndex >= 0) {
      this.mapViewController.removeDestinationPin(deleteId);
      this.project.destinations.splice(findIndex, 1);
    }

    this.reCalculateDestCount();
  }


  /**
   * 다수의 아이디의 목적지를 지운다
   */
  removeDestinations(deleteIds) {
    const _this = this;

    for (const id of deleteIds) {
      this.removeDestination(id);
    }

    this.updateMapView();
  }

  /**
   * hubTotalCount,branchTotalCount,lastDestinationTotalCount 를 재 계산 한다
   */
  reCalculateDestCount() {

    this.project.hubTotalCount = 0;
    this.project.branchTotalCount = 0;
    this.project.lastDestinationTotalCount = 0;
    const _this = this;
    this.project.destinations.forEach(destination => {
      switch (destination.type) {
        case Constant.DestinationType.HUB:
          _this.project.hubTotalCount++;
          _this.project.lastDestinationTotalCount++;
          break;
        case Constant.DestinationType.INTERMEDIATE:
          _this.project.branchTotalCount++;
          _this.project.lastDestinationTotalCount++;
          break;
        case Constant.DestinationType.FINAL:
          _this.project.lastDestinationTotalCount++;
          break;
        default:
          console.error("Wrong type " + destination.type);
          break;
      }
    });

  }

  /**
   *  프런트에서 수행한 명령을 저장한다. 나중에 백앤드로 동기화 된다
   */
  makeModifyCommand(type, data) {
    let riderIds = [];
    if (type === Constant.RESERVED_COMMAND.REMOVE_DESTINATION) {// 삭제될 id에 해당하는 기사 리스트를 만든다
      for (const dest of data) {// 삭제할 목적지의 리스트가 온다
        //백엔드에서 수행하기 때문에 필요 없음
        // TMS_WEB_API.updateNotiByDeleteDestination(dest.deliveryId, {
        //     onSuccess: (response) => {
        //         console.log("[updateNotiByDeleteDestination] Success --- deliveryId: " + dest.deliveryId);
        //     },
        //     onError: (error) => {
        //         console.error("[updateNotiByDeleteDestination] Error --- deliveryId: " + dest.deliveryId);
        //     }
        // });

        const findRiderId = PlaceUtil.findRiderIdByDestinationId(this.project.riders, dest.deliveryId);
        if (findRiderId) {
          riderIds.push(findRiderId);
        }
      }
    } else if (type === Constant.RESERVED_COMMAND.MODIFY_DESTINATION_DEEP_CHANGE || type === Constant.RESERVED_COMMAND.MODIFY_DESTINATION) {
      const findRiderId = PlaceUtil.findRiderIdByDestinationId(this.project.riders, data.origin.deliveryId);
      if (findRiderId) {
        riderIds.push(findRiderId);
      }
    } else if (type === Constant.RESERVED_COMMAND.ADD_DESTINATION) {
      // Clustering 동작을 를 백엔드로 이전.
      if (!this.isGeoCodingStatus()) {
        //     const clusterRider = this.getSimpleClusterRider( data.new );
        //     if( clusterRider ) {
        //         const riderId = clusterRider.riderId;
        //         data.new.riderId = riderId;//생성을 하면서 배차가 이루어 진다 WebDestinationDTO saveDelivery(Long userId, WebDeliveryDTO dto)
        //         riderIds.push( riderId );
        //     }
        console.log("추가된 배송지에 대해 자동 클러스터링및 경로 탐색이 수행될  예정입니다 ");
      }
    } else if (type === Constant.RESERVED_COMMAND.CHANGE_CLUSTERING_DESTINATION) {//배송지 배차 기사 변경
      if (data.fromRiders) {
        for (const fromRider of data.fromRiders) {
          if (fromRider && fromRider.riderId) {
            riderIds.push(fromRider.riderId);
          }
        }
      }

      //미배차배송지로 이동시 조건 추가함.
      if (data.toRider) {
        const toRiderData = data.toRider;
        console.log(toRiderData);
        console.log(toRiderData.riderId);
        riderIds.push(toRiderData.riderId);
      }
    }

    this.project.isModifyState = true;

    return {type: type, data: data, riderIds: riderIds, done: false};

  }


  findAndReplaceDeliveryId(destinations, oldDeliveryId, newDeliveryId) {
    destinations.forEach(d => {
      if (d.deliveryId == oldDeliveryId) d.deliveryId = newDeliveryId;
    })
  }


  /**
   *  모든 목적지가 클러스터링이 되어 있지 않은지 검사
   */
  isCheckAllDestinationNotClustered() {

    let allDestinationClusterNotCompleted = true;
    for (const rider of this.project.riders) {
      for (const dest of rider.destinations) {
        if (dest.groupName && dest.groupName !== Constant.NO_GROUP_KEYWORD_NAME) {
          allDestinationClusterNotCompleted = false;
          break;
        }
      }
    }

    return allDestinationClusterNotCompleted;
  }


  /**
   * @deprecated 간단하게 배차하는 로직 . 나중에 Backend에서 하도록 하자
   */
  getSimpleClusterRider(newDest) {
    let clusterRider = null;

    if (newDest.groupRiderInfo && newDest.groupRiderInfo.riderId) {
      clusterRider = RiderUtil.findRiderById(this.project.riders, newDest.groupRiderInfo.riderId);
    } else if (newDest.groupName && newDest.groupName !== Constant.NO_GROUP_KEYWORD_NAME) {//그룹이름이 있으면
      clusterRider = RiderUtil.findRiderByGroupAllocation(this.project.riders, newDest.groupName);
    }

    if (!clusterRider) {
      let clusterRule = this.storageGetters().getClusterRuleOnDemand;

      if (!this.isDispatchDone()) {// 계획 단계에서 ETA,DISTANCE 무의미 함.
        clusterRule = Constant.ON_DEMAND_CLUSTER_RULE.DELIVERY_COUNT;
      }

      console.log("[clustering] getSimpleClusterRider :  clusterRule : " + clusterRule);
      if (clusterRule === Constant.ON_DEMAND_CLUSTER_RULE.ETA) {//ETA 우선 - 간단하게 할당된 배송지의 직선 거리가 가장 가까운 곳에 할당한다
        let minDistance = Number.MAX_SAFE_INTEGER;
        for (const rider of this.project.riders) {
          if (rider.destinations.length > 0) {
            // const lastDest = rider.destinations[rider.destinations.length - 1];
            // if( lastDest ){
            //     const timeStamp = moment( lastDest.estimatedArrivalTime , Constant.TIME_FORMAT.TIME_SECONDS ).unix();
            //     if( timeStamp < minTimeStamp ){
            //         minTimeStamp = timeStamp;
            //         clusterRider = rider;
            //     }
            // }

            for (const dest of rider.destinations) {//남은 배송지
              if (dest.deliveryStatus === Constant.DELIVERY_STATUS.READY || dest.deliveryStatus === Constant.DELIVERY_STATUS.WAITING) {
                let getDistance = Util.getDistanceTwoPoint(dest, newDest);
                if (getDistance < minDistance) {
                  minDistance = getDistance;
                  clusterRider = rider;
                }
              }
            }
          }
        }

      } else if (clusterRule === Constant.ON_DEMAND_CLUSTER_RULE.DISTANCE) {//기사 근접거리 우선.
        let minDistance = Number.MAX_SAFE_INTEGER;
        for (const rider of this.project.riders) {//간단하게 직선 거리

          //기사의 직선 거리
          let getDistance = Util.getDistanceTwoPoint(rider, newDest);
          if (getDistance < minDistance) {
            minDistance = getDistance;
            clusterRider = rider;
          }

        }

      } else if (clusterRule === Constant.ON_DEMAND_CLUSTER_RULE.DELIVERY_COUNT) {//최소 방문지 우선
        let minClusterCount = Number.MAX_SAFE_INTEGER;
        for (const rider of this.project.riders) {
          let count = 0;
          for (const dest of rider.destinations) {//남은 배송지
            if (dest.deliveryStatus === Constant.DELIVERY_STATUS.READY || dest.deliveryStatus === Constant.DELIVERY_STATUS.WAITING) {
              count++;
            }
          }

          if (count < minClusterCount) {
            minClusterCount = rider.destinations.length;
            clusterRider = rider;
          }
        }

      }
    }

    if (clusterRider == null) {
      console.log("[clustering] Error clusterRider is NULL set First One ");
      clusterRider = this.project.riders[0];
    }

    return clusterRider;
  }


  setModifyRiderCommand(rider) {
    const _this = this;

    TMS_WEB_API.registerRiderVehicle(rider, false, {
      onSuccess: (response) => {
        const cmd = {type: Constant.RESERVED_COMMAND.MODIFY_RIDER};
        _this.performRefreshActionAfterModified(cmd);
        PopupUtil.showNotificationPopup("기사 정보가 수정되었습니다.");
      },
      onError: (error) => {
        console.error('기사 정보 수정에 실패하였습니다');
        PopupUtil.showErrorPopup(error);
      }
    });
  }

  /**
   * 기사 추가 명령
   */
  setAddRidersCommand(data) {

    let rider = data.rider;
    var isRiderAdd = data.isRiderAdd;

    if (this.project.deleted) {
      PopupUtil.alertPopup("임시 프로젝트에서 기사를 추가할수 없습니다");
      return;
    }

    const _this = this;
    TMS_WEB_API.registerRiderVehicle(rider, isRiderAdd, {
      onSuccess: (response) => {
        const cmd = {type: Constant.RESERVED_COMMAND.ADD_RIDER_TO_PROJECT};
        _this.performRefreshActionAfterModified(cmd);
        if (data.isEdit)
          PopupUtil.showNotificationPopup("기사 정보가 변경되었습니다.");
        else
          PopupUtil.showNotificationPopup("기사가 추가되었습니다.");
      },
      onError: (error) => {
        console.error('기사 추가에 실패 하였습니다');
        PopupUtil.showErrorPopup(error);
      }
    });
  }

  /**
   * 가배차 기사 추가 명령
   */
  setSimulationAddRiders(data) {

    console.log(data);
    console.log(this.project)

    if (this.project.deleted) {
      PopupUtil.alertPopup("임시 프로젝트에서 기사를 추가할수 없습니다");
      return;
    }

    PopupUtil.showLoadingPopup("추가중...", "잠시만 기다려 주세요.");

    const _this = this;
    TMS_WEB_API.addSimulationRidersAndProjectAdd(this.project.id, data, {
      onSuccess: (response) => {
        const cmd = {type: Constant.RESERVED_COMMAND.ADD_RIDER_TO_PROJECT};
        _this.performRefreshActionAfterModified(cmd);
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showNotificationPopup("가배차 기사들이 추가되었습니다");
      },
      onError: (error) => {
        PopupUtil.dismissLoadingPopup();
        console.error('기사 추가에 실패 하였습니다');
        PopupUtil.showErrorPopup(error);
      }
    })
  }

  setSimulationRiderChangedRiderProject(data) {

    console.log(data);
    console.log(this.project)

    if (this.project.deleted) {
      PopupUtil.alertPopup("임시 프로젝트에서 기사를 추가할수 없습니다");
      return;
    }

    const _this = this;
    TMS_WEB_API.setSimulationRiderChangedRiderProject(this.project.id, data.simulationRiderId, data.riderId, {
      onSuccess: (response) => {
        _this.refreshProject(this.project.id);
        PopupUtil.showNotificationPopup("배송 이관이 완료 되었습니다.");
      },
      onError: (error) => {
        console.error("배송 이관이 실패되었습니다.");
        PopupUtil.showErrorPopup(error);
      }
    })
  }

  setSimulationRidersToDispatchRidersProjectExcel(data, userInfo) {
    let _this = this;
    PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

    const API_URL = Url.WEB.PROJECT_SIMULATION_RIDER_TO_DISPATCH_RIDER_CHANGED_EXCEL;
    let params = {
      'projectId': data.projectId
    }
    let headers = {'Content-Type': 'multipart/form-data'};

    let body = new FormData();
    body.append('excelFile', data.file);
    let callback = {
      onSuccess: (response) => {
        PopupUtil.dismissLoadingPopup();
// 가배차 일 경우 다시 프로젝트 로딩하게 함.
//                    const cmd = {type: Constant.RESERVED_COMMAND.DROP_EXCEL};
//                    _this.performRefreshActionAfterModified(cmd);
        _this.loadProject(data.projectId, null, {moveMap: true});
      },
      onError: (error) => {
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showErrorPopup(error);
        //프로젝트 생성시 중간에 이슈가 발생하더라고 지금 까지 저장되어 있는 정보를 프로젝트에 생성 시킴.
        _this.loadProject(data.projectId, null, {moveMap: true});
      },
    };

    //this.subscribeMQTTProject(data.projectId, userInfo.id);
    API.post(API_URL, params, body, headers, callback);
  }

  removeRiderPinByRiderIds(ids) {
    const riders = RiderUtil.getRidersByRiderIds(this.project.riders, ids);
    this.cleanRidersClusterAndRoutePath(riders)

    ids.forEach(id => {
        this.mapViewController.removeRiderPin(id);
      }
    );
  }

  /**
   * 기사 삭제 명령
   */
  async setRemoveRidersCommand(ids) {
    //관제중일때는 삭제가 불가능하게 함.
    if (this.isDispatchDone()) {
      PopupUtil.alertPopup("관제중에는 기사를 삭제할수 없습니다");
      return;
    }

    // 삭제될 목적지를 제외한 목록 조회
    // const riders = this.project.riders.filter(function (r) {
    //     return ( ids.indexOf( r.riderId ) < 0 ) ;
    // });
    //
    // this.project.riders = riders;

    const projectId = this.project.id;
    const _this = this;

    PopupUtil.showLoadingPopup("처리 중...", "잠시만 기다려 주세요.");
    TMS_WEB_API.deleteRiderBelongToProject(ids, projectId, {
      onSuccess: (response) => {
        PopupUtil.dismissLoadingPopup();
        _this.removeRiderPinByRiderIds(ids);
        const cmd = {type: Constant.RESERVED_COMMAND.REMOVE_RIDER_FROM_PROJECT};
        _this.performRefreshActionAfterModified(cmd);
        PopupUtil.showNotificationPopup("기사를 삭제 하였습니다.");
      },
      onError: (error) => {
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showNotificationPopup("기사 삭제에 실패햐였습니다");
      }
    });
  }


  showDeliveryDetailPopup(data) {
    this.closeAllPopup();

    let dest = data.dest;
    dest.projectId = dest.projectId ? dest.projectId : this.project.id;
    dest.groupNameList = dest.groupNameList ? dest.groupNameList : this.updateGroupNameList();
    dest.riderInfoList = dest.riderInfoList ? dest.riderInfoList : this.getRiderInfoList();
    //배송 정보만 있을 경우 조건 추가함.
    if (dest.rider)
      dest.rider.groupName = dest.rider.groupName ? dest.rider.groupName : dest.delivery.groupName;
    dest.projectStatus = dest.projectStatus ? dest.projectStatus : this.project.status;
    dest.projectAttribute = dest.projectAttribute ? dest.projectAttribute : this.project.attribute;
    dest.projectUserId = dest.projectUserId ? dest.projectUserId : this.project.userId;
    // riderInfoList가 없으면 빈 배열을 기본값으로 설정
    dest.riderInfoList = (dest.riderInfoList ?? []).map(item => ({
      ...item,
      groupName: item.groupName || dest.rider?.groupName
    }));
    window.parent.app.$refs.leftPanel.$emit('show-delivery-detail-popup', dest, data.viewMode);
    console.log(" [popup] Delivery detail popup Opened , deliveryId  : " + dest.delivery.deliveryId + " , customerOrderId : " + dest.delivery.customerOrderId + ", Rider id : " + dest.delivery.riderId + " ( project Id : " + this.project.id + ")");

    if (dest.projectId === this.project.id) {
      this.setCenterFocusByObject(dest.delivery);
    }

  }

  async showRiderDetailPopup(data) {
    this.closeAllPopup();

    let rider;
    let findRiderInProject = true;
    if (data.rider) {
      rider = data.rider;
      //이경우에는 현재 프로젝트를 세팅한다
      rider.projectId = this.project.id;
      rider.projectStatus = this.project.status;
      rider.projectAttribute = this.project.attribute;
      rider.projectUserId = this.project.userId;
      rider.projectName = this.project.name;
    } else if (data.riderInfo) { //riderInfo로 넘어온 경우( 기사 관리 페이지 )
      //현재 프로젝트에 기사 Id가 있으면 현재 projectId를 넘긴다. - 우선 보류
      // findRiderInProject = this.project.riders.filter(r => r.riderId === data.riderId ).length > 0 ;
      const riderInfo = data.riderInfo;
      let response = await RIDER_API.getRider(riderInfo.riderId);
      if (response) {
        rider = response.data;
        rider.projectId = riderInfo.riderProjectId;
        rider.projectStatus = riderInfo.status;
        rider.projectAttribute = riderInfo.attribute;
        rider.projectUserId = riderInfo.userId;
      }
    }

    if (!rider) {
      PopupUtil.alertPopup(_t("기사 정보 로딩에 실패하였습니다."));
      return;
    }

    rider.groupNameList = this.updateGroupNameList();
    rider.riderInfoList = this.getRiderInfoList();


    if (!rider.groupName) {
      //현재 프로젝트에 포함된 기사가 아닐 경우에는 최신 사용된 그룹명을 세팅해준다
      if (!RiderUtil.findRiderById(this.project.riders, rider.riderId)) {
        const response = await RIDER_API.getGroupName(rider.riderId);
        const groupName = response?.data;

        if (groupName && !rider.groupName) {
          rider.groupName = groupName;
        }
        if (groupName && this.groupNameList.indexOf(groupName) < 0) {
          rider.groupNameList.push(groupName);
        }
      } else {//현재 프로젝트에 속해 있는 기사이면 riderInfoList에 그룹 정보를 세팅해준다
        const findRiderInfo = (rider.riderInfoList ?? []).find(r => r?.riderId === rider?.riderId);
        if (findRiderInfo?.groupName) {
          rider.groupName = findRiderInfo.groupName;
        }
      }
    }

    window.parent.app.$refs.leftPanel.$emit('show-rider-detail-popup', rider, data.viewMode);
    console.log("[popup] Rider detail popup Opened :  riderId: " + rider.riderId + ", status: " + rider.status + ", projectId: " + this.project.id);
    this.setCenterFocusByObject(rider);
  }

  /**
   *   목적지 삭제 명령
   */
  async setRemoveDestinationsCommand(ids) {

    //id리스트 중에 삭제 불가능한 목적지가 1개 이상 있으면 삭제가 불가능 하다.
    let deletedDestList = [];
    for (const id of ids) {
      const dest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, id);
      if (!PlaceUtil.isDestinationEditEnabled(dest)) {//만약 편집(삭제) 불가능이면 오류 메시지 보이고 리턴한다
        PopupUtil.showModifyDestinationFailedPopup(dest);
        return;
      }
      deletedDestList.push(MapUtil.copyObject(dest));
    }
    // this.removeDestinations(ids);

    const cmd = this.makeModifyCommand(Constant.RESERVED_COMMAND.REMOVE_DESTINATION, deletedDestList);
    this.modifyCommandList.push(cmd);

    let deliveryList = [];
    for (const d of cmd.data) {
      deliveryList.push(d.deliveryId);
    }

    const _this = this;
    const projectId = this.project.id;
    TMS_WEB_API.deleteDelivery(deliveryList, projectId, {
      onSuccess: (response) => {
        cmd.done = true;
        console.log("[action]Success to delete destination from DB");
        PopupUtil.showNotificationPopup("배송지를 삭제하였습니다");
        _this.removeDestinations(ids);
        _this.performRefreshActionAfterModified(cmd);

      },
      onError: (error) => {
        console.log("[action]Failed to delete destination from DB");
        PopupUtil.alertPopup("배송지를 삭제하지 못하였습니다");
        this.refreshProject(this.project.id);
      }
    });

  }

  /**
   *   목적지 정보 변경 명령.
   */
  // async setModifyDestinationCommand(updateDest) {
  async setModifyDestinationCommand(data) {
    //popup_delivery_detail.js data.form부분 참조하여 업데이트 하는 부분을 넣어준다

    let isChanged = false;
//        let originDest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, updateDest.deliveryId);


    if (data.form.customerProductImageUrl) {// 문자열 형태를 배열 형태로 바꾼다  [http://helloworld.com ]
      data.form.customerProductImageUrl = PlaceUtil.convertArrayFromString(data.form.customerProductImageUrl)
    }

    let cmdType = Constant.RESERVED_COMMAND.MODIFY_DESTINATION;
    if (!PlaceUtil.compareDestinationFormData(data.org, data.form)) {
      isChanged = true;
    }

    if (Util.isNotEqualString(data.org.baseAddr, data.form.baseAddr)) {
      const response = await SEARCH_API.getAddressByString(this.fields.selectedCountry, data.form.baseAddr);
      if (response && response.data) {
        isChanged = true;
        cmdType = Constant.RESERVED_COMMAND.MODIFY_DESTINATION_DEEP_CHANGE;//주소 변경시에는 다시 clustering 으로 이동한다
      } else {
        PopupUtil.alertPopup("주소 형식이 올바르지 않습니다 다시 입력해주세요.");
        return;
      }
    }

    //예약 시간 또는 권역이 바뀌면 다시 배차 모드로 변경한다.
    if (Util.isNotEqualString(data.org.deliveryStartTime, data.form.deliveryStartTime)
      || Util.isNotEqualString(data.org.deliveryEndTime, data.form.deliveryEndTime)
      || Util.isNotEqualString(data.org.groupName ? data.org.groupName : Constant.NO_GROUP_KEYWORD_NAME, data.form.groupName ? data.form.groupName : Constant.NO_GROUP_KEYWORD_NAME)) {
      cmdType = Constant.RESERVED_COMMAND.MODIFY_DESTINATION_DEEP_CHANGE;
    }

    //배차 정보가 변경이 되었을때 배차를 바꾼다
    let isChangedClustering = false;
    let dragDestId, dropoffRiderId;
    if (data.form.groupRiderInfo && data.form.groupRiderInfo.riderId) {
      if (data.org.riderId !== data.form.groupRiderInfo.riderId) {
        // if (!this.isGeoCodingStatus()) {
        isChangedClustering = true;
        dragDestId = data.org.deliveryId;
        dropoffRiderId = data.form.groupRiderInfo.riderId;
        cmdType = Constant.RESERVED_COMMAND.MODIFY_DESTINATION_DEEP_CHANGE;
        // } else {
        //     PopupUtil.showNotificationPopup("배차 전에는 배차기사를 변경할수 없습니다");
        //     return;
        // }
      }
    }

    if (isChanged) {
      if (cmdType === Constant.RESERVED_COMMAND.MODIFY_DESTINATION_DEEP_CHANGE) {//주소 자체나 예약 시간이 변경또는 권역이 바뀌는 등  현재 진행중인 배송지는 편집이 가능하면 안된다
        if (!PlaceUtil.isDestinationEditEnabled(data.org)) {
          PopupUtil.alertPopup("현재 운행중인 배송지는 변경할 수 없습니다");
          return;
        }
      }

      const cmd = this.makeModifyCommand(cmdType, {new: data.form, origin: data.org});

      let newDest = cmd.data.new;
      newDest.projectId = this.project.id;
      const _this = this;
      PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");
      TMS_WEB_API.saveDelivery(newDest, {
          onSuccess: (response) => {
            if (_this.project.destinations.length > 0) {
              let originDest = PlaceUtil.findDestinationByDeliveryId(_this.project.destinations, data.org.deliveryId);
              MapUtil.mergeObjectNotNullData(originDest, response.data  /* 주소가 업데이트 안됨. updateDest*/);
              //MapUtil.mergeObjectNotNullData(data.org, response.data  /* 주소가 업데이트 안됨. updateDest*/ );

              _this.addDestinationPin(originDest);

            }
            if (!isChangedClustering) {//Clustering 변경시에는 아래 부분에서 performRefreshActionAfterModified 가 호출 되므로 굳이 호출해줄 필요가 없다
              _this.performRefreshActionAfterModified(cmd);
            }
            PopupUtil.dismissLoadingPopup();
            _this.location_list_refresh();

            if (isChangedClustering) {
              _this.changeClustering({dragDestId: dragDestId, dropoffRiderId: dropoffRiderId});
            } else {
              this.refreshDestinationsList();
              PopupUtil.showNotificationPopup("배송지 정보를 편집하였습니다");
            }
          }
          , onError: (error) => {
            PopupUtil.dismissLoadingPopup();
            PopupUtil.alertPopup("배송지 편집에 실패하였습니다");
            this.refreshProject(this.project.id);
          }

        }
      );
      this.modifyCommandList.push(cmd);
    } else {
      if (isChangedClustering) {
        if (!PlaceUtil.isDestinationChangeClusteringEnabled(data.org)) {
          PopupUtil.showModifyDestinationFailedPopup(data.org);
          return;
        }

        this.changeClustering({dragDestId: dragDestId, dropoffRiderId: dropoffRiderId, isolate: true});
      }
    }


  }

  location_list_refresh() {
    //방문지 관리 페이지 UI 업데이트
    const locationList = window.parent.app.$root.popup.locationList;
    if (locationList && locationList.isShow) {
      const locationListPopup = window.parent.app.$refs.locationListPopup;
      if (locationListPopup) {
        locationListPopup.$emit(EVENT.LOCATION_LIST_POPUP.UPDATE);
      }
    }
  }

  isOpenedProject(projectId) {
    return this.project && this.project.id === projectId ? true : false;
  }

  //배송 상태 변경
  modifyForceDeliveryStatus(data) {
    console.log("[modifyForceDeliveryStatus] status: " + data.deliveryStatus + ", deliveryType: " + data.deliveryType + ", deliveryMemo: " + data.deliveryMemo);

    const deliveryId = data.deliveryId;

    let body = {
      'deliveryStatus': data.deliveryStatus,
      'deliveryFailureType': null,
      'deliveryFailureMessage': null,
      'deliveryCompletedType': null,
      'deliveryCompletedMessage': null
    };

    let destination = null;
    let pinMode = null;
    const isModifyInProject = this.isOpenedProject(data.projectId);
    console.log("[modifyForceDeliveryStatus] isModifyInProject: " + isModifyInProject);
    if (isModifyInProject) {//현재 열린 프로젝트의 요청인지 확인
      destination = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, deliveryId);
      pinMode = this.getPinModeByDestination(destination);

      if (data.deliveryStatus === Constant.DELIVERY_STATUS.COMPLETED) {
        data.deliveryCompletedType = data.deliveryType;
        data.deliveryCompletedMessage = data.deliveryMemo;
        pinMode = Constant.DESTINATION_PIN_MODE.PAST;

        body.deliveryCompletedType = data.deliveryType;
        body.deliveryCompletedMessage = data.deliveryMemo;
      } else if (data.deliveryStatus === Constant.DELIVERY_STATUS.FAILURE) {
        data.deliveryFailureType = data.deliveryType;
        data.deliveryFailureMessage = data.deliveryMemo;
        pinMode = Constant.DESTINATION_PIN_MODE.FAILED;

        body.deliveryFailureType = data.deliveryType;
        body.deliveryFailureMessage = data.deliveryMemo;
      } else {
        data.deliveryCompletedType = null;
        data.deliveryCompletedMessage = null;
        data.deliveryFailureType = null;
        data.deliveryFailureMessage = null;
        if (data.deliveryStatus === Constant.DELIVERY_STATUS.UNDELIVERED) {
          pinMode = Constant.DESTINATION_PIN_MODE.FAILED;
        }
      }

      destination.deliveryStatus = data.deliveryStatus;
    } else {
      if (data.deliveryStatus === Constant.DELIVERY_STATUS.COMPLETED) {
        body.deliveryCompletedType = data.deliveryType;
        body.deliveryCompletedMessage = data.deliveryMemo;
      } else if (data.deliveryStatus === Constant.DELIVERY_STATUS.FAILURE) {
        body.deliveryFailureType = data.deliveryType;
        body.deliveryFailureMessage = data.deliveryMemo;
      } else {
        // nothing
      }
    }

    const _this = this;
    TMS_WEB_API.updateForceDeliveryStatusInfo(data.projectId, deliveryId, body, {
        onSuccess: (response) => {
          PopupUtil.showNotificationPopup("배송상태를 변경하였습니다");
          if (isModifyInProject) {
            _this.addDestinationPin(destination, pinMode);
            const cmd = {type: Constant.RESERVED_COMMAND.FORCE_DELIVERY_STATUS};
            _this.performRefreshActionAfterModified(cmd);
          } else {//배송지 리스트에서 수정되었을 경우 업데이트
            const deliveryListPopup = window.parent.app.$refs.deliveryListPopup;
            if (deliveryListPopup) {
              deliveryListPopup.$emit(EVENT.DELIVERY_LIST_POPUP.UPDATE_DATA);
            }
          }
        },
        onError: (error) => {
          PopupUtil.alertPopup("배송상태 변경에 실패하였습니다");
          PopupUtil.showErrorPopup(error);
        }
      }
    );
//        TMS_WEB_API.updateForceDeliveryStatusInfo(data.riderId, deliveryId, data, {
//                onSuccess: (response) => {
//                    PopupUtil.showNotificationPopup("배송상태를 변경하였습니다");
//                    _this.addDestinationPin(destination, pinMode);
//                    const cmd = {type: Constant.RESERVED_COMMAND.FORCE_DELIVERY_STATUS};
//                    _this.performRefreshActionAfterModified(cmd)
//                },
//                onError: (error) => {
//                    PopupUtil.alertPopup("배송상태 변경에 실패하였습니다");
//                    PopupUtil.showErrorPopup(error);
//                }
//            }
//        );
  }

  addDestinationToProject(destination) {
    destination.isSelected = true;
    this.project.destinations.push(destination);
    if (destination.riderId) {
      let rider = RiderUtil.findRiderById(this.project.riders, destination.riderId);
      rider.destinations.push(destination);
    }

    this.addDestinationPin(destination);

  }


  /**
   *   목적지 정보 명령
   */
  async setAddDestinationCommand(addDest) {
    addDest.isNewAdded = true;//새롭게 추가된 목적지라는 표시를 한다
    addDest.deliveryId = null;

    const cmd = this.makeModifyCommand(Constant.RESERVED_COMMAND.ADD_DESTINATION, {new: addDest, deliveryId: null});
    let newDest = cmd.data.new;
    newDest.projectId = this.project.id;
    const _this = this;
    PopupUtil.showLoadingPopup("로딩 중...", "최적의 배송지를 담당할 기사를 찾는 중입니다")
    TMS_WEB_API.saveDelivery(newDest, {
        onSuccess: (response) => {
          PopupUtil.dismissLoadingPopup();
          PopupUtil.showNotificationPopup("배송지가 추가 되었습니다");
          _this.addDestinationToProject(response.data);
          if (response.data.riderId) {
            cmd.riderIds.push(response.data.riderId);
          }
          _this.performRefreshActionAfterModified(cmd);
        }
        , onError: (error) => {
          PopupUtil.dismissLoadingPopup();
          PopupUtil.alertPopup("배송지 추가 실패하였습니다");
          _this.refreshProject(this.project.id);
        }
      }
    )

    this.modifyCommandList.push(cmd);
    // this.performRefreshActionAfterModified();
  }

  performRefreshProject(data) {
    let projectId = this.project.id;

    if (data) {
      projectId = data.projectId ? data.projectId : data;
    }

    this.refreshProject(projectId);
  }

  async redrawAllRoutePathByRiderId(riderId) {
    const rider = RiderUtil.findRiderById(this.project.riders, riderId);

    if (!this.isGeoCodingStatus()) {
      this.clearRiderAllRoutePath(rider);
      await this.updateNewRoutePath({riderId: riderId});

      if (this.isClusteringStatus() && rider.isSelected) {
        this.processShowCluster([rider]);
      }
    }
  }

  performGetDeliveryOrdersAndRoutesByRider(projectId, cmd) {

    const updateRiderIds = cmd.riderIds;
    console.log("[action] Call performGetDeliveryOrdersAndRoutesByRider :  rider ids: " + updateRiderIds);

    const _this = this;

    PopupUtil.showLoadingPopup("경로 탐색", "경로탐색을 수행합니다.");
    PROJECT_API.getDeliveryOrdersAndRoutesByRider(projectId, this.fields.routeOption, updateRiderIds, {
      onSuccess: async () => {
        for (const riderId of updateRiderIds) {
          console.log("[action] Call performGetDeliveryOrdersAndRoutesByRider :  update rider new Path : " + riderId);
          await _this.redrawAllRoutePathByRiderId(riderId);
        }
        _this.updateTreeViewOnLeftPanel();

        // _this.loadProjectData(projectId);
        _this.execPerformSendingIfNeed();
        PopupUtil.dismissLoadingPopup();
      },
      onError: (e) => {
        _this.refreshProject(projectId);
        PopupUtil.dismissLoadingPopup();
      }

    });
  }

  // 수정 발생시 바로 전송 모드
  execPerformSendingIfNeed() {
    if (this.isDispatchDone() && this.storageGetters().getIsSendImmediatelyOnDemand) {
      const silentMode = true;
      this.onPerformSending(silentMode);
    }
  }

  /**
   * 계획/온디멘드 단계에서 수정이 발생하였을떄
   */
  refreshProjectAfterExecuteCommand(cmd, callback) {
    const projectId = this.project.id;

    //(1)Schedule State
    const isGeoCodingStatus = this.isGeoCodingStatus();
    const isClusterMode = this.isClusteringStatus();
    const isRoutingDoneMode = this.isRoutingDoneStatus();
    //(2)on-demand State
    const isDispatchDone = this.isDispatchDone();
    //(3)project Done State
    const isProjectDone = this.isProjectDone();

    let refreshMode = Constant.REFRESH_MODE.LOAD_PROJECT;

    if (callback && callback.onSuccess) {
      refreshMode = Constant.REFRESH_MODE.CALLBACK;
    } else if (cmd.type === Constant.RESERVED_COMMAND.MODIFY_DESTINATION ||
      cmd.type === Constant.RESERVED_COMMAND.FORCE_DELIVERY_STATUS) {
      refreshMode = Constant.REFRESH_MODE.UPDATE_LEFT_PANEL;
    } else if (cmd.type === Constant.RESERVED_COMMAND.ADD_RIDER_TO_PROJECT
      || cmd.type === Constant.RESERVED_COMMAND.REMOVE_RIDER_FROM_PROJECT
      || cmd.type === Constant.RESERVED_COMMAND.SWITCH_RIDER_CLUSTERING
    ) {
      if (isClusterMode || isRoutingDoneMode) {
        refreshMode = Constant.REFRESH_MODE.MOVE_BACK_CLUSTERING;
      }
    } else if (cmd.type === Constant.RESERVED_COMMAND.DROP_EXCEL) {
      // if (isClusterMode || isRoutingDoneMode) {
      //     refreshMode = Constant.REFRESH_MODE.PERFORM_CLUSTER;
      // }
    } else if ( /*cmd.type === Constant.RESERVED_COMMAND.REMOVE_DESTINATION
            || */ cmd.type === Constant.RESERVED_COMMAND.MODIFY_DESTINATION_DEEP_CHANGE
      || cmd.type === Constant.RESERVED_COMMAND.CHANGE_CLUSTERING_DESTINATION) {
      if (isClusterMode) {
        refreshMode = Constant.REFRESH_MODE.MOVE_BACK_CLUSTERING;
      } else if (isRoutingDoneMode || isDispatchDone) {
        if (cmd.riderIds && cmd.riderIds.length > 0) {
          refreshMode = Constant.REFRESH_MODE.PERFORM_PARTIAL_ROUTING;
        }
      }
    } else if (cmd.type === Constant.RESERVED_COMMAND.REMOVE_DESTINATION || cmd.type === Constant.RESERVED_COMMAND.ADD_DESTINATION) {
      refreshMode = Constant.REFRESH_MODE.PERFORM_UPDATE_RIDER; //이미 추가/삭제 경로 탐색은 완료된 상태. 이때는 해당 기사만 업데이트를 한다
    }

    console.log("[action] refreshProjectAfterExecuteCommand -  cmd type : " + cmd.type + ", refreshMode : " + refreshMode);

    const updateRiderIds = this.getModifiedRiderIdList();

    if (refreshMode === Constant.REFRESH_MODE.CALLBACK) {
      callback.onSuccess();
    } else if (refreshMode === Constant.REFRESH_MODE.UPDATE_LEFT_PANEL) {
      this.updateTreeViewOnLeftPanel();
    } else if (refreshMode === Constant.REFRESH_MODE.MOVE_BACK_CLUSTERING) {
      this.moveBackToClusteringMode();
    } else if (refreshMode === Constant.REFRESH_MODE.PERFORM_CLUSTER) {
      const _this = this;
      this.performClusterAll({onError: (e) => _this.moveBackToClusteringMode()});
    } else if (refreshMode === Constant.REFRESH_MODE.PERFORM_PARTIAL_ROUTING) {
      if (cmd.riderIds && cmd.riderIds.length > 0) {
        this.performGetDeliveryOrdersAndRoutesByRider(projectId, cmd);
      }
    } else if (refreshMode === Constant.REFRESH_MODE.LOAD_PROJECT) {
      this.loadProjectData(projectId);
    } else if (refreshMode === Constant.REFRESH_MODE.PERFORM_UPDATE_RIDER) {
      if (cmd.riderIds && cmd.riderIds.length > 0) {
        for (const riderId of cmd.riderIds) {
          this.redrawAllRoutePathByRiderId(riderId);
        }
      } else {
        this.updateMapView();
      }
      this.updateTreeViewOnLeftPanel();
    }

    if (updateRiderIds.length > 0) {
      this.execPerformSendingIfNeed();
    }
  }

  /**
   *  수정사항이 발생한 이후에 다시 배차/경로탐색을 수행한다
   */
  performRefreshActionAfterModified(cmd, callback) {
    try {
      this.refreshProjectAfterExecuteCommand(cmd, callback);
    } catch (e) {
      console.log("[performRefreshActionAfterModified] " + e);
      this.refreshProject(this.project.id);
    }

    //기사 관리 페이지 UI 업데이트
    const riderList = window.parent.app.$root.popup.riderList;
    if (riderList && riderList.isShow) {
      const riderListPopup = window.parent.app.$refs.riderListPopup;
      if (riderListPopup) {
        riderListPopup.$emit(EVENT.RIDER_LIST_POPUP.UPDATE);
      }
    }
  }

  //현재 경로를 취소하고 다시 배차 모드로 이동한다
  moveBackToClusteringMode() {
    const _this = this;
    const projectId = this.project.id;
    if (this.isClusteringStatus() || this.isDispatchDone() || this.isProjectDone()) {
      _this.loadProjectData(projectId);
    } else {
      DELIVERY_API.clearDeliveriesRoutePath(projectId, {
        onSuccess: () => {
          _this.refreshProject(projectId);
        },
      });
    }

  }


  showPostalCodeLayer(onoff = false) {
    this.mapViewController.showPostalCodeLayer(onoff);
    this.callUpdateMapViewSync();
  }

  showSiGunGuLayer(onoff = false) {
    this.mapViewController.showSiGunGuLayer(onoff);
    this.callUpdateMapViewSync();
  }


  getModifiedRiderIdList() {
    let updatedRiderIds = [];
    console.log("[getModifiedRiderIdList] modifyCommandList: " + this.modifyCommandList);
    for (const cmd of this.modifyCommandList) {
      updatedRiderIds = updatedRiderIds.concat(cmd.riderIds); // 삭제된 목적지에 해당하는 기사 리스트를 넣어준다
    }
    updatedRiderIds = Array.from(new Set(updatedRiderIds));//중복 제거
    console.log("[getModifiedRiderIdList] updatedRiderIds: " + updatedRiderIds);
    return updatedRiderIds;
  }

  //MQTT Push를 보낸 기사에 한해서만 리스트를 뽑는다.
  filterMqttPushedRiderIds(riderIds) {
    let mqttPushedRiderIds = [];
    const riders = RiderUtil.getRidersByRiderIds(this.project.riders, riderIds);
    for (const rider of riders) {
      if (rider.projectPushedAt) {
        mqttPushedRiderIds.push(rider.riderId)
      } else {
        console.log("[filterMqttPushedRiderIds] 기사 " + rider.name + " (" + rider.riderId + ")MQTT 전송이 된 상태가 아니기 때문에 MQTT 전송에서 제외 합니다");
      }
    }
    return mqttPushedRiderIds;
  }

  getAllRiderIdList() {
    let allRiderIds = [];
    for (const rider of this.project.riders) {
      allRiderIds = allRiderIds.concat(rider.riderId);
    }
    console.log("[getAllRiderIdList] allRiderIds: " + allRiderIds);

    return allRiderIds;
  }

  getSelectedRiderIdList() {
    let selectedRiderIds = [];
    for (const rider of this.project.riders) {
      if (rider.isSelected)
        selectedRiderIds = selectedRiderIds.concat(rider.riderId);
    }
    console.log("[getSelectedRiderIdList] selectedRiderIds: " + selectedRiderIds);
    return selectedRiderIds;
  }

  getSelectedRiderList() {
    let selectedRiders = [];
    for (const rider of this.project.riders) {
      if (rider.isSelected) {
        selectedRiders.push(rider);
      }
    }
    return selectedRiders;
  }

  clearModifyCommandList() {
    let updatedRiderIds = this.getModifiedRiderIdList();
    this.modifyCommandList = [];
    this.project.isModifyState = false;
    console.log("[clearModifyCommandList] updatedRiderIds: " + updatedRiderIds);
    return updatedRiderIds;
  }


  setRidersColorIndex(riders) {
    for (let i = 0; i < riders.length; i++) {
      let rider = riders[i];
      if (!rider.colorIndex) {
        rider.colorIndex = i;//Util.getClusteringColor(i);
      }
    }
  }

  showClusterAll(riders, isShow) {
    if (riders) {
      if (isShow == false) {
        riders.forEach(rider => {
          this.mapViewController.showCluster(rider, isShow);
        })
      } else {
        riders.forEach(rider => {
          if (rider.isSelected)
            this.mapViewController.showCluster(rider, this.getRouteLineDisplay());
        })
      }
    }
    this.updateMapView();
  }

  processShowCluster(riders) {
    if (this.isClusteringStatus()) {
      this.showClusterAll(riders, false); //remove
      this.showClusterAll(riders, true);
    } else {
      this.showClusterAll(riders, false); //remove
    }
  }

  isGeoCodingStatus() {
    return !this.project.attribute.isClusterDone;
  }

  isClusteringStatus() {
    return MapUtil.isClusteringStatus(this.project);
  }

  //계획 단계에서 경로탐색단계인지 여부  , 프로젝트 시작 - Geocoding -> Clustering -> Routing -> 관제 시작
  isRoutingDoneStatus() {
    return MapUtil.isRoutingDoneStatus(this.project);
  }


  //경로탐색이 되었는지 여부
  isRoutingDone() {
    return (this.project.attribute.isClusterDone
      && this.project.attribute.isFirstRoutingDone
      && this.project.riders.length > 0);
  }

  isDispatchDone() {
    return (this.project.status === Constant.PROJECT_STATUS.IN_PROGRESS);
  }

  isProjectDone() {
    return (this.project.status === Constant.PROJECT_STATUS.DONE);
  }

  updateClusteringOption(clusterRuleOption) {
    this.saveLocalStorage('setClusterRuleOption', clusterRuleOption);
    this.syncProjectAttributeToProject({clusterRule: clusterRuleOption});
    if (this.isClusteringStatus()) {
      PopupUtil.confirmPopup("배차 옵션이 변경되었습니다. 다시 배차를 실행하시겠습니까?", {
        onConfirm: () => {
          this.performClusterAll();
        },
      });
    }
  }

  addRiderOnLeftPanel() {
    this.addRiderByClick(null, null, Constant.PINMODE.TRUCK, null);
  }

  removeRiderFromProject(data) {
    this.setRemoveRidersCommand([data.riderId]);
  }

  async addRiderToProject(data) {

    if (this.project.deleted) {
      PopupUtil.alertPopup("임시 프로젝트에서는 기사를 추가할수 없습니다");
      return;
    }

    let response = await RIDER_API.addRiderToProject(data.riderId, this.project.id);
    if (response) {
      const cmd = {type: Constant.RESERVED_COMMAND.ADD_RIDER_TO_PROJECT};
      this.performRefreshActionAfterModified(cmd);
      PopupUtil.alertPopup("기사를 프로젝트에 추가하였습니다.");
    } else {
      PopupUtil.alertPopup("기사 추가에 실패하였습니다. ");
    }
  }

  async addRidersToProject(data) {

    if (this.project.deleted) {
      PopupUtil.alertPopup(Util.getSeparateEngString("임시 프로젝트에서는 기사를 추가할수 없습니다.", "선택한 기사님 프로젝트에 추가 실패하였습니다."));
      return;
    }

    let response = await RIDER_API.addRidersForManagement(this.project.id, data);

    if (response) {
      const cmd = {type: Constant.RESERVED_COMMAND.ADD_RIDER_TO_PROJECT};
      this.performRefreshActionAfterModified(cmd);
      PopupUtil.alertPopup("기사를 현재 프로젝트에 추가합니다.");
    } else {
      PopupUtil.alertPopup("선택한 기사님 프로젝트에 추가 실패하였습니다.");
    }
  }


  showGridView(data) {
    let cloneRider = [];
    let cloneDests = [];

    for (const rider of this.project.riders) {
      // cloneRider.push( MapUtil.copyObject(rider));
      cloneRider.push(RiderUtil.convertVehicleDataFromVehicleForm(rider));//rider.vehicle로 데이터를 다루면 이상하게 꼬인다.
    }

    for (const dest of this.project.destinations) {
      cloneDests.push(MapUtil.copyObject(dest));
    }

    window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.OPEN_TOP_BAR_MENU_CLOSE);
    window.parent.app.$refs.leftPanel.$emit('show-gridview-edit-popup', cloneRider, cloneDests);
  }

  showProductPage(data) {
    let cloneRider = [];

    for (const rider of this.project.riders) {
      // cloneRider.push( MapUtil.copyObject(rider));
      cloneRider.push(RiderUtil.convertVehicleDataFromVehicleForm(rider));//rider.vehicle로 데이터를 다루면 이상하게 꼬인다.
    }
    //차량 정보 page close 함
    window.parent.app.$emit(EVENT.MAIN.CLOSE_MOCEAN_VEHICLE_POPUP);
    window.parent.app.$emit(EVENT.MAIN.CLOSE_MOCEAN_REALTIME_TEMPERATURE_POPUP);
    window.parent.app.$emit(EVENT.MAIN.CLOSE_MOCEAN_TODAY_DRVING_DAILY_POPUP);

    window.parent.app.$refs.topToolBar.$emit('show-product-page-popup', cloneRider);
  }

  showRouteLineDisplay(data) {
    console.log(data);

    this.isRouteLineDisplay = data;

    _.forEach(this.project.riders, rider => {
      if (data === false) {
        for (const destination of rider.destinations) {
          this.mapViewController.clearRiderRoutePath(rider, destination);
        }
        if (this.isClusteringStatus())
          this.mapViewController.showCluster(rider, this.getRouteLineDisplay());
      } else {
        this.riderSelectedItem(rider, rider.isSelected);
      }
    })
  }

  cancelDelivery(data) {
    console.log("[cancelDelivery] data: {}" + data);
    const _this = this;
    PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");
    TMS_WEB_API.cancelDelivery(data, {
      onSuccess: (response) => {
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showNotificationPopup("배달취소 요청 성공입니다.");
      },
      onError: (error) => {
        PopupUtil.dismissLoadingPopup();
        PopupUtil.alertPopup("배달취소 요청 실패입니다.");
        this.refreshProject(this.project.id);
      }
    });
  }

  showMoceanListView() {
    //상차 물품 page close 함
    window.parent.app.$emit(EVENT.MAIN.CLOSE_PRODUCT_PAGE_POPUP);
    window.parent.app.$refs.topToolBar.$emit('show-mocean_vehicle_popup');
  }

  showMoceanRealTimeTemperaturePopup(vinnumber) {
    window.parent.app.$refs.topToolBar.$emit('show-mocean_real_time_temperature', vinnumber);
  }

  showMoceanTodayDrivingDailyPopup(vinnumber) {
    window.parent.app.$refs.topToolBar.$emit('show-mocean_today_driving_daily', vinnumber);
  }

  callPerformRefreshActionAfterModified(totalUpdatedCnt, currentUpdatedCnt, errorMessage) {
    if (currentUpdatedCnt >= totalUpdatedCnt) {
      if (errorMessage.length > 0) {
        PopupUtil.alertPopup(errorMessage);
      }
      this.performRefreshActionAfterModified();
    }
  }


  changedGridViewData(data) {
    const updatedRiders = data.riders;
    const updatedDests = data.destinations;

    let errorMessage = "";

    const _this = this;

    let totalUpdatedCnt = (updatedRiders ? updatedRiders.length : 0) + (updatedDests ? updatedDests.length : 0);
    let currentUpdatedCnt = 0;

    //수정된 기사 업데이트
    for (let updateRider of updatedRiders) {
      const originRider = RiderUtil.findRiderById(this.project.riders, updateRider.riderId);
      const originRiderFormData = RiderUtil.convertVehicleDataFromVehicleForm(originRider);
      if (!RiderUtil.compareRiderFormData(updateRider, originRiderFormData)) {
        updateRider.projectId = this.project.id;
        console.log("[GridView] 기사 업데이트 :  " + JSON.stringify(updateRider));
        TMS_WEB_API.registerRiderVehicle(updateRider, false, {
          onSuccess: () => {
            _this.callPerformRefreshActionAfterModified(totalUpdatedCnt, ++currentUpdatedCnt, errorMessage);
          },
          onError: (e) => {
            errorMessage += "기사 변경에 실패 했습니다. ( " + Util.getErrorMessage(e) + ")";
            _this.callPerformRefreshActionAfterModified(totalUpdatedCnt, ++currentUpdatedCnt, errorMessage);
          }
        });
      } else {
        this.callPerformRefreshActionAfterModified(totalUpdatedCnt, ++currentUpdatedCnt, errorMessage);
      }
    }


    //수정된 방문지  업데이트
    for (let updateDest of updatedDests) {
      const originDest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, updateDest.deliveryId);
      updateDest.projectId = this.project.id;
      if (!PlaceUtil.compareDestinationFormData(originDest, updateDest)) {
        console.log("[GridView] 방문지 업데이트 :  " + JSON.stringify(updateDest));

        if (updateDest.customerProductImageUrl) {// 문자열 형태를 배열 형태로 바꾼다  [http://helloworld.com ]
          updateDest.customerProductImageUrl = PlaceUtil.convertArrayFromString(updateDest.customerProductImageUrl)
        }

        TMS_WEB_API.saveDelivery(updateDest, {
          onSuccess: () => {
            _this.callPerformRefreshActionAfterModified(totalUpdatedCnt, ++currentUpdatedCnt, errorMessage);
          },
          onError: (e) => {
            errorMessage += "기사 변경에 실패 했습니다. ( " + Util.getErrorMessage(e) + ")";
            _this.callPerformRefreshActionAfterModified(totalUpdatedCnt, ++currentUpdatedCnt, errorMessage);
          }
        });
      } else {
        this.callPerformRefreshActionAfterModified(totalUpdatedCnt, ++currentUpdatedCnt, errorMessage);
      }
    }

  }

  /**
   * 현재 프로젝트 편집이 가능한지 체크함
   */
  isEditableProject() {
    return (this.project.status !== Constant.PROJECT_STATUS.DONE)
      && (!this.project.attribute || !this.project.attribute.isReadOnly)
      && (Util.isThisProjectMine(this.project)
      );
  }

  //destinations를 조사해서 동일 좌표 중복된 핀일경우 조사.
  checkOverlappingDestinations(destinations) {
    let destMap = new Map;
    for (const dest of destinations) {
      const key = dest.x + "," + dest.y;
      let value = destMap.get(key);
      if (!value) {
        value = [];
        destMap.set(key, value);
      }
      value.push(dest);
    }

    destMap.forEach((value, key) => {
        if (value.length > 1) { //중복 핀일 경우
          //가장 작은 라벨을 찾아서 세팅한다
          let smallestLabel = null;
          value.forEach(d => {
            const label = d.label ? d.label : d.userDefinedOrderNum;
            if (label && (!smallestLabel || smallestLabel > label)) {
              smallestLabel = label;
            }
          });

          value.forEach(d => {
            d.overlapLabel = smallestLabel + "+";
          });
        } else {
          value[0].overlapLabel = null;
        }
      }
    );

  }


  getPinModeByDestination(destination) {

    let pinMode = this.project.status === Constant.PROJECT_STATUS.SCHEDULED ? Constant.DESTINATION_PIN_MODE.EDIT : Constant.DESTINATION_PIN_MODE.SET;
    if (Util.isPassedDestination(destination)) {
      pinMode = Constant.DESTINATION_PIN_MODE.PAST;
    } else if (destination.deliveryStatus === Constant.DELIVERY_STATUS.GOING || destination.deliveryStatus === Constant.DELIVERY_STATUS.SERVICING) {
      pinMode = Constant.DESTINATION_PIN_MODE.FUTURE;
    }

    //배송 실패시 핀 변경
    if (destination.deliveryStatus === Constant.DELIVERY_STATUS.FAILURE
      || destination.deliveryStatus === Constant.DELIVERY_STATUS.UNDELIVERED
      || destination.deliveryStatus === Constant.DELIVERY_STATUS.REJECTED
      /*|| ((this.isDispatchDone() || this.isProjectDone()) && (destination.inspectionStatus && destination.inspectionStatus === Constant.INSPECTION_STATUS.UNINSPECTED))*/
    ) {
      pinMode = Constant.DESTINATION_PIN_MODE.FAILED;
    }
    return pinMode;
  }


  getRiderColorIndexByDestination(destination) {
    let colorIndex = null;

    if (destination.riderId) {
      const rider = RiderUtil.findRiderById(this.project.riders, destination.riderId);
      if (rider) {
        colorIndex = rider.colorIndex;
      }
    }

    return colorIndex;
  }

  addDestinationPin(destination, mode) {

    if (mode == null) {
      mode = this.getPinModeByDestination(destination);
    }


    const isShowColorPin = (this.isClusteringStatus() || this.isRoutingDoneStatus() || mode === Constant.DESTINATION_PIN_MODE.SET || mode === Constant.DESTINATION_PIN_MODE.FUTURE) && destination.type === Constant.DestinationType.FINAL;
    destination.colorIndex = null;
    if (isShowColorPin) {
      destination.colorIndex = this.getRiderColorIndexByDestination(destination);
    }

    this.mapViewController.removeDestinationPin(destination.deliveryId);

    if (destination.onCheck) {
      this.disableOverlapSetImageMask();
      this.mapViewController.addDestinationPin(destination, mode);
      this.recoverOverlapSetImageMask();
    } else {
      this.mapViewController.addDestinationPin(destination, mode);
    }

    //예약시간 태그 그리기
    if (this.hasReservationTimeDestination(destination)) {
      // destination.isArrivedOnTime = PlaceUtil.isArrivedOnReservationTime(destination);
      this.mapViewController.addReservationTimeTag(destination, destination.isArrivedOnTime != null ? true : destination.isArrivedOnTime);
    }
  }

  //예약시간을 가지고있는 배송지인지 체크함
  hasReservationTimeDestination(destination) {
    return (Util.isNotNull(destination.deliveryStartTime) && Util.isNotNull(destination.deliveryEndTime));
  }


  //마우스 드래깅 모드 취소
  cancelMouseDragging() {
    this.isMouseDragging = false;
    this.mouseDraggingMode = null;
    this.mouseMoveOnRider = null;
    this.mouseDownPlace = null;
    this.mapViewController.setLockMapScreen(false);
    this.cancelDraggingCheckCatchTimer();
    // this.clearMouseMoveAllDraggingLine();
    // this.mapViewController.clearRectangleLine();
    console.debug("[mouse] cancelMouseDragging");
  }

  //배송지 선택 체크 모드 해지
  cancelDestinationChecked() {
    this.cancelMouseDragging();
    this.clearMouseMoveAllDraggingLine();
    for (const dest of this.project.destinations) {
      if (dest.onCheck) {
        dest.onCheck = false;
        this.addDestinationPin(dest);
      }
    }
  }

  isDraggingDestinationsMode() {
    return this.isMouseDragging && this.mouseDraggingMode === Constant.MOUSE_DRAGGING_MODE.DRAGGING_DESTINATIONS && this.mouseDownPlace;
  }

  isDraggingDrawRectangleMode() {
    return this.isMouseDragging && this.mouseDraggingMode === Constant.MOUSE_DRAGGING_MODE.DRAWING_RECTANGLE;
  }

  //드래깅 라인을 지운다
  clearMouseMoveAllDraggingLine() {
    for (const dest of this.project.destinations) {
      if (dest.onCheck) {
        this.mapViewController.clearMouseMoveDraggingLine(dest.deliveryId);
        dest.onDragging = false;
        this.addDestinationPin(dest);
      }
    }
  }

  cancelDraggingCheckCatchTimer() {
    if (this.mouseDraggingCatchTimer > 0) {
      clearTimeout(this.mouseDraggingCatchTimer);
      this.mouseDraggingCatchTimer = -1;
    }
  }


  //사각형 영역안의 목적지를 모두 선택처리한다
  setSelectDestinationInRectangle(event) {

    if (!this.ctrlMouseDownEvent.y || !this.ctrlMouseDownEvent.y)
      return;

    const mouseDownPos = this.mapViewController.screen2world(this.ctrlMouseDownEvent.x, this.ctrlMouseDownEvent.y);
    const mouseUpPos = this.mapViewController.screen2world(event.clientX, event.clientY);
    let selectDestCount = 0;

    for (let dest of this.project.destinations) {
      if (!dest.onCheck
        && PlaceUtil.isDestinationInRectangle(mouseDownPos.x, mouseDownPos.y, mouseUpPos.x, mouseUpPos.y, dest)
        && !PlaceUtil.isReturnPlace(dest) /* !PlaceUtil.isDestinationEditEnabled (dest ) edit가 가능한것만 체크하는게 낮지 않을까 생각 함. 우선 보류 */) {
        let riderChecked = true;
        const rider = RiderUtil.findRiderById(this.project.riders, dest.riderId);
        if (rider) {
          riderChecked = rider.isSelected;
        }

        if (riderChecked) {//체크된 기사만 배송지 선택이 되도록 한다
          dest.onCheck = true;
          this.addDestinationPin(dest);
        }
        selectDestCount += 1;
      }
    }
    if (selectDestCount)
      PopupUtil.showNotificationPopup("총 " + selectDestCount + "배송지가 선택되었습니다.", "top");
  }

  processMouseDraggingOnMouseDown(event) {

    this.mouseDownEvent.x = event.clientX;
    this.mouseDownEvent.y = event.clientY;
    this.mouseDownEvent.isPressed = true;
    let isDraggingEnabledPin = false;

    this.mouseDownPlace = null;
    if (!this.isDraggingDestinationsMode() && !this.isDraggingDrawRectangleMode()) {
      const selectedItems = this.getDataFromMultiPositions(event.clientX, event.clientY);
      for (const item of selectedItems) {
        if (item.type === Constant.TP.DESTINATION) {
          let destination = item.info;
          if (destination.onCheck) {
            isDraggingEnabledPin = true;
            this.mouseDownPlace = destination;
            break;
          }
        }
      }
    }
  }


  cancelAllMouseDraggingAction() {
    this.cancelMouseDragging();
    this.clearMouseMoveAllDraggingLine();
    this.mapViewController.clearRectangleLine();
    this.mapViewController.setDeleteLabelByTag('rider_pin_multi_select');
  }

  processMouseDraggingOnMouseUp(event) {

    this.cancelDraggingCheckCatchTimer();

    if (this.isDraggingDestinationsMode()) {
      if (this.mouseMoveOnRider) {
        this.changeClusteringMouseDragging(this.mouseMoveOnRider);
      }
      this.cancelAllMouseDraggingAction();
    }

    if (this.isDraggingDrawRectangleMode()) {
      this.cancelAllMouseDraggingAction();

      if (this.ctrlMouseDownEvent.isPressed)
        this.setSelectDestinationInRectangle(event);
      else if (this.shiftMouseDownEvent.isPressed)
        this.setMultiSelectDestinationInRectangle(event);

    } else if (!this.ctrlMouseDownEvent.isPressed && !this.fields.isMouseMove) {
      const selectedItems = this.getDataFromMultiPositions(event.clientX, event.clientY);
      if (selectedItems.length === 0) { //빈공간 찍으면 선택한 핀 취소
        this.cancelDestinationChecked();
      }
    }

    this.ctrlMouseDownEvent = {x: null, y: null, isPressed: false};
    this.shiftMouseDownEvent = {x: null, y: null, isPressed: false};
    this.mouseDownEvent = {x: null, y: null, isPressed: false};
    this.drawingmouseMoveEvent = {x: null, y: null};
  }

  processMouseDraggingOnMouseMove(event) {

    if (this.isDraggingDestinationsMode()) {
      console.log("[mouse-event] drawing moving destination ");
      const mouseMovePos = this.mapViewController.screen2world(event.clientX, event.clientY);
      // this.cancelMouseDragging();
      const mouseDownDest = {x: this.mouseDownPlace.x, y: this.mouseDownPlace.y};
      const mouseMoveRider = this.getRiderByMapPosition({x: event.clientX, y: event.clientY});

      //마우스의 위치에 해당하는 기사의 색을 표현
      let color = null;
      // if( this.mouseDownPlace.riderId && mouseMoveRider && this.mouseDownPlace.riderId !== mouseMoveRider.riderId ){
      if (mouseMoveRider && mouseMoveRider.riderId) {
        color = Util.getClusteringColor(mouseMoveRider.colorIndex);
        this.mouseMoveOnRider = RiderUtil.findRiderById(this.project.riders, mouseMoveRider.riderId);
      } else {
        this.mouseMoveOnRider = null;
      }

      const onCheckDestCount = this.getCheckDestinationCount();

      for (const dest of this.project.destinations) {
        if (dest.onCheck) {
          this.mapViewController.drawMouseMoveDraggingLine(dest.deliveryId, {
            x: dest.x,
            y: dest.y
          }, mouseMovePos, color);

          dest.onDragging = true;
          dest.draggingColorIndex = this.mouseMoveOnRider ? this.mouseMoveOnRider.colorIndex : null;

          if (onCheckDestCount < 50) {//많은 배송지가 새로 그려질 경우 오류 발생하므로 그리지 않음
            this.calculateMovingPinPosition(dest, mouseMovePos, mouseDownDest);
            this.addDestinationPin(dest);
          }

        }
      }
      this.callUpdateMapViewSync();
      this.drawingmouseMoveEvent.x = event.clientX;
      this.drawingmouseMoveEvent.y = event.clientY;


    } else if (this.isDraggingDrawRectangleMode()) {
      if (this.ctrlMouseDownEvent.x && this.ctrlMouseDownEvent.y) {
        console.log("[mouse-event] drawRectangleLine ");
        const mouseDownPos = this.mapViewController.screen2world(this.ctrlMouseDownEvent.x, this.ctrlMouseDownEvent.y);
        const mouseMovePos = this.mapViewController.screen2world(event.clientX, event.clientY);

        const color = {r: 0x4A, g: 0x6B, b: 0x71, a: 0};
        this.mapViewController.drawRectangleLine(mouseDownPos.x, mouseDownPos.y, mouseMovePos.x, mouseMovePos.y, color);
        this.mapViewController.setAddOnMapLabel('rider_pin_multi_select', '배송지 선택', mouseDownPos.x, mouseDownPos.y);
        this.callUpdateMapViewSync();

      } else if (this.shiftMouseDownEvent.x && this.shiftMouseDownEvent.y) {
        console.log("[mouse-event] drawRectangleLine ");
        const mouseDownPos = this.mapViewController.screen2world(this.shiftMouseDownEvent.x, this.shiftMouseDownEvent.y);
        const mouseMovePos = this.mapViewController.screen2world(event.clientX, event.clientY);

        const color = {r: 0x00, g: 0xCB, b: 0xCB, a: 0};
        this.mapViewController.drawRectangleLine(mouseDownPos.x, mouseDownPos.y, mouseMovePos.x, mouseMovePos.y, color);
        this.mapViewController.setAddOnMapLabel('rider_pin_multi_select', '배송핀 선택', mouseDownPos.x, mouseDownPos.y);
        this.callUpdateMapViewSync();
      }
    } else if (this.ctrlMouseDownEvent.isPressed || this.shiftMouseDownEvent.isPressed || this.mouseDownEvent.isPressed) {//불필요해 보이지만 한번 체크
      //핀 드래깅 모드인지 체크함. 마우스 다운 부터 일정 좌표 이상 움직여야 마우스 드래깅으로 판단함
      const draggingDistance = Util.getDistanceTwoTinyPoint(this.mouseDownEvent, {
        x: event.clientX,
        y: event.clientY
      });

      if (draggingDistance > 10000) {//일정 거리 드래깅이 일어나야 함
        if (this.mouseDownPlace) {//핀 드래그 모드
          if (!this.isEditableProject() || this.isGeoCodingStatus()) {
            return;
          }

          this.isMouseDragging = true;
          this.mouseDraggingMode = Constant.MOUSE_DRAGGING_MODE.DRAGGING_DESTINATIONS;
          this.mapViewController.setLockMapScreen(true);
        } else if (this.ctrlMouseDownEvent.isPressed || this.shiftMouseDownEvent.isPressed) {//사각형 모드
          console.log("mouseDownCtrlKey - enter drawing rectangle mode");
          this.isMouseDragging = true;
          this.mouseDraggingMode = Constant.MOUSE_DRAGGING_MODE.DRAWING_RECTANGLE;
          this.mapViewController.setLockMapScreen(true);
        }
      }
    }
  }

  //드래깅 시에 좌표값 구함
  calculateMovingPinPosition(destination, mouseMovePos, mouseDownDest) {
    const DISTANCE = 0.005;
    const draggingDistance = Util.getDistanceTwoPoint(mouseMovePos, mouseDownDest);
    // const lineDistance = Util.getDistanceTwoPoint( destination, mouseMovePos );

    let movingPercent = ((draggingDistance * 10000) / ((DISTANCE) * 10000)) * 100;
    if (movingPercent > 100) movingPercent = 100;

    const deltaX = mouseMovePos.x - destination.x;
    const deltaY = mouseMovePos.y - destination.y;

    destination.movingX = destination.x + (deltaX * (movingPercent / 100));
    destination.movingY = destination.y + (deltaY * (movingPercent / 100));
  }

  getCheckDestinationCount() {
    let count = 0;
    for (const dest of this.project.destinations) {
      if (dest.onCheck) {
        count++;
      }
    }
    return count;
  }


  //마우스가 올려진 영역의 소유 기사를 구함
  getRiderByMapPosition(pos) {
    let findRider = null;

    //1단계: 배송지위에 올라갔을 경우
    const selectedItems = this.getDataFromMultiPositions(pos.x, pos.y);
    for (const item of selectedItems) {
      if (item.type === Constant.TP.DESTINATION) {
        let destination = item.info;
        if (!destination.onCheck) {
          if (destination.riderId) {
            findRider = RiderUtil.findRiderById(this.project.riders, destination.riderId);
            break;
          }
        }
      }
    }

    //2단계 : 클러스터링 영역조사
    if (findRider == null) {
      const polygon = this.mapViewController.hitPolygonTestByScrPoint(pos.x, pos.y);
      if (polygon && polygon.tag) {
        const polygonRiderId = parseInt(polygon.tag.replace(Constant.TP.CLUSTER + Constant.TP.RIDER, ""));
        if (polygonRiderId) {

          let isChangeableClustering = false;
          for (const dest of this.project.destinations) {
            if (dest.onCheck && (!dest.riderId || dest.riderId !== polygonRiderId)) {
              isChangeableClustering = true;
              break;
            }
          }

          if (isChangeableClustering) {
            findRider = RiderUtil.findRiderById(this.project.riders, polygonRiderId);
          }
        }
      }
    }

    //3단계 : 기사 핀
    for (const item of selectedItems) {
      if (item.type === Constant.TP.RIDER) {
        const rider = item.info;
        if (rider) {
          findRider = RiderUtil.findRiderById(this.project.riders, rider.riderId);
          break;
        }
      }
    }

    return findRider;
  }


  checkValidDestinationByClickMousePosition(dest, pos) {//hitImageMapPositionsByScrPoint 값이 정확하지 않음. 또한 zoomLevel에 따라서  clientX, clientY screen2world 로 얻은 좌표가 정확하지 않아서 보정을 함.
    const deltaY = (pos.y - dest.y) * 100000;
    const deltaX = Math.abs(pos.x - dest.x) * 100000;
    const zoomLevel = this.callGetZoomLevel(); //0 -6까지만 지원
    if (zoomLevel < 0 || zoomLevel > 7)
      return false;

    //zoomLevel            0   1    2   3   4   5    6    7
    const withinWidths = [5, 8, 16, 32, 50, 100, 160, 300];
    const withinHeights = [10, 15, 30, 60, 95, 184, 361, 544];

    const withinWidth = withinWidths[zoomLevel];
    const withinHeight = withinHeights[zoomLevel];
    console.log("[checkValidDestinationByClickMousePosition] deltaX.x  : " + deltaX + "( width: " + withinWidth + ")" + ", deltaY: " + deltaY + "( height: " + withinHeight + ")" + "( zoomLevel : " + zoomLevel + ")");
    if (deltaY >= 0 && deltaY < withinHeight && deltaX < withinWidth) {
      return true;
    }

    return false;
  }

  async mouseUpCtrlKey(event) {

    if (this.isDraggingDrawRectangleMode() || this.isDraggingDestinationsMode())
      return;

    const selectedItems = this.getDataFromMultiPositions(event.clientX, event.clientY);

    for (const item of selectedItems) {
      if (item.type === Constant.TP.DESTINATION) {
        let destination = item.info;
        const mouseMapPos = this.mapViewController.screen2world(event.clientX, event.clientY);
        if (!this.checkValidDestinationByClickMousePosition(destination, mouseMapPos)) {
          console.warn("[mouseUpCtrlKey] position mismatching -destination.x  : " + destination.x + ", destination.y : " + destination.y);
          console.warn("[mouseUpCtrlKey] position mismatching -mouseMapPos.x  : " + mouseMapPos.x + ", mouseMapPos.y : " + mouseMapPos.y);
          return;
        }
        destination.onCheck = destination.onCheck != null ? !destination.onCheck : true;
        this.addDestinationPin(destination);

        let samePositionDestinations = PlaceUtil.getSamePositionDestinations(this.project.destinations, destination);//동일 좌표도 모두 체크 처리
        for (let sameDest of samePositionDestinations) {
          sameDest.onCheck = destination.onCheck;
          this.addDestinationPin(sameDest);
        }
        break;
      }
    }

    this.callUpdateMapViewSync();
  }

  async mouseDownCtrlKey(event) {

    if (this.isDraggingDrawRectangleMode() || this.isDraggingDestinationsMode())
      return;

    this.ctrlMouseDownEvent.x = event.clientX;
    this.ctrlMouseDownEvent.y = event.clientY;
    this.ctrlMouseDownEvent.isPressed = true;
    console.debug("[mouse-event] mouseDownCtrlKey ");
  }

  async mouseDownShiftKey(event) {
    if (this.isDraggingDrawRectangleMode() || this.isDraggingDestinationsMode())
      return;

    this.shiftMouseDownEvent.x = event.clientX;
    this.shiftMouseDownEvent.y = event.clientY;
    this.shiftMouseDownEvent.isPressed = true;
    console.debug("[mouse-event] mouseDownShiftKey ");
  }

  async mouseUpShiftKey(event) {

    if (this.isDraggingDrawRectangleMode() || this.isDraggingDestinationsMode())
      return;

    this.setMultiSelectDestinationInRectangle(event);
  }

  //사각형 영역안의 핀 정보 선택처리한다
  setMultiSelectDestinationInRectangle(event) {

    //지도의 선택 한 위치를 좌표로 변경함.
    if (this.shiftMouseDownEvent.isPressed) {
      const mouseDownPos = this.mapViewController.screen2world(this.shiftMouseDownEvent.x, this.shiftMouseDownEvent.y);
      const mouseUpPos = this.mapViewController.screen2world(event.clientX, event.clientY);

      const riderIds = [];

      for (let dest of this.project.destinations) {
        if (PlaceUtil.isDestinationInRectangle(mouseDownPos.x, mouseDownPos.y, mouseUpPos.x, mouseUpPos.y, dest)) {
          if (!_.includes(riderIds, dest.riderId)) {
            riderIds.push(dest.riderId);
          }
        }
      }

      console.log(riderIds);

      if (riderIds.length) {
        _.forEach(riderIds, riderId => {
          const findRider = _.find(this.project.riders, {riderId: riderId})
          console.log(findRider);
          this.riderSelectedItem(findRider, true);
        })

      }
    }
  }

  mouseLeave(event) {
    //맵 위부에서 mouse up할 경우 오류
    // this.mouseUp(event);
    this.fields.isMouseDown = false;
    this.fields.isMouseMove = false;
  }


  drawRiderRouteLine(rider, isView = false) {
    if (!rider.isSelected && !isView)
      return;

    console.log("drawRiderRouteLine")
    console.log(this.project)

    for (const destination of rider.destinations) {
      if (this.project.status === Constant.PROJECT_STATUS.SCHEDULED) {
        this.mapViewController.drawPlannedRoutePath(rider, destination);
      } else if (this.project.status === Constant.PROJECT_STATUS.DONE || rider.status === Constant.RIDER_STATUS.COMPLETED) {
        this.mapViewController.drawDoneRoutePath(rider, destination);
      } else {
        this.mapViewController.drawTrackingRoutePath(rider, destination);
      }
    }

    this.mapViewController.drawRealDrivingPath(rider);
    this.drawMppRoutePath(rider);

  }


  updateOnDemandClusteringOption(onDemandOptions) {
    this.saveLocalStorage("setClusterRuleOnDemand", onDemandOptions.clusterRuleOnDemand);
    this.saveLocalStorage("setIsAddDeliveryToLastOrder", onDemandOptions.isAddDeliveryToLastOrder);
    this.saveLocalStorage("setIsSendImmediatelyOnDemand", onDemandOptions.isSendImmediatelyOnDemand);
    this.syncProjectAttributeToProject(onDemandOptions);
  }


  // 관제웹 팝업 설정 창 변경이 Project 속성에 저장.
  syncProjectAttributeToProject(updateAttribute) {
    PROJECT_API.setProjectAttribute(this.project.id, updateAttribute);
    MapUtil.mergeObject(this.project.attribute, updateAttribute);
    console.log("local-storage]  save project-attributes project Id : " + this.project.id + ", value : " + JSON.stringify(updateAttribute));
  }


  //현재 프로젝트 속성이 관제 웹에 저장됨
  syncProjectAttributeToStore() {

    let store = {}

    store.clusterRule = this.storageGetters().getClusterRuleOption != null ? this.storageGetters().getClusterRuleOption : Constant.CLUSTER_RULE.NORMAL;
    store.clusterRuleOnDemand = this.storageGetters().getClusterRuleOnDemand != null ? this.storageGetters().getClusterRuleOnDemand : Constant.ON_DEMAND_CLUSTER_RULE.ETA;
    store.isAddDeliveryToLastOrder = this.storageGetters().getIsAddDeliveryToLastOrder != null ? this.storageGetters().getIsAddDeliveryToLastOrder : false;
    store.isSendImmediatelyOnDemand = this.storageGetters().getIsSendImmediatelyOnDemand != null ? this.storageGetters().getIsSendImmediatelyOnDemand : false;

    if (this.project.attribute.clusterRule == null) {
      this.syncProjectAttributeToProject({clusterRule: store.clusterRule});
    }

    if (this.project.attribute.clusterRuleOnDemand == null) {
      this.syncProjectAttributeToProject({clusterRuleOnDemand: store.clusterRuleOnDemand})
    }

    if (this.project.attribute.isAddDeliveryToLastOrder == null) {
      this.syncProjectAttributeToProject({isAddDeliveryToLastOrder: store.isAddDeliveryToLastOrder})
    }

    this.saveLocalStorage("setClusterRuleOption", this.project.attribute.clusterRule);
    this.saveLocalStorage("setClusterRuleOnDemand", this.project.attribute.clusterRuleOnDemand);
    this.saveLocalStorage("setIsAddDeliveryToLastOrder", this.project.attribute.isAddDeliveryToLastOrder);
    this.saveLocalStorage("setIsSendImmediatelyOnDemand", this.project.attribute.isSendImmediatelyOnDemand);


  }

  saveLocalStorage(target, value) {
    if (value == null) return;
    console.log("[local-storage]  saveLocalStorage " + target + " , value : " + JSON.stringify(value));
    window.parent.app.$store.dispatch(target, value);
  }

  storageGetters() {
    return window.parent.app.$store.getters;
  }

  //배송지 갯수 기준으로 많아지면 speedup 모드
  setMapSpeedUpMode() {
    const MAX_DEST_COUNT_SPEEDUP_MODE = 10000;

    if (this.project && this.project.destinations
      && this.project.destinations.length > MAX_DEST_COUNT_SPEEDUP_MODE
      && this.project.status === Constant.PROJECT_STATUS.IN_PROGRESS) {//관제 중일때만 Enable 함.
      this.mapSpeedUpMode = true;
      console.log("mapSpeedUpMode is true");
    } else {
      this.mapSpeedUpMode = false;
      console.log("mapSpeedUpMode is false");
    }
  }

  //speedUp모드에 따른 각 파라미터값 설정 기본 디폴트 값은 true임
  setMapParametersByMapSpeedMode() {

    if (this.mapSpeedUpMode) {
      this.mapParamSetImageMask = true;
      this.mapViewController.setImageMaskLabel(false)//사용자 이미지 겹침 개수를 Label 로 표시할 수 있는 API 기능
      this.mapViewController.setLineVisibleLimit(true)//사용자 PolyLine 객체를 레벨(축척)별 표시 제한값을 적용하여 표시할 수 있는 API 기능

      this.mapViewController.setMoveDrawUserPoly(true)//지도화면 이동 시 사용자 PolyObject 객체를 표시하거나 숨기는 API 기능
      this.mapViewController.setMoveDrawUserImage(true)//지도화면 이동 시 사용자 이미지를 표시하거나 숨기는 API 기능
      this.mapViewController.setMoveDrawUserText(true)//지도화면 이동 시 사용자 Label 을 표시하거나 숨기는 API 기능
    } else {
      this.mapParamSetImageMask = false;
      this.mapViewController.setImageMaskLabel(false)//사용자 이미지 겹침 개수를 Label 로 표시할 수 있는 API 기능
      this.mapViewController.setLineVisibleLimit(false)//사용자 PolyLine 객체를 레벨(축척)별 표시 제한값을 적용하여 표시할 수 있는 API 기능

      this.mapViewController.setMoveDrawUserPoly(true)//지도화면 이동 시 사용자 PolyObject 객체를 표시하거나 숨기는 API 기능
      this.mapViewController.setMoveDrawUserImage(true)//지도화면 이동 시 사용자 이미지를 표시하거나 숨기는 API 기능
      this.mapViewController.setMoveDrawUserText(true)//지도화면 이동 시 사용자 Label 을 표시하거나 숨기는 API 기능
    }

    this.mapViewController.setImageMask(this.mapParamSetImageMask);//사용자 이미지 주변의 겹치는 이미지를 하나의 이미지로 표시할 수 있는 API 기능
  }


  //오버랩이 되지 않아야 하는 이미지 (기사, 배송지 선택핀) 일 경우 임시적으로 false 처리함
  disableOverlapSetImageMask() {
    if (this.mapParamSetImageMask) {
      this.mapViewController.setImageMask(false);
    }
  }

  //다시 setImageMask 값 복구
  recoverOverlapSetImageMask() {
    if (this.mapParamSetImageMask) {
      this.mapViewController.setImageMask(this.mapParamSetImageMask);//사용자 이미지 주변의 겹치는 이미지를 하나의 이미지로 표시할 수 있는 API 기능
    }
  }

  //기사 이미지등 움직이는 이미지 경우에는 setImageMask = false로 한다. 이후에 그린다음에 다시 풀어준다.
  addRiderPinToMap(rider) {
    this.disableOverlapSetImageMask();
    this.mapViewController.addRiderPin(rider);
    this.recoverOverlapSetImageMask();
  }

  //배송지에 대한 변경 사항 및 배송지 이전에 따른 destinationPanel update 시킴
  refreshDestinationsList() {
    if (window.parent.app.$refs.leftPanel.$refs.destinationListPanel !== undefined)
      window.parent.app.$refs.leftPanel.$refs.destinationListPanel.refreshDestinationsList();
  }

  //배송지에 대한 변경 사항 및 배송지 이전에 따른 destinationPanel update 시킴
  refreshSectionDestinationsList(rider, destinations) {
    if (window.parent.app.$refs.leftPanel.$refs.destinationListPanel !== undefined) {
      window.parent.app.$refs.leftPanel.$refs.destinationListPanel.$emit(EVENT.PANEL.SET_CHANGE_DELIVERY_STATUS, {
        rider: rider,
        destinations: destinations,
      });
    }
  }

  autoAddRider() {

    const projectId = this.project.id;
    const _this = this;
    PopupUtil.confirmPopup(`현재 프로젝트에 자동으로 권역 기사를 추가합니다. <br> 계속 진행하시겠습니까? `
      , {
        onConfirm: () => {
          PROJECT_API.setAutoAddRider(projectId, {
            onSuccess: async (response) => {
              PopupUtil.showNotificationPopup("총 " + response.data.length + " 명의 기사를 추가하였습니다");
              _this.refreshProject(projectId);
            },
            onError: (error) => {
              PopupUtil.alertPopup("기사 추가에 실패하였습니다 ");
            }
          });
        },
      });
  }

  autoAddDispatchRider() {

    const projectId = this.project.id;
    const _this = this;
    PopupUtil.confirmPopup(`현재 프로젝트에 자동으로 기사를 추가합니다. <br> 계속 진행하시겠습니까? `
      , {
        onConfirm: () => {
          PROJECT_API.setAutoAddDispatchRider(projectId, {
            onSuccess: async (response) => {
              PopupUtil.showNotificationPopup("총 " + response.data.length + " 명의 기사를 추가하였습니다");
              _this.refreshProject(projectId);
            },
            onError: (error) => {
              PopupUtil.showErrorPopup(error);
            }
          });
        },
      });
  }

  /**
   *  MPP Interface functions
   */
  performMpp(rider, isAppended) {
  }

  clearMppRoutePath(rider) {
  }

  drawMppRoutePath(rider) {
  }

  resumeSimulationTracking() {
  }

  startSimulationTracking() {
  }

  onUpdateMPPSetting(data) {
  }

  refreshMPP(isMppChanged = true) {
  }


  /**
   * 모의주행 모드 ON
   */
  simulatedDrivingMode() {
  }

  simulationTrackingPaused() {
  }

  simulationTrackingResumed() {
  }

  pauseSimulationTracking() {
  }

  startPathControlMessageInterval() {
  }

  clearPathControlMessageInterval() {
  }

  isShowClusterName(rider) {
    this.mapViewController.isShowClusterName(rider);
  }

  isWelStoryUserCompany(userInfoData) {
    if (userInfoData && Constant.WELSTORY_ORG_CODE_NAME === userInfoData.orgCodeName) {
      return true;
    } else
      return false;
  }

  onPerformSendingAfterRouting(data) {
    this.performSendingAfterRouting(data);
  }

  async performSendingAfterRouting(data, callback) {
    let routeOption = (data && data.routeOption) ? data.routeOption : this.fields.routeOption;

    console.log("[performSendingAfterRouting] routeOption: " + routeOption);
    PopupUtil.showLoadingPopup("탐색 중...", "잠시만 기다려 주세요.");
    const projectId = this.project.id;
    const _this = this;

    await PROJECT_API.getDeliveryOrdersAndRoutes(projectId, routeOption, {
      onSuccess: (response) => {
        PopupUtil.dismissLoadingPopup();

        console.log("[performSendingAfterRouting] 경로생성후 기사에게 전송");
        _this.onPerformSending(false);

        console.log("[performSendingAfterRouting] loadProject(), projectId: " + projectId);
        _this.loadProject(projectId, callback);
        _this.setTopToolbarRountingButtonEnabled();
      },
      onError: (error) => {
        PopupUtil.dismissLoadingPopup();
        PopupUtil.showErrorPopup(error);
        _this.setTopToolbarRountingButtonEnabled();
        if (callback && callback.onError) {
          callback.onError(error);
        }
      }
    });
  }

}