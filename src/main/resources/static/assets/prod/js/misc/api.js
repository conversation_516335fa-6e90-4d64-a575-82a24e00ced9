const setAccessTokenToHeader = (token) => {
    axios.defaults.headers.common['Authorization'] = 'bearer ' + token;
}

const API = {
    get: (url, params, headers, callback) => {
        callback = API.getCallbackAssign(callback);
        return axios.get(url, {params: params, headers: headers})
            .then(response => {
                callback.onSuccess(response);
            })
            .catch(error => {
                callback.onError(error);
            });
    },

    getSync: (url, params, headers) => {
        return axios.get(url, {params: params, headers: headers})
            .catch(error => {
                console.error('Failed GET Request!!!');
            });
    },

    post: (url, params, body, headers, callback) => {
        callback = API.getCallbackAssign(callback);
        return axios.post(url, body, {params: params, headers: headers, onUploadProgress: callback.onUploadProgress})
            .then(response => {
                callback.onSuccess(response);
            })
            .catch(error => {
                callback.onError(error);
            });
    },

    put: (url, params, body, headers, callback) => {
        callback = API.getCallbackAssign(callback);
        return axios.put(url, body, {params: params, headers: headers})
            .then(response => {
                callback.onSuccess(response);
            })
            .catch(error => {
                callback.onError(error);
            });
    },

    putSync: (url, params, body, headers) => {
        return axios.put(url, body, {params: params, headers: headers})
            .catch(error => {
                console.error('Failed PUT Request!!!');
            });
    },

    delete: (url, params, headers, callback) => {
        callback = API.getCallbackAssign(callback);
        axios.delete(url, {params: params, headers: headers, onUploadProgress: callback.onUploadProgress})
            .then(response => {
                callback.onSuccess(response);
            })
            .catch(error => {
                callback.onError(error);
            });
    },

    getCallbackAssign: (callbackObj) => {
        return Object.assign({
            onSuccess: (response) => {
                console.log(response);
            }
            , onError: (error) => {
                console.log(error);
                // Util.alert('API 요청 실패! 브라우저 콘솔 로그 확인 필요!' );
            }
            , onUploadProgress: (progressEvent) => {
            }
        }, callbackObj);
    },

    getDownload: (url, params, headers, callback) => {
        callback = API.getCallbackAssign(callback);
        axios.get(url, {responseType: 'blob', params: params, headers: headers})
            .then(response => {
                callback.onSuccess(response);
            })
            .catch(error => {
                callback.onError(error);
            });
    },

    downloadFileByGet: (url, params, headers, downloadFileName, callback) => {
        callback = API.getCallbackAssign(callback);
        return axios.get(url, {params: params, headers: headers, responseType: 'arraybuffer'})
            .then(response => {
                const contentType = response.headers["content-type"];
                const contentDisposition = response.headers["content-disposition"];

                var attachedFileName = "download.file";
                if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
                    var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    var matches = filenameRegex.exec(contentDisposition);
                    if (matches != null && matches[1]) {
                        attachedFileName = matches[1].replace(/['"]/g, '');
                        if ('UTF-8' === attachedFileName.substr(0, 5).toUpperCase()) {
                            attachedFileName = decodeURIComponent(attachedFileName.substr(5));
                        }
                    }
                }

                downloadFileName = downloadFileName || attachedFileName;
                const url = window.URL.createObjectURL(new Blob([response.data], {type: contentType}));
                // window.open(url);
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', downloadFileName);
                document.body.appendChild(link);
                link.click();
                callback.onSuccess(response);
            })
            .catch(error => {
                error.response.data = new TextDecoder().decode(error.response.data);
                callback.onError(error);
            });
    },

    downloadFileByGetEx: (url, params, headers, downloadFileName, callback) => {
        callback = API.getCallbackAssign(callback);
        return axios.get(url, {params: params, headers: headers, responseType: 'arraybuffer'})
            .then(response => {
                if (response.status == 204) {
                    callback.onSuccess(response);
                    return;
                }
                const contentType = response.headers["content-type"];
                const contentDisposition = response.headers["content-disposition"];

                var attachedFileName = "download.file";
                if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
                    var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    var matches = filenameRegex.exec(contentDisposition);
                    if (matches != null && matches[1]) {
                        attachedFileName = matches[1].replace(/['"]/g, '');
                        if ('UTF-8' === attachedFileName.substr(0, 5).toUpperCase()) {
                            attachedFileName = decodeURIComponent(attachedFileName.substr(5));
                        }
                    }
                }

                downloadFileName = downloadFileName || attachedFileName;
                const url = window.URL.createObjectURL(new Blob([response.data], {type: contentType}));
                // window.open(url);
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', downloadFileName);
                document.body.appendChild(link);
                link.click();
                callback.onSuccess(response);
            })
            .catch(error => {
                error.response.data = new TextDecoder().decode(error.response.data);
                callback.onError(error);
            });
    },

    downloadFileByPost: (url, params, headers, body, downloadFileName, callback) => {
        callback = API.getCallbackAssign(callback);
        return axios.post(url, body, {params: params, headers: headers, responseType: 'arraybuffer'})
            .then(response => {
                const contentType = response.headers["content-type"];
                const contentDisposition = response.headers["content-disposition"];

                var attachedFileName = "download.file";
                if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
                    var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    var matches = filenameRegex.exec(contentDisposition);
                    if (matches != null && matches[1]) {
                        attachedFileName = matches[1].replace(/['"]/g, '');
                        if ('UTF-8' === attachedFileName.substr(0, 5).toUpperCase()) {
                            attachedFileName = decodeURIComponent(attachedFileName.substr(5));
                        }
                    }
                }

                downloadFileName = downloadFileName || attachedFileName;
                const url = window.URL.createObjectURL(new Blob([response.data], {type: contentType}));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', downloadFileName);
                document.body.appendChild(link);
                link.click();
                callback.onSuccess(response);
            })
            .catch(error => {
                error.response.data = new TextDecoder().decode(error.response.data);
                callback.onError(error);
            });
    },

};

const USER_API = {

    /**
     * 프로젝트 목록 조회
     */
    getProjects: (callback) => {
        const userInfo = app.$store.getters.getLoginUserInfo;
        return API.get(Url.WEB.USER_PROJECT.replace("{userId}", userInfo.id), null, null, callback);
    },

    /**
     * 신규 프로젝트 생성
     */
    createNewProject: (name, callback) => {
        const userInfo = app.$store.getters.getLoginUserInfo;
        let body = {
            "name": name
        };
        return API.post(Url.WEB.USER_PROJECT.replace("{userId}", userInfo.id), null, body, null, callback);
    },

    login: (email, password, authcode, openDeepLink) => {
        let formData = new FormData();
        formData.append('email', email);
        formData.append('password', password);
        if (authcode) {
            formData.append('authcode', authcode);
        }
        formData.append('remember-me', 'true');
        if (openDeepLink) {
            formData.append('open-deep-link', 'true');
        }

        return axios
            .post(Url.WEB.LOGIN, formData)
            .then((res) => res.data);
    },

    /**
     * 로그아웃
     */
    logout: () => {
        API.get(Url.WEB.LOGOUT, null, null, {
            onSuccess: (response) => {
                console.log('logout success');
                window.location.href = "/";
            },
            onError: (error) => {
                console.error('logout error');
            }
        });
    },

    registerAnonymousUser: (anonymousUserKey, callback) => {
        return API.post(Url.WEB.USER_REG_ANONYMOUS, {'anonymousUserKey': anonymousUserKey}, null, null, callback);
    },

    /**
     * 가입 사용자 중복 체크
     */
    checkUserDuplicate: (email, callback) => {
        const body = {
            "email": email,
        };
        return API.post(Url.WEB.CHECK_USER_DUPLICATE, null, body, null, callback);
    },

    /**
     * 사용자 비밀번호 변경
     */
    changeUserPassword: (body, callback) => {
        return API.put(Url.WEB.CHANGE_USER_PASSWORD, null, body, null, callback);
    },

    /**
     * 사용자 이름 변경
     */
    changeUserName: (body, callback) => {
        return API.put(Url.WEB.CHANGE_USER_NAME, null, body, null, callback);
    },

    /**
     * 사용자 전화번호 변경
     */
    changeUserPhoneNumber: (body, callback) => {
        return API.put(Url.WEB.CHANGE_USER_PHONE_NUMBER, null, body, null, callback);
    },

    /**
     * 사용자 프로파일 이미지 업로드
     */
    uploadUserProfileImg: (file, callback) => {
        let headers = {'Content-Type': 'multipart/form-data'};
        let body = new FormData();
        body.append('profileImgFile', file);
        return API.post(Url.WEB.USER_PROFILE_IMG, null, body, headers, callback);
    },

    /**
     * 사용자 프로파일 이미지 삭제
     */
    deleteUserProfileImg: (callback) => {
        return API.delete(Url.WEB.USER_PROFILE_IMG, null, null, callback);
    },

    /**
     * 로그인 실패 횟수 조회
     */
    getLoginFailedCount: (email, callback) => {
        const body = {"email": email};
        return API.post(Url.WEB.LOGIN_FAILED_COUNT, null, body, null, callback);
    },

    /**
     * 계정 잠금 일시 조회
     */
    getAccountLockedAt: (email, callback) => {
        const body = {"email": email};
        return API.post(Url.WEB.ACCOUNT_LOCKED_AT, null, body, null, callback);
    },

    /**
     * common user 등록
     */
    registerCommonUser: (user, organization, callback) => {
        let body = {
            "email": user.email,
            "name": user.name,
            "password": user.password,
            "organization": {
                "organizationName": organization.organizationName,
                "organizationOwner": organization.organizationOwner,
                "codeName": organization.codeName
            }
        };
        return API.post(Url.WEB.USERS, null, body, null, callback);
    },

    /**
     * 인증 번호 전송
     */
    sendUserAuthKey: (phoneNumber, callback) => {
        const body = {
            phoneNumber: phoneNumber
        };
        return API.post(Url.WEB.USER_SEND_AUTH_KEY, null, body, null, callback);
    },
};


/**
 * Math API
 */
const MATH_API = {

    /**
     * 클러스터링 결과
     */
    getClusterInfo: (country, group, data, callback) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        API.post(Url.TMS_CORE.MATH_CLUSTER
            , null
            , JSON.stringify({
                command: "ReqClust",
                vehipos: DemoUtil.getVehiposFromGroup(group),
                destpos: DemoUtil.getDestposFromData(data)
            })
            , headers
            , callback);
    },

    getDeliveryOrderByTimeSync: (country, vehicleInfo, nDataInfoList) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        const body = JSON.stringify({
            command: "RouteByReqTime",
            vehipos: vehicleInfo,
            destpos: nDataInfoList
        });
        return axios.post(Url.WEB.MATH_DELIVERY, body, {headers: headers});
    }
};


/**
 * 배송 API
 */
const DELIVERY_API = {

    /**
     * 배송지 저장
     */
    saveDelivery: (delivery, projectId, callback) => {
        let headers = null;
        let params = {"projectId": projectId};
        let body = {
            "deliveryType": delivery.type,
            "destinationAddress": {
                "base": delivery.addr,
                "location": {"x": delivery.x, "y": delivery.y}
            }
        };
        return API.post(Url.WEB.DELIVERY, params, body, headers, callback);
    },

    /**
     * 배송 목록 삭제
     */
    deleteDeliveries: (projectId, callback) => {
        API.delete(Url.WEB.DELIVERY + "?projectId=" + projectId, null, null, callback);
    },

    /**
     * 배송지 클러스터
     */
    performDeliveriesCluster: (projectId, callback) => {
        const headers = {'Content-Type': 'application/json'};
        let params = {"projectId": projectId};
        let body = null;
        return API.post(Url.WEB.DELIVERIES_CLUSTER, params, body, headers, callback);
    },

    /**
     * 배송지 경로탐색
     */
    performDeliveriesRoute: (projectId, callback) => {
        let headers = null;
        let params = {"projectId": projectId};
        let body = null;
        return API.post(Url.WEB.DELIVERIES_ROUTE, params, body, headers, callback);
    },

    clearDeliveriesRoutePath: (projectId, callback) => {
        // const params = { "projectId" : projectId };
        return API.delete(Url.WEB.DELIVERIES_ROUTE + "?projectId=" + projectId, null, null, callback);
    },

    /**
     * 배송 목록 조회
     */
    getDeliveries: (projectId, riderId, callback) => {
        const params = {
            "projectId": projectId,
            "riderId": riderId,
            "type": "page",
            "sortFieldName": "allocationsOrderNum"
        };
        return API.get(Url.WEB.DELIVERIES, params, null, callback);
    },

    /**
     * 배송 경로 조회
     */
    getDeliveryRoutePath: (riderId, projectId, callback) => {
        return API.get(Url.WEB.DELIVERIES_ROUTE, {riderId: riderId, projectId: projectId}, null, callback);
    },

    /**
     * 배송 ETA 조회
     */
    getDeliveryETA: (riderId, projectId, callback) => {
        return API.get(Url.WEB.DELIVERIES_ROUTE_ETA, {riderId: riderId, projectId: projectId}, null, callback);
    },

    /**
     * 특정 프로젝트의 배송 목록 조회 (Feat. Reporting)
     */
    getDeliveryAllocationReport: (projectId, callback) => {
        return API.get(Url.WEB.DELIVERIES_REPORT, {projectId: projectId}, null, callback);
    },

    /**
     * 방문지 목록 조회 (방문지 관리에 사용)
     */
    getDeliveryForManagement: (deliveryId, callback) => {
        const params = {"type": "manage"};
        return API.get(Url.WEB.DELIVERY_INFO.replace("{deliveryId}", deliveryId), params, null, callback);
    },

    /**
     * 방문지 목록 조회 (방문지 관리에 사용)
     */
    getDeliveriesForManagement: (page, keyword, locationKeyword, fromDate, toDate, callback) => {
        const params = {
            "type": "manage",
            "page": page,
            "keyword": keyword,
            "locationKeyword": locationKeyword,
            "fromDate": fromDate,
            "toDate": toDate
        };
        return API.get(Url.WEB.DELIVERIES, params, null, callback);
    },

    /**
     * 프로젝트별 송장 프린터를 위한 배송지 list
     */
    getInvoicePrintingDeliveriesListForProjectId: (projectId, riderIds, callback) => {
        const body = {
            "projectId": projectId,
            "includeSimulationRider": true,
            "riderIdList": riderIds,
        };
        return API.post(Url.WEB.DELIVERY_GET_INVOICE_PRINTING_LIST_PROJECT, null, body, null, callback);
    },

    /**
     * 관제자 배송 완료 체크 기능
     * @param projectId
     * @param deliveryId
     * @param callback
     * @returns {*}
     */
    putDeliveryCsSaveHistory: (projectId, deliveryId, callback) => {
        const body = {
            projectId: projectId,
            deliveryId: deliveryId
        };

        return API.put(Url.WEB.DELIVERY_CS_SAVE_HISTORY, null, body, null, callback);
    },

    /**
     * 방문지들 삭제 (방문지 관리에 사용)
     */
    deleteDeliveriesForManagement: (deliveryIdList, callback) => {
        const params = {"type": "manage", "deliveryIdList": deliveryIdList.join(','),};
        return API.delete(Url.WEB.DELIVERIES, params, null, callback);
    },

    /**
     * 방문지별 과거 주문내역 조회 (과거 주문내역 보기에 사용)
     */
    getPastDeliveries: (deliveryId, callback) => {
        return API.get(Url.WEB.DELIVERIES_PAST, {deliveryId: deliveryId}, null, callback);
    },

    /**
     * delivery 완료 이미지 조회
     */
    getUploadFile: (deliveryId, callback) => {
        return API.get(Url.WEB.DELIVERY_FILE.replace("{deliveryId}", deliveryId), null, null, callback);
    },

    /**
     * 배송 관리 list 요청
     */
    getDeliveryListInfo: (projectId, page, vocStatus, keyword, size, searchValue, fromDate, toDate, callback) => {
        let params = {
            "type": "delivery_manage",
            "projectId": projectId,
            "page": page,
            "vocStatus": vocStatus,
            "keyword": keyword,
            "size": size,
            "searchValue": searchValue,
            "fromDate": fromDate,
            "toDate": toDate
        };
        return API.get(Url.WEB.DELIVERIES, params, null, callback);
    },

    /**
     * delivery VOC message
     */
    postVocMessage: (deliveryId, vocStatus, vocMessage, callback) => {
        let params = {
            "vocStatus": vocStatus,
            "vocMessage": vocMessage,
        };
        return API.post(Url.WEB.DELIVERY_VOC_SAVE.replace("{deliveryId}", deliveryId), params, null, null, callback);
    },

    /**
     * delivery VOC message get list
     */
    getVocMessageList: (deliveryId, callback) => {
        let params = {
            "deliveryId": deliveryId,
        };
        return API.get(Url.WEB.DELIVERY_VOC_GET_MESSAGE_LIST, params, null, callback);
    },

    // /**
    //  * 프로젝트의 배송 목록 조회
    //  */
    // getProjectDeliveryReport : (projectId, callback) => {
    //     return API.get(Url.WEB.DELIVERIES_REPORT, {projectId: projectId}, null, callback);
    // },

    /**
     * 송장 출력 프린터 Count
     */
    setDeliveryDeliveryInvoicePrintCount: (deliveryIdList, callback) => {
        const body = {
            deliveryIds: deliveryIdList,
        }
        return API.put(Url.WEB.DELIVERY_SET_INVOICE_PRINT_COUNT, null, body, null, callback)
    },

    /**
     * 배송 리포트 엑셀 이메일 전송
     */
    postDeliveryReportEmail: (body, callback) => {
        console.log(body)
        return API.post(Url.WEB.DELIVERIES_REPORT_EXCEL, null, body, null, callback)
    },

    getDeliveriesProjectList: (fromtDt, toDt, warehouseCode, callback) => {
        const params = {
            "fromDate": fromtDt,
            "toDate": toDt,
            "warehouseCode": warehouseCode
        }
        return API.get(Url.WEB.DELIVERIES_PROJECT_LIST, params, null, callback)
    },

    getDeliveriesWareHouseList: (callback) => {
        return API.get(Url.WEB.DELIVERIES_WAREHOUSE_LIST, null, null, callback)
    }

};

/**
 * Rider API
 */
const RIDER_API = {
    /**`
     * 기사 등록
     */
    registerRider: (riderInfo, callback) => {
        return API.post(Url.WEB.RIDERS, null, riderInfo, null, callback);
    },

    /**
     * 기사에게 프로젝트 할당
     */
    updateRiderProject: (riderId, projectId, callback) => {
        return API.put(Url.WEB.RIDERS_PROJECT.replace("{riderId}", riderId)
            , null              // params
            , {id: projectId}  // body
            , null              // header
            , callback);        // callback
    },

    /**
     * 특정 기사 프로젝트 정보 불러오기[demo]
     */
    getProjectSetting: (riderId, projectId) => {
        try {
            return API.getSync(Url.WEB.RIDER_PROJECT_SETTING.replace("{riderId}", riderId)
                , null              // params
                , {projectId: projectId});
        } catch (error) {
            PopupUtil.alertPopup('특정 기사 프로젝트 정보 조회  실패 : riderId(' + riderId + ')' + ' projectId(' + projectId + ')');
        }
    },

    /**
     * 기사 상세 조회
     */
    getRider: (riderId) => {
        try {
            let url = Url.WEB.RIDER_DETAIL.replace("{riderId}", riderId);
            return API.getSync(url, null, null);

        } catch (error) {
            PopupUtil.alertPopup('기사 상세 조회 실패 : riderId(' + riderId + ')');
        }
    },

    /**
     * 기사 상세 조회
     */
    getFindMobileRider: (mobile, callback) => {
        let params = {
            "mobile": mobile,
        };
        try {
            return API.get(Url.WEB.RIDER_FIND_MOBILE_DETAIL, params, null, callback);
        } catch (error) {
            PopupUtil.alertPopup('기사 상세 조회 실패 : mobile(' + mobile + ')');
        }
    },

    /**
     * 기사 상세 조회
     */
    getProjectRider: (riderId, projectId) => {
        try {
            let url = Url.WEB.RIDERS_DETAIL.replace("{riderId}", riderId);
            if (projectId) {
                url += "?projectId=" + projectId;
            }
            return API.getSync(url, null, null);

        } catch (error) {
            PopupUtil.alertPopup('기사 상세 조회 실패 : riderId(' + riderId + ')');
        }
    },

    /**
     * 기사에 대한 일별 프로젝트 조회
     */
    getProjectDateOfRider: (riderId, toDate, callback) => {

        let params = {'to': toDate};

        try {
            return API.get(Url.WEB.RIDERS_DETAIL_DATA_PROJECT.replace("{riderId}", riderId), params, null, callback);
        } catch (error) {
            PopupUtil.alertPopup('기사 상세 조회 실패 : riderId(' + riderId + ')');
        }
    },

    putProjectDataIntoRiderNoteOfRider: (riderId, projectId, riderNote, callback) => {
        const params = {
            "riderId": riderId,
            "projectId": projectId,
            "riderNote": riderNote,
        }

        return API.put(Url.WEB.RIDERS_PROJECT_SETTING_NOTE, params, null, null, callback);
    },

    /**
     * 기사 프로젝트 추가하기
     */
    addRiderToProject: (riderId, projectId) => {
        try {
            let url = Url.WEB.RIDER_ADD_TO_PROJECT.replace("{riderId}", riderId);
            if (projectId) {
                url += "?projectId=" + projectId;
            }
            return API.getSync(url, null, null);
        } catch (error) {
            PopupUtil.alertPopup('기사 상세 조회 실패 : riderId(' + riderId + ')');
        }
    },

    /**
     * 기사들 프로젝트에 추가하기 (기사 관리에 사용)
     */
    addRidersForManagement: (projectId, riderIdList) => {
        try {
            return API.getSync(Url.WEB.RIDERS_ADD_TO_PROJECT + "?riderIdList=" + riderIdList.join(","), {projectId: projectId}, null);
        } catch (e) {
            PopupUtil.alertPopup(`기사들을 : (' + ${projectId} + ') 추가 실패하였습니다.`);
        }
    },

    /**
     * 라이더의 리스트와 배차 상태정보를 조회 - left panel용 //@~~ywlee20200330
     */
    getRidersAndDispatchStatus: (projectId, callback) => {
        return API.get(Url.WEB.GET_RIDERS_DISPATCH_STATUS, {projectId: projectId}, null, callback);
    },

    getWebRider: (projectId, callback) => {
        return API.get(Url.WEB.PROJECT_RIDER_INFO.replace("{projectId}", projectId), null, null, callback);
    },

    /**
     * 기사 목록 조회
     */
    getRiders: (projectId, callback) => {
        return API.get(Url.WEB.RIDERS, {projectId: projectId, deleted: false}, null, callback);
    },

    /**
     * 기사 목록 조회 (기사 관리에 사용)
     */
    getRidersForManagement: (page, size, sort, direction, keyword, isTransit, workAuthorities, callback) => {
        const params = {
            "type": "manage",
            "page": page,
            "keyword": keyword,
            "inTransit": isTransit,
            "workAuthorities": workAuthorities,
        };
        if (size) {
            params.size = size;
        }
        if (sort) {
            params.sort = sort + (direction ? ("," + direction) : "");
        }
        return API.get(Url.WEB.RIDERS, params, null, callback);
    },

    /**
     * 기사들 삭제 (기사 관리에 사용)
     */
    deleteRidersForManagement: (riderIdList, callback) => {
        const params = {"type": "manage", "riderIdList": riderIdList.join(','),};
        return API.delete(Url.WEB.RIDERS, params, null, callback);
    },

    /**
     * 현재 위치 정보 조회
     */
    getCurrentLocations: (projectId, callback) => {
        return API.get(Url.WEB.TRACKS_CURRENT_LOCATIONS, {projectId: projectId}, null, callback);
    },

    /**
     * 특정 기사 위치 목록 조회
     */
    getRiderLocations: (riderId, projectId, fromDate, toDate, callback) => {
        return API.get(Url.WEB.TRACKS_RIDER_LOCATIONS.replace("{riderId}", riderId), {
            'projectId': projectId, 'from': fromDate, 'to': toDate
        }, null, callback);
    },

    /**
     * 주어진 좌표를 기사가 지나간 시간 조회
     */
    getRiderTimestamp: (riderId, projectId, x, y, callback) => {
        return API.get(Url.WEB.TRACKS_RIDER_TIMESTAMP, {
            'riderId': riderId, 'projectId': projectId, 'x': x, 'y': y
        }, null, callback);
    },

    /*
     * 현재 위지 정보 저장
     */
    saveCurrentLocation: (riderId, projectId, speed, angle, x, y, callback) => {
        const headers = {'Content-Type': 'application/json'};
        let params = null;
        let body = {
            "riderId": riderId,
            "projectId": projectId,
            "speed": speed,
            "angle": angle,
            "location": {"x": x, "y": y},
            "timestamp": new Date().toISOString()
        };
        return API.post(Url.WEB.TRACKS, params, body, headers, callback);
    },

    /*
     * 특정 vinNumber 그 차량의 정보 조회
     */
    getMoceanVinInfo: (vinnumber, callback) => {
        const params = {
            "vin": vinnumber,
        };
        return API.get(Url.WEB.TRACKS_MOCEAN_VIN_INFO, params, null, callback);
    },

    /*
     * 특정 차량번호로 그 차량의 Mocean 상태 조회
     */
    getMoceanLastVehicleStatus: (licensePlate, callback) => {
        const params = {
            "licensePlate": licensePlate,
        };
        return API.get(Url.WEB.TRACKS_MOCEAN_LAST_VEHICLE_STATUS, params, null, callback);
    },

    /*
     * 특정 차량번호로 그 차량의 Mocean 상태 조회
     */
    getMoceanLastVehicleStatusSync: (licensePlate) => {
        try {
            const params = {
                "licensePlate": licensePlate,
            };
            return API.getSync(Url.WEB.TRACKS_MOCEAN_LAST_VEHICLE_STATUS, params, null);
        } catch (error) {
        }
    },

    /*
     *  차량들의 Mocean 상태 조회
     */
    getMoceanLastVehicleStatuses: (page, size, sort, direction, callback) => {
        const params = {};
        if (page) {
            params.page = page;
        }
        if (size) {
            params.size = size;
        }
        if (sort) {
            params.sort = sort + (direction ? ("," + direction) : "");
        }
        return API.get(Url.WEB.TRACKS_MOCEAN_LAST_VEHICLE_STATUSES, params, null, callback);
    },

    /*
     *  특정 차량번호로 그 차량의 Mocean 상태 목록 조회
     */
    getMoceanVehicleStatus: (vin, licensePlate, originCreatedAt, boundCreatedAt, page, size, sort, direction, callback) => {
        const params = {
            "vin": vin,
            "licensePlate": licensePlate,
            "originCreatedAt": originCreatedAt,
            "boundCreatedAt": boundCreatedAt,
        };

        if (page) {
            params.page = page;
        }
        if (size) {
            params.size = size;
        }
        if (sort) {
            params.sort = sort + (direction ? ("," + direction) : "");
        }
        return API.get(Url.WEB.TRACKS_MOCEAN_VEHICLE_STATUS, params, null, callback);
    },

    /*
  *  차량의 Mocean Temperature 조회
  */
    getMoceanTemperatures: (vin, licensePlate, originTime, boundTime, page, size, sort, direction, callback) => {
        const params = {
            "vin": vin,
            "licensePlate": licensePlate,
            "originCreatedAt": originTime,
            "boundCreatedAt": boundTime,
        };
        if (page) {
            params.page = page;
        }
        if (size) {
            params.size = size;
        }
        if (sort) {
            params.sort = sort + (direction ? ("," + direction) : "");
        }
        return API.get(Url.WEB.TRACKS_MOCEAN_TEMPERATURES, params, null, callback);
    },

    putMoceanTemperaturesBounds: (vin, licensePlate, temperatureFirstLowerBound, temperatureFirstUpperBound, temperatureSecondLowerBound, temperatureSecondUpperBound, callback) => {
        const params = {
            "vin": vin,
            "licensePlate": licensePlate,
            "temperatureFirstLowerBound": temperatureFirstLowerBound,
            "temperatureFirstUpperBound": temperatureFirstUpperBound,
            "temperatureSecondLowerBound": temperatureSecondLowerBound,
            "temperatureSecondUpperBound": temperatureSecondUpperBound,
        };
        return API.put(Url.WEB.TRACKS_MOCEAN_TEMPERATURES_BOUNDS, params, null, null, callback);
    },

    getMoceanDailyStat: (vin, licensePlate, originCriteriaDate, boundCriteriaDate, callback) => {
        const params = {
            "vin": vin,
            "licensePlate": licensePlate,
            "originCriteriaDate": originCriteriaDate,
            "boundCriteriaDate": boundCriteriaDate,
        };

        return API.get(Url.WEB.TRACKS_MOCEAN_DAILY_STAT, params, null, callback);
    },

    getMoceanWeeklyStat: (vin, licensePlate, originCriteriaYearWeek, boundCriteriaYearWeek, callback) => {

        const params = {
            "vin": vin,
            "licensePlate": licensePlate,
            "originCriteriaYearWeek": originCriteriaYearWeek,
            "boundCriteriaYearWeek": boundCriteriaYearWeek,
        };

        return API.get(Url.WEB.TRACKS_MOCEAN_WEEK_STAT, params, null, callback);
    },

    getMoceanMonthlyStat: (vin, licensePlate, originCriteriaYearMonth, boundCriteriaYearMonth, callback) => {

        const params = {
            "vin": vin,
            "licensePlate": licensePlate,
            "originCriteriaYearMonth": originCriteriaYearMonth,
            "boundCriteriaYearMonth": boundCriteriaYearMonth,
        };

        return API.get(Url.WEB.TRACKS_MOCEAN_MONTHLY_STAT, params, null, callback);
    },

    getProjectRiderInfo: (projectId, riderId, vehicleId, callback) => {
        return API.get(Url.WEB.GET_RIDER_PROJECT_INFO, {
            'projectId': projectId,
            'riderId': riderId,
            'vehicleId': vehicleId
        }, null, callback);
    },

    /*
     * 기사의 차량ID 설정 [demo]
     */
    updateVehicleIdOfRider: (riderId, vehicleTypeOfDemo, callback) => {
        return API.put(Url.WEB.RIDERS_DETAIL.replace("{riderId}", riderId)
            , null              // params
            , {vehicleTypeOfDemo: vehicleTypeOfDemo}  // body
            , null              // header
            , callback);        // callback
    },

    /*
     * 특정 프로젝트의 기사목록 삭제 [demo]
     */
    deleteAllOfProject: (projectId, callback) => {
        return API.delete(Url.WEB.RIDERS + "?projectId=" + projectId, null, null, callback);
    },

    /**
     * 기사 프로파일 이미지 조회
     */
    getRiderProfileImage: (riderId, callback) => {
        return API.get(Url.WEB.RIDER_PROFILE_IMAGE_URL.replace("{riderId}", riderId), null, null, callback);
    },

    /**
     * 기사 Org max count (기사 관리에 사용)
     */
    getOrgMaxRiderCount: (callback) => {
        return API.get(Url.WEB.RIDER_GET_ORG_MAX_COUNT, null, null, callback);
    },

    /**
     * 차량 정보를 이용한 rider name
     */
    getRidersNameList: (vehicleNumberList, callback) => {
        let params = {
            "vehicleNumberList": vehicleNumberList.join(',')
        };
        return API.get(Url.WEB.RIDERS_VEICLE_NUMBER_GET_RIDER, params, null, callback);
    },

    getGroupName: (riderId) => {
        let params = {
            "riderId": riderId
        };
        return API.getSync(Url.WEB.RIDERS_GROUP_NAME, params, null, null);
    },

    sendRiderPushMessage: (riderId, projectId, message, callback) => {
        const body = {
            riderId: riderId,
            projectId: projectId ? projectId : null,
            message: message
        };

        return API.post(Url.WEB.RIDER_PUSH_MESSAGE, null, body, null, callback);
    },

    sendRiderRequestDeliveryStatusPushMessage: (body, callback) => {
        return API.post(Url.WEB.RIDER_REQUEST_CHANGE_DELIVERY_STATUS, null, body, null, callback);
    }
};

/**
 * 경로탐색 API
 */
const ROUTE_API = {
    getRouteInfo: (country, startPoint, destPoints, routeoption, callback) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        API.post(Url.WEB.ROUTE
            , null
            , JSON.stringify({
                command: "RequestRoute"
                , reqid: Util.generateRandomId()
                , routeoption: routeoption
                , start: startPoint
                , destpos: destPoints
            })
            , headers
            , callback);
    },

    getRouteInfoSync: (country, startPoint, destPoints, routeoption, currentDrivableDistance, maxDrivableDistance) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        const data = JSON.stringify({
            command: "RequestRoute"
            , reqid: Util.generateRandomId()
            , routeoption: routeoption
            , start: startPoint
            , destpos: destPoints
        });

        return axios.post(Url.WEB.ROUTE, data, {headers: headers});
    },

    getRouteInfoSyncV2: (reqObject) => {
        const headers = {'Content-Type': 'application/json', "code": reqObject.country};

        const body = JSON.stringify({
            command: "RequestRoute"
            , reqid: Util.generateRandomId()
            , routeoption: reqObject.routeoption ? reqObject.routeoption : reqObject.routeOption// TODO
            , start: reqObject.startPoint
            , destpos: reqObject.destPoints
            , riderId: reqObject.riderId
            /*options*/
            , currentDrivableDistance: reqObject.currentDrivableDistance ? reqObject.currentDrivableDistance : 0 //[demo] 현재 이동 가능 거리
            , maxDrivableDistance: reqObject.maxDrivableDistance ? reqObject.maxDrivableDistance : 0			//[demo] 최대 이동 가능 거리
            , isFindEvStationOnRoute: reqObject.isFindEvStationOnRoute ? reqObject.isFindEvStationOnRoute : false //[demo] EV_Demo 경로상에 있는 EvStation을 검색할건지의 옵션
        });

        return axios.post(Url.WEB.ROUTE, body, {headers: headers});
    },

    getRouteInfoSyncReservationTime: (reqObject) => {
        const headers = {'Content-Type': 'application/json', "code": reqObject.country};

        const body = JSON.stringify({
            command: "RequestRoute"
            , reqid: Util.generateRandomId()
            , routeoption: reqObject.routeoption ? reqObject.routeoption : reqObject.routeOption// TODO
            , start: reqObject.startPoint
            , destpos: reqObject.destPoints
            , riderId: reqObject.riderId
            /*options*/
            , currentDrivableDistance: reqObject.currentDrivableDistance ? reqObject.currentDrivableDistance : 0 //[demo] 현재 이동 가능 거리
            , maxDrivableDistance: reqObject.maxDrivableDistance ? reqObject.maxDrivableDistance : 0			//[demo] 최대 이동 가능 거리
            , isFindEvStationOnRoute: reqObject.isFindEvStationOnRoute ? reqObject.isFindEvStationOnRoute : false //[demo] EV_Demo 경로상에 있는 EvStation을 검색할건지의 옵션

            , destinationPosInfoList: reqObject.destPoints//예약 시간 요청
            , startTime: reqObject.startTime
        });

        return axios.post(Url.WEB.ROUTE_RESERVATION, body, {headers: headers});
    },

    getMppInfo: (country, x, y, angle, distance, callback) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        API.post(Url.WEB.MPP
            , null
            , JSON.stringify({
                x: x,
                y: y,
                angle: angle,
                distance: distance
            })
            , headers
            , callback);
    },

    getMppInfoSync: (country, x, y, angle, distance, mppOptions) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        const data = JSON.stringify({
            dx: x,
            dy: y,
            dir: angle,
            meter: distance,
            subdepth: mppOptions.subPathDepth,
            subquerymeter: mppOptions.subPathQueryRadius,
            submeter: mppOptions.subPathLength
        });

        console.log("[MPP] call getMppInfoSync : " + data);

        return axios.post(Url.WEB.MPP, data, {headers: headers});
    },
};

/**
 * 클러스터링 API
 */
const CLUSTER_API = {

    /**
     * 클러스터링 결과
     */
    getClusterInfo: (country, group, data, callback) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        API.post(Url.WEB.CLUSTER
            , null
            , JSON.stringify({
                command: "ReqClust",
                vehipos: DemoUtil.getVehiposFromGroup(group),
                destpos: DemoUtil.getDestposFromData(data)
            })
            , headers
            , callback);
    },

    getClusterInfoSync: (country, group, data) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        const body = JSON.stringify({
            command: "ReqClust",
            vehipos: DemoUtil.getVehiposFromGroup(group),
            destpos: DemoUtil.getDestposFromData(data)
        });
        return axios.post(Url.WEB.CLUSTER, body, {headers: headers});
    },

    getClusterInfoSyncByGroupCount: (country, groupCount, data) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        const body = JSON.stringify({
            command: "ReqClust",
            vehipos: null,
            destpos: DemoUtil.getDestposFromData(data),
            vehicnt: groupCount,
        });
        return axios.post(Url.WEB.CLUSTER, body, {headers: headers});
    },


    getClusterInfoSyncByReservationTime: (country, group, data) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        const body = JSON.stringify({
            command: "ReqClust",
            vehicnt: group.length,
            vehipos: DemoUtil.getVehiposFromGroupWithTime(group),
            destpos: DemoUtil.getDestposFromDataWithTime(data)
        });
        return axios.post(Url.WEB.CLUSTER_RESERVATION, body, {headers: headers});
    }
};

const SEARCH_API = {

    /**
     * 주소 검색 by String
     */
    getSmartSearchByString: (country, keyword) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        try {
            return API.getSync(Url.WEB.SMART_SEARCH, {keyword: keyword}, {headers: headers});
        } catch (error) {
            console.log(error);
            const warnStr = _t("주소 검색 실패");
            PopupUtil.alertPopup(warnStr + ` : ${keyword}`);
        }
    },

    /**
     * 주소 검색 by String
     */
    getAddressByString: (country, keyword) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        try {
            return API.getSync(Url.WEB.SEARCH, {keyword: keyword}, {headers: headers});
        } catch (error) {
            console.log(error);
            const warnStr = _t("주소 검색 실패");
            PopupUtil.alertPopup(warnStr + ` : ${keyword}`);
        }
    },

    /**
     * 주소 상세 검색 by String
     */
    getExtendAddressByString: (country, keyword) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        try {
            return API.getSync(Url.WEB.EXTEND_SEARCH, {keyword: keyword}, {headers: headers});
        } catch (error) {
            console.log(error);
            const warnStr = _t("주소 검색 실패");
            PopupUtil.alertPopup(warnStr + ` : ${keyword}`);
        }
    },

    /**
     * 주소 검색 by Position
     */
    getAddressByPosition: (country, x, y, callback) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        try {
            //return API.get(Url.TMS_CORE.SEARCH_COORD, {x: x, y: y}, { headers: headers}, callback);
            return API.getSync(Url.WEB.SEARCH_COORD, {x: x, y: y}, {headers: headers});
        } catch (error) {
            console.log(error);
            const warnStr = _t("주소 검색 실패");
            PopupUtil.alertPopup(warnStr + ` : x:${x}, y:${y}`);
        }
    },

    getExtendAddressByPosition: (country, x, y, callback) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        try {
            //return API.get(Url.TMS_CORE.SEARCH_COORD, {x: x, y: y}, { headers: headers}, callback);
            return API.getSync(Url.WEB.EXTEND_SEARCH_COORD, {x: x, y: y}, {headers: headers});
        } catch (error) {
            console.log(error);
            const warnStr = _t("주소 검색 실패");
            PopupUtil.alertPopup(warnStr + ` : x:${x}, y:${y}`);
        }
    },

    getEnteranceByPosition: (country, x, y, callback) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        try {
            return API.get(Url.WEB.SEARCH_ENTRANCE, {x: x, y: y}, {headers: headers}, callback);
        } catch (error) {
            console.log("입구점 검색 실패" + error);
        }
    },
};

const RANGE_PROJECTION_API = {

    /*
     * 차량 이동 가능 영역 조회
     */
    getRangeProjectionInfo: (country, x, y, angle, distance, range, callback) => {
        const headers = {'Content-Type': 'application/json', "code": country};

        let body = {};
        body.dx = x;			// 경도
        body.dy = y;			// 위도
        body.dir = angle;		// 각도 (on 차량)
        body.meter = distance;	// 거리 (남은 연로로 갈수 있는 거리)
        body.range = range;		// 영역 표현 각도

        return API.post(Url.WEB.RANGE_PROJECTION
            , null
            , body
            , headers
            , callback);
    },
};


/**
 * TMS-WEB API
 */
const TMS_WEB_API = {

    /**
     * 관제 웹 버전 정보 불러 오기
     */
    getWebVersion: () => {
        try {
            return API.getSync(Url.WEB.VERSION);
        } catch (error) {
            console.log(error);
        }
    },

    /**
     * 경로  버전 정보 불러 오기
     */
    getRouteVersion: () => {
        try {
            return API.getSync(Url.WEB.ROUTE_VERSION);
        } catch (error) {
            console.log(error);
        }
    },

    /**
     * 검색 웹 버전 정보 불러 오기
     */
    getSearchVersion: () => {
        try {
            return API.getSync(Url.WEB.SEARCH_VERSION);
        } catch (error) {
            console.log(error);
        }
    },

    /**
     * 맵 버전 정보 불러 오기
     */
    getMapVersion: () => {
        try {
            let version_url = MapUtil.getMapServerURL();
            version_url += "/version";
            const res = axios.get(version_url);
            return res;
        } catch (error) {
            console.log(error);
        }
    },

    /**
     * 세션에 정보 저장
     */
    setSessionAttribute: (key, value, callback) => {
        let attr = {};
        attr[key] = value;
        API.post(Url.WEB.SESSION_ATTR, null, attr, null, callback);
    },

    getAuthUserInfo: (callback) => {
        return API.get(Url.WEB.USER_AUTH, null, null, callback);
    },

    /**
     * 기사 및 vehicle등록
     */
    registerRiderVehicle: (riderVehicleInfo, isRiderAdd, callback) => {
        const headers = {'Content-Type': 'application/json'};

        let params = null;

        if (isRiderAdd)
            params = {"isRiderAdd": isRiderAdd};


        let body = {
            "groupName": riderVehicleInfo.groupName,
            "name": riderVehicleInfo.name,
            "licensePlate": riderVehicleInfo.licensePlate,
            "mobile": riderVehicleInfo.mobile,
            "position": riderVehicleInfo.position,
            "workingStartAddress": riderVehicleInfo.workingStartAddress,
            "workingEndAddress": riderVehicleInfo.workingEndAddress,
            "modelName": riderVehicleInfo.modelName,
            "milesType": riderVehicleInfo.milesType,
            "workingStartTime": riderVehicleInfo.workingStartTime,
            "workingEndTime": riderVehicleInfo.workingEndTime,
            "lunchStartTime": riderVehicleInfo.lunchStartTime,
            "lunchEndTime": riderVehicleInfo.lunchEndTime,
            "payload": Util.isFloat(riderVehicleInfo.payload) ? riderVehicleInfo.payload : null,
            "skillLevel": riderVehicleInfo.skillLevel,
            "note": riderVehicleInfo.note,
            "projectId": riderVehicleInfo.projectId,
            "riderId": riderVehicleInfo.riderId,
            "vehicleType": riderVehicleInfo.vehicleType,
            "fuelType": riderVehicleInfo.fuelType,
            "currentDistanceCapacity": riderVehicleInfo.currentDistanceCapacity,
            "maxDistanceCapacity": riderVehicleInfo.maxDistanceCapacity,
            "autoCreate": riderVehicleInfo.autoCreate,
            "workAuthority": riderVehicleInfo.workAuthority,
            "dispatchNumber": riderVehicleInfo.dispatchNumber ? parseInt(riderVehicleInfo.dispatchNumber) : null,
            "loadingLength": riderVehicleInfo.loadingLength ? parseInt(riderVehicleInfo.loadingLength) : null,
            "loadingWidth": riderVehicleInfo.loadingWidth ? parseInt(riderVehicleInfo.loadingWidth) : null,
            "loadingHeight": riderVehicleInfo.loadingHeight ? parseInt(riderVehicleInfo.loadingHeight) : null,
        };
        return API.post(Url.WEB.RIDERS_VEHICLES, params, body, headers, callback);
    },

    /**
     * 가배차 기사들 저장 및 프로젝트 할당
     */
    addSimulationRidersAndProjectAdd: (projectId, data, callback) => {
        const headers = {'Content-Type': 'application/json'};

        let body = {
            "projectId": projectId,
            "riderCount": parseInt(data.riderCount),
            "carStartNumber": parseInt(data.carStartNumber),
            "address": data.address,
        };

        return API.post(Url.WEB.SIMULATION_ADD_RIDERS, null, body, headers, callback);
    },

    setSimulationRiderChangedRiderProject: (projectId, simulationRiderId, riderId, callback) => {
        const headers = {'Content-Type': 'application/json'};
        let body = {
            projectId: projectId,
            riderId1: simulationRiderId,
            riderId2: riderId,
        };

        return API.post(Url.WEB.SIMULATION_RIDER_TO_DISPATCH_RIDER_PROJECT, null, body, headers, callback);
    },

    /**
     * project에 속한 rider 제거 (rider삭제만 아니고 rider가 속한 project만 빼기한....
     */
    deleteRiderBelongToProject: (riderList, projectId, callback) => {
        const headers = {'Content-Type': 'application/json'};
        let params = null;
        let body = {
            "riderIds": riderList,
            "projectId": projectId
        };

        return API.post(Url.WEB.D_RIDERS, params, body, headers, callback);
    },

    deleteDelivery: (deliveryList, projectId, callback) => {
        const headers = {'Content-Type': 'application/json'};
        let params = null;
        let body = {
            "deliveries": deliveryList,
            "projectId": projectId
        };
        return API.post(Url.WEB.D_DELIVERY, params, body, headers, callback);
    },

    saveDelivery: (deliveryInfo, callback) => {
        const headers = {'Content-Type': 'application/json'};
        let params = null;
        let body = {
            "deliveryId": deliveryInfo.deliveryId,//업데이트용
            "groupName": deliveryInfo.groupName,
            "orderNum": deliveryInfo.orderNum,
            "receiverName": deliveryInfo.receiverName,
            "baseAddr": deliveryInfo.baseAddr,
            "detailAddr": deliveryInfo.detailAddr,
            "type": deliveryInfo.type,
            "visitType": deliveryInfo.visitType,
            "receiverPhoneNumber": deliveryInfo.receiverPhoneNumber,
            "receiverOwner": deliveryInfo.receiverOwner,
            "duration": deliveryInfo.duration,
            "startTime": deliveryInfo.deliveryStartTime,
            "endTime": deliveryInfo.deliveryEndTime,
            "productName": deliveryInfo.productName,
            "productSize": deliveryInfo.productSize,
            "productQuantity": deliveryInfo.productQuantity,
            "note": deliveryInfo.note,
            "projectId": deliveryInfo.projectId,
            "riderId": deliveryInfo.riderId,
            "deliveryType": deliveryInfo.deliveryType,
            "deliveryStartTime": deliveryInfo.deliveryStartTime,
            "deliveryEndTime": deliveryInfo.deliveryEndTime,
            "customerOrderId": deliveryInfo.customerOrderId,
            "customerProductImageUrl": deliveryInfo.customerProductImageUrl,
            "userDefinedOrderNum": deliveryInfo.userDefinedOrderNum,
            "trackingNumber": deliveryInfo.trackingNumber,
            "deliveryStatus": deliveryInfo.deliveryStatus,
            "qrBarCode": deliveryInfo.qrBarCode,
            "inspectionStatus": deliveryInfo.inspectionStatus,
            "productWidth": deliveryInfo.productWidth,
            "productLength": deliveryInfo.productLength,
            "productHeight": deliveryInfo.productHeight,
            "productWeight": deliveryInfo.productWeight,
        };
        return API.post(Url.WEB.SAVE_DELIVERY, params, body, headers, callback);
    },

    getGroupNameList: (projectId, callback) => {
        let params = {"projectId": projectId};

        return API.get(Url.WEB.GET_GROUPNAMES, params, null, callback);
    },

    getNotiList: (projectId, callback) => {
        let params = {"projectId": projectId};

        return API.get(Url.WEB.NOTIFICATION, params, null, callback);
    },

    updateNotiList: (notiIdList, projectId, callback) => {
        let params = null;
        const headers = {'Content-Type': 'application/json'};
        let body = {
            "notificationIdList": notiIdList,
            "projectId": projectId
        };
        return API.put(Url.WEB.NOTIFICATION, params, body, headers, callback);

    },

    getNotiCount: (projectId, callback) => {
        let params = {"projectId": projectId};

        return API.get(Url.WEB.NOTIFICATION_COUNT, params, null, callback);
    },

    updateNotiByDeleteDestination: (deliveryId, callback) => {
        console.log("[updateNotiByDeleteDestination] deliveryId: " + deliveryId);
        let body = {"deliveryId": deliveryId};
        return API.put(Url.WEB.NOTIFICATION_DELETE_DESTINATION, null, body, null, callback);
    },

    checkLicensePlateExists: (licensePlate, callback) => {
        const params = {"licensePlate": licensePlate};
        return API.get(Url.WEB.CHECK_LICENSE_PLATE_EXISTS, params, null, callback);
    },

    checkRiderMobileExists: (mobile, callback) => {
        const params = {"mobile": mobile};
        return API.get(Url.WEB.CHECK_RIDER_MOBILE_EXISTS, params, null, callback);
    },

    checkRiderWorkingTime: (projectId) => {
        return API.getSync(Url.WEB.CHECK_RIDER_WORKING_TIME, {"projectId": projectId}, null);
    },

    getDeliveryTypes: (callback) => {
        return API.get(Url.WEB.ENUM_DELIVERY_TYPES, null, null, callback);
    },

    getVisitTypes: (callback) => {
        return API.get(Url.WEB.ENUM_VISIT_TYPES, null, null, callback);
    },

    getDeliveryStatus: (callback) => {
        return API.get(Url.WEB.ENUM_DELIVERY_STATUS, null, null, callback);
    },

    getDeliveryCompletedTypes: (callback) => {
        return API.get(Url.WEB.ENUM_DELIVERY_COMPLETED_TYPES, null, null, callback);
    },

    getDeliveryFailureTypes: (callback) => {
        return API.get(Url.WEB.ENUM_DELIVERY_FAILURE_TYPES, null, null, callback);
    },

    getCargoSubTypes: (callback) => {
        return API.get(Url.WEB.ENUM_CARGO_SUB_TYPES, null, null, callback);
    },

    getCargoTypes: (callback) => {
        return API.get(Url.WEB.ENUM_CARGO_TYPES, null, null, callback);
    },

    getFuelTypes: (callback) => {
        return API.get(Url.WEB.ENUM_FUEL_TYPES, null, null, callback);
    },

    getMilesTypes: (callback) => {
        return API.get(Url.WEB.ENUM_MILES_TYPES, null, null, callback);
    },

    getVehicleSizeTypes: (callback) => {
        return API.get(Url.WEB.ENUM_VEHICLE_SIZE_TYPES, null, null, callback);
    },

    getVehicleTypes: (callback) => {
        return API.get(Url.WEB.ENUM_VEHICLE_TYPES, null, null, callback);
    },

    getWheelDriveTypes: (callback) => {
        return API.get(Url.WEB.ENUM_WHEEL_DRIVE_TYPES, null, null, callback);
    },

    getProductSizeTypes: (callback) => {
        return API.get(Url.WEB.ENUM_PRODUCT_SIZE_TYPES, null, null, callback);
    },

    getWorkAuthorities: (callback) => {
        return API.get(Url.WEB.ENUM_WORK_AUTHORITIES, null, null, callback);
    },

    getDeliveryStatusInfo: (riderId, deliveryId, callback) => {
        return API.get(Url.WEB.DELIVERY_STATUS.replace("{deliveryId}", deliveryId),
            {riderId: riderId},
            null, callback);
    },

    updateDeliveryStatusInfo: (riderId, deliveryId, data, callback) => {
        return API.put(Url.WEB.DELIVERY_STATUS.replace("{deliveryId}", deliveryId) + "?riderId=" + riderId,
            null,
            data, null, callback);
    },


    updateForceDeliveryStatusInfoDemo: (riderId, deliveryId, data, callback) => {
        return API.put(Url.WEB.DELIVERY_FORCE_STATUS.replace("{deliveryId}", deliveryId) + "?riderId=" + riderId,
            null,
            data, null, callback);
    },

    updateForceDeliveryStatusInfo: (projectId, deliveryId, data, callback) => {
        const params = {
            "deliveryIds": deliveryId,
        };
        return API.put(Url.WEB.DELIVERIES_FORCE_STATUS,
            params,
            data, null, callback);
    },

    //글로비스용 상태 리스트 조회
    getGlovisDeliveryStatus: (invoiceNumber, callback) => {
        return API.get(Url.WEB.GLOVIS_DELIVERY_STATUS,
            {"invoiceNumber": invoiceNumber}, null, callback);
    },

    cancelDelivery: (deliveryId, callback) => {
        const headers = {'Content-Type': 'application/json'};
        let params = null;
        let body = {"deliveryId": deliveryId};
        return API.post(Url.WEB.DELIVERIES_CANCEL, params, body, headers, callback);
    },

};

const DOWNLOAD_API = {
    getProjectSample: () => {
        API.getDownload(Url.WEB.PROJECT_SAMPLE, null, null, {
            onSuccess: (response) => {
                var fileURL = window.URL.createObjectURL(new Blob([response.data], {type: response.headers['content-type']}));
                var fileLink = document.createElement('a');
                fileLink.href = fileURL;
                fileLink.setAttribute('download', 'project_sample.xlsx');
                document.body.appendChild(fileLink);
                fileLink.click();
            },
            onError: (error) => {
                console.log(error);
            }
        });
    },
    getProjectExcel: (projectId) => {
        API.getDownload(Url.WEB.PROJECT_EXCEL + "?projectId=" + projectId, null, null, {
            onSuccess: (response) => {
                var fileURL = window.URL.createObjectURL(new Blob([response.data], {type: response.headers['content-type']}));
                var fileLink = document.createElement('a');
                fileLink.href = fileURL;
                fileLink.setAttribute('download', 'project_sample.xlsx');
                document.body.appendChild(fileLink);
                fileLink.click();
            },
            onError: (error) => {
                console.log(error);
            }
        });
    },
};

const PROJECT_API = {

    createEmptyProject: (callback) => {
        return API.post(Url.WEB.PROJECT, null, null, null, callback);
    },

    loadEmptyProjectOrCreateIfNotExists: (callback) => {
        return API.get(Url.WEB.PROJECT_EMPTY_PROJECT, null, null, callback);
    },

    getProjectInfo: (projectId, callback) => {
        //  /api/projects/{projectId}
        return API.get(Url.WEB.PROJECT_DETAIL.replace("{projectId}", projectId), null, null, callback);
    },

    deleteProject: (projectId, callback) => {
        return API.delete(Url.WEB.PROJECT_DETAIL.replace("{projectId}", projectId), null, null, callback);
    },

    getProjectListInfo: (page, projectStatus, keyword, size, sort, direction, fromDateTime, toDateTime, callback) => {
        let params = {
            "page": page,
            "size": size,
            "sort": sort ? sort + "," + direction : sort,
            "projectStatusList": projectStatus,
            "keyword": keyword,
            "fromDateTime": fromDateTime,
            "toDateTime": toDateTime
        };
        return API.get(Url.WEB.PROJECT, params, null, callback);
    },

    /**
     * 프로젝트들 삭제 (프로젝트 관리에 사용)
     */
    deleteProjectsForManagement: (projectIdList, callback) => {
        const params = {"type": "manage", "projectIdList": projectIdList.join(','),};
        return API.delete(Url.WEB.PROJECT, params, null, callback);
    },

    /**
     * 프로젝트 실행일시 및 속성 설정
     */
    setEffectiveDateTime: (projectId, effectiveDateTime, attribute) => {
        try {
            return API.putSync(Url.WEB.PROJECT_DETAIL.replace("{projectId}", projectId), null, {
                id: projectId,
                effectiveDateTime: effectiveDateTime,
                attribute: attribute
            });
        } catch (error) {
            PopupUtil.alertPopup('프로젝트 실행일 설정 실패 : projectId(' + projectId + ')');
        }
    },

    /**
     * 프로젝트 속성 변경
     */
    setProjectAttribute: (projectId, attribute) => {
        try {
            return API.putSync(Url.WEB.PROJECT_DETAIL.replace("{projectId}", projectId), null, {
                id: projectId,
                attribute: attribute
            });
        } catch (error) {
            PopupUtil.alertPopup('프로젝트 속성 변경: projectId(' + projectId + ')');
        }
    },

    /**
     * 프로젝트 마감시간(cutofftime) 설정
     */
    setCutoffTime: (projectId, cutoffTime) => {
        try {
            return API.putSync(Url.WEB.PROJECT_DETAIL.replace("{projectId}", projectId), null, {
                id: projectId,
                cutoffTime: cutoffTime
            });
        } catch (error) {
            PopupUtil.alertPopup('프로젝트 마감일 설정 실패 : projectId(' + projectId + ')');
        }
    },

    /**
     * 경로 생성 및 배송 순서 요청
     */
    getDeliveryOrdersAndRoutes: (projectId, routeOption, callback) => {
        return API.get(Url.WEB.PROJECT_ROUTE.replace("{projectId}", projectId) + "?routeOption=" + routeOption, null, null, callback);
    },


    /**
     * 배차 실행
     */
    getDeliveryClustering: (projectId, clusterRuleOption, callback) => {
        return API.get(Url.WEB.PROJECT_CLUSTER.replace("{projectId}", projectId) + "?clusterRuleOption=" + clusterRuleOption, null, null, callback);
    },

    /**
     * 배차 실행
     */
    getDeliveryClusteringByRider: (projectId, riderId, callback) => {
        return API.get(Url.WEB.PROJECT_CLUSTER_RIDER.replace("{projectId}", projectId) + "?riderId=" + riderId, null, null, callback);
    },


    /**
     * @deprecated
     * 배차 변경
     */
    changeDeliveryClustering: (deliveryId, updateCluster, callback) => {
        return API.put(Url.WEB.DELIVERY_CLUSTER + "?deliveryId=" + deliveryId, null, updateCluster, null, callback);
    },


    /**
     * 배차 변경
     */
    changeDeliveriesClustering: (deliveryIdList, updateCluster, callback) => {
        return API.put(Url.WEB.DELIVERIES_CLUSTER + "?deliveryIds=" + deliveryIdList.join(","), null, updateCluster, null, callback);
    },

    /**
     * riders 배차 변경
     */
    putSwitchRidersClustering: (projectId, dragDestRiderId, dropItemRiderId, callback) => {
        let body = {
            "projectId": projectId,
            "riderId1": dragDestRiderId,
            "riderId2": dropItemRiderId
        }
        return API.put(Url.WEB.RIDERS_SWITCH_CLUSTERING, null, body, null, callback);
    },

    /**
     * 특정 라이더 리스트의 경로 생성 및 배송 순서 요청
     */
    getDeliveryOrdersAndRoutesByRider: (projectId, routeOption, updatedRiderIds, callback) => {
        const headers = {'Content-Type': 'application/json'};
        let params = {
            routeOption: routeOption,
            withClustering: false, //경로 탐색만 수행하지 clustering은 하지 않는다
        };
        let body = {
            "riderIds": updatedRiderIds,
            "projectId": projectId
        };
        return API.post(Url.WEB.PROJECT_ROUTE.replace("{projectId}", projectId), params, body, headers, callback);
    },

    /**
     * 프로젝트 종료
     */
    terminateProject: (projectId, callback) => {
        return API.put(Url.WEB.PROJECT_TERMINATE.replace("{projectId}", projectId), null, null, null, callback);
    },

    createProjectFromData: (projectData, callback) => {
        return API.post(Url.WEB.PROJECT_ADD, null, projectData, null, callback);
    },

    createCopyNormalProject: (projectId, callback) => {
        return API.get(Url.WEB.PROJECT_COPY_PROJECT, {"projectId": projectId}, null, callback);
    },

    updateProjectName: (projectId, projectName, callback) => {
        const headers = {'Content-Type': 'application/json'};
        let body = {
            "name": projectName,
        };

        return API.put(Url.WEB.UPDATE_PROJECT_NAME.replace("{projectId}", projectId), null, body, headers, callback);
    },

    changeProjectDeliveryItems: (oldProjectId, newProjectId, callback) => {
        let params = {
            "oldProjectId": oldProjectId,
            "newProjectId": newProjectId
        };
        return API.get(Url.WEB.PROJECT_DELIVERY_CHANGED, params, null, callback);
    },

    transferProject: (projectId, callback) => {
        let params = {
            "projectId": projectId,
        };
        return API.get(Url.WEB.PROJECT_TRANSFER_PROJECT, params, null, callback);
    },

    pushProjectToRiders: (projectId, projectMqttList, callback) => {
        return API.post(Url.WEB.PROJECT_PUSH_RIDER.replace("{projectId}", projectId), null, projectMqttList, null, callback);
    },

    getProjectExcelByPeriod: (params, callback) => {
        return API.get(Url.WEB.PROJECT_EXCEL_BY_PERIOD, params, null, callback);
    },

    setAutoAddRider: (projectId, callback) => {
        return API.get(Url.WEB.PROJECT_AUTO_ADD_RIDER, {projectId: projectId}, null, callback);
    },

    setAutoAddDispatchRider: (projectId, callback) => {
        return API.get(Url.WEB.PROJECT_SIMULATION_RIDERS_AUTO_CHANGED_DISPATCH_RIDERS, {projectId: projectId}, null, callback);
    },

    sendDispatchToOMS: (projectId, callback) => {
        return API.get(Url.WEB.PROJECT_SEND_DISPATCH_TO_OMS, {projectId: projectId}, null, callback);
    }

};

const PRODUCT_API = {
    getRidersProducts: (projectId, callback) => {
        return API.get(Url.WEB.PRODUCT_PROJECT, {"projectId": projectId}, null, callback);
    },

    getProductsOverallStatusByProject: (projectId, callback) => {
        return API.get(Url.WEB.PRODUCT_OVERALL_STATUS, {"projectId": projectId}, null, callback);
    },
};

const VEHICLE_API = { // ywlee vehicleModel api추가..
    getVehicleInfo: (callback) => {
        let params = null;
        let headers = null;
        //==>   /api/vehicleModel
        return API.get(Url.WEB.VEHICLEMODELS, params, headers, callback);
    },
};

const STAT_API = {
    getStatRiderTotalDeliveries: (riderId, page, size, keyword, callback) => {
        return API.get(Url.WEB.STAT_RIDER_TOTAL_DELIVERIES,
            {
                "riderId": riderId,
                "page": page,
                "size": size,
                "likeRiderName": keyword,
            },
            null,
            callback);
    },

    getStatRidersYearlyDeliveries: (riderIdList, keyword, fromYear, toYear, callback) => {
        return API.get(Url.WEB.STAT_RIDER_YEARLY_DELIVERIES + "?riderIdList=" + riderIdList.join(","),
            {
                "likeRiderName": keyword,
                "fromYear": fromYear,
                "toYear": toYear,
            },
            null,
            callback);
    },

    getStatRidersMonthlyDeliveries: (riderIdList, keyword, fromYearMonth, toYearMonth, callback) => {
        return API.get(Url.WEB.STAT_RIDER_MONTHLY_DELIVERIES + "?riderIdList=" + riderIdList.join(","),
            {
                "likeRiderName": keyword,
                "fromYearMonth": fromYearMonth,
                "toYearMonth": toYearMonth,
            },
            null,
            callback);
    },

    getStatRidersWeeklyDeliveries: (riderIdList, keyword, fromWeek, toWeek, callback) => {
        return API.get(Url.WEB.STAT_RIDER_WEEKLY_DELIVERIES + "?riderIdList=" + riderIdList.join(","),
            {
                "likeRiderName": keyword,
                "fromWeek": fromWeek,
                "toWeek": toWeek,
            },
            null,
            callback);
    },

    getStatRidersDailyDeliveries: (riderIdList, keyword, fromDate, toDate, callback) => {
        return API.get(Url.WEB.STAT_RIDER_DAILY_DELIVERIES + "?riderIdList=" + riderIdList.join(","),
            {
                "likeRiderName": keyword,
                "fromDate": fromDate,
                "toDate": toDate,
            },
            null,
            callback);
    },

    getStatRiderDailyProjectDeliveries: (riderId, keyword, callback) => {
        return API.get(Url.WEB.STAT_RIDER_DAILY_PROJECT_DELIVERIES,
            {
                "riderId": riderId,
                "likeRiderName": keyword,
            },
            null,
            callback);
    },

    getStatProjectDeliveries: (projectId, page, size, keyword, callback) => {
        return API.get(Url.WEB.STAT_PROJECT_DELIVERIES,
            {
                "projectId": projectId,
                "page": page,
                "size": size,
                "likeProjectName": keyword,
            },
            null,
            callback);
    },

    getStatGeneralTotalDeliveries: (callback) => {
        return API.get(Url.WEB.STAT_GENERAL_TOTAL_DELIVERIES,
            {},
            null,
            callback);
    },

    getStatGeneralYearlyDeliveries: (fromYear, toYear, callback) => {
        return API.get(Url.WEB.STAT_GENERAL_YEARLY_DELIVERIES,
            {
                "fromYear": fromYear,
                "toYear": toYear,
            },
            null,
            callback);
    },

    getStatGeneralMonthlyDeliveries: (fromYearMonth, toYearMonth, callback) => {
        return API.get(Url.WEB.STAT_GENERAL_MONTHLY_DELIVERIES,
            {
                "fromYearMonth": fromYearMonth,
                "toYearMonth": toYearMonth,
            },
            null,
            callback);
    },

    getStatGeneralWeeklyDeliveries: (fromWeek, toWeek, callback) => {
        return API.get(Url.WEB.STAT_GENERAL_WEEKLY_DELIVERIES,
            {
                "fromWeek": fromWeek,
                "toWeek": toWeek,
            },
            null,
            callback);
    },

    getStatGeneralDailyDeliveries: (fromDate, toDate, callback) => {
        return API.get(Url.WEB.STAT_GENERAL_DAILY_DELIVERIES,
            {
                "fromDate": fromDate,
                "toDate": toDate,
            },
            null,
            callback);
    },

    getStatGroupNameTotalDeliveries: (groupName, page, size, keyword, callback) => {
        return API.get(Url.WEB.STAT_GROUP_NAME_TOTAL_DELIVERIES,
            {
                "groupName": groupName,
                "page": page,
                "size": size,
                "likeGroupName": keyword,
            },
            null,
            callback);
    },

    getStatGroupNamesYearlyDeliveries: (groupNameList, keyword, fromYear, toYear, callback) => {
        return API.get(Url.WEB.STAT_GROUP_NAME_YEARLY_DELIVERIES + "?groupNameList=" + groupNameList.join(","),
            {
                "likeGroupName": keyword,
                "fromYear": fromYear,
                "toYear": toYear,
            },
            null,
            callback);
    },

    getStatGroupNamesMonthlyDeliveries: (groupNameList, keyword, fromYearMonth, toYearMonth, callback) => {
        return API.get(Url.WEB.STAT_GROUP_NAME_MONTHLY_DELIVERIES + "?groupNameList=" + groupNameList.join(","),
            {
                "likeGroupName": keyword,
                "fromYearMonth": fromYearMonth,
                "toYearMonth": toYearMonth,
            },
            null,
            callback);
    },

    getStatGroupNamesWeeklyDeliveries: (groupNameList, keyword, fromWeek, toWeek, callback) => {
        return API.get(Url.WEB.STAT_GROUP_NAME_WEEKLY_DELIVERIES + "?groupNameList=" + groupNameList.join(","),
            {
                "likeGroupName": keyword,
                "fromWeek": fromWeek,
                "toWeek": toWeek,
            },
            null,
            callback);
    },

    getStatGroupNamesDailyDeliveries: (groupNameList, keyword, fromDate, toDate, callback) => {
        return API.get(Url.WEB.STAT_GROUP_NAME_DAILY_DELIVERIES + "?groupNameList=" + groupNameList.join(","),
            {
                "likeGroupName": keyword,
                "fromDate": fromDate,
                "toDate": toDate,
            },
            null,
            callback);
    },

    getStatGroupNameDailyProjectDeliveries: (groupName, keyword, callback) => {
        return API.get(Url.WEB.STAT_GROUP_NAME_DAILY_PROJECT_DELIVERIES,
            {
                "groupName": groupName,
                "likeGroupName": keyword,
            },
            null,
            callback);
    },

};

const BO_API = {
    getOrganizationInfoList: (callback) => {
        return API.get(Url.WEB.WEB_BO_COMPANY_INFO_LIST, null, null, callback);
    },

    getOrganizationList: (callback) => {
        return API.get(Url.WEB.WEB_BO_COMPANY_LIST, null, null, callback);
    },

    getOrganizationUserList: (orgId, callback) => {
        return API.post(Url.WEB.WEB_BO_COMPANY_USER_LIST, {"orgId": orgId}, null, null, callback);
    },

    getOrganizationIdFamilyList: (orgId, callback) => {
        return API.get(Url.WEB.WEB_BO_DEPARTMENT_FAMILY_LIST, {"organizationId": orgId}, null, callback);
    },

    getRiderLocationsList: (pageIndex, callback) => {
        let params = {
            "page": pageIndex,
        }
        return API.get(Url.WEB.WEB_BO_RIDER_LOCATION_LIST, params, null, callback);
    },

    getDeliveryLocationsList: (pageIndex, callback) => {
        let params = {
            "page": pageIndex,
        }
        return API.get(Url.WEB.WEB_BO_DELIVERY_LOCATION_LIST, params, null, callback);
    },

    getRiderInfoAccessList: (pageIndex, callback) => {
        let params = {
            "page": pageIndex,
        }
        return API.get(Url.WEB.WEB_BO_RIDER_INFO_ACCESS_LIST, params, null, callback);
    },

    getRiderAccessList: (pageIndex, callback) => {
        let params = {
            "page": pageIndex,
        }
        return API.get(Url.WEB.WEB_BO_RIDER_ACCESS_LIST, params, null, callback);
    },

    getUserAccessList: (pageIndex, callback) => {
        let params = {
            "page": pageIndex,
        }
        return API.get(Url.WEB.WEB_BO_USER_ACCESS_LIST, params, null, callback);
    },

    postOrganizationModify: (body, callback) => {
        return API.post(Url.WEB.WEB_BO_ORGANIZATION_MODIFY, null, body, null, callback);
    },

    postOrganizationUseCaseModify: (body, callback) => {
        return API.post(Url.WEB.WEB_BO_ORGANIZATION_USE_CASE_MODIFY, null, body, null, callback);
    },

    postOrganizationUserModify: (body, callback) => {
        return API.post(Url.WEB.WEB_BO_ORGANIZATION_ADMIN_USER_MODIFY, null, body, null, callback);
    },

    deletedOrganization: (params, callback) => {
        return API.delete(Url.WEB.WEB_BO_ORGANIZATION_DELETED, params, null, callback);
    },

    postOrganizationAdd: (body, callback) => {
        return API.post(Url.WEB.WEB_BO_ORGANIZATION_ADD, null, body, null, callback);
    },

    /////////////////////user
    postUserEditModify: (body, callback) => {
        return API.post(Url.WEB.WEB_BO_USER_EDIT, null, body, null, callback);
    },

    postUserPasswordModify: (body, callback) => {
        return API.post(Url.WEB.WEB_BO_USER_PASSWORD, null, body, null, callback);
    },

    deletedUser: (params, callback) => {
        return API.delete(Url.WEB.WEB_BO_USER_DELETED, params, null, callback);
    },

    postUserAdd: (body, callback) => {
        return API.post(Url.WEB.WEB_BO_USER_ADD, null, body, null, callback);
    },
};

const CODE_SERVICE_API = {
    getCodeServiceList(callback) {
        return API.get(Url.WEB.GLOVIS_DELIVERY_CODES, null, null, callback);
    }
};

const DEPARTMENT_API = {
    getUserDepartmentsList(callback) {
        return API.get(Url.WEB.DEPARTMENTS_AUTHORIZED, null, null, callback);
    }
};