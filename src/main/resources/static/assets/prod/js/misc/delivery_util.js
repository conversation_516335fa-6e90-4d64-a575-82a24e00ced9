/**
 * Rider에 관련된 Delivery.
 *
 */
const DeliveryUtil = {

    getDeliveryStartTime(dest) {
        let ret="";
        if(dest.realStartTime) {
            ret = " / " + TimeUtil.changedTimeHourMinString(dest.realStartTime);
        }
        else if(dest.estimatedStartTime)
            ret = " / " + TimeUtil.changedTimeHourMinString(dest.estimatedStartTime);

        return ret;
    },

    getDeliveryEndTime(dest) {
        let ret="";
        if(dest.realEndTime)
            ret = " / " + TimeUtil.changedTimeHourMinString(dest.realEndTime);
        else if(dest.estimatedEndTime)
            ret = " / " + TimeUtil.changedTimeHourMinString(dest.estimatedEndTime);

        return ret;
    },

    getDeliveryLocality(dest){
        let ret="";
        if(dest.locality)
            ret = " / " + dest.locality;

        return ret;
    },

    getDestinationFontColor( dest ){
        let fontColor = "#22394d";
        if( dest.deliveryStatus ===  Constant.DELIVERY_STATUS.COMPLETED  ){
            fontColor = "#888888";
        }else if ( dest.deliveryStatus === Constant.DELIVERY_STATUS.FAILURE || dest.deliveryStatus ===  Constant.DELIVERY_STATUS.REJECTED  || dest.deliveryStatus ===  Constant.DELIVERY_STATUS.UNDELIVERED ){
            fontColor = "#FF6077";
        }else if ( dest.deliveryStatus ===  Constant.DELIVERY_STATUS.GOING ){
            fontColor = "#0098EE";
        }else if ( dest.deliveryStatus ===  Constant.DELIVERY_STATUS.SERVICING ){
            fontColor = "#0069A5";
        }

        return fontColor;
    },

    getDestinationStatusText( dest ){
        let fontText = "배송전";
        if( dest.deliveryStatus ===  Constant.DELIVERY_STATUS.COMPLETED  ){
            fontText = "배송완료";
        }else if ( dest.deliveryStatus === Constant.DELIVERY_STATUS.FAILURE || dest.deliveryStatus ===  Constant.DELIVERY_STATUS.REJECTED  || dest.deliveryStatus ===  Constant.DELIVERY_STATUS.UNDELIVERED ){
            fontText = "배송실패";
        }else if ( dest.deliveryStatus ===  Constant.DELIVERY_STATUS.GOING ){
            fontText = "배송중";
        }else if ( dest.deliveryStatus ===  Constant.DELIVERY_STATUS.SERVICING ){
            fontText = "서비스중";
        }

        return _t(fontText);
    },

    getDestinationTimeText( dest ){
        let fontText = "예정 시간";
        if( dest.deliveryStatus ===  Constant.DELIVERY_STATUS.COMPLETED  ){
            fontText = "완료 시간";
        }else if ( dest.deliveryStatus === Constant.DELIVERY_STATUS.FAILURE || dest.deliveryStatus ===  Constant.DELIVERY_STATUS.REJECTED  || dest.deliveryStatus ===  Constant.DELIVERY_STATUS.UNDELIVERED ){
            fontText = "실패 시간";
        }else if ( dest.deliveryStatus ===  Constant.DELIVERY_STATUS.GOING ){
            fontText = "배송중 시간";
        }else if ( dest.deliveryStatus ===  Constant.DELIVERY_STATUS.SERVICING ){
            fontText = "서비스중 시간";
        }

        return _t(fontText) + " : ";
    },

    // 배송 상태 표시
    getDispNameFromDeliveryStatus : function(deliveryStatus) {
        let str = "N/A";
        switch (deliveryStatus) {
            case Constant.DELIVERY_STATUS.WAITING:
                str = "배차대기";
                break;
            case Constant.DELIVERY_STATUS.READY:
                str = "배송전";
                break;
            case Constant.DELIVERY_STATUS.GOING:
                str = "배송중";
                break;
            case Constant.DELIVERY_STATUS.SERVICING:
                str = "서비스중";
                break;
            case Constant.DELIVERY_STATUS.FAILURE:
                str = "배송실패";
                break;
            case Constant.DELIVERY_STATUS.COMPLETED:
                str = "배송완료";
                break;
            case Constant.DELIVERY_STATUS.DELAYED:
                str = "지연배송";
                break;
            case Constant.DELIVERY_STATUS.UNDELIVERED:
                str = "미배송";
                break;
            default:
                str = "N/A";
                break;
        }

        return _t(str);
    },

}