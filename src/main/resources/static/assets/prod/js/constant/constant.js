const Constant = {
  PROFILE: {
    LOCAL: 'local',
    DEFAULT: 'default',
  },

  ROLE_TYPE: {
    ROLE_ADMIN: "ROLE_ADMIN",
    ROLE_ORG_ADMIN: "ROLE_ORG_ADMIN",
    ROLE_COMMON: "R<PERSON><PERSON>_COMMON",
  },

  POLLING_INTERVAL_MS: 30000,				// 정보 업데이트 주기: 30초

  TP: {									// tag prefix
    RIDER: 'rider',
    DESTINATION: 'destination_',
    ENTRANCE: 'aaa_entrance_',
    ENTRANCE_VEHICLE: 'aaa_entrance_v_',
    DESTINATION_LABEL: 'd_label_',
    DESTINATION_TAG_IMAGE: "d_tag_image_",
    DESTINATION_TAG_LABEL: "d_tag_label_",
    PICKUP: 'pickup',
    CLUSTER: 'cluster',
    ROUTE: 'route1_',
    ROUTE_BORDER: 'route0_',
    PLANNED_ROUTE: 'planned_route0_',
    WORK_LINE: 'aaaaa_walk_',
    RIDER_LABEL: 'l_label',
    RIDER_TAG_IMAGE: 'l_label_tag_image',
    SEARCH_ADDRESS: 'search_addr',
    SEARCH_ADDRESS_LABEL: 'search_addr_label',
    RANGE: 'range',
    RIDER_TIMESTAMP_IMAGE: 'timestamp_image',
    RIDER_TIMESTAMP_LABEL: 'timestamp_label',

    STATION_ON_ROUTE: 'station_on_route', //EV_Demo 추가
    WAYPOINT_STATION: 'waypoint_station',
    ALL_STATION: 'all_station',

    FUEL_GAUGE: 'fuel_gauge',

    PAST_PATH: 'past_route_path_',
  },

  MAPMODE: {
    EDIT: 'edit',					// 배차
    TRACKING: 'tracking',			// 관제
    MPP: 'mpp',						// Most Possible Path
    DEFAULT: 'edit',
  },

  PINMODE: {
    HUB: 0,							// 물류센터
    BRANCH: 1,						// 거점
    DESTINATION: 2,					// 배송지
    LOCATION_FIRST: 0,
    LOCATION_LAST: 2,

    HAILING_PICKUP: 3,						// Hailing(출발지)
    HAILING_DROPOFF: 4,						// Hailing(도착지)
    HAILING_FIRST: 3,
    HAILING_LAST: 4,

    TRUCK: 5,						// 배송 차량(트럭)
    BIKE: 6,						// 배송(바이크)
    PEDESTRIAN: 7,					// 배송(도보)
    EV: 8,							// 배송 차량(EV)
    RIDER_FIRST: 5,
    RIDER_LAST: 8,

    EV_STATION: 9,  //EV_Demo -EV_Station 핀
    DEFAULT: 0,


    FIRST_MILE_PICKUP: 10, //퍼스트 마일에 해당하는 픽업
    FIRST_MILE_DROPOFF: 11, //퍼스트 마일에 해당하는 픽업

    LAST_MILE_PICKUP: 12, //라스트 마일에 해당하는 픽업
    LAST_MILE_DROPOFF: 13, //라스트 마일에 해당하는 픽업

    LOCATION_PICKUP_DROPOFF_FIRST: 10,
    LOCATION_PICKUP_DROPOFF_LAST: 13,
  },

  MilesType: {
    FIRST_MILE: '거점',
    LAST_MILE: '방문지',	// 최종방문지
  },

  WorkAuthority: {
    DELIVERY: 'DELIVERY',
    SCANNING: 'SCANNING',
    DELIVERY_AND_SCANNING: 'DELIVERY_AND_SCANNING',

    getDriverDeliveryType: () => {
      return Constant.WorkAuthority.DELIVERY + ',' + Constant.WorkAuthority.DELIVERY_AND_SCANNING
    }
  },

  DestinationType: {
    HUB: '물류센터',
    INTERMEDIATE: "물류거점",
    FINAL: "방문지",
    RETURN: "회차지점",

    getPinMode: (deliveryType) => {
      const types = Object.values(Constant.DestinationType).filter(value => value && {}.toString.call(value) !== '[object Function]');
      for (let i = 0; i < types.length; i++) {
        if (types[i] == deliveryType) {
          return i;
        }
      }
    },

    getPickupDestinationType: (destType) => {
      if (destType == Constant.DestinationType.INTERMEDIATE) {
        return Constant.DestinationType.HUB;
      } else if (destType == Constant.DestinationType.FINAL) {
        return Constant.DestinationType.INTERMEDIATE;
      } else {
        return null;
      }
    },
  },

  RiderType: {
    TRUCK: 'TRUCK',
    BIKE: 'BIKE',
    PEDE: 'PEDESTRIAN',
    EV: 'EV',

    getPinMode: (riderType) => {
      const types = Object.values(Constant.RiderType).filter(value => value && {}.toString.call(value) !== '[object Function]');
      for (let i = 0; i < types.length; i++) {
        if (types[i] == riderType) {
          return i + 5;
        }
      }
    },
  },

  TOOLBAR_HEIGHT: 100, // 40(title) + 60(bottom)
  TOOLBAR_HEIGHT_TITLE: 40,

  MIN_LEFT_PANEL_WIDTH: 25,
  MAX_LEFT_PANEL_WIDTH: 375,

  LEFT_PANEL_CONTENT_INFO_WIDTH: 232,
  LEFT_PANEL_TOGGLE_BUTTON_WIDTH: 20,
  LEFT_PANEL_DEST_LIST_PANEL_WIDTH: 1323,  /*  destination-list-panel.css 의 값 확인  */

  DEFAULT_ORDER: 0,

  MAX_MAP_LEVEL: 12,
  MAP_LEVEL_RANGE: [{'from': 0, 'to': 3}, {'from': 4, 'to': 6}, {'from': 7, 'to': 8}, {'from': 9, 'to': 12}],

  DELIVERY_STATUS: {
    UNASSIGNED: '-',
    WAITING: 'WAITING',
    READY: 'READY',
    GOING: 'GOING',
    SERVICING: 'SERVICING',
    REJECTED: 'REJECTED',
    FAILURE: 'FAILURE',
    COMPLETED: 'COMPLETED',
    DELAYED: 'DELAYED',
    UNDELIVERED: 'UNDELIVERED',
    ISCSCHECKING: 'ISCSCHECKING',
  },

  //backend 사용하는 all status
  DELIVERY_STATUS_ALL: [
    'WAITING',
    'READY',
    'GOING',
    'SERVICING',
    'REJECTED',
    'FAILURE',
    'COMPLETED',
    'UNDELIVERED'
  ],

  DELIVERY_STATUS_READY: [
    'WAITING',
    'READY',
  ],

  VISIT_TYPE: {
    PICKUP: '상차',
    DROPOFF: '하차',
    DELIVERY: '배송',
    PREMIUM: '프리미엄',
    RETRIEVAL: '회수',
  },

  RIDER_STATUS: {
    WAITING: '배차대기',
    DISPATCHING: '배차중',
    DISPATCHED: '배차완료',
    GOING: '운행중',
    SERVICING: '전달중',
    RETRIEVING: '회수중',
    COMPLETED: '운행완료',
  },

  PROJECT_STATUS: {
    SCHEDULED: "예정",
    IN_PROGRESS: "관제중",
    DONE: "완료",
    UNKNOWN: "알 수 없음",
  },

  RIDER_WORK_STATUS: {
    NONE: "NONE",
    START_WORK: "START_WORK",
    START_WORK_LUNCH: "START_WORK_LUNCH",
    START_WORK_REST: "START_WORK_REST",
    END_WORK: "END_WORK",
    LEAVE: "LEAVE",
    SUSPENSION: "SUSPENSION",
    RETIREMENT: "RETIREMENT",
  },

  RIDER_DISPATCH_STATUS: {
    ACCEPTANCE: "ACCEPTANCE",
    REJECTION: "REJECTION",
  },

  NOTIFICATION_STATUS: {
    START: "운행시작",
    FAILURE: "배송실패",
    DELAYED: "배송지연",
    OFF_DUTY: "배송중단",
    ALL_COMPLETED: "모든배송완료",
    ACCIDENT: "사고발생",
    BREAKDOWN: "고장발생",
    PULLOVER: "정차차량",
    ROUTEDEVIATION: "경로이탈",
    RIDER_MESSAGE: "기사메세지",
    ETC: "기타문제발생",
    AWAY: "자리비움",
    SERVICING_DELAYED: "서비스지연",
    WORK_COMPLETION: "업무완료",
    NORMAL_DELIVERY: "정상배송",
    CORRESPONDENCE: "대응배송",
    RETRIEVAL: "정상회수",
    RETRIEVAL_FAILURE: "회수실패",
    ADD_DESTINATION: "방문지 추가",
    DELETE_DESTINATION: "방문지 삭제",
    ATTENDANCE: "출근",
    NO_ATTENDANCE: "미출근",
    LATE_ATTENDANCE: "지각",
    ON_DUTY: "근무중",
    DISPATCH_REJECTION: "배차거부",
    DISPATCH_ACCEPTANCE: "배차수락",
  },

  DESTINATION_PIN_MODE: {
    EDIT: 0,
    SET: 1,
    PAST: 2,
    FAILED: 3,
    FUTURE: 4,
    IMPOSSIBLE_ARRIVAL_IN_TIME: 5,
    MAX: 6,
  },

  WORKLOCATION_PIN_MODE: {
    START: 0,
    END: 1,
  },

  HAILING_TYPE: {
    PICKUP: 'PICKUP',
    DROP: 'DROP',
  },


  RANGE_PROJECTION: [
    // {name: '안전', color: { r: 145 ,g: 255, b: 183, a:70}, percent: 0.3},
    // {name: '중간', color: { r: 244 ,g: 255, b: 154, a:60}, percent: 0.6},
    {name: '위험', color: {r: 255, g: 192, b: 172, a: 60}, percent: 1},
  ],

  ROUTE_ECO_COLOR: {
    EXCELLENT: {r: 155, g: 89, b: 76},//#9B594C
    GOOD: {r: 112, g: 52, b: 84}, //#703454
    NORMAL: {r: 65, g: 100, b: 89},//#416459
    BAD: {r: 35, g: 58, b: 92},//#233a5c
    POOR: {r: 21, g: 60, b: 94},//#153c5e
  },


  MQTT_EVENT_TYPE: {
    NEW_PROJECT: 1,                   // 신규 프로젝트 생성
    UPDATE_DELIVERIES: 2,            // 배송 목록 업데이트
    UPDATE_DELIVERIES_ROUTE_PATH: 3, // 배송 경로 업데이트
    NEW_DELIVERY_ADDED: 4,           // 신규 배송지 추가
    UPDATE_CALL_ROUTE_PATH: 5,       // 콜앱 배송 경로 업데이트
    UPDATE_DELIVERY_STATUS: 6,       // 배송 상태 업데이트
    NOTIFICATION_OCCUR: 7,           // notification발생...
    SESSION_TIMEOUT: 8,              // 로그인 세션 타임아웃
    DUPLICATE_LOGIN: 9,              // 중복 로그인
    UPDATE_RIDER_WORK_STATUS: 10,    // 기사 근무상태 업데이트
    PROJECT_TERMINATE: 11,           // 프로젝트 종료
    NEW_DRIVING_REQUEST_ARRIVED: 12,	// 차량 운행 요청 (Pickup / Dropoff)
    UPDATE_RIDER_WORK_COMPLETION: 14,	// 기사 업무완료 업데이트
    LOAD_PROJECT: 16,              // 프로젝트 로드
    PROJECT_PRODUCT_LOADING: 17,           // 프로젝트 상품 상차 시작 알림
    UPDATE_PRODUCT_PAGE: 19,	// 상품 현황 페이지 업데이트
    UPDATE_RIDER_DISPATCH_STATUS: 20,    // 기사 배차상태 업데이트
    SESSION_INVALIDATION: 21,			// 로그인 세션 무효
    SESSION_LOGOUT_INVALIDATION: 22,           // 로그 아웃을 전달함.
    LOAD_OTHER_USER_WATCH_PROJECT: 23,                   // 다른 사용자에게 프로젝트 로드
    NEW_EBAY_DELIVERY_ADDED: 24, //ebay delivery add api 호출시
    UPDATE_DELIVERY_CS_STATUS: 30, //배송 CS 상태 업데이트

    UPDATE_DIALOG_MESSAGE: 100,   //다이얼 로그  업데이트


    RESET_PROJECT: 999,              // 프로젝트 리셋
  },

  MQTT_EVENT_STRING_TYPE: {
    NEW_PROJECT: "NEW_PROJECT",
    UPDATE_DELIVERIES: "UPDATE_DELIVERIES",
  },

  MQTT_SERVICE_TYPE: {
    WEB: "web",
    APP: "app",
  },

  MQTT_APP_TYPE: {
    MONITORING: "monitoring",
    USER: "user",
    RIDER: "rider",
    CALL: "call",
  },


  MAP_BOUNDS: {
    KR: {xMin: 125, xMax: 132, yMin: 33, yMax: 39}	// 남한 기준
  },


  //EV_Demo RouteDestinationType.java 참조한다. Enum과 동일한 스트링을 가져야 한다
  ROUTE_DESTINATION_TYPE: {
    GENERAL: "GENERAL", 		// 일반
    GAS_STATION: "GAS_STATION", 			// 주유소
    EV_STATION: "EV_STATION", 			// 전기차 충전소
    NG_STATION: "NG_STATION",			// 가스 충전소
    ETC: "ETC",					// 그 외
  },

  CONTEXT_MENU_ITEM: {//for Demo
    ITEM_NOTHING: 0X0000,

    ITEM_SHOW_RIDER_DETAIL: 0X0001,
    ITEM_SHOW_DEST_DETAIL: 0X0002,

    ITEM_SHOW_RANGE_PROJECTION: 0X0010,
    ITEM_HIDE_RANGE_PROJECTION: 0X0020,

    ITEM_DELETE_OBJECT_DEST: 0X0040,
    ITEM_DELETE_OBJECT_RIDER: 0X0080,

    ITEM_SELECT_STATION: 0X0100,
    ITEM_CANCEL_SELECT_STATION: 0X0200,

    ITEM_MULTIPLE_SELECT: 0X1000
  },

  STATION_CHARGE_STATE: {//EV_Demo
    NOT_ARRIVED: 0,
    CHARGING: 1,
    CHARGE_COMPLETED: 2,
  },

  RANGE_PROJECTION_ANGLE: 360,//EV_Demo 디폴트 값

  TRUCK_COLORS: {//EV_Demo Truck 이미지 추가
    BLACK: "BLACK",
    GREEN: "GREEN",
    RED: "RED",
  },

  DRAWING_RANGE_PROJECTION: {
    RIDER_RANGE: 0x0001,
    STATION_RANGE_PROJECTION: 0x0001,
    WAYPOINT_STATION: 0x0002,
    STATION_ON_ROUTE: 0x0004,
    ALL: 0xFFFF,
  },

  START_WAYPOINT_ID: 10000,

  WAYPOINT_STATION_SERVICE_DURATION: 1800,

  POPUP_TYPE: {
    NONE: 0,
    SEARCH_ADDRESS: 1,

    ENERGY_GAUGE: 2,//infoBox
    GAS_STATION: 3,

  },

  DEMO_NO_SELECTED_PROJECT_TEMP_ID: 2,

  /** 예약 배송 추가 **/
  TIME_FORMAT: {
    DATETIME_SECONDS: "YYYY-MM-DDTHH:mm:ss",
    DATETIME__MILLI_SECONDS: "YYYY-MM-DDTHH:mm:ss.SSSSSS",
    DATETIME: "YYYY-MM-DDTHH:mm",
    TIME: "HH:mm",
    TIME_SECONDS: "HH:mm:ss",
    DATE: "YYYY-MM-DD",
  },

  RESERVATION_MODE: {
    NONE: 0,
    FIRST_MILE_PICKUP: 1,
    FIRST_MILE_DROPOFF: 2,

    LAST_MILE_PICKUP: 3,
    LAST_MILE_DROPOFF: 4,

    NORMAL: 5, //Normal startReservation Time  endReservation Time
  },

  TRUCK_COLOR: {
    DEFAULT: "",
    MINT: "_m",//민트(옥색)
  },

  DEMO_ROUTING_MODE: {
    NORMAL: 0,
    // TIME_RESERVATION : 1, //NORMAL과 통합 한다.
    FULFILLMENT: 1,
  },

  DEMO_USER_ID: 1, //익명 사용자 로그인 시 사용된는 UserId는 1로 통일한다

  MILE_TYPE: {
    LAST_MILE: 0,
    FIRST_MILE: 1,
    FIRST_PICKUP_PLACE: 2,
    UNKNOWN: 99,
  },

  LANGUAGE: {
    ENGLISH: 0,
    KOREAN: 1,
    ENGLISH_UK: 2
  },


  DEFAULT_SETTING: {
    LANGUAGE: 1,  /* 0: English(US) , 1: Korean *, 2: English(UK) /

		//////사용 금지 ////////
		ROUTING_MODE : 0, /*  처음 로딩시에 Constant.DEMO_ROUTING_MODE.NORMAL  함. 디폴트값을 바꾸고 싶으면 여기를 바꾼다 Constant.DEMO_ROUTING_MODE.NORMAL */
    MOBILE_TRACKING_MODE: false, /* 처음 로딩시에 모바일 연동 모드 */
    //////사용 금지 ////////

    PROJECT_ID_FOR_MOBILE_APP: 2, /*모바일 연동시 기본이 되는 프로젝트 Id */
    SERVICE_DURATION_MINUTES: 3,  // 기본 디폴트 서비스 듀레이션(목적지에서 머무는 시간)
    MPP_ENABLED: false,
    // CLUSTER_MODE : 0,
  },

  SUPPORT_REAL_ROUTE_PATH: true,			// by hclim
  SUPPORT_REAL_MULTI_ROUTE_PATH: true,	// by hclim
  SUPPORT_WALK_LINE_PATH: false,			// by hclim

  NO_GROUP_KEYWORD_NAME: "권역선택안함",
  NEW_GROUP_KEYWORD_NAME:"새로생성",

  ADDED_TEMP_FLAG_STRING: "추가방문지_",

  PROJECT_LOADING_MODE: {//ProjectLoadingMode.java 와 동일한 description 가져야 함
    AUTO: "AUTO",  // 자동 배차
    GROUP: "GROUP", // 권역만 있는 배차
    GROUP_ORDER: "GROUP_ORDER",// 그룹,순서 모두 있는 배차 - 이건 아직 알수 있는 방법이 없음
  },


  PROJECT_CREATE_FROM: {//TMProjectService.java 와 동일한 CreateFrom 과 동일한 값
    EXCEL: "EXCEL",
    DTO: "DTO",
    DTO_READONLY: "DTO_READONLY",
  },

  WORK_LOCATION_PIN_TAG: "_WLP_",

  INSPECTION_STATUS: {
    UNINSPECTED: "UNINSPECTED",
    PICKUP: "PICKUP",
    DROPOFF: "DROPOFF",
    NA: "NA",
  },

  PICKUP_VERIFICATION_STATUS: {
    UNVERIFIED: "UNVERIFIED",
    VERIFIED: "VERIFIED",
    NA: "NA",
  },

  //DeliveryCompletedType.java
  DELIVERY_COMPLETED_TYPE: {
    NORMAL_DELIVERY: "NORMAL_DELIVERY",  //정상배송
    CORRESPONDENCE: "CORRESPONDENCE",    //대응배송
    COMMON_ENTRANCE: "COMMON_ENTRANCE",  //공동현관
    SECURITY_OFFICE: "SECURITY_OFFICE",  //경비실
    OTHER: "OTHER",                     //기타
    RETRIEVAL: "RETRIEVAL",             //정상회수
    TRANSFER_DELIVERY: "TRANSFER_DELIVERY",             //정상배송 이전
    NORMAL_DELIVERY_CONTACT: "NORMAL_DELIVERY_CONTACT",  //대면배송
    DOOR: "DOOR",  //문앞
    STORAGE_BOX: "STORAGE_BOX",  //택배보관함
  },

  //DeliveryFailureType.java
  DELIVERY_FAILURE_TYPE: {
    NOBODY_HOME: "NOBODY_HOME",  //고객부재
    WRONG_ADDR: "WRONG_ADDR",    //주소불명
    LOST_ITEM: "LOST_ITEM",  //배송품 분실
    BROKEN_ITEM: "BROKEN_ITEM",  //배송품 파손
    OTHER: "OTHER",                     //기타
    MISDELIVERY: "MISDELIVERY",  //오배송
    TRANSFER_ITEM: "TRANSFER_ITEM",  //배송품 이전
    ORDER_CANCELLATION: "ORDER_CANCELLATION",  //주문취소
    UNVISIT: "UNVISIT",  //미방문
    POSTPONEMENT: "POSTPONEMENT",  //배송연기
    DELIVERY_CANCELLATION: "DELIVERY_CANCELLATION",  //배송취소
    QR_VERIFICATION_FAIL: "QR_VERIFICATION_FAIL",  //QR검수실패
  },

  RIDER_SHOW_STATUS: {
    DISCONNECTED: "disconnected",
    START_WORK: "start_work",
    END_WORK: "end_work",
    CONNECTED: "connected",
  },

  RESERVED_COMMAND: {
    //destination
    ADD_DESTINATION: "ADD_DESTINATION",
    REMOVE_DESTINATION: "REMOVE_DESTINATION",
    MODIFY_DESTINATION: "MODIFY_DESTINATION",
    MODIFY_DESTINATION_DEEP_CHANGE: "MODIFY_DESTINATION_DEEP_CHANGE",

    //rider
    ADD_RIDER_TO_PROJECT: "ADD_RIDER_TO_PROJECT",
    REMOVE_RIDER_FROM_PROJECT: "REMOVE_RIDER_FROM_PROJECT",
    MODIFY_RIDER: "MODIFY_RIDER",
    SWITCH_RIDER_CLUSTERING: "SWITCH_RIDER_CLUSTERING",

    CHANGE_CLUSTERING_DESTINATION: "CHANGE_CLUSTERING_DESTINATION",
    FORCE_DELIVERY_STATUS: "FORCE_DELIVERY_STATUS",
    DROP_EXCEL: "DROP_EXCEL"
  },

  REFRESH_MODE: {
    CALLBACK: "CALLBACK",
    UPDATE_LEFT_PANEL: "UPDATE_LEFT_PANEL",
    MOVE_BACK_CLUSTERING: "MOVE_BACK_CLUSTERING",
    PERFORM_CLUSTER: "PERFORM_CLUSTER",
    PERFORM_PARTIAL_ROUTING: "PERFORM_PARTIAL_ROUTING",
    PERFORM_UPDATE_RIDER: "PERFORM_UPDATE_RIDER",
    LOAD_PROJECT: "LOAD_PROJECT",
  },


  DESTINATION_COLOR_PIN_NUMBER: 32, //getClusteringColor와 갯수가 동일해야 한다

  MOUSE_DRAGGING_MODE: {
    DRAWING_RECTANGLE: "DRAWING_RECTANGLE",
    DRAGGING_DESTINATIONS: "DRAGGING_DESTINATIONS"
  },

  //ClusterRule.java
  CLUSTER_RULE: {
    NORMAL: "NORMAL",    //지정된 권역에 의한 배차
    EQUALITY: "EQUALITY", //균등 배분 배차
    LOCATION: "LOCATION", //위치 기반 배차

    //미지원
    NUMBER: "NUMBER", //배송 수량 우선 배차
  },

  //ClusterRuleOnDemand.java 일치
  ON_DEMAND_CLUSTER_RULE: {
    ETA: "ETA",            // ETA 우선
    DISTANCE_TO_RIDER: "DISTANCE_TO_RIDER",       // 근접거리 우선
    DELIVERY_COUNT: "DELIVERY_COUNT", //최소 방문지 우선
  },


  MULTIPLE_POPUP_COUNT_MAX: 10,
  ADD_POPUP_KEYWORD: "ADD",

  LOGISTEQ_ORG_CODE_NAME: "logisteq",
  GLOVIS_ORG_CODE_NAME: "glovis",
  TPIRATES_ORG_CODE_NAME: "tpirates",
  BUNJANG_ORG_CODE_NAME: "bunjang",
  OLIVE_YOUNG_ORG_CODE_NAME: "oliveyoung",
  EFOODIST_ORG_CODE_NAME: "efoodist",
  BAROGO_ORG_CODE_NAME: "barogo",
  WELSTORY_ORG_CODE_NAME: "welstory",
  HYUNDAI_ORG_CODE_NAME: "thehyundai",
  JOINS_ORG_CODE_NAME: "joins",
  TOHOME_ORG_CODE_NAME: "tohome",

  // ProjectConstant.java의 PROJECT_DEEP_LINK_AES128_SECRET_KEY과 동일하게 맞춘다.
  PROJECT_DEEP_LINK_AES128_SECRET_KEY: "fhwltmxprdkffhdk",

  TIME_SPLIT: ":",

};
