const EVENT = {
	sendEvent: function(myEventBus, event, data, callback) {
		myEventBus.$emit(event, data, callback);
	},

	registerEventHandler: function(myEventBus, eventHandlers) {
		eventHandlers.forEach(eventHandler => {
			myEventBus.$on(eventHandler.event, function(data, callback) {
				//console.log("event received: " + eventHandler.event);
				eventHandler.handler(data);        			
				if (callback) {
					callback();
				}
			});
		});
	},

	WINDOW_EVENT : {
		CHANGE_MAP_SIZE: 'CHANGE_MAP_SIZE',
		SET_ACTIVE_PROFILE: 'SET_ACTIVE_PROFILE',
		SETTING_CHANGE: 'SETTING_CHANGE',
	},
	
	MAP: {	// aloa-map 컴포넌트용 이벤트
		LOGIN_SUCCESS: "LOGIN_SUCCESS",
		
		PROJECT_ADD: "PROJECT_ADD",
		PROJECT_LOAD: "PROJECT_LOAD",
		
		// 기존 이벤트 [
		SET_PIN_MODE: 'SET_PIN_MODE',
		PERFORM_CLUSTERING: 'PERFORM_CLUSTERING',
		PERFORM_ROUTING: 'PERFORM_ROUTING',
		PERFORM_SENDING_AFTER_ROUTING: 'PERFORM_SENDING_AFTER_ROUTING',
		PERFORM_CANCEL_ROUTING: 'PERFORM_CANCEL_ROUTING',
		SELECTION_MODE_CHANGED: 'SELECTION_MODE_CHANGED',
		PROJECT_EXCEL_DROP: 'PROJECT_EXCEL_DROP',
		PROJECT_CSV_DROP: 'PROJECT_CSV_DROP',
		PROJECT_RIDER_EXCEL_DROP: 'PROJECT_RIDER_EXCEL_DROP',
		PERFORM_SHOW_SETTING_POPUP: 'PERFORM_SHOW_SETTING_POPUP',
		RIDER_SELECTED: 'RIDER_SELECTED',
		DESTINATIONS_SELECTED: 'DESTINATIONS_SELECTED',
		ALL_RIDERS_SELECTED: 'ALL_RIDERS_SELECTED',		
		DESTINATION_SELECTED: 'DESTINATION_SELECTED',
		ALL_DESTINATIONS_SELECTED: 'ALL_DESTINATIONS_SELECTED',
		SETTING_CHANGED_NOTIFY: 'SETTING_CHANGED_NOTIFY',
		PERFORM_SENDING: 'PERFORM_SENDING',
		PERFORM_RIDER_SENDING: 'PERFORM_RIDER_SENDING',
		SCREEN_SIZE_CHANGED_NOTIFY: 'SCREEN_SIZE_CHANGED_NOTIFY',
		UPDATE_DELIVERIES_ROUTE_PATH: 'UPDATE_DELIVERIES_ROUTE_PATH',
		NEW_DELIVERY_ADDED: 'NEW_DELIVERY_ADDED',
		NEW_EBAY_DELIVERY_ADDED : 'NEW_EBAY_DELIVERY_ADDED',
		UPDATE_DELIVERY_STATUS: 'UPDATE_DELIVERY_STATUS',
		UPDATE_DELIVERIES: 'UPDATE_DELIVERIES',
		// 기존 이벤트 ]
		UPDATE_RIDER_WORK_STATUS: 'UPDATE_RIDER_WORK_STATUS',
		PROJECT_TERMINATE: 'PROJECT_TERMINATE',

		SET_CENTER_FOCUS: 'SELECT_DESTINATION',

		ADD_PROJECT_DELIVERY: 'ADD_PROJECT_DELIVERY',
		MODIFY_PROJECT_DELIVERY: 'MODIFY_PROJECT_DELIVERY',
		MODIFY_FORCE_DELIVERY_STATUS: 'MODIFY_FORCE_DELIVERY_STATUS',
		ADD_PROJECT_RIDER: 'ADD_PROJECT_RIDER',
		ADD_RIDER: 'ADD_RIDER',
		SIMULATION_ADD_RIDERS : 'SIMULATION_ADD_RIDERS',
		SIMULATION_RIDER_CHANGED_RIDER_PROJECT : 'SIMULATION_RIDER_CHANGED_RIDER_PROJECT',
		SIMULATION_RIDERS_TO_DISPATCH_RIDERS_PROJECT_EXCEL : 'SIMULATION_RIDERS_TO_DISPATCH_RIDERS_PROJECT_EXCEL',

		PERFORM_ROLLBACK: 'PERFORM_ROLLBACK',
		PERFORM_REFRESH_PROJECT: 'PERFORM_REFRESH_PROJECT',

		UPDATE_ROUTING_OPTION: 'UPDATE_ROUTING_OPTION',
		UPDATE_CLUSTERING_OPTION: 'UPDATE_CLUSTERING_OPTION',
		UPDATE_CLUSTERING_ON_DEMAND_OPTION: 'UPDATE_CLUSTERING_ON_DEMAND_OPTION',

		SHOW_DRIVER_DETAIL_POPUP :"SHOW_DRIVER_DETAIL_POPUP",
		SHOW_DELIVERY_DETAIL_POPUP : "SHOW_DELIVERY_DETAIL_POPUP",

		//[Demo]
		PERFORM_RESET: 'PERFORM_RESET',
		PERFORM_SIMULATED_DRIVING: 'PERFORM_SIMULATED_DRIVING',//DEMO
		REMOVE_SELECTED_RIDERS : 'REMOVE_SELECTED_RIDERS',
		REMOVE_SELECTED_DESTINATIONS : 'REMOVE_SELECTED_DESTINATIONS',

		MODIFY_PROJECT_RIDER: 'MODIFY_PROJECT_RIDER',
		DEMO_PROJECT_SELECTED: 'DEMO_PROJECT_SELECTED',

		SET_DEMO_MODE_CHANGED: 'SET_DEMO_MODE_CHANGED',

		SET_MOBILE_TRACKING_MODE_CHANGED: 'SET_MOBILE_TRACKING_MODE_CHANGED',

		ADD_DESTINATION_BY_SEARCH: "ADD_DESTINATION_BY_SEARCH",

		LEFT_PANEL_ADD_RIDER:       "LEFT_PANEL_ADD_RIDER",


		RIDER_LIST_POPUP_REMOVE_RIDER_FROM_PROJECT : "RIDER_LIST_POPUP_REMOVE_RIDER_FROM_PROJECT" ,
		RIDER_LIST_POPUP_REMOVE_RIDERS_FROM_PROJECT : "RIDER_LIST_POPUP_REMOVE_RIDERS_FROM_PROJECT" ,
		RIDER_LIST_POPUP_ADD_RIDER_TO_PROJECT    : "RIDER_LIST_POPUP_ADD_RIDER_TO_PROJECT" ,
		RIDER_LIST_POPUP_ADD_RIDERS_TO_PROJECT    : "RIDER_LIST_POPUP_ADD_RIDERS_TO_PROJECT" ,

		CHANGE_DESTINATION_CLUSTER : "CHANGE_DESTINATION_CLUSTER",
		RIDERS_SWITCH_CLUSTERING : "RIDERS_SWITCH_CLUSTERING",

		SHOW_GRID_VIEW_PROJECT :  "SHOW_GRID_VIEW",
		CHANGED_GRID_VIEW_DATA_PROJECT : "CHANGED_GRID_VIEW_DATA_PROJECT",
		SHOW_PRODUCT_VIEW_PROJECT:	"SHOW_PRODUCT_VIEW_PROJECT",
		SHOW_MOCEAN_VEHICLE_LIST_VIEW:	"SHOW_MOCEAN_VEHICLE_LIST_VIEW",
		SHOW_MOCEAN_REAL_TIME_TEMPERATURE_VIEW:	"SHOW_MOCEAN_REAL_TIME_TEMPERATURE_VIEW",
		SHOW_MOVEAN_TODAY_DRIVING_DAILY_VIEW:	"SHOW_MOVEAN_TODAY_DRIVING_DAILY_VIEW",

		UPDATE_RIDER_WORK_COMPLETION:	"UPDATE_RIDER_WORK_COMPLETION",

		PROJECT_PRODUCT_LOADING : "PROJECT_PRODUCT_LOADING",
		UPDATE_PRODUCT_PAGE : "UPDATE_PRODUCT_PAGE",

		UPDATE_MPP_SETTING: 'UPDATE_MPP_SETTING',

		LOGOUT_USER : 'LOGOUT_USER',

		UPDATE_RIDER_DISPATCH_STATUS: 'UPDATE_RIDER_DISPATCH_STATUS',

		LOAD_OTHER_USER_WATCH_PROJECT: "LOAD_OTHER_USER_WATCH_PROJECT",

		UPDATE_DIALOG_MESSAGE: 'UPDATE_DIALOG_MESSAGE',   // 팝업 메시지 업데이트

		WATCH_PROJECT_CHANGED: 'WATCH_PROJECT_CHANGED',//프로젝트 watch가 호출 되었을때 변경되는 코드

		UPDATE_MAP_VIEW : 'UPDATE_MAP_VIEW',//updateMapView가 호출되었을때

		AUTO_ADD_RIDER: 'AUTO_ADD_RIDER', //권역 자동 기사 추가

		AUTO_ADD_DISPATCH_RIDER: 'AUTO_ADD_DISPATCH_RIDER', // 자동 배차 기사 추가

		IS_ROUTE_LINE_DISPLAY : 'IS_ROUTE_LINE_DISPLAY', // 경로선 보일지 말지를 결정하는 event

		CANCEL_DELIVERY : 'CANCEL_DELIVERY', // [올리브영] 배달취소

	},
	
	RIDER_DELIVERY_LIST : {			// rider-delivery-list 컴포넌트
		SHOW: "show_driver_delivery_list",
	},

	ALL_DELIVERY_STATUS : {
		INIT: "init",
		RELOAD: "reload",
	},

	PANEL : {
		UPDATE: "update",
		// UPDATE_DATA: "UPDATE_DATA",
		SHOW:   "show",
		HIDE:   "hide",
		HIDE_DEST_LIST : "HIDE_DEST_LIST",
		INIT_LEFT_PANEL_TAB : "INIT_LEFT_PANEL_TAB",
		INIT_TOOLBARMODE: "INIT_TOOLBARMODE",
		SELECT_FILES: "SELECT_FILES",
		UPDATE_PROJECT: "update-project",//[demo] -todo 쓰는지 체크
		SELECTION_MODE_CHANGED:"SELECTION_MODE_CHANGED", //[demo] 경로 생성시 topToolbarSelectionMode 값이 바뀌었을때의 처리
		SELECTION_DEMO_MODE_CHANGED: 'SELECTION_DEMO_MODE_CHANGED',
		SHOW_DELIVERY_DETAIL_POPUP: 'SHOW_DELIVERY_DETAIL_POPUP',
		INIT_LEFT_PANEL_TREE_SOURCES: 'INIT_LEFT_PANEL_TREE_SOURCES',
		SET_CHANGE_RIDER_STATUS: "SET_CHANGE_RIDER_STATUS",
		SET_CHANGE_DELIVERY_STATUS : "SET_CHANGE_DELIVERY_STATUS",
		STOP_CALCULATING_LATENCY : "STOP_CALCULATING_LATENCY",
		IS_ROUTE_LINE_DISPLAY : 'IS_ROUTE_LINE_DISPLAY', // 경로선 보일지 말지를 결정하는 event(left panel에서 rider check 이벤트 안되게 하기 위함.)
	},

	TOP_TOOLBAR : {
		MAP_DATA_CHANGED: "MAP_DATA_CHANGED",
		MINIMIZE_TOOLBAR: "MINIMIZE_TOOLBAR",
		NORMALIZE_TOOLBAR: "NORMALIZE_TOOLBAR",
		OPEN_EXCEL_NOTIFY: "OPEN_EXCEL_NOTIFY",
		CLEAR_SELECTION_MODE: "CLEAR_SELECTION_MODE",
		SET_PROJECT_ATTRIBUTE: "SET_PROJECT_ATTRIBUTE",
		GET_NOTI_COUNT: "GET_NOTI_COUNT",
		SET_PROJECT_NAME: "SET_PROJECT_NAME", //임시 [demo] - 불필요한 부분으로 생각됨
		UPDATE_DATA : "UPDATE_DATA", //[demo]
		INIT_NO_READ_COUNT:"INIT_NO_READ_COUNT",
		UPDATE_PRODUCT_LOADING_STATUS:"UPDATE_PRODUCT_LOADING_STATUS", // 즉시 배송 아이콘 변경을 위함
		PERFORM_CLUSTERING_BUTTON_DISABLED : "PERFORM_CLUSTERING_BUTTON_DISABLED",
		PERFORM_ROUTING_BUTTON_DISABLED : "PERFORM_ROUTING_BUTTON_DISABLED",
		OPEN_TOP_BAR_MENU_CLOSE : "OPEN_TOP_BAR_MENU_CLOSE",
		LEFT_PANEL_CHECK_RIDERS : "LEFT_PANEL_CHECK_RIDERS",
	},
	
	BREAD_CRUMS : {
		SET_RIDER_DATA: "SET_RIDER_DATA",
	},

	SET :{
		I18N: "I18N", //i18n은 잘 되지 않음 .
		STORE: "STORE",
	},
	MAIN:{
		MAP_MOUNTED_COMPLETED: "MAP_MOUNTED_COMPLETED",
		INIT_MQTT_AND_CONNECT: "INIT_MQTT_AND_CONNECT",//[demo]
		MQTT_SUBSCRIBE_USER: "MQTT_SUBSCRIBE_USER",
		MQTT_SUBSCRIBE_NEW_PROJECT: "MQTT_SUBSCRIBE_NEW_PROJECT",
		MQTT_SUBSCRIBE_PROJECT: "MQTT_SUBSCRIBE_PROJECT",
		MQTT_UNSUBSCRIBE_PROJECT: "MQTT_UNSUBSCRIBE_PROJECT",
		LOGOUT_REFRESH_USER: "LOGOUT_REFRESH_USER",
		MQTT_PUB: "MQTT_PUB",
		CLOSE_DRIVER_DETAIL_POPUP: "CLOSE_DRIVER_DETAIL_POPUP",
		OPEN_DRIVER_DETAIL_POPUP_R: "OPEN_DRIVER_DETAIL_POPUP_R",
		CLOSE_DELIVERY_DETAIL_POPUP: "CLOSE_DELIVERY_DETAIL_POPUP",
		CLOSE_GRIDVIEW_EDIT_POPUP: "CLOSE_GRIDVIEW_EDIT_POPUP",
		CLOSE_PROJECT_GRIDVIEW_POPUP: "CLOSE_PROJECT_GRIDVIEW_POPUP",
		CLOSE_PRODUCT_PAGE_POPUP: "CLOSE_PRODUCT_PAGE_POPUP",
		CLOSE_MOCEAN_VEHICLE_POPUP: "CLOSE_MOCEAN_VEHICLE_POPUP",
		CLOSE_MOCEAN_REALTIME_TEMPERATURE_POPUP: "CLOSE_MOCEAN_REALTIME_TEMPERATURE_POPUP",
		CLOSE_MOCEAN_TODAY_DRVING_DAILY_POPUP: "CLOSE_MOCEAN_TODAY_DRVING_DAILY_POPUP",
		CONFIRM_POPUP: "CONFIRM_POPUP",
		CONFIRM_POPUP_CLOSE : "CONFIRM_POPUP_CLOSE",
		ALERT_POPUP: "ALERT_POPUP",
		LOGIN_AUTH_PASSWORD_OPEN_POPUP : "LOGIN_AUTH_PASSWORD_OPEN_POPUP",
		LOGIN_AUTH_PASSWORD_CLOSE_POPUP : "LOGIN_AUTH_PASSWORD_CLOSE_POPUP",
		USER_PHONE_NUMBER_OPEN_POPUP : "USER_PHONE_NUMBER_OPEN_POPUP",
		USER_PHONE_NUMBER_CLOSE_POPUP : "USER_PHONE_NUMBER_CLOSE_POPUP",
		RESERVATION_TIME_EDIT_POPUP: "RESERVATION_TIME_EDIT_POPUP",//[demo]
		RESERVATION_TIME_INFO_POPUP: "RESERVATION_TIME_INFO_POPUP",
		OPEN_PROJECT_ADD_LIST_POPUP : "OPEN_PROJECT_ADD_LIST_POPUP",
		CLOSE_PROJECT_ADD_LIST_POPUP : "CLOSE_PROJECT_ADD_LIST_POPUP",
		CLOSE_PROJECT_EXCEL_PERIOD : "CLOSE_PROJECT_EXCEL_PERIOD",
		CLOSE_DELIVERY_REPORT_EXCEL_PERIOD : "CLOSE_DELIVERY_REPORT_EXCEL_PERIOD",
		OPEN_RIDER_DELIVERY_RESULT_POPUP : "OPEN_RIDER_DELIVERY_RESULT_POPUP",
		CLOSE_RIDER_DELIVERY_RESULT_POPUP : "CLOSE_RIDER_DELIVERY_RESULT_POPUP",
		CLOSE_MPP_SETTING_POPUP : "CLOSE_MPP_SETTING_POPUP",
		APPLY_MPP_SETTING_POPUP : "APPLY_MPP_SETTING_POPUP",
		OPEN_VOC_MESSAGES_LIST_POPUP : "OPEN_VOC_MESSAGES_LIST_POPUP",
		CLOSE_VOC_MESSAGES_LIST_POPUP : "CLOSE_VOC_MESSAGES_LIST_POPUP",
		OPEN_INVOICE_PRINT_POPUP : "OPEN_INVOICE_PRINT_POPUP",
		CLOSE_INVOICE_PRINT_POPUP : "CLOSE_INVOICE_PRINT_POPUP",
		OPEN_INVOICE_WELSTORY_PRINT_POPUP : "OPEN_INVOICE_WELSTORY_PRINT_POPUP",
		CLOSE_INVOICE_WELSTORY_PRINT_POPUP : "CLOSE_INVOICE_WELSTORY_PRINT_POPUP",
		OPEN_INVOICE_HYUNDAI_PRINT_POPUP : "OPEN_INVOICE_HYUNDAI_PRINT_POPUP",
		CLOSE_INVOICE_HYUNDAI_PRINT_POPUP : "CLOSE_INVOICE_HYUNDAI_PRINT_POPUP",
		OPEN_RIDER_CHANGED_POPUP : "OPEN_RIDER_CHANGED_POPUP",
		CLOSE_RIDER_CHANGED_POPUP : "CLOSE_RIDER_CHANGED_POPUP",
		OPEN_RIDER_SEND_MESSAGE_POPUP : "OPEN_RIDER_SEND_MESSAGE_POPUP",
		CLOSE_RIDER_SEND_MESSAGE_POPUP : "CLOSE_RIDER_SEND_MESSAGE_POPUP",
		OPEN_DELIVERY_STATUS_SEND_MESSAGE_POPUP : "OPEN_DELIVERY_STATUS_SEND_MESSAGE_POPUP",
		CLOSE_DELIVERY_STATUS_SEND_MESSAGE_POPUP : "CLOSE_DELIVERY_STATUS_SEND_MESSAGE_POPUP",
		OPEN_PROJECT_DELIVERY_PRINT_LIST_POPUP : "OPEN_PROJECT_DELIVERY_PRINT_LIST_POPUP",
		CLOSE_PROJECT_DELIVERY_PRINT_LIST_POPUP : "CLOSE_PROJECT_DELIVERY_PRINT_LIST_POPUP",
		OPEN_PROJECT_DELIVERY_LIST_POPUP : "OPEN_PROJECT_DELIVERY_LIST_POPUP",
		CLOSE_PROJECT_DELIVERY_LIST_POPUP : "CLOSE_PROJECT_DELIVERY_LIST_POPUP",
		SET_MAP_CLIENT_VERSION : 'SET_MAP_CLIENT_VERSION'
	},
	DELIVERY_LIST_POPUP : {
		UPDATE_DATA : "UPDATE_DATA"
	},
	PROJECT_LIST_POPUP : {
		SHOW : "SHOW"
	},
	RIDER_LIST_POPUP : {
		SHOW : "SHOW",
		UPDATE_DATA : "UPDATE_DATA"
	},
	LOCATION_LIST_POPUP : {
		SHOW : "SHOW",
	},
	LOCATION_PAST_LIST_POPUP : {
		SHOW : "SHOW"
	},

	REPORT_PROJECT_LIST_POPUP : {
		SHOW : "SHOW"
	},
	REPORT_PROJECT_CHART_POPUP : {
		SHOW : "SHOW"
	},
	REPORT_DRIVER_LIST_POPUP : {
		SHOW : "SHOW"
	},
	REPORT_DRIVER_CHART_1_POPUP : {
		SHOW : "SHOW"
	},
	REPORT_DRIVER_CHART_2_POPUP : {
		SHOW : "SHOW"
	},
	REPORT_MOCEAN_VEHICLE_POPUP : {
		SHOW : "SHOW"
	},

	DELIVERY_PRINT_LIST_POPUP: {
		LIST_CLOSED: "LIST_CLOSED"
	},

	CLOCK : {//[demo]
		PAUSE_TRACKING: "PAUSE_TRACKING",
		PLAY_TRACKING : "PLAY_TRACKING",
	},

	PRODUCT_PAGE : {
		REFRESH_PRODUCT_PAGE : "REFRESH_PRODUCT_PAGE"
	},
};
