const ENDPOINT = {
	TMS_API: {
		NON_SECURED: "http://tms-dev.logisteq.com:5900/api",
		SECURED: "https://tms-dev.logisteq.com:5800/api",
		DEV: "http://************:7070/api",
		LOCAL: "http://localhost:7070/api",
	}
};

const WEB_API = "/web";
const PROJECT = WEB_API + "/projects";
const RIDER  = WEB_API + "/rider";
const RIDERS  = WEB_API + "/riders";
const VEHICLEMODEL = WEB_API + "/vehicleModel";
const NOTIFICATION = WEB_API + "/notifications";
const GROUP = WEB_API + "/groups";
const DELIVERY = WEB_API + "/delivery";
const DELIVERIES = WEB_API + "/deliveries";
const DEPARTMENTS = WEB_API + "/departments";
const CORE_API = "/api/tms-core";
const TRACKS = WEB_API + "/tracks";
const TRACKS_MOCEAN = WEB_API + "/tracks/mocean";
const STAT = WEB_API + "/stat";
const WEB_DEMO = WEB_API + "/demo"; //[demo]
const WEB_AUTH = WEB_API + "/auth/v2";

const Url = {
	WEB: {
		// 사용자 서비스
		LOGIN: WEB_API + "/login"
		, LOGOUT: WEB_API + "/logout"
		, CHECK_USER_DUPLICATE : WEB_API + "/user/check-duplicate"
		, CHANGE_USER_PASSWORD : WEB_API + "/user/change-pw"
		, CHANGE_USER_NAME : WEB_API + "/user/change-name"
		, CHANGE_USER_PHONE_NUMBER : WEB_API + "/user/change-phone-number"
		, USER_PROFILE_IMG : WEB_API + "/user/profile-img"
		, USER_REG_ANONYMOUS: WEB_API + "/users/anonymous"
		, USERS: WEB_API + "/users"
		, USER_SEND_AUTH_KEY : WEB_API + "/user/send-auth-key"
		, SESSION_ATTR: WEB_API + "/session-attr"
		, USER_AUTH: WEB_API + "/auth-user"
		, USER_PROJECT: WEB_API + "/users/{userId}/projects"
		, LOGIN_FAILED_COUNT: WEB_API + "/user/login-failed-count"
		, ACCOUNT_LOCKED_AT: WEB_API + "/user/account-locked-at"

		// 기사(차량) 서비스
		, TRACKS_CURRENT_LOCATIONS: TRACKS + "/current-locations"
		, TRACKS_RIDER_LAST_LOCATIONS: TRACKS + "/rider/last-locations"
		, TRACKS_RIDER_LOCATIONS : TRACKS + "/rider/{riderId}/locations"
		, TRACKS_RIDER_TIMESTAMP : TRACKS + "/timestamp"
		, TRACKS_MOCEAN_VIN_INFO : TRACKS_MOCEAN + "/vininfo"
		, TRACKS_MOCEAN_LAST_VEHICLE_STATUS : TRACKS_MOCEAN + "/vehicle/status/last"
		, TRACKS_MOCEAN_LAST_VEHICLE_STATUSES : TRACKS_MOCEAN + "/vehicle/statuses/last"
		, TRACKS_MOCEAN_VEHICLE_STATUS : TRACKS_MOCEAN + "/vehicle/status"
		, TRACKS_MOCEAN_TEMPERATURES : TRACKS_MOCEAN + "/temperatures"
		, TRACKS_MOCEAN_TEMPERATURES_BOUNDS : TRACKS_MOCEAN + "/temperatures/bounds"
		, TRACKS_MOCEAN_DAILY_STAT : TRACKS_MOCEAN + "/stat/daily"
		, TRACKS_MOCEAN_WEEK_STAT : TRACKS_MOCEAN + "/stat/weekly"
		, TRACKS_MOCEAN_MONTHLY_STAT : TRACKS_MOCEAN + "/stat/monthly"

		// 기사 + 배차 상태
		, GET_RIDERS_DISPATCH_STATUS: WEB_API + "/riders-dispatchstatus"
		, RIDERS_VEHICLES: WEB_API + "/riders/vehicles"
		, D_RIDERS: WEB_API + "/delete-riders"
		, SAVE_DELIVERY: WEB_API + "/delivery"
		, D_DELIVERY: WEB_API + "/delete-delivery"
		// 기사
		, RIDER : RIDER
		, RIDER_DETAIL : RIDER + "/{riderId}"
		, RIDER_FIND_MOBILE_DETAIL : RIDER + "/find-mobile"
		, RIDERS: RIDERS
		, RIDERS_DETAIL: RIDERS + "/{riderId}"
		, RIDERS_DETAIL_DATA_PROJECT: RIDERS + "/{riderId}/get-data-project"
		, RIDERS_PROJECT_SETTING_NOTE: RIDERS + "/put-project-setting-rider-note"
		, RIDERS_PROJECT: RIDERS + "/{riderId}/projects"
		, RIDER_PROFILE_IMAGE_URL: RIDERS + "/{riderId}/profile-image-url"
		, RIDER_ADD_TO_PROJECT : RIDERS + "/{riderId}/add"
		, RIDERS_ADD_TO_PROJECT : RIDERS + "/addproject"
		, RIDER_GET_ORG_MAX_COUNT : RIDERS + "/orgridermaxcount"
		, RIDERS_EXCEL_ADD : RIDERS +"/excel"
		, RIDERS_SWITCH_CLUSTERING : RIDERS +  "/switch-cluster"
		, RIDERS_VEICLE_NUMBER_GET_RIDER : RIDERS + "/vehicleNumberList"
		, RIDERS_GROUP_NAME : RIDERS + "/groupname"
		, RIDER_PUSH_MESSAGE : RIDER + "/push-message"
		, RIDER_REQUEST_CHANGE_DELIVERY_STATUS : RIDER + "/request-change-delivery-status"
		, SIMULATION_ADD_RIDERS: RIDERS + "/simulation-riders-add-project"
		, SIMULATION_RIDER_TO_DISPATCH_RIDER_PROJECT: RIDERS + "/simulation-rider-to-dispatch-rider-project"

		// 그룹네임
		, GET_GROUPNAMES: GROUP
		// 노티피케이션
		, NOTIFICATION: NOTIFICATION
		, NOTIFICATION_COUNT: NOTIFICATION + "/count"
		, NOTIFICATION_DELETE_DESTINATION: NOTIFICATION + "/delete-destination"
		// vehicle
		, VEHICLEMODELS: VEHICLEMODEL
		, VEHICLE_ITEM: WEB_API + "/vehicle/{vehicleId}"//[demo]
		// projects
		, PROJECT: PROJECT
		, PROJECT_DETAIL: PROJECT + "/{projectId}"
		, PROJECT_ROUTE: PROJECT + "/{projectId}/route"
		, PROJECT_CLUSTER: PROJECT + "/{projectId}/cluster"
		, PROJECT_CLUSTER_RIDER: PROJECT + "/{projectId}/cluster-rider"
		, PROJECT_PUSH_RIDER: PROJECT + "/{projectId}/push-rider"
		, PROJECT_TERMINATE: PROJECT + "/{projectId}/terminate"
		, PROJECT_RIDER_INFO: PROJECT + "/{projectId}/riders"
		, PROJECT_EXCEL: PROJECT + "/excel"
		, PROJECT_HAND_WRITTEN_EXCEL: PROJECT + "/hand-written-excel"
		, PROJECT_CSV: PROJECT + "/csv"
		, PROJECT_EMPTY_PROJECT: PROJECT + "/empty"
		, PROJECT_ADD: PROJECT + "/add"     //WebProjectDTO를 사용하여 프로젝트를 만든다
		, PROJECT_COPY_PROJECT : PROJECT + "/copy" // 프로젝트 복사를 위한 api
		, UPDATE_PROJECT_NAME: PROJECT + "/projectname" + "/{projectId}" // 프로젝트 이름 변경
		, PROJECT_FILES_ZIP_DOWNLOAD:  PROJECT + "/{projectId}/files/zip-download"			// 완료된 프로젝트 내의 전체 첨부파일을 zip으로 다운로드 받는다.
		, PROJECT_EXCEL_FROM_DESTINATION_LIST_PANEL:  PROJECT + "/{projectId}/excel/destination-list-panel"			// 배송지 상세 목록에서 엑셀 다운로드 수행
		, PROJECT_EXCEL_FROM_LIST_PANEL: PROJECT + "/{projectId}/excel/project-excel" //excel 추출을 위함
		, PROJECT_EXCEL_JOINS_INVOICE: PROJECT + "/{projectId}/excel/joins-invoice"		// 중앙일보 송달증
		, PROJECT_EXCEL_FROM_TOP_SIMULATION_RIDERS: PROJECT + "/{projectId}/excel/project-simulation-riders-export-excel" //가배차 기사 엑셀
		, PROJECT_SIMULATION_RIDER_TO_DISPATCH_RIDER_CHANGED_EXCEL : PROJECT + "/simulation-rider-to-dispatch-rider-changed-excel"
		, PROJECT_SAMPLE: "/assets/file/project_sample.xlsx"
		, PROJECT_DELIVERY_CHANGED : PROJECT + "/delivery-changed"
		, PROJECT_TRANSFER_PROJECT : PROJECT + "/transfer"
		, PROJECT_EXCEL_BY_PERIOD : PROJECT + "/excel-by-period"
		, PROJECT_AUTO_ADD_RIDER : PROJECT + "/auto-add-rider"
		, PROJECT_SIMULATION_RIDERS_AUTO_CHANGED_DISPATCH_RIDERS : PROJECT + "/simulation-riders-auto-changed-dispatch-riders"
		, PROJECT_SEND_DISPATCH_TO_OMS : PROJECT + "/send-dispatch-to-oms"
		, PROJECT_EXCEL_DELIVERY_PRODUCT_PER_RIDER : PROJECT + "/excel/delivery-product-per-rider"
		, PROJECT_EXCEL_DELIVERY_RIDER_PER_PRODUCT :PROJECT + "/excel/delivery-rider-per-product"

		//delivery
		, DELIVERY: DELIVERY
		, DELIVERIES: DELIVERIES
		, DELIVERY_INFO : DELIVERIES + "/{deliveryId}"
		, DELIVERY_CLUSTER:   DELIVERY + "/cluster"
		, DELIVERIES_CLUSTER: DELIVERIES + "/cluster"
		, DELIVERIES_ROUTE: DELIVERIES + "/route"
		, DELIVERIES_ROUTE_ETA: DELIVERIES + "/routeeta"
		, DELIVERIES_REPORT: DELIVERIES + "/report"
		, DELIVERIES_FILE_ZIP_DOWNLOAD: DELIVERIES + "/{deliveryId}/file/zip-download"
		, DELIVERIES_EXCEL: DELIVERIES + "/excel"
		, DELIVERIES_PAST_EXCEL: DELIVERIES + "/past-excel"
		, DELIVERIES_PAST: DELIVERIES + "/past"
		, DELIVERY_STATUS: DELIVERY + "/{deliveryId}/status"
		, DELIVERY_FORCE_STATUS: DELIVERY + "/{deliveryId}/status-force"
		, DELIVERIES_FORCE_STATUS: DELIVERIES + "/status-force"
		, DELIVERY_FILE : DELIVERIES + "/{deliveryId}/file"
		, DELIVERY_VOC_SAVE : DELIVERIES + "/{deliveryId}/voc"
		, DELIVERY_VOC_GET_MESSAGE_LIST : DELIVERIES +"/voc-list"
		, DELIVERY_SET_INVOICE_PRINT_COUNT : DELIVERIES +"/invoice-print-count"
		, DELIVERY_GET_INVOICE_PRINTING_LIST_PROJECT : DELIVERIES + "/invoice-info"
		, DELIVERIES_REPORT_EXCEL : DELIVERIES + "/tms-report"
		, DELIVERY_CS_SAVE_HISTORY : DELIVERY + "/cs-save-history"
		, DELIVERIES_PROJECT_LIST : DELIVERIES + "/projects-deliveries"
		, DELIVERIES_WAREHOUSE_LIST : DELIVERIES + "/warehouse"
		, DELIVERIES_CANCEL : DELIVERIES + "/cancel"

		// tracks에 위치 저장
		, TRACKS: "http://" + location.hostname + ":7070/api/tracks"
		// core api
		// 경로탐색
		, MPP: WEB_API + "/mpp"


		//Math
		, MATH_DELIVERY: WEB_API + "/math/delivery"

		//Core API
		// 검색.
		, SMART_SEARCH: WEB_API + "/smart-search"
		, SEARCH: WEB_API + "/search"
		, EXTEND_SEARCH: WEB_API + "/extend-search"
		, SEARCH_COORD: WEB_API + "/search-coord"
		, EXTEND_SEARCH_COORD: WEB_API + "/extend-search-coord"
		, SEARCH_ENTRANCE: WEB_API + "/search-entrance"
		// 경로 탐색
		, ROUTE: WEB_API + "/route"
		, ROUTE_RESERVATION: WEB_API + "/route-reservation"
		, MPP: WEB_API + "/mpp"
		// 클러스터
		, CLUSTER: WEB_API + "/cluster"
		, CLUSTER_RESERVATION: WEB_API + "/cluster-reservation"
		, RANGE_PROJECTION: WEB_API + "/range-projection"

		// 기사 등록시 validation 체크
		,CHECK_LICENSE_PLATE_EXISTS: WEB_API + "/check-license-plate-exists"
		,CHECK_RIDER_MOBILE_EXISTS: WEB_API + "/check-rider-mobile-exists"

		,CHECK_RIDER_WORKING_TIME: WEB_API + "/check-rider-working-time"

		,ENUM_DELIVERY_TYPES: WEB_API + "/enum/delivery-types"
		,ENUM_VISIT_TYPES: WEB_API + "/enum/visit-types"
		,ENUM_DELIVERY_STATUS: WEB_API + "/enum/delivery-status"
		,ENUM_DELIVERY_COMPLETED_TYPES: WEB_API + "/enum/delivery-completed-types"
		,ENUM_DELIVERY_FAILURE_TYPES: WEB_API + "/enum/delivery-failure-types"
		,ENUM_CARGO_SUB_TYPES: WEB_API + "/enum/cargo-sub-types"
		,ENUM_CARGO_TYPES: WEB_API + "/enum/cargo-types"
		,ENUM_FUEL_TYPES: WEB_API + "/enum/fuel-types"
		,ENUM_MILES_TYPES: WEB_API + "/enum/miles-types"
		,ENUM_VEHICLE_SIZE_TYPES: WEB_API + "/enum/vehicle-size-types"
		,ENUM_VEHICLE_TYPES: WEB_API + "/enum/vehicle-types"
		,ENUM_WHEEL_DRIVE_TYPES: WEB_API + "/enum/wheel-drive-types"
		,ENUM_PRODUCT_SIZE_TYPES: WEB_API + "/enum/product-size-types"
		,ENUM_WORK_AUTHORITIES: WEB_API + "/enum/work-authorities"

		,STAT_RIDER_TOTAL_DELIVERIES: STAT + "/rider/total-deliveries"
		,STAT_RIDER_YEARLY_DELIVERIES: STAT + "/rider/yearly-deliveries"
		,STAT_RIDER_MONTHLY_DELIVERIES: STAT + "/rider/monthly-deliveries"
		,STAT_RIDER_WEEKLY_DELIVERIES: STAT + "/rider/weekly-deliveries"
		,STAT_RIDER_DAILY_DELIVERIES: STAT + "/rider/daily-deliveries"
		,STAT_RIDER_DAILY_PROJECT_DELIVERIES: STAT + "/rider/daily-project-deliveries"

		,STAT_PROJECT_DELIVERIES: STAT + "/project/deliveries"

		,STAT_GENERAL_TOTAL_DELIVERIES: STAT + "/general/total-deliveries"
		,STAT_GENERAL_YEARLY_DELIVERIES: STAT + "/general/yearly-deliveries"
		,STAT_GENERAL_MONTHLY_DELIVERIES: STAT + "/general/monthly-deliveries"
		,STAT_GENERAL_WEEKLY_DELIVERIES: STAT + "/general/weekly-deliveries"
		,STAT_GENERAL_DAILY_DELIVERIES: STAT + "/general/daily-deliveries"

		,STAT_GROUP_NAME_TOTAL_DELIVERIES: STAT + "/group-name/total-deliveries"
		,STAT_GROUP_NAME_YEARLY_DELIVERIES: STAT + "/group-name/yearly-deliveries"
		,STAT_GROUP_NAME_MONTHLY_DELIVERIES: STAT + "/group-name/monthly-deliveries"
		,STAT_GROUP_NAME_WEEKLY_DELIVERIES: STAT + "/group-name/weekly-deliveries"
		,STAT_GROUP_NAME_DAILY_DELIVERIES: STAT + "/group-name/daily-deliveries"
		,STAT_GROUP_NAME_DAILY_PROJECT_DELIVERIES: STAT + "/group-name/daily-project-deliveries"

		,STAT_GROUP_NAME_DAILY_PROJECT_EXCEL_DELIVERIES: STAT + "/group-name/daily-project-excel-deliveries"

		,STAT_RIDER_DAILY_PROJECT_EXCEL_DELIVERIES: STAT + "/rider/daily-project-excel-deliveries"
		,STAT_RIDER_TODAY_PROJECT_EXCEL_DELIVERIES: STAT + "/rider/today-project-excel-deliveries"
		,GET_RIDER_PROJECT_INFO: WEB_API + "/rider-project-info"

		,PRODUCT_PROJECT: WEB_API+"/product/project/"
		,PRODUCT_OVERALL_STATUS: WEB_API+"/product/overall-status/"

		, GLOVIS_DELIVERY_CODES: WEB_API + "/glovis/delivery-codes"

		, GLOVIS_DELIVERY_STATUS: WEB_API + "/glovis/delivery-status"

		// 데모[demo]
		, DEMO_STATUS: WEB_DEMO + "/status"
		, DEMO_PROJECT: WEB_DEMO + "/project"
		, DEMO_DELIVERIES: WEB_DEMO + "/deliveries"
		, DEMO_GET_DELIVERY_ROUTE_PATH : WEB_DEMO + "/deliveries/route"
		, DEMO_ROUTE_NEAREST_INFO :  WEB_DEMO + "/route/nearest"
		, RIDER_PROJECT_SETTING: RIDERS + "/{riderId}/project-setting"
		// 통계 [ 미사용 삭제]
		// , STATISTICS_COLLECT_BASE_ITEM_ALL:  WEB_API + "/statistics/collect-statics-all"
		// , STATISTICS_COLLECT_BASE_ITEM:  WEB_API     + "/statistics/collect-statics"
		// , STATISTICS_COLLECT_REFRESH_BASE_ITEM:  WEB_API     + "/statistics/collect-statics-refresh"
		// , STATISTICS_GET_ENERGY_GAUGE_ROUTE: WEB_API     + "/statistics/get-path"
		// , STATISTICS_REMOVE_ITEM: WEB_API     + "/statistics/delete-statics"
		// , STATISTICS_REMOVE_ALL: WEB_API     + "/statistics/delete-statics-all"
		// , STATISTICS_GET_VEHICLE_INFO_BY_TYPE: WEB_API     + "/statistics/get-vehicle-info/type"
		// , STATISTICS_GET_VEHICLE_INFO_BY_RIDER: WEB_API     + "/statistics/get-vehicle-info/rider"
		//EV_Demo Show_All_EVStation
		, EV_STATION: 	WEB_API + "/evstation"
		, FIND_NEAR_EV_STATION: WEB_API + "/evstation/find-near"

		, VERSION: WEB_API + "/version"
		, ROUTE_VERSION: WEB_API + "/route/version"
		, SEARCH_VERSION: WEB_API + "/search/version"

		//DEPARTMENTS
		,DEPARTMENTS_AUTHORIZED : DEPARTMENTS + "/authorized"

		, WEB_BO_COMPANY_INFO_LIST : WEB_API + "/bo/company-info-list"
		, WEB_BO_COMPANY_LIST : WEB_API + "/bo/company-list"
		, WEB_BO_COMPANY_USER_LIST : WEB_API + "/bo/company-user-list"
		, WEB_BO_RIDER_LOCATION_LIST : WEB_API + "/bo/rider-location-list"
		, WEB_BO_DELIVERY_LOCATION_LIST : WEB_API + "/bo/delivery-info-access-list"
		, WEB_BO_RIDER_INFO_ACCESS_LIST : WEB_API + "/bo/rider-info-access-list"
		, WEB_BO_RIDER_ACCESS_LIST : WEB_API + "/bo/rider-access-list"
		, WEB_BO_USER_ACCESS_LIST : WEB_API + "/bo/user-access-list"
		, WEB_BO_ORGANIZATION_ADD : WEB_API + "/bo/organization-add"
		, WEB_BO_ORGANIZATION_MODIFY :  WEB_API + "/bo/organization-modify"
		, WEB_BO_ORGANIZATION_USE_CASE_MODIFY : WEB_API + "/bo/organization-use-case-modify"
		, WEB_BO_ORGANIZATION_ADMIN_USER_MODIFY  : WEB_API + "/bo/organization-admin-user-modify"
		, WEB_BO_ORGANIZATION_DELETED : WEB_API + "/bo/organization-deleted"
		, WEB_BO_DEPARTMENT_FAMILY_LIST : WEB_API + "/bo/departments/family"

		, WEB_BO_USER_EDIT : WEB_API + "/bo/user-edit"
		, WEB_BO_USER_PASSWORD : WEB_API + "/bo/user-password"
		, WEB_BO_USER_DELETED : WEB_API + "/bo/user-deleted"
		, WEB_BO_USER_ADD : WEB_API + "/bo/user-add"
	}
};
