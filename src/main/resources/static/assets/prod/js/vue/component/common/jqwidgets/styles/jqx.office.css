.jqx-rc-tl-office
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
}
.jqx-rc-tr-office
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
.jqx-rc-bl-office
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
.jqx-rc-br-office
{
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*top rounded Corners*/
.jqx-rc-t-office
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
/*bottom rounded Corners*/
.jqx-rc-b-office
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*right rounded Corners*/
.jqx-rc-r-office
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*left rounded Corners*/
.jqx-rc-l-office
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
/*all rounded Corners*/
.jqx-rc-all-office
{
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
}
.jqx-widget-office {
    font-family: Calibri;
}
.jqx-widget-content-office{font-family: Calibri; border-color: #d4d4d4; color: #444444; background-color: #fff;}
.jqx-widget-header-office{font-family: Calibri; color: #444444; border-color:#d4d4d4; background-color:#ffffff;}

.jqx-fill-state-normal-office{font-family: Calibri; border-color: #d4d4d4; color: #444444; background: #fdfdfd;}
.jqx-grid-cell-sort-alt-office, .jqx-grid-cell-filter-alt-office, .jqx-grid-cell-pinned-office, .jqx-grid-cell-alt-office, .jqx-grid-cell-sort-office{ background:#ededed; color: #000;}
.jqx-button-office {border-color: #d4d4d4;}
.jqx-fill-state-hover-office{border-color:#86bfa0; color: #000; background:#d3f0e0;}
.jqx-input-office, .jqx-input-button-content-office {border-color: #d4d4d4;}
.jqx-fill-state-focus-office, .jqx-combobox-content-focus-office, .jqx-numberinput-focus, .jqx-datetimeinput-office .jqx-fill-state-hover-office, .jqx-datetimeinput-office .jqx-fill-state-pressed-office{ border-color: #86bfa0; }
.jqx-fill-state-pressed-office{border-color:#429366; color: #000; background:#86bfa0;}

.jqx-scrollbar-state-normal-office, .jqx-grid-bottomright-office, .jqx-panel-bottomright-office, .jqx-listbox-bottomright-office{background:#f3f3f3;}
.jqx-widget-office .jqx-grid-column-header-office, .jqx-grid-cell-office, .jqx-widget-office .jqx-grid-cell-office, .jqx-widget-office .jqx-grid-group-cell-office, .jqx-grid-group-cell-office{font-family: Calibri; border-color:#f3f3f3;}
.jqx-tabs-title-bottom-office, .jqx-tabs-title-office{color: #666666;}
.jqx-tabs-title-hover-bottom-office, .jqx-tabs-title-hover-top-office{color: #217346; background: transparent; border-color: transparent;}
.jqx-tabs-title-selected-bottom-office, .jqx-tabs-selection-tracker-bottom-office, .jqx-tabs-title-selected-top-office, .jqx-tabs-selection-tracker-top-office{font-weight: bold; color: #217346; border-color:#d4d4d4; border-bottom:1px solid #fff; background:#fff}
.jqx-menu-vertical-office{background: #fff; border-color: #d4d4d4;}
.jqx-widget-office .jqx-grid-cell-office, .jqx-widget-office .jqx-grid-column-header-office, .jqx-widget-office .jqx-grid-group-cell-office {color: #000; border-color: #d4d4d4;}
.jqx-widget-office .jqx-grid-column-menubutton-office, .jqx-widget-office .jqx-grid-column-sortascbutton-office, .jqx-widget-office .jqx-grid-column-sortdescbutton-office, .jqx-widget-office .jqx-grid-column-filterbutton-office {
    background-color: transparent;
    border-color: #d4d4d4;
}
 .jqx-input-button-header-office, .jqx-calendar-title-header-office, .jqx-grid-office .jqx-widget-header-office, .jqx-grid-header-office, .jqx-grid-column-header-office, .jqx-grid-office {font-family: Calibri; border-color: #d4d4d4; color: #444444; background: #fff;}
.jqx-window-header-office{font-family: Calibri; color: #444444; background: #fff;}
.jqx-grid-column-menubutton-office {
    background-image: url('images/office-icon-down.png');
 }
 /*applied to the column's sort button when the sort order is ascending.*/
 .jqx-grid-column-sortascbutton-office {
    background-image: url('images/office-icon-up.png');
 }
.jqx-grid-column-sortdescbutton-office {
    background-image: url('images/office-icon-down.png');
}
.jqx-checkbox-hover-office {
    background: #fff;
}
.jqx-radiobutton-hover-office {
    background: #fff;
}
.jqx-scrollbar-thumb-state-normal-horizontal-office, .jqx-scrollbar-thumb-state-normal-office {
    background: #ffffff; border-color: #d4d4d4;
}
.jqx-scrollbar-thumb-state-hover-horizontal-office, .jqx-scrollbar-thumb-state-hover-office {
    background: #f0f0f0; border-color: #d4d4d4;
}
.jqx-scrollbar-thumb-state-pressed-horizontal-office, .jqx-scrollbar-thumb-state-pressed-office {
    background: #f0f0f0; border-color: #777777;
}
.jqx-scrollbar-button-state-normal-office
{
    border: 1px solid #d4d4d4; 
    background: #fff;
}
/*applied to the scrollbar buttons in hovered state.*/
.jqx-scrollbar-button-state-hover-office
{
    border: 1px solid #777777;
    background: #fff;
}
/*applied to the scrollbar buttons in pressed state.*/
.jqx-scrollbar-button-state-pressed-office
{
    border: 1px solid #777777;
    background: #f0f0f0;
}

/*icons*/
.jqx-window-collapse-button-office
{
    background-image: url(images/office-icon-up.png);
    background-repeat: no-repeat;
}
.jqx-window-collapse-button-collapsed-office {
    background-image: url(images/office-icon-down.png);
    background-repeat: no-repeat;
}
.jqx-icon-arrow-up-office, .jqx-expander-arrow-bottom-office, .jqx-menu-item-arrow-up-office
{
    background-image: url('images/office-icon-up.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-down-office, .jqx-expander-arrow-top-office, .jqx-tree-item-arrow-expand-office, .jqx-tree-item-arrow-expand-hover-office, .jqx-menu-item-arrow-down-office
{
    background-image: url('images/office-icon-down.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-left-office, .jqx-menu-item-arrow-left-office
{
    background-image: url('images/office-icon-left.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-right-office, .jqx-menu-item-arrow-right-office, .jqx-tree-item-arrow-collapse-office, .jqx-tree-item-arrow-collapse-hover-office
{
    background-image: url('images/office-icon-right.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tabs-arrow-left-office, .jqx-tree-item-arrow-collapse-rtl-office, .jqx-tree-item-arrow-collapse-hover-rtl-office
{
    background-image: url('images/office-icon-left.png');
    background-repeat: no-repeat;
}
.jqx-tabs-arrow-right-office
{
    background-image: url('images/office-icon-right.png');
    background-repeat: no-repeat;
}

.jqx-menu-item-arrow-up-selected-office, .jqx-icon-arrow-up-selected-office{background-image:url('images/office-icon-up.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-down-selected-office, .jqx-icon-arrow-down-selected-office{background-image:url('images/office-icon-down.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-left-selected-office, .jqx-icon-arrow-left-selected-office{background-image:url('images/office-icon-left.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-right-selected-office, .jqx-icon-arrow-right-selected-office{background-image:url('images/office-icon-right.png');background-repeat:no-repeat;background-position:center;}
.jqx-tabs-close-button-office{background-image:url(images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-tabs-close-button-selected-office{background-image:url(images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-tabs-close-button-hover-office{background-image:url(images/close.png);  background-repeat:no-repeat;  background-position:center;}
.jqx-expander-arrow-bottom-office,.jqx-scrollbar-office .jqx-icon-arrow-up-selected-office{background-image:url('images/office-icon-up.png'); background-repeat:no-repeat; background-position:center;}
.jqx-expander-arrow-top-office, .jqx-scrollbar-office .jqx-icon-arrow-down-selected-office{background-image:url('images/office-icon-down.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-office .jqx-icon-arrow-left-selected-office{background-image:url('images/office-icon-left.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-office .jqx-icon-arrow-right-selected-office{background-image:url('images/office-icon-right.png');background-repeat:no-repeat; background-position:center;}
.jqx-slider-slider-office
{
}
.jqx-slider-button-office
{
    -moz-border-radius: 9px;
    -webkit-border-radius: 9px;
    border-radius: 9px;
}
.jqx-input-button-content-office
{  
    font-size: 10px;
}

.jqx-dropdownlist-state-normal-office, .jqx-dropdownlist-state-hover-office, .jqx-dropdownlist-state-selected-office,
.jqx-scrollbar-button-state-hover-office, .jqx-scrollbar-button-state-normal-office, .jqx-scrollbar-button-state-pressed-office,
.jqx-scrollbar-thumb-state-normal-horizontal-office, .jqx-scrollbar-thumb-state-hover-horizontal-office, .jqx-scrollbar-thumb-state-pressed-horizontal-office,
.jqx-scrollbar-thumb-state-normal-office, .jqx-scrollbar-thumb-state-pressed-office, .jqx-button-office, .jqx-tree-item-hover-office, .jqx-tree-item-selected-office,
.jqx-tree-item-office, .jqx-menu-item-office, .jqx-menu-item-hover-office, .jqx-menu-item-selected-office, .jqx-menu-item-top-office, .jqx-menu-item-top-hover-office, 
.jqx-menu-item-top-selected-office, .jqx-slider-button-office, .jqx-slider-slider-office
 {
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-switchbutton-office {
    -moz-border-radius: 0px; 
    -webkit-border-radius: 0px; 
    border-radius: 0px;
    border: 2px solid #a6a6a6;
}
.jqx-switchbutton-thumb-office {
    width: 12px;
    background: #000;
    border: 1px solid #000;
}
.jqx-switchbutton-label-on-office {
    background: #429366;
    color: #429366;
}
.jqx-switchbutton-label-off-office {
    background: #a6a6a6;
    color: #a6a6a6;
}
.jqx-icon-arrow-first-metro
{
    background-image: url('images/office-icon-first.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-metro
{
    background-image: url('images/office-icon-last.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-switchbutton-wrapper-office {
}
.jqx-layout-office
{
    background-color: #d4d4d4;
}

/*applied to the timepicker labels*/
.jqx-time-picker .jqx-header .jqx-hour-container-office:focus {
    outline: 1px solid rgb(66, 147, 102) !important;
}
.jqx-time-picker .jqx-header .jqx-hour-container-office:hover {
    outline: 1px solid rgb(66, 147, 102) !important;
	background-color: rgb(134, 191, 160);
}
.jqx-time-picker .jqx-header .jqx-minute-container-office:focus {
    outline: 1px solid rgb(66, 147, 102) !important;
}
.jqx-time-picker .jqx-header .jqx-minute-container-office:hover {
    outline: 1px solid rgb(66, 147, 102) !important;
	background-color: rgb(134, 191, 160);
}
.jqx-time-picker .jqx-header .jqx-am-container-office:focus {
    outline: 1px solid rgb(66, 147, 102) !important;
}
.jqx-time-picker .jqx-header .jqx-am-container-office:hover {
    outline: 1px solid rgb(66, 147, 102) !important;
	background-color: rgb(134, 191, 160);
}
.jqx-time-picker .jqx-header .jqx-pm-container-office:focus {
    outline: 1px solid rgb(66, 147, 102) !important;
}
.jqx-time-picker .jqx-header .jqx-pm-container-office:hover {
    outline: 1px solid rgb(66, 147, 102) !important;
	background-color: rgb(134, 191, 160);
}
.jqx-label-office {
	fill: darkgray;
}
.jqx-header-office {
	border-bottom: 1px solid rgb(212, 212, 212);
}
.jqx-svg-picker-office:focus {
	border: 1px solid rgb(66, 147, 102) !important;
}
.jqx-needle-office {
	fill: rgb(66, 147, 102);
}
.jqx-needle-central-circle-office {
	fill: rgb(134, 191, 160);
	stroke: rgb(66, 147, 102);
}
.jqx-time-picker .jqx-label.jqx-selected-office {
	fill: black !important;
}