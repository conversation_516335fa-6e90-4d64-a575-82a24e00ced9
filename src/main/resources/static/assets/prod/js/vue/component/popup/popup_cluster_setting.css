@charset "UTF-8";
@import url("/assets/common/css/font.css");
* {
	font-family: Noto Sans KR;
	padding: 0px;
	margin: 0px; 
}

.popup_cluster_window{
	position: absolute;
	top:50%;
	left: 50%;
	margin-top: -214px;
	margin-left: -225px;
	width: 450px;
	height: 428px;
	background-color: #fff;	
	border-radius: 3px;
/*	border: 1px solid red;*/
}

.popup_cluster_window_close{
	position: absolute;
	top:5px;
	right: 30px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	z-index: 10;
	background-image: url("/assets/image/prod/popup/bt_popup_close_n.svg");
}

.popup_cluster_title{
	position: relative;
	height:41px;
	padding-top: 10px;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	color:#083338;
/*	border: 1px solid red;*/
}

.popup_cluster_title_line{
	position: relative;
	height: 1px;
	background-color: #D5D5D5; 	
}

/*----------------------------내부 컨텐츠(버튼)-------------------------------*/

/* The container */
.popup_cluster_container_radio {
    display: block;
    position: relative;
    top:6px;
    padding-left: 35px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Hide the browser's default radio button */
.popup_cluster_container_radio input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

/* Create a custom radio button */
.popup_cluster_container_radio .popup_cluster_checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 25px;
    width: 25px;
    background-color: #fff;
    border-radius: 50%;
    border:1px solid #97A0A2;
}

/* On mouse-over, add a grey background color */
.popup_cluster_container_radio:hover input ~ .popup_cluster_checkmark {
    background-color: #ccc;
}

/* When the radio button is checked, add a blue background */
.popup_cluster_container_radio input:checked ~ .popup_cluster_checkmark {
    background-color: #6DEBAE;
    background-image: url("/assets/image/prod/popup/bt_radio_checked1.svg");
    border:0px solid #97A0A2;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.popup_cluster_container_radio .checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the indicator (dot/circle) when checked */
.popup_cluster_container_radio input:checked ~ .popup_cluster_checkmark:after {
    display: block;
}

/* Style the indicator (dot/circle) */
.popup_cluster_container_radio .checkmark:after {
 	top: 9px;
	left: 9px;
	width: 8px;
	height: 8px;
	border-radius: 50%;
	background: white;
}

.popup_cluster_sector1{
	position: absolute;
	top: 108px;
	left: 50px;
	width: 352px;
	height: 42px;
	/*border: 1px solid;*/
}

.popup_cluster_sector2{
	position: absolute;
	top: 183px;
	left: 50px;
	width: 352px;
	height: 42px;
	/*border: 1px solid;*/
}
.popup_cluster_sector3{
	position: absolute;
	top: 258px;
	left: 50px;
	width: 352px;
	height: 42px;
	/*border: 1px solid;*/
}

.popup_cluster_sector1_text1{
	position: absolute;
	top: 0px;
	left: 38px;
	width: 300px;
	height: 17px;
	font-weight: 500;
	text-align: left;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	color: #083338;
	/*border: 1px solid;	*/
}

.popup_cluster_sector1_text2{
	position: absolute;
	top: 25px;
	left: 38px;
	width: 340px;
	height: 17px;	
	font-weight: 500;
	text-align: left;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
	color: #97A0A2;	
	/*border: 1px solid;*/
}

.popup_cluster_button{
	position: absolute;
	bottom: 30px;
	left: 50%;
	margin-left: -130px;
	width: 260px;
	height: 45px;
	border: 0px;
	border-radius: 4px;
	outline: 0px;
	background-color: #FF5A00;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	color: #fff;
}