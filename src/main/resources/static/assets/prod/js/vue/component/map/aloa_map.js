var aloaMap = {
  template: `
	<div id="map">
	
		<clock  ref="clock" v-show="isAnonymousUser && project.status === Constant.PROJECT_STATUS.IN_PROGRESS" v-bind:style="{right:  fields.isShowStationListPanel ? '240px' : '150px' }" ></clock>

		<div class="coordinate_info" v-if="fields.centerPos != null">
        	<div class="coordinate_icon"></div>
        	<div class="coordinate_text">{{(fields.centerPos.x).toFixed(6)}},{{(fields.centerPos.y).toFixed(6)}}</div>
		</div>

		<canvas id="canvas" ref="map_canvas" tabindex="1" 
			@mousemove="onMouseMove" 
			@mousedown="onMouseDown" 
			@mouseup="onMouseUp"
			@dblclick="onDoubleClick"
			@wheel="onTestZoom"
			@mousedown.ctrl="onMouseDownCtrlKey"
			@mouseup.ctrl="onMouseUpCtrlKey"			
			@mousedown.shift="onMouseDownShiftKey"
			@mouseup.shift="onMouseUpShiftKey"
			@mouseleave="onMouseLeave"
			@contextmenu.prevent.stop="onShowContextMenu($event)"> 
		</canvas>
		
		<div v-if="false" class="map_side_prev_next_button_area">
			<div class="map_side_button1_next" @click="onRestoreLocationHistory(false)" >				
			</div>
			<div class="map_side_button1_prev" @click="onRestoreLocationHistory(true)" >				
			</div>		
		</div>
		
		<div class="map_side_button_area">		
<!--			<button class="map_side_button_1" style="position:relative; top: 0px;" @dblclick="onLoadRealDrivingPath">-->
<!--				<div class="map_side_button1"></div>-->
<!--			</button>			-->
			<!-- 미구현 기능 막음
			<button class="map_side_button_1" style="position:relative; top: 3px;" >
				<div class="map_side_button2"></div>
			</button>			
			-->
			<button class="map_side_button" style="position:relative; top: 15px;" @click="onZoomIn">
				<div class="map_side_button3"></div>
			</button>			
			<button class="map_side_button" style="position:relative; top: 11px;" @click="onZoomOut">
				<div class="map_side_button4"></div>
			</button>		

<!--주유소 버튼 삭제-->
<!--			<button class="map_side_button_1" style="position:relative; top: 20px;" v-if="fields.isShowStationListButton "  @click="onSetShowStationListPanel( true ) ">-->
<!--				<div class="map_side_button2"></div>-->
<!--			</button>-->
		</div>
		
		
		<div class="map_side_button_area2" v-bind:style="{bottom: this.fields.breadCrums.isShow ? '256px' : '15px' }" >		
			<input type="checkbox" name="" class="input_layer_map" id="bt_layer_map">
			<label for="bt_layer_map" class="bt_layermap"></label>
			<div class="bt_layer_option_area"> 
				<div class="bt_layer_option_area_animation">
					<label></label>
					<input type="checkbox" name="option" class="option_input_layer" id="option_layer" v-model="fields.layerMode.landRegister" @click="toggleShowSiGunGuLayer">
					<label for="option_layer" class="bt_option_layer"></label>
					<input type="checkbox" name="option" class="option_input_player" id="option_player" v-model="fields.layerMode.zipCode" @click="toggleShowPostalCodeLayer" >
					<label for="option_player" class="bt_option_player"></label>					
					
					<input  v-if="isAnonymousUser" type="checkbox" name="option" class="option_input_range" id="option_range" v-model="fields.layerMode.evStation" @click="onChangeEvStationLayerMode()" >            
					<label  v-if="isAnonymousUser" for="option_range" class="bt_option_range"></label>
					<!--
					<input  v-if="isAnonymousUser" type="checkbox" name="option" class="option_input_flayer" id="option_flayer" v-model="fields.layerMode.gasStation" >               
					<label  v-if="isAnonymousUser" for="option_flayer" class="bt_option_flayer"></label>
					-->
				</div>
			</div>
          
			<input type="button" v-bind:id=" fields.fullMapMode ? 'map_side_button7' : 'map_side_button6' " class="map_side_button" style="position:relative; top: 55px"  @click="onToggleMapViewMode" >	
		</div>		
		
		<button class="bt_breadcrum" v-if="!fields.breadCrums.isShow" @click="onShowRiderTimeline"></button>
		
		<infobox-energy-gauge v-if="fields.popUp == Constant.POPUP_TYPE.ENERGY_GAUGE" v-bind:style= "getEnergyGaugeInfoBoxStyle(fields.focusedObject)" v-bind:rider="fields.focusedObject" v-bind:param= "fields.infoBoxParam" @close="onCloseAllPopup()" > </infobox-energy-gauge>
		<infobox-gas-station  v-if="fields.popUp == Constant.POPUP_TYPE.GAS_STATION" v-bind:style= "getGasStationInfoBoxStyle(fields.focusedObject)" v-bind:station="fields.focusedObject" v-bind:param= "fields.infoBoxParam" @close="onCloseAllPopup()"  @clicked ="onClickButtonStationOnStationInfoBox(fields.infoBoxParam)"> </infobox-gas-station>
		
		
<!--		미국/일본 지도 나오지 않아서 버튼 삭제-->
<!--		<div v-if="isAnonymousUser" class="map_bottom_toolbar">-->
<!--			<div class="map_bottom_button_bar">-->
<!--			   <div class="map_bottom_button_bar__item">-->
<!--				 <button class="map_bottom_button_bar__button" @click="showMap('kr')">{{$t("한국지도")}}</button>-->
<!--			   </div>-->
<!--			   <div class="map_bottom_button_bar__item">-->
<!--				 <button class="map_bottom_button_bar__button" @click="showMap('us')">{{$t("미국지도")}}</button>-->
<!--			   </div>-->
<!--			   <div class="map_bottom_button_bar__item">-->
<!--				 <button class="map_bottom_button_bar__button" @click="showMap('jp')">{{$t("일본지도")}}</button>-->
<!--			   </div>-->
<!--			   <div class="map_bottom_button_bar__item">-->
<!--				 <button class="map_bottom_button_bar__button" @click="showMap('de')">{{$t("독일지도")}}</button>-->
<!--			   </div>			   -->
<!--			</div>-->
<!--		</div>-->
		
		<context-menu
			ref="contextMenu"
			:menuId="'mapContextMenu'"
			:menuItems="onGetContextMenuItem()"
			@menuItem-clicked="onContextMenuItemClicked"
			@menu-opened="onContextMenuOpened"
			@menu-close="onContextMenuClose">
			</context-menu>		

			<jqx-tooltip 
					ref="deliveryInfoToolTip"
                    :autoHide="false" 
                    :position="'absolute'" 
                    :absolutePositionX="500" 
                    :absolutePositionY="500"
                    :name="'movieTooltip'"
                    :closeOnClick="true" 
                    :trigger="'none'" 
                    :content= "deliveryInfoData"
            >
			</jqx-tooltip>
		
		<rider-search-popup ref="riderSearchPopup"></rider-search-popup>
		<address-search-popup ref="addressSearchPopup"></address-search-popup>

		<div id="map_bottom_breadcrums" v-if="fields.breadCrums.isShow">
			<bread-crums @close="fields.breadCrums.isShow = false"
			ref="breadCrums"
			:startTime=fields.breadCrums.startTime 
			:endTime=fields.breadCrums.endTime 
			:timeInterval=fields.breadCrums.timeInterval 
			:projectStatus=project.status
			:riders=project.riders 
			>
			</bread-crums>
		</div>
		
		<station-list-panel v-if="fields.isShowStationListPanel" v-bind:stations="fields.allEvStationListOnRoute" @search-station = "onSearchStation" @close="onSetShowStationListPanel(false)" @item-clicked="onStationClickedOnList"></station-list-panel>
		
		<alert-reservation-setting v-if="fields.isShowAlertReservationSetting"  @dismiss="fields.isShowAlertReservationSetting = false"> </alert-reservation-setting>
		
	</div>`,
  props: {
    mapModule: null,
    profile: null
  },

  data: function () {
    return {
      map: null,
      imageBasePath: "/assets/image",
      eventHandlers: [ // 이벤트 핸들러
        {event: EVENT.MAP.LOGIN_SUCCESS, handler: this.onLoginSuccess},
        {event: EVENT.MAP.PROJECT_LOAD, handler: this.onLoadProject},
        {event: EVENT.MAP.PROJECT_ADD, handler: this.onResetProject},
        // Demo 소스에서 넘어온 이벤트 처리 [
        {event: EVENT.MAP.SET_PIN_MODE, handler: this.onSetPinMode},
        {event: EVENT.MAP.PERFORM_CLUSTERING, handler: this.onPerformClusterAll},
        {event: EVENT.MAP.PERFORM_ROUTING, handler: this.onPerformRouting},
        {event: EVENT.MAP.PERFORM_CANCEL_ROUTING, handler: this.onPerformCancelRouting},

        {event: EVENT.MAP.SELECTION_MODE_CHANGED, handler: this.onSelectionModeChanged},
        {event: EVENT.MAP.PROJECT_EXCEL_DROP, handler: this.onDropExcel},
        {event: EVENT.MAP.PROJECT_CSV_DROP, handler: this.onDropCsv},
        {event: EVENT.MAP.PROJECT_RIDER_EXCEL_DROP, handler: this.onRiderDropExcel},
        {event: EVENT.MAP.PERFORM_SHOW_SETTING_POPUP, handler: this.onShowSettingPopup},
        {event: EVENT.MAP.RIDER_SELECTED, handler: this.onRiderSelected},
        {event: EVENT.MAP.DESTINATIONS_SELECTED, handler: this.onDestinationsSelected},
        {event: EVENT.MAP.ALL_RIDERS_SELECTED, handler: this.onAllRidersSelected},
        {event: EVENT.MAP.DESTINATION_SELECTED, handler: this.onDestinationSelected},
        {event: EVENT.MAP.ALL_DESTINATIONS_SELECTED, handler: this.onAllDestinationsSelected},

        {event: EVENT.MAP.SETTING_CHANGED_NOTIFY, handler: this.onSettingChanged},
        {event: EVENT.MAP.PERFORM_SENDING, handler: this.onPerformSending},
        {event: EVENT.MAP.PERFORM_SENDING_AFTER_ROUTING, handler: this.onPerformSendingAfterRouting},
        {event: EVENT.MAP.PERFORM_RIDER_SENDING, handler: this.onPerformRiderSending},
        {event: EVENT.MAP.SCREEN_SIZE_CHANGED_NOTIFY, handler: this.onScreenSizeChanged},

        {event: EVENT.MAP.UPDATE_DELIVERY_STATUS, handler: this.onUpdateDeliveriesStatus},
        {event: EVENT.MAP.UPDATE_DELIVERIES_ROUTE_PATH, handler: this.onUpdateNewRoutePath},
        {event: EVENT.MAP.UPDATE_DELIVERIES, handler: this.onUpdateDeliveries},
        {event: EVENT.MAP.NOTIFICATION_OCCUR, handler: this.onNotificationToNoReadCount},
        // Demo 소스에서 넘어온 이벤트 처리 ]

        {event: EVENT.MAP.UPDATE_RIDER_WORK_STATUS, handler: this.onUpdateRiderWorkStatus},
        {event: EVENT.MAP.UPDATE_RIDER_DISPATCH_STATUS, handler: this.onUpdateRiderDispatchStatus},
        {event: EVENT.MAP.PROJECT_TERMINATE, handler: this.onProjectTerminate},

        {event: EVENT.MAP.UPDATE_DIALOG_MESSAGE, handler: this.onUpdateDialogMessage},


        {event: EVENT.MAP.SET_CENTER_FOCUS, handler: this.onSetCenterFocus},
        {event: EVENT.MAP.ADD_PROJECT_DELIVERY, handler: this.onAddDestination},
        {event: EVENT.MAP.MODIFY_PROJECT_DELIVERY, handler: this.onModifyDestination},
        {event: EVENT.MAP.MODIFY_FORCE_DELIVERY_STATUS, handler: this.onModifyForceDeliveryStatus},
        {event: EVENT.MAP.ADD_PROJECT_RIDER, handler: this.onAddRider},
        {event: EVENT.MAP.ADD_RIDER, handler: this.onAddRider},
        {event: EVENT.MAP.SIMULATION_ADD_RIDERS, handler: this.onSimulationAddRiders},
        {event: EVENT.MAP.SIMULATION_RIDER_CHANGED_RIDER_PROJECT, handler: this.onSimulationRiderChangedRiderProject},
        {
          event: EVENT.MAP.SIMULATION_RIDERS_TO_DISPATCH_RIDERS_PROJECT_EXCEL,
          handler: this.onSimulationRidersToDispatchRidersProjectExcel
        },

        {event: EVENT.MAP.CHANGE_DESTINATION_CLUSTER, handler: this.onChangeClustering},
        {event: EVENT.MAP.RIDERS_SWITCH_CLUSTERING, handler: this.onRidersSwitchClustering},


        //목적지,기사 정보 업데이트 이벤트
        {event: EVENT.MAP.REMOVE_SELECTED_DESTINATIONS, handler: this.onCommandRemoveDestinations},
        {event: EVENT.MAP.REMOVE_SELECTED_RIDERS, handler: this.onCommandRemoveRiders},

        {event: EVENT.MAP.PERFORM_ROLLBACK, handler: this.onRollbackProject}, //명령 롤백 제거

        {event: EVENT.MAP.PERFORM_REFRESH_PROJECT, handler: this.onRefreshProject},

        {event: EVENT.MAP.ADD_DESTINATION_BY_SEARCH, handler: this.onAddDestinationBySearch},
        {event: EVENT.MAP.MODIFY_PROJECT_RIDER, handler: this.onUpdatedRiderInfo},

        {event: EVENT.MAP.SHOW_DELIVERY_DETAIL_POPUP, handler: this.onShowDeliveryDetailPopup},
        {event: EVENT.MAP.SHOW_DRIVER_DETAIL_POPUP, handler: this.onShowRiderDetailPopup},

        {event: EVENT.MAP.RIDER_LIST_POPUP_REMOVE_RIDER_FROM_PROJECT, handler: this.onRemoveRiderFromProject},
        {event: EVENT.MAP.RIDER_LIST_POPUP_REMOVE_RIDERS_FROM_PROJECT, handler: this.onRemoveRidersFromProject},
        {event: EVENT.MAP.RIDER_LIST_POPUP_ADD_RIDER_TO_PROJECT, handler: this.onAddRiderToProject},
        {event: EVENT.MAP.RIDER_LIST_POPUP_ADD_RIDERS_TO_PROJECT, handler: this.onAddRidersToProject},

        {event: EVENT.MAP.SHOW_GRID_VIEW_PROJECT, handler: this.onShowGridView},
        {event: EVENT.MAP.CHANGED_GRID_VIEW_DATA_PROJECT, handler: this.onChangedGridViewData},
        // 관제 중인 프로젝트를 프로젝트 관리에서 삭제시 삭제 후 empty project load 하게 구현함.
        {event: EVENT.MAP.PERFORM_RESET, handler: this.onResetProjectAndEmptyProject},

        {event: EVENT.MAP.UPDATE_ROUTING_OPTION, handler: this.onLoadRouteOption},
        {event: EVENT.MAP.UPDATE_CLUSTERING_OPTION, handler: this.onUpdateClusteringOption},
        {event: EVENT.MAP.UPDATE_CLUSTERING_ON_DEMAND_OPTION, handler: this.onUpdateOnDemandClusteringOption},
        {event: EVENT.MAP.LEFT_PANEL_ADD_RIDER, handler: this.onAddRiderOnLeftPanel},
        {event: EVENT.MAP.SHOW_PRODUCT_VIEW_PROJECT, handler: this.onShowProductPage},
        {event: EVENT.MAP.SHOW_MOCEAN_VEHICLE_LIST_VIEW, handler: this.onShowMoceanListView},
        {
          event: EVENT.MAP.SHOW_MOCEAN_REAL_TIME_TEMPERATURE_VIEW,
          handler: this.onShowMoceanRealTimeTemperaturePopupView
        },
        {event: EVENT.MAP.SHOW_MOVEAN_TODAY_DRIVING_DAILY_VIEW, handler: this.onShowMoceanTodayDrivingDailyPopupView},
        {event: EVENT.MAP.UPDATE_RIDER_WORK_COMPLETION, handler: this.onUpdateRiderWorkCompletion},
        {event: EVENT.MAP.PROJECT_PRODUCT_LOADING, handler: this.onUpdateProductLoadProject},
        {event: EVENT.MAP.UPDATE_PRODUCT_PAGE, handler: this.onUpdateProductPage},
        {event: EVENT.MAP.LOAD_OTHER_USER_WATCH_PROJECT, handler: this.loadOtherUserWatchProject},
        {event: EVENT.MAP.NEW_EBAY_DELIVERY_ADDED, handler: this.onProjectRefresh},

        {event: EVENT.MAP.UPDATE_MPP_SETTING, handler: this.onUpdateMPPSetting},

        {event: EVENT.MAP.LOGOUT_USER, handler: this.onUserLogout},

        //[Demo] Event-Handlers
        {event: EVENT.MAP.PERFORM_SIMULATED_DRIVING, handler: this.onSimulatedDrivingMode},
        {event: EVENT.MAP.NEW_DELIVERY_ADDED, handler: this.onPerformNewDeliveries},

        // { event: EVENT.MAP.DEMO_PROJECT_SELECTED, handler: this.onSelectDemoProject},
        // { event: EVENT.MAP.SET_DEMO_MODE_CHANGED, handler: this.onSetDemoModeChanged },
        // { event: EVENT.MAP.SET_MOBILE_TRACKING_MODE_CHANGED, handler: this.onMobileTrackingModeChanged },

        {event: EVENT.MAP.AUTO_ADD_RIDER, handler: this.onAutoAddRider},

        {event: EVENT.MAP.AUTO_ADD_DISPATCH_RIDER, handler: this.onAutoAddDispatchRider},

        {event: EVENT.MAP.IS_ROUTE_LINE_DISPLAY, handler: this.onShowRouteLineDisplay},

        {event: EVENT.MAP.CANCEL_DELIVERY, handler: this.onCancelDelivery},

        {event: EVENT.MAP.PERFORM_RIDER_ROUTING, handler: this.onPerformRiderRouting},

      ],

      project: {
        "id": 0,
        "name": 'Project',
        "status": null,
        "riders": [],
        "destinations": [],
        "hubTotalCount": 0,
        "branchTotalCount": 0,
        "lastDestinationTotalCount": 0,
        "riderTotalCount": 0,
        "lastWaypointId": -1,
        "statistics": {},
        "attribute": {
          "isClusterDone": false,
          "isRouteEnabled": false,
          "isSendingRiderEnabled": false,
          "isFirstRoutingDone": false,
          "isReadOnly": false,
          "projectLoadingMode": Constant.PROJECT_LOADING_MODE.AUTO,
          "clusterRuleOnDemand": Constant.ON_DEMAND_CLUSTER_RULE.ETA,
          "isAddDeliveryToLastOrder": false,
        },
        "isAnonymous": true,
        "isTempProject": false,
        "isModifyState": false,
        "updateCount": 0,//[브래드 크럼 업데이트 이슈] 강제로 이값을 바꿔서 watch가 호출되도록 할때 사용
        "cutoffTime": null,
        "isAllClustered": false,
      },

      zoomLevel: 0,

      fields: {
        lastMousePoint: {x: -1, y: -1},
        isMouseMove: false,
        isMouseDown: false,

        // Rider Timestamp
        processingFlagRiderTimestamp: false,
        timerRiderTimestamp: null,
        savedShowRiderTimestamp: false,

        topToolbarSelectionMode: 0,			// 0: None, 1: Rider, 2: Destination

        pinMode: Constant.PINMODE.DEFAULT,

        isMapRedrawable: false,
        isShowSettingPopup: false,

        pollingCurrentLocations: undefined,

        popUp: 0,

        fullMapMode: false,
        isClusteringDone: false,
        // mapMode: Constant.MAPMODE.DEFAULT, //사용하지 않음 project.status로 대채
        breadCrums: {
          isShow: false,
          startTime: null,
          endTime: null,
          timeInterval: 30,
        },
        routeOption: 0,

        localeTags: new Map,

        savedScreenUpdatedMapLevel: -1,
        savedShowScreenRiderPin: false,
        mouseWheelCheckTimer: -1,
        pollingIntervalId: null,
        isShowErrorPopup: false,

        locationWorker: undefined,
        simulationWorker: undefined,
        deliveryStatusWorker: undefined,
        geoBufferWorker: undefined,

        selectedCountry: "kr",


        // mapViewController: undefined,// service를 통하여 호출함.

        vehiclesBoundRect: {left: 180, top: 90, right: -180, bottom: -90},		// 선택된 기사들에 대해 setZoomBound을 수행하기 위해 전역적으로 영역값을 관리한다.
        destinationsBoundRect: {left: 180, top: 90, right: -180, bottom: -90},		// 선택된 기사들에 대해 setZoomBound을 수행하기 위해 전역적으로 영역값을 관리한다.

        isMppTest: false,

        tempRiderCount: 0,
        tempDestCount: 0,

        countryCenterPosition: {
          kr: {x: 127.045109, y: 37.542816},
          us: {x: -73.974302, y: 40.783081},
          jp: {x: 139.777390, y: 35.696660},
        },

        centerPos: null, //지도 가운데 좌표값

        //[demo]
        isShowContextMenu: false,
        contextMenuType: 0,

        focusedObject: null,
        infoBoxParam: {},
        trackingPause: false,
        isShowStationListPanel: false,
        allEvStationListOnRoute: [],
        isShowStationListButton: true,

        activatedRiderId: null,
        isShowAlertReservationSetting: false,

        isExpandLayerButton: false,
        layerMode: {
          landRegister: false,
          zipCode: false,
          evStation: false,
          gasStation: false,
        },

        isMobile: false, // 모바일인지
        isTouchMove: false, // 터치로 움직이고 있는지
        isShowSiGunGuLayer: false,			// 시군구 레이어 표시 여부
        isShowPostalCodeLayer: false,		// 우편번호 레이어 표시 여부

        currentDeliveryType: null,

      },//fields

      /**service **/
      service: undefined,
      prodService: undefined,
      demoService: undefined,

      deliveryInfoData: '',

      toggle: false,

    }
  },

  watch: {
    project: {
      deep: true,
      immediate: true,
      handler(project) {
        if (window.parent.app && window.parent.app.$refs != undefined) {
          this.$emit(EVENT.MAP.WATCH_PROJECT_CHANGED, project);

          // 로컬 스토리지 저장
          // (0.1초 딜레이 후에 저장해야 빠지는 데이터 없이 저장 된다.
          // 하지만 배열속 배열값이 변경시 watch에 들어오지 않는다.
          // 그래서 경로탐색 후에 프로젝트를 로컬 스토리지에 저장하도록 한다.
        }
      },
    },

    zoomLevel: function (newValue, oldValue) {
      // 차량 Tag 처리 & 방문지 Pin 처리
      this.service.changedZoomLevel(newValue, oldValue);
      this.fields.zoomLevel = this.zoomLevel;
    },

    fields: {
      deep: true,
      immediate: true,
      handler(fields) {
        // console.log("[watch] fields updated");
        if (!this.fields.isShowContextMenu) {
          this.closeContextMenu();
        }
      },
    },

  },

  created: function () {

    EVENT.registerEventHandler(this, this.eventHandlers);

    //watch callback은 최대 1초에 1회만 호출됨
    EVENT.registerEventHandler(this,
        [{event: EVENT.MAP.WATCH_PROJECT_CHANGED, handler: _.throttle(this.onWatchProjectChanged, 1000)}
        ]);

    //updateMapView 는 최대 1초에 1회만 호출됨
    EVENT.registerEventHandler(this,
        [{event: EVENT.MAP.UPDATE_MAP_VIEW, handler: _.throttle(this.onUpdateMap, 1500)}
        ]);

    Vue.use(VTooltip);
  },

  mounted() {
    // 순서가 매우 중요하다. 절대 변경 금지 [
    const MAP_COUNTRY = this.chooseCountry();

    const mapModulePlugin = document.createElement("script");
    mapModulePlugin.setAttribute(
        "src", "/assets/common/js/map/logimap-module-handler.js"
    );
    mapModulePlugin.async = true;
    document.body.appendChild(mapModulePlugin);

    const mapPlugin = document.createElement("script");

    // mapPlugin.setAttribute(
    // 	"src", "/assets/common/js/map/" + MAP_COUNTRY + "/map.js"
    // );

    mapPlugin.setAttribute(
        "src", "/assets/common/js/map/map.js"
    );

    mapPlugin.async = true;
    document.body.appendChild(mapPlugin);
    // 변경 금지 ]

    this.fields.isMobile = Util.checkUseMobile();

    /*
   * property로 넘어온 mapModule을 vue component의 map에 할당한다. vue component에서는
   * map 객체를 사용하여 wasm의 메서드를 호출한다.
   */
    if (this.mapModule) {
      //this.mapViewController = new MapViewController(this.mapModule);//TODO:궁극적으로는 삭제함. service를 통하여 호출함.

      try {
        console.log("mapModule exists! " + mapModule);
        this.initService();
        // this.map = eval(this.mapModule);

        let _this = this;
        const initInterval = setInterval(function () { // 초기화하는데 시간이 다소 소요되기 때문에 1초마다 수행 (성공할 때 까지)
          try {

            //
            //엔진이 로드 되기 전에 함수가 호출 되면 안 되기 때문에 this.mapModule.getLoadedWasm 함수를 호출하여 엔진이 로드 되었는지 확인 후 진행합니다.
            if (this.mapModule.getLoadedWasm()) {
              _this.demoService.loadImageFiles();
              let position = MapUtil.getCountryCenterPosition(_this.fields.selectedCountry);
              _this.service.callSetCenterFocus(position.x, position.y, 5, false);
              _this.zoomLevel = _this.service.callGetZoomLevel();		// 위에 setCenterFocus 호출 후에 불려야 한다.

              const userInfo = _this.$store.getters.getLoginUserInfo;
              if (userInfo) {
                _this.onLoginSuccess();
              }
              clearInterval(initInterval);

              Global.mapClientVersion = this.mapModule.getClientVersion();
              window.parent.app.$emit(EVENT.MAIN.SET_MAP_CLIENT_VERSION, Global.mapClientVersion); //map과 main은 Global변수를 공유할수 없으므로 이벤트로 전달해 준다
              console.log("mapClientVersion is " + Global.mapClientVersion);

            } else {
              console.warn("Warning - getLoadedWasm return false ");
            }
          } catch (e) {
            console.warn("Warning - Map module loading is failed during mounted() !!! - Retry again ");
          }
        }, 1000);
      } catch (e) {
        console.error("mapModule Exception: " + e)
      }
    } else {
      console.log("mapModule NOT exists!: ");
    }

    window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.SET_PROJECT_NAME, this.project.name);

    this.initWebWorkers();

    const _this = this;
    this.$nextTick(() => {
      window.parent.app.$emit(EVENT.MAIN.MAP_MOUNTED_COMPLETED);
      _this.checkAnonymousAndSwitchService();
      _this.service.updateMapMapFullScreen();
      _this.restoreLocaleMap();
      // _this.onSetDemoModeChanged( { mode : Constant.DEFAULT_SETTING.ROUTING_MODE });//부팅이후 데모 모드에 따른 초기값 설정
      _this.onSettingChanged();
      _this.onLoadRouteOption();


    })
  },

  beforeDestroy() {
    if (this.pollingIntervalId) {
      clearInterval(this.pollingIntervalId);
      this.pollingIntervalId = null;
    }
    this.stopTracking();
  },

  computed: {
    isAnonymousUser: function () {
      return this.project.isAnonymous;
    },
  },

  methods: {

    /**
     * service초기화
     */
    initService() {
      this.map = eval(this.mapModule); //eval
      this.prodService = new ProdService(this.map, this.project, this.fields);
      this.demoService = new DemoService(this.map, this.project, this.fields);
      this.service = this.demoService;
    },

    /**
     * 익명/상용 사용자에 따른 service 전환
     */
    checkAnonymousAndSwitchService() {
      this.project.isAnonymous = isAnonymous();
      if (this.project.isAnonymous == null)//처음 브라우저를 띄우면 null이  리턴된다. 이때는 익명 사용자로 간주하자.
        this.project.isAnonymous = true;
      this.service = this.project.isAnonymous ? this.demoService : this.prodService;
    },

    /**
     * 로그인 성공
     */
    async onLoginSuccess(isProject) {
      if (isProject)
        this.onResetProject();

      this.checkAnonymousAndSwitchService();//[demo]

      if (isProject)
        await this.service.loadEmptyProject();
    },

    initWebWorkers() {
      if (typeof (Worker) !== "undefined") {
        if (typeof (this.fields.locationWorker) == "undefined") {
          this.fields.locationWorker = new TMSWebWorkerProxy("/assets/prod/js/vue/component/map/worker/location_worker.js", this.onHandleLocationWorker);
        }

        if (typeof (this.fields.simulationWorker) == "undefined") {
          this.fields.simulationWorker = new TMSWebWorkerProxy("/assets/prod/js/vue/component/map/worker/simulation_worker.js", this.onHandleSimulationWorker);
        }

        if (typeof (this.fields.deliveryStatusWorker) == "undefined") {
          this.fields.deliveryStatusWorker = new TMSWebWorkerProxy("/assets/prod/js/vue/component/map/worker/delivery_status_worker.js", this.onHandleDeliveryStatusWorker);
        }

        if (typeof (this.newDeliveriesWorker) == "undefined") {
          this.fields.newDeliveriesWorker = new TMSWebWorkerProxy("/assets/prod/js/vue/component/map/worker/new_deliveries_worker.js", this.onHandlePollingWorker);
        }

        if (typeof (this.fields.geoBufferWorker) == "undefined") {
          this.fields.geoBufferWorker = new TMSWebWorkerProxy("/assets/prod/js/vue/component/map/worker/geo_buffer_worker.js", this.onHandleGeoBufferWorker);
        }

      } else {
        console.log("Oops!, we can't use Web Worker on this browser. :(");
      }
    },

    async onHandleLocationWorker(event) {
      await this.service.handleLocationWorker(event);
    },

    async onHandleSimulationWorker(event) {
      await this.service.handleSimulationWorker(event);
    },

    async onHandleDeliveryStatusWorker(event) {
      await this.service.handleDeliveryStatusWorker(event);
    },

    async onHandleGeoBufferWorker(event) {
      await this.service.handleGeoBufferWorker(event);
    },

    /**
     * Dispatch 버튼 클릭
     */
    async onPerformSending() {
      await this.service.onPerformSending();
    },

    async onPerformRiderSending(rider) {
      await this.service.onPerformRiderSending(rider);
    },

    /**
     * 클러스터링 버튼 클릭
     */
    async onPerformClusterAll(data) {
      await this.service.onPerformClusterAll();
    },

    /**
     * 라우팅 버튼 클릭
     */
    async onPerformRouting(data) {
      await this.service.onPerformRouting(data);
    },

    onPerformSendingAfterRouting(data) {
      this.service.onPerformSendingAfterRouting(data);
    },

    onPerformCancelRouting(data) {
      this.service.performCancelRouting(data);
    },

    onPerformRiderRouting(data) {
      this.service.onPerformRiderRouting(data);
    },

    resize: function (rect) {
      if (rect.w) {
        document.getElementById("aloaMap").style.width = rect.w;
      }

      if (rect.h) {
        document.getElementById("aloaMap").style.height = rect.h;
      }
    },

    onZoomIn: function () {
      this.service.callSetZoomIn();
      this.zoomLevel = this.service.callGetZoomLevel();
			this.showClusterNameChecking();
    },

    onZoomOut: function () {
      if (this.service.callGetZoomLevel() == Constant.MAX_MAP_LEVEL) {
        return;
      }
      this.service.callSetZoomOut();
      this.zoomLevel = this.service.callGetZoomLevel();
			this.showClusterNameChecking();
    },

		showClusterNameChecking(){
			if (this.isClusteringStatus()) {
				_.forEach(this.project.riders, rider => {
					this.service.isShowClusterName(rider);
				})
			}
		},

    onMouseMove: function (event) {
      this.service.mouseMove(event);
    },

    onDoubleClick: function (event) {
      this.service.doubleClick(event);
    },

    onMouseDown: function (event) {
      //context-menu click가 아닐 경우에만 나타나게 수정함. Glovis User 아닌 경우에만 나타나게 수정함.

      //마운스 왼쪽 버튼
      if (event.button === 0) {
        this.service.mouseDown(event);
        if (!this.isGlovisUserCompany()) {

          if (this.$refs.deliveryInfoToolTip)
            this.$refs.deliveryInfoToolTip.close();

          let selectedItems = this.service.getDataFromMultiPositions(event.clientX, event.clientY);
          console.log(selectedItems);

          //배송지가 너무 많을 경우 delivery info 나타나기 않게 하기 위해서 사용함.
          if (this.project.destinations.length <= 200 && selectedItems.length)
            this.service.showDeliveryInfo(selectedItems[0], this.$refs.deliveryInfoToolTip); // pin 정보 tooltip 지원

          if (selectedItems.length && selectedItems[0].info.riderId) {
            const findRider = _.find(this.project.riders, {riderId: selectedItems[0].info.riderId})
            console.log(findRider)
            this.service.riderSelectedItem(findRider, true);
          }
        }
      }
      //마운스 오늘쪽 버튼
      else if (event.button === 2) {
        if (this.$refs.deliveryInfoToolTip)
          this.$refs.deliveryInfoToolTip.close();

        _.forEach(this.project.riders, rider => {
          this.service.riderSelectedItem(rider, false);
        })
      }

      // Mobile Map Touch 주석처리
      //MapDown(event.clientX, event.clientY);
    },

    onMouseUp: async function (event) {
      // Mobile Map Touch 주석처리
      //MapUp(event.clientX, event.clientY);
      await this.service.mouseUp(event, this.$refs);
    },

    onDoubleClick: function (event) {
      this.service.doubleClick(event);
    },

    onTouchStart: async function (event) {
      tcpoints = prevtcpoints = event.targetTouches;
      if (tcpoints.length == 1) {
        active = false;
        this.onMouseDown(tcpoints[0]);
      } else active = true;
    },
    onTouchMove: async function (event) {
      prevtcpoints = tcpoints;
      tcpoints = event.targetTouches;
      if (tcpoints.length == 1) {
        active = false;
        this.onMouseMove(tcpoints[0]);
      } else active = true;

      this.fields.isTouchMove = true;
    },
    onTouchEnd: async function (event) {
      active = false;
      if (tcpoints.length == 1) this.onMouseUp(tcpoints[0]);

      this.fields.isTouchMove = false;
    },

    onAllRidersSelected(on, mapClicked = false) {
      this.service.allRidersSelected(on, mapClicked);
    },


    onRiderSelected(rider, mapClicked = false, allRiders = false) {
      this.service.riderSelected(rider, mapClicked, allRiders);
      this.zoomLevel = this.fields.zoomLevel;
    },

    onDestinationsSelected(destination, mapClicked = false) {
      this.service.destinationsSelected(destination, mapClicked)
    },

    onAllDestinationsSelected(on, mapClicked = false) {
      this.service.allDestinationsSelected(on, mapClicked);
    },

    onDestinationSelected(destination, mapClicked = false, allDestinations = false) {
      this.service.destinationSelected(destination, mapClicked, allDestinations);
      this.zoomLevel = this.fields.zoomLevel;
    },

    onSelectionModeChanged: function (mode) {
      this.fields.topToolbarSelectionMode = mode;
    },

    onSetPinMode(pinMode) {
      this.fields.pinMode = pinMode;
    },

    onAddDestination(destination) {
      this.service.addDestination(destination);
    },

    onAddDestinationBySearch(delivery) {
      this.service.onAddDestinationBySearch(delivery);
    },

    onModifyDestination(data) {
      this.service.setModifyDestinationCommand(data);
    },

    onModifyForceDeliveryStatus(data) {
      this.service.modifyForceDeliveryStatus(data);
    },

    onAddRider(data) {
      this.service.setAddRidersCommand(data);
    },

    onSimulationAddRiders(data) {
      this.service.setSimulationAddRiders(data);
    },

    onSimulationRiderChangedRiderProject(data) {
      this.service.setSimulationRiderChangedRiderProject(data);
    },

    onSimulationRidersToDispatchRidersProjectExcel(data) {
      const userInfo = this.$store.getters.getLoginUserInfo;
      this.service.setSimulationRidersToDispatchRidersProjectExcel(data, userInfo);
    },

    onSetCenterFocus: function (obj) {
      this.service.setCenterFocusByObject(obj);
    },

    onCommandRemoveDestinations: function (ids) {
      this.service.setRemoveDestinationsCommand(ids);
    },

    onCommandRemoveRiders: function (ids) {
      this.service.setRemoveRidersCommand(ids);
    },

    onRollbackProject: function (obj) {
      this.service.performRollback();
    },

    onRefreshProject: function (data) {
      this.service.performRefreshProject(data);
    },

    // Context menu Callback [

    /**
     * Context menu 표시
     */
    onShowContextMenu(event) {
      this.service.showContextMenu(event, this.$refs.contextMenu);
      // if (!this.fields.contextMenuType !== Constant.CONTEXT_MENU_ITEM.ITEM_NOTHING) {
      // 	this.showContextMenu( event, selectedItem );
      // }
    },

    onContextMenuItemClicked(data) {
      data.menuItem.handler(data.selectedItem);
    },

    onContextMenuOpened(event) {
      this.service.contextMenuOpened(event);
      // console.log("onContextMenuOpened()");
    },

    onContextMenuClose(event) {
      this.fields.isShowContextMenu = false;
    },

    onResetProject(reload = true, isDeleteServerData = true) {
      this.initClock();
      this.service.resetProject(reload, isDeleteServerData);
    },

    async onResetProjectAndEmptyProject() {
      this.initClock();
      this.service.resetProject(true, true);
      await this.service.loadEmptyProject();
    },


    onShowSettingPopup() {//사용 안하는것 같음
      this.fields.isShowSettingPopup = !this.fields.isShowSettingPopup;
    },

    onToggleMapViewMode() {
      this.service.toggleMapViewMode();
    },

    showPopup(popUp) {
      this.closeAllPopup();
      this.fields.popUp = popUp;
    },

    isShowPopup(popUp) {
      return (this.fields.popUp == popUp);
    },

    closeAllPopup() {
      this.service.closeAllPopup();
      this.$refs.addressSearchPopup.hideAddressSearchPopup();

    },

    async onShowRiderTimeline() {
      this.service.showRiderTimeline();
    },

    async dismissRiderTimeline() {
      this.fields.breadCrums.isShow = false;
    },

    dismissNotificationPopup() {
      try {
        window.parent.app.$refs.notificationPopup.$emit('dismiss');
      } catch (e) {
        console.error("dismissNotificationPopup() Exception: " + e);
      }
    },

    async getSearchAddressByPoint(x, y) {
      return this.service.getSearchAddressByPoint(x, y);
    },

    getDetailAddress(addr) {
      return this.service.getDetailAddress(addr);
    },


    onScreenSizeChanged() {
      this.closeAllPopup();
    },

    onTestZoom: function (event) {
      const WHEEL_CHECK_INTERVAL_MILLIS = 50;
      // Mobile Map Touch 주석처리
      //OnWheel(event);

      this.$refs.deliveryInfoToolTip.close();

      let _this = this;
      if (this.fields.mouseWheelCheckTimer > 0)
        clearTimeout(this.fields.mouseWheelCheckTimer);
      this.fields.mouseWheelCheckTimer = setTimeout(() => {
        _this.zoomLevel = _this.service.callGetZoomLevel();
      }, WHEEL_CHECK_INTERVAL_MILLIS);
			this.showClusterNameChecking();
    },

    isClusteringStatus() {
      return MapUtil.isClusteringStatus(this.project);
    },

    chooseCountry() {
      this.fields.selectedCountry = this.$route.query.location;

      if (!this.fields.selectedCountry) {
        this.fields.selectedCountry = "kr";
      }
      return this.fields.selectedCountry;
    },

    showMap(country) {

      //우선 항상 한국 지도가 나오도록 한다
      country = "kr";

      this.$store.dispatch('setMapLocale', country);
      window.location.replace("?location=" + country);
    },

    restoreLocaleMap() {
      let currentLocation = this.$route.query.location;
      if (!currentLocation) {
        currentLocation = "kr";
      }

      const mapLocale = this.$store.getters.getMapLocale;
      if (MapUtil.isAvailableCountryCode(mapLocale) && MapUtil.isAvailableCountryCode(currentLocation) && mapLocale !== currentLocation) {
        this.showMap(mapLocale);
      }
    },

    /**
     * 프로젝트 로딩
     */
    async onLoadProject(data) {//data.projectId 또는 projectId 형태로 온다
      //clusterRule이 문제가 많아서 새로운 프로젝트 열릴때마다 초기값으로 0을 준다
      this.$store.dispatch('setClusterRuleOption', Constant.CLUSTER_RULE.NORMAL);

      await this.service.loadProject(data.projectId ? data.projectId : data, null, {moveMap: true});

    },

    /**
     * 배송 상태 변경
     */
    async onUpdateDeliveriesStatus(data) {
      await this.service.updateDeliveriesStatus(data);
    },

    onNotificationToNoReadCount() {
      // top tool bar noti 갯수 세팅
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.GET_NOTI_COUNT, this.project.id);

      // destinationListPanel 실시간 상태알림 변경을 위해 업데이트
      if (window.parent.app.$refs.leftPanel.$refs.destinationListPanel !== undefined) {
        window.parent.app.$refs.leftPanel.$refs.destinationListPanel.getNotificationList(this.project.id);
      }
    },

    async onUpdateNewRoutePath(data) {
      await this.service.updateNewRoutePath(data);
    },

    /**
     * 프로젝트 생성시 Status
     */
    onUpdateDialogMessage(data) {
      // window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.UPDATE_DIALOG_MESSAGE, data);
      console.log("onUpdateDialogMessage: " + data.message);
      PopupUtil.updateLoadingPopup(_t(data.message));
    },


    /**
     * 배송 목록 업데이트
     */
    onUpdateDeliveries(data) {
      this.service.updateDeliveries(data);
    },

    onProjectTerminate(data) {
      this.service.projectTerminate(data);
    },

    onUpdateRiderWorkStatus(data) {
      this.service.updateRiderWorkStatus(data);
    },

    onUpdateRiderDispatchStatus(data) {
      this.service.updateRiderDispatchStatus(data);
    },

    onUpdateRiderWorkCompletion(data) {
      this.service.updateRiderWorkCompletion(data);
    },

    onUpdateProductLoadProject(data) {
      this.service.UpdateProductLoadProject(data);
    },

    onUpdateProductPage(data) {
      this.service.UpdateProductPage(data);
    },

    onProjectRefresh(data) {
      this.service.onProjectRefresh(data);
    },

    loadOtherUserWatchProject(data) {
      this.service.loadOtherUserWatchProject(data);
    },

    async onLoadRealDrivingPath() {
      await this.service.loadRealDrivingPath();
    },

    onDropExcel(data) {
      const userInfo = this.$store.getters.getLoginUserInfo;
      this.service.dropExcel(data, userInfo);
    },

    onDropCsv(files) {
      const userInfo = this.$store.getters.getLoginUserInfo;
      this.service.dropCsv(files, userInfo);
    },

    onRiderDropExcel(files) {
      const userInfo = this.$store.getters.getLoginUserInfo;
      this.service.riderDropExcel(files, userInfo);
    },

    isGlovisUserCompany: function () {
      const userInfo = this.$store.getters.getLoginUserInfo;

      if (userInfo && Constant.GLOVIS_ORG_CODE_NAME === userInfo.orgCodeName) {
        return true;
      } else
        return false;
    },


    onSettingChanged() {
      this.service.settingChanged();
    },

    closeContextMenu() {
      // console.log("closeContextMenu()");
      if (this.$refs.contextMenu && this.$refs.contextMenu.isShow()) {
        this.$refs.contextMenu.hideContextMenu();
      }
    },


    getMapViewController() {
      return this.service.getMapViewController();
    },

    onLoadRouteOption() {
      const ROUTE_SETTING = this.$store.getters.getRouteSetting;
      console.log("Route option :" + SETTING.ROUTE.VEHICLE[ROUTE_SETTING.vehicle].text + "/" + SETTING.ROUTE.SEQUENCE_ORDER[ROUTE_SETTING.sequenceOrder].text + "/" + SETTING.ROUTE.OPTION[ROUTE_SETTING.option].text);
      this.fields.routeOption = SETTING.ROUTE.VEHICLE[ROUTE_SETTING.vehicle].value
          + SETTING.ROUTE.SEQUENCE_ORDER[ROUTE_SETTING.sequenceOrder].value
          + SETTING.ROUTE.OPTION[ROUTE_SETTING.option].value;
      console.log("onLoadRouteOption update route option : " + this.fields.routeOption);
    },

    onUpdateClusteringOption(clusterRuleOption) {
      //세팅이 바쓀때 자동으로 배차가 바뀌도록 함 - 삭제
      this.service.updateClusteringOption(clusterRuleOption);
    },

    onUpdateOnDemandClusteringOption(onDemandOptions) {
      console.log("onUpdateOnDemandClusteringOption : " + JSON.stringify(onDemandOptions));
      this.service.updateOnDemandClusteringOption(onDemandOptions);
    },

    onAddRiderOnLeftPanel() {
      this.service.addRiderOnLeftPanel();
    },

    async onUpdatedRiderInfo(data) {
      await this.service.setModifyRiderCommand(data);
    },

    onShowRiderDetailPopup(data) {
      this.service.showRiderDetailPopup(data);
    },


    onShowDeliveryDetailPopup(data) {
      this.service.showDeliveryDetailPopup(data);
    },

    onChangeClustering(data) {
      this.service.changeClustering(data);
    },

    onRidersSwitchClustering(data) {
      this.service.ridersSwitchClustering(data);
    },

    onRemoveRiderFromProject(data) {
      this.service.removeRiderFromProject(data);
    },

    onRemoveRidersFromProject(data) {
      this.service.setRemoveRidersCommand(data);
    },

    onAddRiderToProject(data) {
      this.service.addRiderToProject(data);
    },

    onAddRidersToProject(data) {
      this.service.addRidersToProject(data);
    },

    onShowGridView(data) {
      this.service.showGridView(data);
    },

    onChangedGridViewData(data) {
      this.service.changedGridViewData(data);
    },

    onShowProductPage(data) {
      this.service.showProductPage(data);
    },

    onShowMoceanListView() {
      this.service.showMoceanListView();
    },

    onShowMoceanRealTimeTemperaturePopupView(vinnumber) {
      this.service.showMoceanRealTimeTemperaturePopup(vinnumber);
    },

    onShowMoceanTodayDrivingDailyPopupView(vinnumber) {
      this.service.showMoceanTodayDrivingDailyPopup(vinnumber);
    },

    /*******************
     * 데모 추가 start functions
     *******************/

    // onSelectDemoProject( data ){
    // 	this.onResetProject(false, false, false );
    // 	this.service.selectDemoProject( data );
    // },

    onSimulatedDrivingMode(data) {
      this.service.simulatedDrivingMode();
    },


    onPerformNewDeliveries(data) {
      this.fields.newDeliveriesWorker.postMessage({command: 0, callUserId: data.callUserId, projectId: data.projectId});
    },


    async onHandlePollingWorker(event) {
      await this.service.handlePollingWorker(event);
    },

    getEnergyGaugeInfoBoxStyle(rider) {
      return this.service.getEnergyGaugeInfoBoxStyle(rider);

    },

    getGasStationInfoBoxStyle(station) {
      return this.service.getGasStationInfoBoxStyle(station);
    },

    getRightClockPositionStyle() {
      return this.service.getRightClockPositionStyle();
    },

    exportProject: function () {
      // if (this.project.riders.length > 0 /*1 && this.project.destinations.length > 0 */) { //EV_Demo 목적지가 없어도 저장할수 있도록 함.
      // 	EXCEL.utils.exportProject(this.project, null);
      // }
      //클러스터링 되지 않아도 저장할수 있는 엑셀 버전
      if (this.project.riders.length > 0 || this.project.destinations.length > 0) {
        EXCEL.utils.exportProjectV2(this.project, null);
      }
    },


    onGetContextMenuItem(contextMenuType) {
      if (this.service) {
        return this.service.getContextMenuItem();
      }
    },

    onSimulationTrackingResumed() {
      this.service.simulationTrackingResumed();
    },

    onSimulationTrackingPaused() {
      this.service.simulationTrackingPaused();
    },


    toggleShowSiGunGuLayer() {
      this.fields.isShowSiGunGuLayer = !this.fields.isShowSiGunGuLayer;
      this.service.showSiGunGuLayer(this.fields.isShowSiGunGuLayer);
    },

    toggleShowPostalCodeLayer() {
      this.fields.isShowPostalCodeLayer = !this.fields.isShowPostalCodeLayer;
      this.service.showPostalCodeLayer(this.fields.isShowPostalCodeLayer);
    },

    pauseClock() {
      if (this.$refs.clock)
        this.$refs.clock.pause();
    },

    initClock() {
      if (this.$refs.clock)
        this.$refs.clock.init();
    },

    onChangeFullfillmentMode() {
      this.service.onChangeFullfillmentMode();
    },

    async onUpdateMppSimulatedDriving(timeUnit, simulationTime) {
      this.service.updateMppSimulatedDriving(timeUnit, simulationTime);
    },

    onMouseDownCtrlKey(event) {
      this.service.mouseDownCtrlKey(event);
    },

    onMouseUpCtrlKey(event) {
      this.service.mouseUpCtrlKey(event);
    },

    onMouseDownShiftKey(event) {
      this.service.mouseDownShiftKey(event);
    },

    onMouseUpShiftKey(event) {
      this.service.mouseUpShiftKey(event);
    },

    onMouseLeave(event) {
      this.service.mouseLeave(event);
    },

    onUpdateMPPSetting(data) {
      this.service.onUpdateMPPSetting(data);
    },

    onUserLogout() {
      this.service.logoutUser();
    },

    onWatchProjectChanged(project) {
      console.debug("[Ui-update] aloa-map : watch called ");
      window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.UPDATE, project);
      window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.MAP_DATA_CHANGED, project);
      if (this.fields.breadCrums.isShow && this.$refs.breadCrums) {
        this.$refs.breadCrums.$emit(EVENT.BREAD_CRUMS.SET_RIDER_DATA, project.riders);//BreadCrum에도 업데이트
      }
    },

    onUpdateMap() {
      this.service.callUpdateMapView();
    },
    onStationClickedOnList(station) {
      this.service.stationClickedOnList(station);
    },

    async onSearchStation() {
      await this.service.searchStation();
    },

    onSetShowStationListPanel(isShow) {
      this.service.setShowStationListPanel(isShow);
    },
    //
    onClickButtonStationOnStationInfoBox(data) {
      this.service.clickButtonStationOnStationInfoBox(data);
    },

    onCloseAllPopup() {
      this.closeAllPopup();
    },

    // onRestoreLocationHistory(forward = false ){
    // 	this.demoService.restoreLocationHistory(forward);
    // },
    //
    onChangeEvStationLayerMode() {
      this.service.changeEvStationLayerMode();
    },


    onAutoAddRider() {
      this.service.autoAddRider();
    },

    onAutoAddDispatchRider() {
      this.service.autoAddDispatchRider();
    },

    onShowRouteLineDisplay(data) {
      this.service.showRouteLineDisplay(data);
    },
    // onSetDemoModeChanged( data ){
    // 	this.service.setDemoModeChanged( data );
    // },
    //
    // onMobileTrackingModeChanged(data){
    // 	this.service.mobileTrackingModeChanged( data);
    // },

    onCancelDelivery(data) {
      this.service.cancelDelivery(data);
    },


    /*******************
     * End 데모 추가 functions
     *******************/
  },
};
