var riderSearchComponent = {
    template:
        `
			<div class="toolbar_rider_search_area">
				<div class="toolbar_top_icon search"></div>
				<input type="text" :placeholder="$t('기사명 / 전화번호 검색')" @keyup.enter="searchSubmit" id="searchInput" v-model="searchKeyword">
			
				<ul class="toolbar_search_list" id="ul_rider">
					<li class="toolbar_search_empty" v-if="riderList.length == 0">
						<div class="toolbar_search_empty_search_text">{{searchResultKeyword}}</div>
						<div class="toolbar_search_empty_text_sub">{{$t("에 대한 결과가 없습니다.")}}</div>
						<div class="toolbar_search_empty_text_sub_text">{{$t("단어의 철자가 정확한지 확인 후")}} </br> {{$t("다시 검색해 주세요.")}} </div>
					</li>
					<li class="toolbar_search_item" v-for="rider in riderList" @click="clickRider(rider)">
						<div class="toolbar_search_rider_icon" v-if="!rider.profileImageUrl"></div>
						<div class="toolbar_search_rider_icon" v-else>
							<img :src="rider.profileImageUrl" style="width: 100%; height: 100%;">
						</div>
						<div class="toolbar_search_rider_name">
							<div class="toolbar_search_rider_circle" v-bind:class="getRiderShowStatus(rider)"></div>
							{{rider.name}}
							</div>
							<div class="toolbar_search_rider_license">{{rider.licensePlate}}</div>
					</li>
				</ul>
				<div class="toolbar_search_paging" v-if="riderList.length > 0">
					<div class="toolbar_search_paging_line"></div>
					<div class="st_search_list_page">
						<button class="st_search_list_page_first_n" @click="firstPage"></button>
						<button class="st_search_list_pageP_n" @click="prevPage"></button>
						<button class="st_search_list_pageNum" :id="'searchRiderPage_'+(currentIndex-1)" :class="(currentIndex-1) == currentPage ? 'pick' : ''" v-for="currentIndex in pages" @click="movePage(currentIndex-1)">{{currentIndex}}</button>
						<button class="st_search_list_pageN_n" @click="nextPage"></button>
						<button class="st_search_list_page_end_n" @click="endPage"></button>						
					</div>
				</div>
				<div>
					<input type="file" @change="onSelectFiles" style="display: none;" id="selectRiderExcel" accept=".xls, .xlsx, .csv" />
				</div>                
				<div class="toolbar_add_rider" @click="document.getElementById('selectRiderExcel').click();">
					<div class="toolbar_add_rider_excel_icon"></div>
					<div class="toolbar_add_rider_text">{{$t("엑셀로 기사 등록")}}</div>
				</div>
			</div>
	`,
    props: {
        project: Object,
    },
    data() {
        return {
            mapViewController: null,
            aloaMap: null,
            searchKeyword: "",
            riderList: [],
            totalElements: 0,
            totalPages: 0,
            currentPage: 0,
            selectedRiderIndex: -1,
            searchResultKeyword: "",
            displayTotalPage: 0,
            pages: [],
        }
    },
    watch: {},
    mounted: function () {
        this.aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;
        this.mapViewController = this.aloaMap.getMapViewController();
        this.loadRiderList(true);
    },
    destroyed: function () {
        this.initData();
    },
    methods: {
        sendEventToMap: function (event, data) {
            try {
                const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;
                aloaMap.$emit(event, data);
            } catch (e) {
                console.error("sendEventToMap exception: " + e);
            }
        },
        initData() {
            this.riderList = [];
            this.totalElements = 0;
            this.totalPages = 0;
            this.selectedRiderIndex = -1;
        },
        loadRiderList: function (isRefresh) {
            const _this = this;
            //PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

            RIDER_API.getRidersForManagement(
                /* page */ this.currentPage,
                /* size */ 20,
                /* sort */ "name",
                /* direction */ "asc",
                /* keyword */ this.searchKeyword,
                /* inTransit */ false,
                null,
                {
                    onSuccess: (response) => {
                        //PopupUtil.dismissLoadingPopup();
                        _this.initData();
                        _this.riderList = response.data.content;
                        _this.totalElements = response.data.totalElements;
                        _this.totalPages = response.data.totalPages;

                        // 검색 및 초기시에는 _this.pags 값을 초기화 해야함. 그래야 1 부터 시작함.
                        if (isRefresh == true) {
                            _this.pages = [];
                        }

                        _this.pages = PageUtil.getDisplayedPageIndexList(_this.currentPage, _this.totalPages, PageUtil.const.DISPLAYED_5PAGES);

                        // 스크롤을 맨 위로
                        document.getElementById('ul_rider').scrollTop = 0;

                        _this.riderList.forEach(rider => {
                            if (rider.profileImageUrl) {
                                rider.profileImageUrl += "?date=" + new Date().toISOString();
                            }
                        })
                    },
                    onError: (error) => {
                        //PopupUtil.dismissLoadingPopup();
                        console.error('기사 리스트 조회 실패');
                    }
                });
        },
        searchSubmit() {
            document.getElementById('searchInput').blur();
            //if (this.searchKeyword.length < 1) { return []; }
            this.currentPage = 0;
            this.searchResultKeyword = this.searchKeyword;
            this.loadRiderList(true);
        },

        firstPage: function () {
            const firstPageIndex = PageUtil.getFirstPageIndex(this.currentPage, this.totalPages);
            if (firstPageIndex != null && firstPageIndex !== this.currentPage) {
                this.currentPage = firstPageIndex;
                this.loadRiderList(false);
            }
        },

        prevPage: function () {
            const prevPageIndex = PageUtil.getPrevPageIndex(this.currentPage, this.totalPages, PageUtil.const.DISPLAYED_5PAGES);
            if (prevPageIndex != null && prevPageIndex !== this.currentPage) {
                this.currentPage = prevPageIndex;
                this.loadRiderList(false);
            }
        },

        movePage(pageIndex) {
            if (pageIndex != null && pageIndex !== this.currentPage) {
                document.getElementById('searchRiderPage_' + this.currentPage).classList.remove('pick');
                this.currentPage = pageIndex;
                this.loadRiderList(false);
                document.getElementById('searchRiderPage_' + this.currentPage).classList.add('pick');
            }
        },

        nextPage: function () {
            const nextPageIndex = PageUtil.getNextPageIndex(this.currentPage, this.totalPages, PageUtil.const.DISPLAYED_5PAGES);
            if (nextPageIndex != null && nextPageIndex !== this.currentPage) {
                this.currentPage = nextPageIndex;
                this.loadRiderList(false);
            }
        },

        endPage: function () {
            const lastPageIndex = PageUtil.getLastPageIndex(this.currentPage, this.totalPages);
            if (lastPageIndex != null && lastPageIndex !== this.currentPage) {
                this.currentPage = lastPageIndex;
                this.loadRiderList(false);
            }
        },

        getRiderShowStatus: function (rider) {
            return RiderUtil.getRiderShowStatus(rider);
        },

        clickRider: function (rider) {

            if (this.isRiderCurrentProject(rider)) {
                PopupUtil.alertPopup("'" + rider.name + "' 기사님은 이미 프로젝트에 포함되어 있습니다.");
            } else if(this.isRiderScanningWork(rider)){
							PopupUtil.alertPopup("'" + rider.name + "' 기사님은 스캔 업무 기사 입니다.");
						}
						else {
                PopupUtil.confirmPopupTitle("프로젝트에 추가", "기사를 현재 프로젝트에 추가합니다.", {
                    onConfirm: () => {
                        this.sendEventToMap(EVENT.MAP.RIDER_LIST_POPUP_ADD_RIDER_TO_PROJECT, rider);
                    },
                    onError: (error) => {
                        PopupUtil.alertPopup('기사 추가 실패');
                    }
                });
            }
        },

        onSelectFiles(e) {
            if (this.project.deleted) {
                PopupUtil.alertPopup("임시 프로젝트에서는 기사를 추가할수 없습니다");
                return;
            }

            //window.parent.app.$refs.leftPanel.onSelectFiles(e);
            window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.SELECT_FILES, e);
        },

        isRiderCurrentProject: function (rider) {
            let isRiderInProject = false;
            if (this.project.riders) {
                isRiderInProject = this.project.riders.filter(r => (r.riderId === rider.riderId)).length > 0;
            }
            return isRiderInProject;
        },

				isRiderScanningWork: function (rider) {
					if(rider.workAuthority === "SCANNING")
						return true
					else
						return false
				}
    },
};
