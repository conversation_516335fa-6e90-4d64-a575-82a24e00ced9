@charset "EUC-KR";
 @import url("/assets/common/css/font.css");
 * {
     margin: 0px;
     padding: 0px;
     font-family: Noto Sans KR;
     font-weight: 500;
}


 body{
     width: 100%;
     height: 100%;
}
 .bg{
     position: absolute;
     width: 100vw;
     height: 100vh;
     background-color: #ccc;
}
/*=======================????????????1=======================*/
 .toptoolbar_area_1{
     position: relative;
     width: 100%;
     min-width: 800px;
     height: 40px;
     background-color: #113E46;
     z-index: 100;
}

 .toptoolbar_area_line_1{
     position: relative;
     height: 1px;
     background-color:#0B3138;
     z-index: 99;
}

.toptoolbar_account_admin_list{
    position: absolute;
    top: 40px;
    right: 0px;
    width: 275px;
    height: 290px;
    background-color: #fff;
    display: none;
    box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.3);
    /*border: 1px solid;
    */
}

 .toptoolbar_account_list{
     position: absolute;
     top: 40px;
     right: 0px;
     width: 275px;
     height: 240px;
     background-color: #fff;
     display: none;
     box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.3);
    /*border: 1px solid;
    */
}

.toptoolbar_account_list0{
    position: relative;
    top: 0px;
    right: 0px;
    width: 275px;
    height: 40px;
    background-color: #F4F4F4;
}

 .toptoolbar_account_list1{
     position: relative;
     top: 0px;
     right: 0px;
     width: 275px;
     height: 80px;
     background-color: #fff;
}

 .toptoolbar_account_list2{
     position: relative;
     top: 0px;
     right: 0px;
     width: 255px;
     height: 33px;
     padding-top: 12px;
     padding-left: 20px;
     background-color: #fff;
     font-weight: 400;
     font-family: 'Noto Sans KR', sans-serif;
     font-size: 14px;
}

 .toptoolbar_account_list2:hover{
     background-color: #F4F4F4;
}

 .toptoolbar_account_pic{
     position: relative;
     top:21px;
     left:21px;
     width: 38px;
     height: 38px;
     background-image: url("/assets/image/prod/toptoolbar/img_input_nophoto.svg");
     background-size: 100%;
     background-color: rgba(0,0,0,0.3);
}

 .toptoolbar_account_button{
     position: absolute;
     top: 7px;
     right: 20px;
     width: 60px;
     height: 26px;
     border: 0px solid;
     border-radius: 2px;
     outline: none;
     background-color: #F4F4F4;
     font-weight: 400;
     font-family: 'Noto Sans KR', sans-serif;
     font-size: 13px;
}

.toptoolbar_account_button:hover {
    text-decoration: underline;
}

.toptoolbar_signup_button{
    position: absolute;
    top: 7px;
    right: 90px;
    width: 60px;
    height: 26px;
    border: 0px solid;
    border-radius: 2px;
    outline: none;
    background-color: #F4F4F4;
    font-weight: 400;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 13px;
}

.toptoolbar_signup_button:hover {
    text-decoration: underline;
}

.toptoolbar_change_ph_button {
     position: fixed;
     right: 170px;
     width: 100px;
     height: 42px;
     border: 0px solid;
     border-radius: 2px;
     outline: none;
     background-color: #F4F4F4;
     font-weight: 400;
     font-family: 'Noto Sans KR', sans-serif;
     font-size: 13px;
 }

.toptoolbar_change_ph_button:hover {
    text-decoration: underline;
}

.toptoolbar_change_pw_button{
    position: fixed;
    right: 85px;
    width: 100px;
    height: 42px;
    border: 0px solid;
    border-radius: 2px;
    outline: none;
    background-color: #F4F4F4;
    font-weight: 400;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 13px;
}

.toptoolbar_change_pw_button:hover {
    text-decoration: underline;
}

 .toptoolbar_account_text{
     position: absolute;
     top:28px;
     left: 70px;
     width: 150px;
     height: 22px;
    /*border: 1px solid;
    */
}

 .toptoolbar_area_2{
     position: relative;
     display: flex;
     width: 100%;
     min-width: 800px;
     height: 60px;
     z-index: 40;
     background-color: #113E46;
     transform: translateY(0px);
     transition: transform 1s cubic-bezier(0,.56,.32,.99);
}
    
.hide_toptoolbar_area_2{
    overflow: hidden;
    transform: translateY(-60px);
    transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
    height: 0;
}
 .toptoolbar_noti_list{
     position: absolute;
     top:40px;
     right: 0px;
     width: 228px;
     height: 100vh;
     z-index: 1;
     overflow: hidden;
     user-select: none;
}
 .toptoolbar_noti_list1{
     position: absolute;
     right: 0px;
     width: 228px;
     height: 100vh;
     z-index: 1;
     overflow-x: hidden;
     background-color: #F1F5F7;
     border: 1px solid #000;
}
 .toptoolbar_noti_list1::-webkit-scrollbar {
     display:none;
}
 .noti_toolbar{
     position: absolute;
     width: 100%;
     height:30px;
     border-bottom: 1px solid #D6E3E5;
}
 .noti_toolbar_text_area{
     position: absolute;
     display: flex;
     top:5px;
     left: 15px;
}
 .noti_toolbar_text1{
     position: relative;
     width: 40px;
     font-weight: 500;
     font-family: 'Noto Sans KR', sans-serif;
     font-size: 12px;
     color: #72949A;
}
 .noti_toolbar_text2{
     position: relative;
     left: 5px;
     font-weight: 500;
     font-family: 'Noto Sans KR', sans-serif;
     font-size: 12px;
     color: #083338;
}
 .noti_toolbar_text_empty{
     position: relative;
     width: 150px;
     font-weight: 500;
     font-family: 'Noto Sans KR', sans-serif;
     font-size: 16px;
     color: #72949A;
}
 .noti_toolbar_icon{
     position: absolute;
     top:0px;
     right:8px;
     width: 30px;
     height: 30px;
     background-image: url("/assets/image/prod/toptoolbar/ic_alarm_icon2.svg");
     background-repeat: no-repeat;
     background-size: 100%;
}

 .alarm_noti_option{
     position: absolute;
     /*display: none;*/
     top:32px;
     margin-left: -109px;
     left: 50%; /*�??????�??????�?????? 기�? ??�?????????? ??�?????????? */
     width: 218px;
     height: 210px;
     z-index: 100;
     background-image: url("/assets/image/prod/toptoolbar/noti_option_list.svg");
     background-repeat: no-repeat;
     background-size: 100%;
     box-shadow: 5px 5px 10px #00000055;
}

#notice_input{display: none;}
#notice_input[type="checkbox"]:checked ~ .alarm_noti_option {display: block;}

 .alarm_noti_option_area{
    position: relative;
    display: flex;
    top:21px;
    left: 13px;
    width: 200px;
    height: 23px;
    margin-bottom: 5px;
    border:0px solid;
}

 .alarm_option_box{
    position: relative;
    display: flex;
    width: 100px;
    height: 23px;
    margin-left: 9px;
    margin-right: 10px;
    border:0px solid;
}

 .alarm_option_input{
    position: relative;
    top:5px;
    width: 12px;
    height: 12px;
}

 .alarm_option_text{
    position: relative;
    top:2px;
    left: 7px;
    color: #fff;
    font-weight: 500;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 12px;
}

 .toptoolbar_noti_list1 ul{
     position: relative;
     top:30px;
}
 .noti_contants1{
     position: relative;
     width: 228px;
     height: 60px;
     border-bottom: 1px solid #D6E3E5;
}
 .noti_contants_text1{
     position: absolute;
     top:10px;
     left: 50px;
     width: 160px;
     height: 18px;
     font-weight: 500;
     font-family: 'Noto Sans KR', sans-serif;
     font-size: 11px;
     color: #34555C;
     border:0px solid;
}
 .noti_contants_text2{
     position: absolute;
     top:30px;
     left: 50px;
     width: 90px;
     height: 18px;
     font-weight: 500;
     font-family: 'Noto Sans KR', sans-serif;
     font-size: 11px;
     color: #34555C;
     border:0px solid;
}
 .noti_contants_text3{
     position: absolute;
     top:28px;
     right: 15px;
     width: 75px;
     height: 18px;
     font-weight: 500;
     font-family: 'Noto Sans KR', sans-serif;
     font-size: 12px;
     color: #72949A;
     text-align: right;
     border:0px solid;
}
 .noti_list_icon1{
     position: absolute;
     top:15px;
     left: 13px;
     width: 26px;
     height: 26vh;
     background-image: url("/assets/image/prod/toptoolbar/ic_alarm_start.svg");
     background-repeat: no-repeat;
     background-size: 100%;
}
 .noti_list_icon2{
     position: absolute;
     top:15px;
     left: 13px;
     width: 26px;
     height: 26vh;
     background-image: url("/assets/image/prod/toptoolbar/ic_alarm_delay.svg");
     background-repeat: no-repeat;
     background-size: 100%;
}
 .noti_list_icon3{
     position: absolute;
     top:15px;
     left: 13px;
     width: 26px;
     height: 26vh;
     background-image: url("/assets/image/prod/toptoolbar/ic_alarm_pause.svg");
     background-repeat: no-repeat;
     background-size: 100%;
}
 .noti_list_icon4{
     position: absolute;
     top:15px;
     left: 13px;
     width: 26px;
     height: 26vh;
     background-image: url("/assets/image/prod/toptoolbar/ic_alarm_accident.svg");
     background-repeat: no-repeat;
     background-size: 100%;
}
 .noti_list_icon5{
     position: absolute;
     top:15px;
     left: 13px;
     width: 26px;
     height: 26vh;
     background-image: url("/assets/image/prod/toptoolbar/ic_alarm_complete.svg");
     background-repeat: no-repeat;
     background-size: 100%;
}
 .noti_list_icon6{
     position: absolute;
     top:15px;
     left: 13px;
     width: 26px;
     height: 26vh;
     background-image: url("/assets/image/prod/toptoolbar/ic_alarm_fail.svg");
     background-repeat: no-repeat;
     background-size: 100%;
}
.noti_list_icon7{
    position: absolute;
    top:15px;
    left: 13px;
    width: 26px;
    height: 26vh;
    background-image: url("/assets/image/prod/toptoolbar/ic_alarm_message.svg");
    background-repeat: no-repeat;
    background-size: 100%;
}
.noti_list_icon8{
    position: absolute;
    top:15px;
    left: 13px;
    width: 26px;
    height: 26vh;
    background-image: url("/assets/image/prod/toptoolbar/ic_alarm_dutyoff.svg");
     background-repeat: no-repeat;
     background-size: 100%;
}
.noti_list_icon9{
    position: absolute;
    top:15px;
    left: 13px;
    width: 26px;
    height: 26vh;
    background-image: url("/assets/image/prod/toptoolbar/ic_alarm_work_completion.svg");
    background-repeat: no-repeat;
    background-size: 100%;
}
.noti_list_icon10{
    position: absolute;
    top:15px;
    left: 13px;
    width: 26px;
    height: 26vh;
    background-image: url("/assets/image/prod/toptoolbar/ic_alarm_attendance.svg");
    background-repeat: no-repeat;
    background-size: 100%;
}
.noti_list_icon11{
    position: absolute;
    top:15px;
    left: 13px;
    width: 26px;
    height: 26vh;
    background-image: url("/assets/image/prod/toptoolbar/ic_alarm_no_attendance.svg");
    background-repeat: no-repeat;
    background-size: 100%;
}
.noti_list_icon12{
    position: absolute;
    top:15px;
    left: 13px;
    width: 26px;
    height: 26vh;
    background-image: url("/assets/image/prod/toptoolbar/ic_alarm_late_attendance.svg");
    background-repeat: no-repeat;
    background-size: 100%;
}
.noti_list_icon13{
    position: absolute;
    top:15px;
    left: 13px;
    width: 26px;
    height: 26vh;
    background-image: url("/assets/image/prod/toptoolbar/ic_alarm_on_duty.svg");
    background-repeat: no-repeat;
    background-size: 100%;
}
.noti_list_icon14{
    position: absolute;
    top:15px;
    left: 13px;
    width: 26px;
    height: 26vh;
    background-image: url("/assets/image/prod/toptoolbar/ic_alarm_dispatch_rejection.svg");
    background-repeat: no-repeat;
    background-size: 100%;
}
.noti_list_icon15{
    position: absolute;
    top:15px;
    left: 13px;
    width: 26px;
    height: 26vh;
    background-image: url("/assets/image/prod/toptoolbar/ic_alarm_dispatch_acceptance.svg");
    background-repeat: no-repeat;
    background-size: 100%;
}
 .noti_list_icon_circle1 {
     position: absolute;
     top: 5px;
     right: 5px;
     width: 6px;
     height: 6px;
     background-color: #FF6077;
     border-radius: 3px;
}


.toptoolbar_setting_on{
    position: relative;
    width: 54px;
    height: 34px;
    cursor:pointer;
    background-image: url("/assets/image/prod/toptoolbar/bt_se_toggle_on.png");
}

.toptoolbar_setting_on:hover{
    background-image: url("/assets/image/prod/toptoolbar/bt_se_toggle_on.png");
}


.toptoolbar_setting_off{
    position: relative;
    width: 54px;
    height: 34px;
    cursor:pointer;
    background-image: url("/assets/image/prod/toptoolbar/bt_se_toggle_off.png");
}

.toptoolbar_setting_off:hover{
    background-image: url("/assets/image/prod/toptoolbar/bt_se_toggle_off.png");
}

.button-gradient {
    background: linear-gradient(90deg, #4485f3 0%, #60a5fa 100%);
}

.button-gradient-orange {
    background: linear-gradient(90deg, #ff9900 0%, #ff8c05 100%);
}

@keyframes scroll {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-50%);
    }
}

.animate-\[scroll_20s_linear_infinite\] {
    animation: scroll 20s linear infinite;
}


