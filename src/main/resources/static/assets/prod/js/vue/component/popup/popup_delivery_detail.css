@charset "UTF-8";
@import url("/assets/common/css/font.css");


* {	
    margin: 0px;
    padding: 0px;
    font-family: Noto Sans KR;
    font-weight: 500;

}

/*=================팝업 프레임==========================*/
.delivery_location_window {
	/*
	position: absolute;
	top:50%;
	left: 50%;
	margin-top: -317.5px;
	margin-left: -270px;
	width: 540px;
	height: 660px;
	background-color: #fff;
	border-radius: 3px;
	 */
	/*	border: 1px solid red;*/
}

.delivery_location_window .location_window_close{
	position: absolute;
	top:45px;
	right: 30px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	background-image: url("/assets/image/popup/bt_popup_close_n.svg");
}

.delivery_location_window .location_window_edit{
	position: absolute;
	top:45px;
	left: 30px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	background-image: url("/assets/image/popup/bt_popup_edit_n.svg");
}

.delivery_location_window .location_window_fix{
	position: absolute;
	top: 45px;
	left: 20px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	background-image: url("/assets/image/popup/bt_popup_edit_n.svg");
}

.delivery_location_window .location_window_delete {
	position: absolute;
	top: 43px;
	left: 50px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	background-image: url("/assets/image/project/bt_list_delivery_del_n.svg");
}



/*=================상단 타이틀==========================*/
.delivery_location_window .location_title{
	position: relative;
	height:41px;
	padding-top: 19px;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	color:#083338;
	/*	border: 1px solid red;*/
}

.delivery_location_window .location_title_line{
	position: relative;
	height: 1px;
	background-color: #D5D5D5;
}


.delivery_location_window .location_left_list_circle{
	position: absolute;
	top:7px;
	right: 0px;
	width: 4px;
	height: 4px;
	border-radius: 2px;
	background-color: #FF5A00;
}


.delivery_location_window .location_left_list_circle{
	position: absolute;
	top: 5px;
	right: -1px;
	width: 4px;
	height: 4px;
	border-radius: 2px;
	background-color: #FF5A00;
}



.delivery_location_window .location_left_list_area{
	position: absolute;
	padding-top: 40px;
	left: 40px;
	width: 456px;
	height: 580px;
	border: 0px solid red;
}

.delivery_location_window .location_left_list_section1{
	position: relative;
	display: flex;
	width: 100%;
	height: 30px;
	top: -25px;
	margin-top: 5px;
	border: 0px solid red;
}


.delivery_location_window .location_left_list_section1_small{
	position: relative;
	display: flex;
	width: 50%;
	height: 30px;
	margin-top: 5px;
	border: 0px solid red;
}

.delivery_location_window .location_left_list_section1_left_small{
	position: relative;
	display: flex;
	width: 50%;
	height: 30px;
	margin-top: 5px;
	border: 0px solid red;
}

.delivery_location_window .location_left_list_section_title_right{
	position: absolute;
	left: 230px;
	width: 70px;
	height: 23px;
	padding-top: 7px;
	padding-right: 5px;
	margin-right: 5px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
	border: 0px solid red;
}


.delivery_location_window .location_left_list_section_title{
	position: relative;
	left: 0px;
	width: 54px;
	height: 23px;
	padding-top: 7px;
	padding-right: 5px;
	margin-right: 5px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
	border: 0px solid red;
}

.delivery_location_window .location_left_list_text1_line{
	position: absolute;
	top:32px;
	right: 0px;
	width: 100%;
	height: 1px;
	background-color: #D5D5D5;
}





.delivery_location_window .location_left_list_section1_1{
	position: absolute;
	left: 0px;
	width: 187px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}


.delivery_location_window .location_left_list_section1_1_1{
	position: absolute;
	display: flex;
	left: 0px;
	width: 192px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}
.delivery_location_window .location_left_list_section1_1_2{
	position: absolute;
	display: flex;
	right: 0px;
	width: 192px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}
.delivery_location_window .location_left_list_text1_line1{
	position: absolute;
	top:32px;
	right: 0px;
	width: 122px;
	height: 1px;
	background-color: #D5D5D5;
}
.delivery_location_window .location_left_list_text1{
	position: absolute;
	top:1px;
	right: 0px;
	width: 60px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border: 1px solid blue;
}








.delivery_location_window .location_left_list_section1_2{
	position: absolute;
	right: 0px;
	width: 180px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}
.delivery_location_window .location_left_list_text2{
	position: absolute;
	top:1px;
	right: 0px;
	width: 310px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border: 0px solid;
}
.delivery_location_window .location_left_list_text1_line2{
	position: absolute;
	top:32px;
	right: 0px;
	width: 320px;
	height: 1px;
	background-color: #D5D5D5;
}








.delivery_location_window .location_left_list_section1_3{
	position: absolute;
	left: 0px;
	width: 260px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}

.delivery_location_window .location_left_list_text3_right{
	position: absolute;
	top:1px;
	left: 315px;
	width: 390px;
	height: 30px;
	outline: 0px;
	padding-left: 10px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border-radius: 2px;
	border: 1px solid #D5D5D5;
}

.delivery_location_window .location_left_list_text3_left{
	position: absolute;
	top:1px;
	left: 65px;
	width: 390px;
	height: 30px;
	outline: 0px;
	padding-left: 10px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border-radius: 2px;
	border: 1px solid #D5D5D5;
}


.delivery_location_window .location_left_list_text3{
	position: relative;
	top:1px;
	left: 0px;
	width: 390px;
	height: 30px;
	outline: 0px;
	padding-left: 10px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border-radius: 2px;
	border: 1px solid #D5D5D5;
}

.delivery_location_window .location_left_list_text1_line3{
	position: absolute;
	top:32px;
	right: 0px;
	width: 190px;
	height: 1px;
	background-color: #D5D5D5;
}

.delivery_location_window .location_left_list_section1_4{
	position: absolute;
	display: flex;
	right: -5px;
	width: 130px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
	border: 0px solid;

}

.delivery_location_window .location_left_list_text4{
	position: relative;
	top:1px;
	right: 0px;
	height: 30px;
	outline: 0px;
	padding-left: 10px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border-radius: 2px;
	border: 1px solid #D5D5D5;
}
.delivery_location_window .location_left_list_text1_line4{
	position: absolute;
	top:32px;
	right: 0px;
	width: 60px;
	height: 1px;
	background-color: #D5D5D5;
}


.delivery_location_window .location_left_list_text3_1{
	position: absolute;
	top:1px;
	left: 70px;
	width: 45px;
	height: 30px;
	outline: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border: 0px solid;
}
.delivery_location_window .location_left_list_text3_2{
	position: absolute;
	top:1px;
	right: 10px;
	width: 45px;
	height: 30px;
	outline: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border: 0px solid;
}
.delivery_location_window .location_left_list_text3_label{
	position: absolute;
	top: 3px;
	right: 60px;
	color: #000;
	opacity: 0.5;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 18px;
	color: #000;
	z-index: 5;
	border: 0px solid;
}



.delivery_location_window .location_left_list_text5{
	position: absolute;
	right: 0px;
	width: 390px;
	height: 70px;
	overflow: hidden;
	border:1px solid #D5D5D5;
	background-color: rgba(0,0,0,0);
	border-radius: 2px;
}

.delivery_location_window .location_left_list_section1_6{
	position: absolute;
	display: flex;
	right: -5px;
	width: 170px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
	border: 0px solid;

}

.delivery_location_window .location_textarea{
	position: absolute;
	right: 0px;
	width: 380px;
	height: 65px;
	padding-top: 5px;
	resize: none;
	outline: 0px;
	border: 0px solid;
	background-color: rgba(0,0,0,0);
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
}

.delivery_location_window .location_textarea_1_line{
	/*position: absolute;*/

	right: 0px;
	width: 380px;
	height: 30px;
	padding-top: 5px;
	resize: none;
	outline: 0px;
	border: 0px solid;
	background-color: rgba(0,0,0,0);
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
}

.delivery_location_window .location_textarea_2_line{
	/*position: absolute;*/
	right: 0px;
	width: 380px;
	height: 35px;
	padding-top: 5px;
	resize: none;
	outline: 0px;
	border: 0px solid;
	background-color: rgba(0,0,0,0);
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
}

.delivery_location_window .location_textarea_2{
	position: absolute;
	right: 0px;
	width: 380px;
	height: 30px;
	padding-top: 5px;
	resize: none;
	outline: 0px;
	border: 0px solid;
	background-color: rgba(0,0,0,0);
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
}

.delivery_location_window .location_add_search1{
	position: absolute;
	top:0px;
	right: 0px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	background-image: url("/assets/image/popup/ic_input_search.svg");
}

.delivery_location_window .location_button{
	position: absolute;
	bottom: 5px;
	left: 50%;
	margin-left: -130px;
	width: 260px;
	height: 35px;
	border: 0px;
	border-radius: 4px;
	outline: 0px;
	background-color: #FF5A00;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	color: #fff;
}

.delivery_location_window .location_button:disabled {
	background-color: #C3C3C3;
}

/*
.delivery_location_window .location_button:hover{
	background-color: #FF5A00;
}
*/

.delivery_location_window .location_left_list_select2{
	position: relative;
	top:1px;
	left: 0px;
	width: 128px;
	height: 30px;
	outline: 0px;
	padding-left: 5px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	border: 1px solid #D5D5D5;
	border-radius: 2px;
	background-color: #ffffff;
}


.delivery_location_window .location_left_list_select2_delivery_status{
	position: relative;
	top:1px;
	left: 0px;
	width: 128px;
	height: 30px;
	outline: 0px;
	padding-left: 5px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	border: 1px solid #D5D5D5;
	border-radius: 2px;
	background-color: #ffffff;
}

.delivery_location_window .location_left_list_select2_delivery_type{
	position: relative;
	top:1px;
	left: 120px;
	width: 145px;
	height: 30px;
	outline: 0px;
	padding-left: 5px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	border: 1px solid #D5D5D5;
	border-radius: 2px;
	background-color: #ffffff;
}


.delivery_location_window .location_left_list_text_label{
	position: absolute;
	top: 7px;
	right: 10px;
	color: #000;
	opacity: 0.5;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	z-index: 5;
	border: 0px solid;
}


.delivery_location_window .location_left_list_text5_disabled {
	position: absolute;
	right: 0px;
	width: 390px;
	height: 70px;
	overflow: hidden;
	border:1px solid #D5D5D5;
	background-color: #e2e2e2;
	border-radius: 2px;
}

.delivery_location_window .location_left_list_text5_disabled_1_line {
	position: absolute;
	right: 0px;
	width: 390px;
	height: 30px;
	overflow: hidden;
	border:1px solid #D5D5D5;
	background-color: #e2e2e2;
	border-radius: 2px;
}

.delivery_location_window .location_left_list_text6_disabled_1_line {
	position: absolute;
	right: 0px;
	width: 390px;
	height: 40px;
	overflow: hidden;
	border:1px solid #D5D5D5;
	background-color: #e2e2e2;
	border-radius: 2px;
}


/* 추가 비 활성화때 배경색 추가 */
.delivery_location_window :disabled{
	background-color: #e2e2e2;
}



.delivery_location_window .sr01_button_area{
	position: absolute;
	bottom: 0px;
	width: 540px;
	height: 48px;
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px;
	background-color: #F1F5F7;
}

.delivery_location_window 	.sr01_button{
	position: absolute;
	top: 13px;
	left: 356px;
	width: 88px;
	height: 25px;
	border: 0px;
	border-radius: 4px;
	outline: 0px;
	background-color: #FF5A00;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 11px;
	color: #fff;
}
.delivery_location_window .sr01_diver_info_area{
	position: absolute;
	display: flex;
	top: 22px;
	left: 51px;
	width: 259px;
	height: 20px;
	/*border: 1px solid;*/
}
.delivery_location_window .sr01_diver_info_title{
	position: relative;
	left: 0px;
	width: 70px;
	height: 20px;
	font-weight: 500;
	text-align: left;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #22394D;
	/*border: 1px solid;*/
}

.delivery_location_window .sr01_diver_info_contants{
	position: relative;
	display: flex;
	width: 210px;
	height: 20px;
	font-weight: 500;
	text-align: left;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #22394D;
	/*border: 1px solid;*/
}

.delivery_location_window .sr01_diver_info_diverName{
	position: relative;
	/*width: 45px;*/
	display:inline-block;
	height: 20px;
	padding-left: 6px;
	text-align: left;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #22394D;
	/*border: 1px solid;*/
}

.delivery_location_window .sr01_diver_info_diverName_space {
	position: relative;
	width: 12px;
	height: 20px;
	text-align: left;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #22394D;
	/*border: 1px solid;*/
}

.delivery_location_window .sr01_diver_info_diverNum{
	position: relative;
	/*width: 83px;*/
	display:inline-block;
	height: 20px;
	text-align: left;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #22394D;
	/*border: 1px solid;*/
}



.delivery_location_window .delivery_popup_time1{
	position: absolute;
	top:3px;
	left: 70px;
	width: 102px;
	height: 24px;
	outline: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border: 1px solid #D5D5D5;
}
.delivery_location_window .delivery_popup_time2{
	position: absolute;
	top:3px;
	left: 195px;
	width: 102px;
	height: 24px;
	outline: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border: 1px solid #D5D5D5;
}

.delivery_location_window 				.or17_left_list_text2{
	position: absolute;
	top:1px;
	right: 0px;
	width: 310px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0,0,0,0);
	border: 0px solid;
}

.delivery_location_window 				.or17_left_list_text1_line2{
	position: absolute;
	top:32px;
	right: 0px;
	width: 320px;
	height: 1px;
	background-color: #D5D5D5;
}

.delivery_location_window 			.or17_left_list_text3_label_w{
	position: absolute;
	top: 3px;
	left: 180px;
	color: #000;
	opacity: 0.5;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 18px;
	color: #000;
	z-index: 5;
	border: 0px solid;
}


.delivery_location_window .sr01_left_list_text1{
	position: absolute;
	top:7px;
	right: 0px;
	width: 180px;
	height: 20px;
	outline: 0px;
	text-align: left;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
	background-color: rgba(0,0,0,0);
	/*border: 1px solid;*/
}


.delivery_location_window 				.or17_left_list_text1_line3{
	position: absolute;
	top:32px;
	right: 0px;
	width: 190px;
	height: 1px;
	background-color: #D5D5D5;
}