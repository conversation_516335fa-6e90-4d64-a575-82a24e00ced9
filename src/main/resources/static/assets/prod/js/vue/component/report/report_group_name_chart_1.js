var reportGroupNameChart1Popup = {
    template: `
    <div id="report_driver_chart_1_body">
        <div class="bg"></div>      
        <div class="report_page_3">
            <div class="report_info_3">
                <button class="or02_window_close" @click="closePopup"></button>
                <div class="report_data_3">
                    <div class="project_manage_date_area">
<!--                        <jqx-buttons class="report_data_icon_3" @click="onClick($event)" value="기간" theme="darkblue"/>-->
                        <button class="report_data_icon_3"></button>                        
                        <div class=project_manage_inputdate1> 
                            <jqx-datetime-input ref="fromDateTimeInput" style="float: left"
                                    @valueChanged="onFromValueChanged($event)" 
                                    @open="onOpen()" 
                                    @close="onClose()"
                                    :formatString="'yyyy-MM-dd'"
                                    :value ="fromMoment"
                                    :width="120" 
                                    :height="25">
                            </jqx-datetime-input>
                        </div>
                        <div class="project_manage_date_middle">~</div>
                        <div class=project_manage_inputdate2> 
                            <jqx-datetime-input ref="toDateTimeInput" style="float: left"
                                    @valueChanged="onToValueChanged($event)" 
                                    @open="onOpen()" 
                                    @close="onClose()"
                                    :formatString="'yyyy-MM-dd'"
                                    :value="toMoment"
                                    :width="120" 
                                    :height="25">
                            </jqx-datetime-input>
                        </div>
                    </div>
                    <div>
                        <select v-model="selectedValue" class="report_data_num_3"  @change="onChange(this.value)">
                            <option value="0">{{$t("전체")}}</option>
                            <option value="1">{{$t("1주일")}}</option>
                            <option value="2">{{$t("1개월")}}</option>
                            <option value="3">{{$t("3개월")}}</option>
                            <option value="4">{{$t("6개월")}}</option>
                            <option value="5">{{$t("1년")}}</option>
                        </select>
                    </div>                    
                </div>
                <div class="report_data_line_3"></div>
            </div>

            <div class="report_chart_area_1_3">    
                <div class="report_chart_area_left_3">
                    <div class="report_chart_left_text_2_3">
                        <div class="report_li_title_3">
                            <div class="report_li_title_pic_4"></div>
                            <div class="report_li_title_5_3">{{statGroupNameTotalDelivery.groupName}}</div>
                        </div>
                        <div class="report_chart_left_text_2_1_3">
                            <div class="report_chart_left_icon_2_4"></div>
                            {{$t("총 프로젝트")}}
                            <div class="report_chart_left_text_2_2_3">{{statGroupNameTotalDelivery.numOfProjects}}</div>
                        </div>
                    </div>
                    
                    <div class="report_doughnut_chart_area_3">
                        
                        <div id="app" class="chart_bar_1">
                            <doughnut-chart
                            :chartData="doughnutChartData"
                            :options="doughnutChartOptions"
                            :plugins="doughnutChartPlugins"
                            />
                        </div>
                    </div>

                    <div class="report_chart_left_text_3">
                        <div class="report_chart_left_text_3_1_3">
                            <div class="report_circle_1"></div>
                                {{$t("배송완료")}}
                            <div class="report_chart_left_text_3_2_3">{{statGroupNameTotalDelivery.numOfCompletedDeliveries}}</div>
                                &nbsp |
                            <div class="report_chart_left_text_3_3_3">{{statGroupNameTotalDelivery.numOfAllDeliveries == 0 ? 0 : (statGroupNameTotalDelivery.numOfCompletedDeliveries * 100 / statGroupNameTotalDelivery.numOfAllDeliveries).toFixed(0)}}%</div>
                        </div>
                        
                        <div class="report_chart_left_text_3_1_3">
                            <div class="report_circle_2"></div>
                                {{$t("미배송")}}
                            <div class="report_chart_left_text_3_2_3">{{statGroupNameTotalDelivery.numOfWaitingDeliveries + statGroupNameTotalDelivery.numOfReadyDeliveries + statGroupNameTotalDelivery.numOfGoingDeliveries + statGroupNameTotalDelivery.numOfServicingDeliveries + statGroupNameTotalDelivery.numOfRejectedDeliveries}}</div>
                                &nbsp |
                            <div class="report_chart_left_text_3_3_3">{{statGroupNameTotalDelivery.numOfAllDeliveries == 0 ? 0 : ((statGroupNameTotalDelivery.numOfWaitingDeliveries + statGroupNameTotalDelivery.numOfReadyDeliveries + statGroupNameTotalDelivery.numOfGoingDeliveries + statGroupNameTotalDelivery.numOfServicingDeliveries + statGroupNameTotalDelivery.numOfRejectedDeliveries) * 100 / statGroupNameTotalDelivery.numOfAllDeliveries).toFixed(0)}}%</div>
                        </div>
                        
                        <div class="report_chart_left_text_3_1_3">
                            <div class="report_circle_3"></div>
                                {{$t("배송실패")}}
                            <div class="report_chart_left_text_3_2_3">{{statGroupNameTotalDelivery.numOfFailedDeliveries}}</div>
                                &nbsp |
                            <div class="report_chart_left_text_3_3_3">{{statGroupNameTotalDelivery.numOfAllDeliveries == 0 ? 0 : (statGroupNameTotalDelivery.numOfFailedDeliveries * 100 / statGroupNameTotalDelivery.numOfAllDeliveries).toFixed(0)}}%</div>
                        </div>
                    </div>
                </div>

                <div class="report_chart_area_line_3"></div>

                <div class="report_chart_area_right_3">
                    <div class="report_bar_chart_area_3">
                        <div class="report_bar_chart_label_3">
                            <div class="report_bar_chart_label_1_3" v-if="currentChartIndex == 0">{{$t("일간비교")}}</div>
                            <div class="report_bar_chart_label_1_3" v-else-if="currentChartIndex == 1">{{$t("주간비교")}}</div>
                            <div class="report_bar_chart_label_1_3" v-else-if="currentChartIndex == 2">{{$t("월간비교")}}</div>
                            <div class="report_bar_chart_label_1_3" v-else-if="currentChartIndex == 3">{{$t("년간비교")}}</div>
                            <div class="report_bar_chart_label_1_3" v-else>{{$t("그래프로 비교보기")}}</div>

                            <div class="report_b_chart_tab_area_3">
                                <input type="radio" name="b_chart" id="b_chart_1" :checked="currentChartIndex == 0" @change="getStatGroupNamesDailyDeliveries">
                                <label for="b_chart_1" class="report_b_chart_tab_1_4">{{$t("일간")}}</label>
                                    &nbsp|&nbsp
                                <input type="radio" name="b_chart" id="b_chart_2" :checked="currentChartIndex == 1" @change="getStatGroupNamesWeeklyDeliveries">
                                <label for="b_chart_2" class="report_b_chart_tab_2_4">{{$t("주간")}}</label>
                                    &nbsp|&nbsp
                                <input type="radio" name="b_chart" id="b_chart_3" :checked="currentChartIndex == 2" @change="getStatGroupNamesMonthlyDeliveries">
                                <label for="b_chart_3" class="report_b_chart_tab_3_4">{{$t("월간")}}</label>
                                    &nbsp|&nbsp
                                <input type="radio" name="b_chart" id="b_chart_4" :checked="currentChartIndex == 3" @change="getStatGroupNamesYearlyDeliveries">
                                <label for="b_chart_4" class="report_b_chart_tab_4_4">{{$t("년간")}}</label>
                            </div>
                        </div>
                        
                        <div class="chart-container">
                            <div id="app1">
                                <bar-chart
                                    :chartData="barChartData"
                                    :options="barChartOptions"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    `,

    props: {
        statGroupNameTotalDelivery: null,
    },

    beforeCreate: function () {
    },

    created: function () {
        EVENT.registerEventHandler(this, this.eventHandlers);
    },

    mounted: function () {
        console.log('Report Driver Chart 1 Popup Mounted!!!' + " groupName: " + this.statGroupNameTotalDelivery.groupName);

        this.baseMoment = moment();
        // this.baseMoment.month(5);   // TODO: 6월 데이터가 많아 임시로 5로 고정

        if (this.currentChartIndex === 0) {
            this.getStatGroupNamesDailyDeliveries();
        } else if (this.currentChartIndex === 1) {
            this.getStatGroupNamesWeeklyDeliveries();
        } else if (this.currentChartIndex === 2) {
            this.getStatGroupNamesMonthlyDeliveries();
        } else if (this.currentChartIndex === 3) {
            this.getStatGroupNamesYearlyDeliveries();
        }
    },

    computed: {
        doughnutChartData: function () {
            let chartData = {
                labels: ['배송실패', '미배송', '배송완료'],
                datasets: [{
                    data: [
                        this.statGroupNameTotalDelivery.numOfFailedDeliveries,
                        this.statGroupNameTotalDelivery.numOfWaitingDeliveries + this.statGroupNameTotalDelivery.numOfReadyDeliveries + this.statGroupNameTotalDelivery.numOfGoingDeliveries + this.statGroupNameTotalDelivery.numOfServicingDeliveries + this.statGroupNameTotalDelivery.numOfRejectedDeliveries,
                        this.statGroupNameTotalDelivery.numOfCompletedDeliveries,
                    ],
                    backgroundColor: ['#F95F7B', '#FFA346', '#CAF419'],
                    borderWidth: [0, 0, 0],
                    numOfAllDeliveries: this.statGroupNameTotalDelivery.numOfAllDeliveries,
                }],
            };

            return chartData;
        },
    },

    data: function () {
        return {
            eventHandlers: [
                {event: EVENT.REPORT_DRIVER_CHART_1_POPUP.SHOW, handler: this.setShow},
            ],
            isShow: false,
            popup: {
                datePicker: {
                    fromDate: new Date(),
                    toDate: new Date(),
                }
            },

            fromValue: new Date(),
            toValue: new Date(),
            fromMoment: null,
            toMoment: null,
            selectedValue : 0,

            currentChartIndex: 0,   // 페이지 진입시 '일간'부터 표시
            baseMoment: null,

            doughnutChartPlugins: [{
                beforeDraw: this.beforeDrawDoughnutChart,
            }],
            doughnutChartOptions: {
                responsive: true,
                maintainAspectRatio: true,
                cutoutPercentage: 65,
                rotation: -1 * Math.PI,
                legend: {
                    display: false,
                },
                tooltips: {
                    enabled: true,
                },
                scales: {
                    xAxes: [{
                        display: false,
                        stacked: true,
                    }],
                    yAxes: [{
                        display: false,
                        stacked: true,
                    }],
                },
                animation: {
                    animateScale: true,
                    animateRotate: true,
                    duration: 800,
                    easing: 'easeOutBounce',
                },
            },
            barChartData: null,
            barChartOptions: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    display: false,
                },
                tooltips: {
                    enabled: true,
                    mode: 'index',
                    callbacks: {
                        title : function (tooltipItems, data) {
                            let title = '';
                            if(tooltipItems[0].xLabel.length > 0) {
                                title += tooltipItems[0].xLabel;
                                title = title.replace(/,/gi, '');
                            }
                            return title;
                        },
                        afterTitle: function (tooltipItems, data) {
                            let afterTitle = '';

                            if (tooltipItems.length > 0) {
                                let item = tooltipItems[0];
                                if (data.datasets[0].numOfProjects) {
                                    afterTitle += '\n' + _t("진행 프로젝트") + ' ' + data.datasets[0].numOfProjects[item.index];
                                }
                            }

                            return afterTitle;
                        },
                        label: function (tooltipItem, data) {
                            let label = data.datasets[tooltipItem.datasetIndex].label || '';

                            if (label) {
                                label += ' ';
                            }

                            label += tooltipItem.value;

                            if (data.datasets.length > 1) {
                                let total = 0;
                                data.datasets.forEach(d => {
                                    total += d.data[tooltipItem.index];
                                });
                                if (total != 0) {
                                    label += ' | ' + (tooltipItem.value * 100 / total).toFixed(0) + '%';
                                }
                            }

                            return label;
                        },
                    },
                    itemSort: function (a, b) {
                        return b.datasetIndex - a.datasetIndex
                    },
                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                    titleFontFamily: "'Noto Sans KR', sans-serif",
                    titleFontStyle: 'normal',
                    titleFontSize: 14,
                    titleFontColor: '#6DEBAE',
                    titleMarginBottom: 20,
                    bodyFontFamily: "'Noto Sans KR', sans-serif",
                    bodyFontSize: 14,
                    bodyFontColor: '#88989B',
                    bodySpacing: 10,
                    xPadding: 20,
                    yPadding: 20,
                    caretSize: 0,
                    cornerRadius: 0,
                },
                scales: {
                    xAxes: [{
                        display: true,
                        stacked: true,
                        ticks: {
                            fontColor: '#83999D',
                        },
                        gridLines: {
                            zeroLineColor: '#83999D',
                            zeroLineWidth: 2,
                            color: '#345B62',
                            lineWidth: 2,
                            borderDash: [1, 10],
                        },
                    }],
                    yAxes: [{
                        display: true,
                        stacked: true,
                        ticks: {
                            fontColor: '#83999D',
                            min: 0,
                            beginAtZero: true,
                            precision: 0,
                        },
                        gridLines: {
                            zeroLineColor: '#83999D',
                            zeroLineWidth: 2,
                            color: '#345B62',
                            lineWidth: 2,
                            borderDash: [1, 10],
                        },
                        scaleLabel: {
                            display: true,
                            labelString: 'EA',
                            fontColor: '#83999D',
                        },
                    }],
                },
            },
        };
    },

    methods: {

        sendEventToMap: function(event, data) {
            try {
                const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;
                aloaMap.$emit(event, data);
            } catch(e) {
                console.error("sendEventToMap Exception: " + e);
            }
        },

        closePopup: function () {
            // this.$parent.popup.reportDriverChart1.isShow = false;
            this.$emit('dismiss');
        },

        onCloseBtnClick: function () {
            this.setShow(false);
        },

        setShow: function (isShow) {
            this.isShow = isShow;
        },

        showDriverDetailPopup: function (statGroupNameTotalDelivery) {
            let rider = statGroupNameTotalDelivery;
            rider.id = statGroupNameTotalDelivery.riderId;

            this.sendEventToMap(EVENT.MAP.SHOW_DRIVER_DETAIL_POPUP, { riderInfo: rider, viewMode: 'r'} );
        },

        beforeDrawDoughnutChart: function (chart) {
            let width = chart.chart.width;
            let height = chart.chart.height;
            let ctx = chart.chart.ctx;
            let text = _t("총 배송");
            let textX = width / 2;
            let textY = height / 2 - 12;

            ctx.restore();

            ctx.font = '14px "Noto Sans KR", sans-serif';
            ctx.fillStyle = "#fff";
            ctx.textAlign = "center";
            ctx.fillText(text, textX, textY);

            textY += 36;
            text = chart.data.datasets.length > 0 ? chart.data.datasets[0].numOfAllDeliveries : 0;
            ctx.font = 'bold 28px "Noto Sans KR", sans-serif';
            ctx.fillStyle = "#fff";
            ctx.textAlign = "center";
            ctx.fillText(text, textX, textY);

            ctx.save();
        },

        getStatGroupNamesDailyDeliveries: function () {
            this.currentChartIndex = 0;

            let fromMoment = this.baseMoment.clone().startOf('month');
            let toMoment = this.baseMoment.clone().endOf('month');
            let fromDate = fromMoment.format('YYYY-MM-DD');
            let toDate = toMoment.format('YYYY-MM-DD');

            if((this.fromMoment != null) && (this.toMoment!=null))
            {
                fromMoment = this.fromMoment;
                toMoment = this.toMoment;

                fromDate = fromMoment.format('YYYY-MM-DD');
                toDate = toMoment.format('YYYY-MM-DD');
            }

            console.log('getStatGroupNamesDailyDeliveries: ' + fromDate + ' ~ ' + toDate);

            const _this = this;
            // PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

            STAT_API.getStatGroupNamesDailyDeliveries([this.statGroupNameTotalDelivery.groupName], null, fromDate, toDate, {
                onSuccess: (response) => {
                    var statRiderDailyDeliveries = response.data[0].content;
                    this.makeRiderPeriodTotalDelivery(statRiderDailyDeliveries);
                    _this.barChartData = _this.makeDailyChartData(statRiderDailyDeliveries, fromMoment, toMoment);

                    // PopupUtil.dismissLoadingPopup();
                },
                onError: (error) => {
                    // PopupUtil.dismissLoadingPopup();
                    PopupUtil.alertPopup('기사의 일간 배송 통계 조회 실패');
                }
            });
        },

        makeDailyChartData: function (statRiderDailyDeliveries, fromMoment, toMoment) {
            let chartData = {
                labels: [],
                datasets: [
                    {
                        label: '배송실패',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#F95F7B',
                        numOfProjects: [],
                    },
                    {
                        label: '미배송',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#FFA346',
                    },
                    {
                        label: '배송완료',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#CAF419',
                    },
                ],
            };

            for (let moment = fromMoment.clone(); moment.isSameOrBefore(toMoment, 'day'); moment.add(1, 'day')) {
                let date = moment.format('YYYY-MM-DD');
                let s = statRiderDailyDeliveries.find(s => date.localeCompare(s.date) === 0);

                if (s) {
                    chartData.datasets[0].data.push(s.numOfFailedDeliveries);
                    chartData.datasets[1].data.push(s.numOfWaitingDeliveries + s.numOfReadyDeliveries + s.numOfGoingDeliveries + s.numOfServicingDeliveries + s.numOfRejectedDeliveries);
                    chartData.datasets[2].data.push(s.numOfCompletedDeliveries);

                    chartData.datasets[0].numOfProjects.push(s.numOfProjects);
                } else {
                    chartData.datasets[0].data.push(0);
                    chartData.datasets[1].data.push(0);
                    chartData.datasets[2].data.push(0);

                    chartData.datasets[0].numOfProjects.push(0);
                }

                chartData.labels.push(date);
            }

            return chartData;
        },

        getStatGroupNamesWeeklyDeliveries: function () {
            this.currentChartIndex = 1;

            let fromMoment = this.baseMoment.clone().startOf('month');
            fromMoment = fromMoment.add(-3, 'month');
            fromMoment = fromMoment.startOf('week');
            fromMoment = fromMoment.add(1,'day');

            let toMoment = this.baseMoment.clone();
            toMoment = toMoment.endOf('week');
            toMoment = toMoment.add(1, 'day');

            let fromDate = fromMoment.format('YYYY-MM-DD');
            let toDate = toMoment.format('YYYY-MM-DD');

            if((this.fromMoment != null) && (this.toMoment!=null))
            {
                fromMoment = this.fromMoment.clone().startOf('week');
                toMoment = this.toMoment.clone().endOf('week');

                fromMoment = fromMoment.add(1, 'day');
                toMoment = toMoment.add(1, 'day');

                fromDate = fromMoment.format('YYYY-MM-DD');
                toDate = toMoment.format('YYYY-MM-DD');
            }

            console.log('getStatGroupNamesWeeklyDeliveries: ' + fromDate + ' ~ ' + toDate);

            const _this = this;
            // PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

            STAT_API.getStatGroupNamesWeeklyDeliveries([this.statGroupNameTotalDelivery.groupName], null, fromDate, toDate, {
                onSuccess: (response) => {
                    //console.log(JSON.stringify(response));
                    var statRiderWeeklyDeliveries = response.data[0].content;
                    this.makeRiderPeriodTotalDelivery(statRiderWeeklyDeliveries);
                    _this.barChartData = _this.makeWeeklyChartData(statRiderWeeklyDeliveries, fromMoment, toMoment);

                    // PopupUtil.dismissLoadingPopup();
                },
                onError: (error) => {
                    // PopupUtil.dismissLoadingPopup();
                    PopupUtil.alertPopup('기사의 주간 배송 통계 조회 실패');
                }
            });
        },

        makeWeeklyChartData: function (statRiderWeeklyDeliveries, fromMoment, toMoment) {
            let chartData = {
                labels: [],
                datasets: [
                    {
                        label: '배송실패',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#F95F7B',
                        numOfProjects: [],
                    },
                    {
                        label: '미배송',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#FFA346',
                    },
                    {
                        label: '배송완료',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#CAF419',
                    },
                ],
            };

            for (let moment = fromMoment.clone(); moment.isSameOrBefore(toMoment, 'day'); moment.add(1, 'week')) {
                let week = moment.format('YYYY-MM-DD');
                let s = statRiderWeeklyDeliveries.find(s => week.localeCompare(s.week) === 0);
                if (s) {
                    chartData.datasets[0].data.push(s.numOfFailedDeliveries);
                    chartData.datasets[1].data.push(s.numOfWaitingDeliveries + s.numOfReadyDeliveries + s.numOfGoingDeliveries + s.numOfServicingDeliveries + s.numOfRejectedDeliveries);
                    chartData.datasets[2].data.push(s.numOfCompletedDeliveries);

                    chartData.datasets[0].numOfProjects.push(s.numOfProjects);
                } else {
                    chartData.datasets[0].data.push(0);
                    chartData.datasets[1].data.push(0);
                    chartData.datasets[2].data.push(0);

                    chartData.datasets[0].numOfProjects.push(0);
                }
                let toDateMoment = moment.clone().add(6, 'day');
                let fromDate = moment.format('MM-DD');
                let toDate = toDateMoment.format('MM-DD');
                let weekDate = [fromDate,'~', toDate];
                chartData.labels.push(weekDate);
            }

            return chartData;
        },

        getStatGroupNamesMonthlyDeliveries: function () {
            this.currentChartIndex = 2;

            let fromMoment = this.baseMoment.clone().startOf('year');
            let toMoment = this.baseMoment.clone().endOf('year');
            let fromYearMonth = fromMoment.format('YYYY-MM');
            let toYearMonth = toMoment.format('YYYY-MM');

            if((this.fromMoment != null) && (this.toMoment!=null))
            {
                fromMoment = this.fromMoment.clone().startOf('month');
                toMoment = this.toMoment.clone().endOf('month');

                fromYearMonth = fromMoment.format('YYYY-MM');
                toYearMonth = toMoment.format('YYYY-MM');
            }

            console.log('getStatGroupNamesMonthlyDeliveries: ' + fromYearMonth + ' ~ ' + toYearMonth);

            const _this = this;
            // PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

            STAT_API.getStatGroupNamesMonthlyDeliveries([this.statGroupNameTotalDelivery.groupName], null, fromYearMonth, toYearMonth, {
                onSuccess: (response) => {
                    var statRiderMonthlyDeliveries = response.data[0].content;
                    this.makeRiderPeriodTotalDelivery(statRiderMonthlyDeliveries);
                    _this.barChartData = _this.makeMonthlyChartData(statRiderMonthlyDeliveries, fromMoment, toMoment);

                    // PopupUtil.dismissLoadingPopup();
                },
                onError: (error) => {
                    // PopupUtil.dismissLoadingPopup();
                    PopupUtil.alertPopup('기사의 월간 배송 통계 조회 실패');
                }
            });
        },

        makeMonthlyChartData: function (statRiderMonthlyDeliveries, fromMoment, toMoment) {
            let chartData = {
                labels: [],
                datasets: [
                    {
                        label: '배송실패',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#F95F7B',
                        numOfProjects: [],
                    },
                    {
                        label: '미배송',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#FFA346',
                    },
                    {
                        label: '배송완료',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#CAF419',
                    },
                ],
            };

            for (let moment = fromMoment.clone(); moment.isSameOrBefore(toMoment, 'month'); moment.add(1, 'month')) {
                let yearMonth = moment.format('YYYY-MM');
                let s = statRiderMonthlyDeliveries.find(s => yearMonth.localeCompare(s.yearMonth) === 0);

                if (s) {
                    chartData.datasets[0].data.push(s.numOfFailedDeliveries);
                    chartData.datasets[1].data.push(s.numOfWaitingDeliveries + s.numOfReadyDeliveries + s.numOfGoingDeliveries + s.numOfServicingDeliveries + s.numOfRejectedDeliveries);
                    chartData.datasets[2].data.push(s.numOfCompletedDeliveries);

                    chartData.datasets[0].numOfProjects.push(s.numOfProjects);
                } else {
                    chartData.datasets[0].data.push(0);
                    chartData.datasets[1].data.push(0);
                    chartData.datasets[2].data.push(0);

                    chartData.datasets[0].numOfProjects.push(0);
                }

                chartData.labels.push(yearMonth);
            }

            return chartData;
        },

        getStatGroupNamesYearlyDeliveries: function () {
            this.currentChartIndex = 3;

            let fromMoment = this.baseMoment.clone().subtract(10, 'year');
            let toMoment = this.baseMoment.clone();
            let fromYear = fromMoment.format('YYYY');
            let toYear = toMoment.format('YYYY');

            if((this.fromMoment != null) && (this.toMoment!=null))
            {
                fromMoment = this.fromMoment.clone().startOf('years');
                toMoment = this.toMoment.clone().endOf('years');

                fromYear = fromMoment.format('YYYY');
                toYear = toMoment.format('YYYY');
            }

            console.log('getStatGroupNamesYearlyDeliveries: ' + fromYear + ' ~ ' + toYear);

            const _this = this;
            // PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

            STAT_API.getStatGroupNamesYearlyDeliveries([this.statGroupNameTotalDelivery.groupName], null, fromYear, toYear, {
                onSuccess: (response) => {
                    var statRiderYearlyDeliveries = response.data[0].content;
                    this.makeRiderPeriodTotalDelivery(statRiderYearlyDeliveries);
                    _this.barChartData = _this.makeYearlyChartData(statRiderYearlyDeliveries, fromMoment, toMoment);

                    // PopupUtil.dismissLoadingPopup();
                },
                onError: (error) => {
                    // PopupUtil.dismissLoadingPopup();
                    PopupUtil.alertPopup('기사의 년간 배송 통계 조회 실패');
                }
            });
        },

        makeYearlyChartData: function (statRiderYearlyDeliveries, fromMoment, toMoment) {
            let chartData = {
                labels: [],
                datasets: [
                    {
                        label: '배송실패',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#F95F7B',
                        numOfProjects: [],
                    },
                    {
                        label: '미배송',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#FFA346',
                    },
                    {
                        label: '배송완료',
                        stack: 'Stack 0',
                        data: [],
                        backgroundColor: '#CAF419',
                    },
                ],
            };

            for (let moment = fromMoment.clone(); moment.isSameOrBefore(toMoment, 'year'); moment.add(1, 'year')) {
                let year = moment.format('YYYY');
                let s = statRiderYearlyDeliveries.find(s => year.localeCompare(s.year) === 0);

                if (s) {
                    chartData.datasets[0].data.push(s.numOfFailedDeliveries);
                    chartData.datasets[1].data.push(s.numOfWaitingDeliveries + s.numOfReadyDeliveries + s.numOfGoingDeliveries + s.numOfServicingDeliveries + s.numOfRejectedDeliveries);
                    chartData.datasets[2].data.push(s.numOfCompletedDeliveries);

                    chartData.datasets[0].numOfProjects.push(s.numOfProjects);
                } else {
                    chartData.datasets[0].data.push(0);
                    chartData.datasets[1].data.push(0);
                    chartData.datasets[2].data.push(0);

                    chartData.datasets[0].numOfProjects.push(0);
                }

                chartData.labels.push(year);
            }

            return chartData;
        },

        onFromValueChanged: function (event) {
            if (event.args.date != null) {
                this.fromMoment = moment(event.args.date, 'ddd MMM D YYYY HH:mm:ss ZZ');
                if (this.fromMoment != null && this.toMoment != null) {
                    this.onClick(event);columns
                }
            }else if(event.args.date == null){
                this.fromMoment = null;
                if (this.fromMoment == null && this.toMoment == null) {
                    this.onClick(event);
                }
            }
        },

        onToValueChanged: function (event) {
            if (event.args.date != null) {
                this.toMoment = moment(event.args.date, 'ddd MMM D YYYY HH:mm:ss ZZ');
                if (this.fromMoment != null && this.toMoment != null) {
                    this.onClick(event);
                }
            }else if(event.args.date == null){
                this.toMoment = null;
                if (this.fromMoment == null && this.toMoment == null) {
                    this.onClick(event);
                }
            }
        },

        onOpen: function () {

        },
        onClose: function () {

        },

        onClick: function (event) {
            this.selectedValue = 0;
            this.displayDeliveries();
        },

        onChange:function(event)
        {
            this.fromMoment = null;
            this.toMoment = null;
            this.$refs.fromDateTimeInput.val( null );
            this.$refs.toDateTimeInput.val( null );

            this.toMoment = moment();

            if (this.selectedValue == 1) {
                this.fromMoment = moment().subtract(7, 'days');
            } else if (this.selectedValue == 2) {
                this.fromMoment = moment().subtract(1, 'months');
            } else if (this.selectedValue == 3) {
                this.fromMoment = moment().subtract(3, 'months');
            } else if (this.selectedValue == 4) {
                this.fromMoment = moment().subtract(6, 'months');
            } else if (this.selectedValue == 5) {
                this.fromMoment = moment().subtract(1, 'years');
            }else {
                this.fromMoment = null;
                this.toMoment = null;
            }

            this.displayDeliveries();
        },

        displayDeliveries:function ()
        {
            switch (this.currentChartIndex) {
                case 0:
                    this.getStatGroupNamesDailyDeliveries();
                    break;
                case 1:
                    this.getStatGroupNamesWeeklyDeliveries();
                    break;
                case 2:
                    this.getStatGroupNamesMonthlyDeliveries();
                    break;
                case 3:
                    this.getStatGroupNamesYearlyDeliveries();
                    break;
                default:
                    break;
            }
        },

        makeRiderPeriodTotalDelivery: function (deliveryData) {
            this.statGroupNameTotalDelivery.numOfProjects = _.sumBy(deliveryData, function(delivery) {return delivery.numOfProjects; });

            this.statGroupNameTotalDelivery.numOfAllDeliveries = _.sumBy(deliveryData, function(delivery) {return delivery.numOfAllDeliveries; });

            this.statGroupNameTotalDelivery.numOfCompletedDeliveries = _.sumBy(deliveryData, function(delivery) {return delivery.numOfCompletedDeliveries; });

            this.statGroupNameTotalDelivery.numOfWaitingDeliveries = _.sumBy(deliveryData, function(delivery) {return delivery.numOfWaitingDeliveries; });
            this.statGroupNameTotalDelivery.numOfReadyDeliveries = _.sumBy(deliveryData, function(delivery) {return delivery.numOfReadyDeliveries; });
            this.statGroupNameTotalDelivery.numOfGoingDeliveries = _.sumBy(deliveryData, function(delivery) {return delivery.numOfGoingDeliveries; });
            this.statGroupNameTotalDelivery.numOfServicingDeliveries = _.sumBy(deliveryData, function(delivery) {return delivery.numOfServicingDeliveries; });
            this.statGroupNameTotalDelivery.numOfRejectedDeliveries = _.sumBy(deliveryData, function(delivery) {return delivery.numOfRejectedDeliveries; });

            this.statGroupNameTotalDelivery.numOfFailedDeliveries = _.sumBy(deliveryData, function(delivery) {return delivery.numOfFailedDeliveries; });
        },
    },
};
