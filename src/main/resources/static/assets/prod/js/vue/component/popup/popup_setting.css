@charset "UTF-8";
@import url("/assets/common/css/font.css");

*{
	font-family: Noto Sans KR;
	padding: 0px;
	margin: 0px; 
}


.setting_popup_guidanceSetting{
	position:absolute;
	top:50%;
	left:50%;
	margin:-191px 0 0 -250px;

	display: block;
	width: 500px;
	height: 382px;
	border-radius: 5px;
	box-shadow: 5px 2px 5px #00000040;
	background-color: #FFFFFF;
/*	border:1px solid;*/

}
.setting_popup_gs_name_area{
	position: relative;
	top:5px;
	display: flex;
	display: flex;
	width: 500px;

}

.setting_popup_gs_name_icon{
	position: relative;
	top:3px;
	left: 20px;
	width: 30px;
	height: 30px;
	background-image: url("/assets/image/popup/icon_setting_option.svg");

}

.setting_popup_gs_name_text{
	position: relative;
	top:7px;
	left: 20px;
	width: 100px;
	height: 21px;
	font-size: 14px;
	overflow: hidden;
	font-family:Noto Sans KR;
	font-weight:700;
	color: #1D3244;
}

.setting_popup_gs_name_close_icon{
	position: relative;
	right: -330px;
	float: right;
	width: 30px;
	height: 30px;
	background-image: url("/assets/image/popup/bt_map_popup_close_W.svg");

}

.setting_popup_gs_name_area{
	position: relative;
	top:5px;
	display: flex;
	width: 500px;

}


.setting_popup_gs_setting1{
display: flex;
border:1px solid;
}


.setting_popup_gs_menu_text{

	font-family:Noto Sans KR;
	font-weight:600;
	font-size: 12px; 
	color: #1D3244;
}



.setting_popup_address_sepLine{
	position: absolute;
	left: 25px;
	width: 450px;
	height: 1px;
	background-color: #E6E6E6;
}



.setting_popup_gs_menu1{
	position: absolute;
	top:80px;
	left:140px;
	width: 300px;
	height: 30px;
}
.setting_popup_gs_menu1_text{
	position: absolute;
	left:30px;
	width: 150px;
	height: 30px;	
	font-family:Noto Sans KR;
	font-weight:600;
	font-size: 12px; 
	color: #1D3244;
}

.setting_popup_gs_menu1_label{
	font-family:Noto Sans KR;
	font-weight:600;
	font-size: 12px;
	color: #1D3244;
	position: absolute;
	left : 80px;

}

.setting_popup_gs_menu2{
	position: absolute;
	top:108px;
	left:140px;
	width: 300px;
	height: 30px;
}
.setting_popup_gs_menu3{
	position: absolute;
	top:254px; 
	left:140px;
	width: 300px;
	height: 30px;
}

.setting_popup_gs_button{
	position: absolute;
	top: 325px;
	left:170px;	
	width: 160px;
	height: 30px;
	font-size: 14px;
	font-weight: 700px;
	color:#fff;
	text-align: center;
	border :0px solid;
	padding-top: 1px;
	background-color: #FF5A00;
	border-radius: 3px
}






[type="radio"] {
  border: 0; 
  clip: rect(0 0 0 0); 
  height: 1px; margin: -1px; 
  overflow: hidden; 
  padding: 0; 
  position: absolute; 
  width: 1px;
}

.setting_popup_guidanceSetting .setting_popup_gs label {
  display: block;
  cursor: pointer;
  line-height: 2.5;
  font-size: 1.5em;
}



.setting_popup_guidanceSetting [type="radio"] + span:before {
  content: '';
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: -0.25em;
  border-radius: 1em;
  border: 0.125em solid #fff;
  margin-right: 0.75em;
  transition: 0.5s ease all;
  box-shadow: 0 0 0 0.15em #aaa;
  position: relative; /* #1777 */
}

[type="radio"]:checked + span:before {
  background: #1CEFCE;
  box-shadow: 0 0 0 0.25em #aaa;
}

.rbt1{
	position: absolute;
	left: 10px;
}
.rbt2{
	position: absolute;
	left: 115px;
}

.rbt3{
	position: absolute;
	left: 220px;
}


.versionLabel{
	position: absolute;
	left: 160px;
}

.versionLabel label{
	font-family:Noto Sans KR;
	font-weight:600;
	font-size: 12px;
	color: #1D3244;
	text-overflow: ellipsis;
}

.toggleWrap{
	position: absolute;
	left: 220px;
}


.toggleWrap input {
	display: none; }

.toggleWrap > div {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 10px;
	width: 34px;
	height: 20px;
	margin-top: 5px; 
}

.toggleWrap label {
	display: block;
	width: 36px;
	height: 20px;
	box-sizing: border-box;
	border-radius: 36px;
	border: 2px solid #e5e5e5;
	background: #fff;
	transition: all 0.3s ease; 
} 

.toggleWrap label > span {
	position: absolute;
	top: 3px;
	left: 3px;
	display: block;
	width: 14px;
	height: 14px;
	border-radius: 50%;
	box-sizing: border-box;
	box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.25), 0 3px 3px 0 rgba(0, 0, 0, 0.15);
	transition: all 0.3s cubic-bezier(0.275, -0.45, 0.725, 1.45);
	background: #fff; 
}



.toggleWrap input:active + div label, input:checked + div label {
	border: 10px solid #1CEFCE;
	box-shadow: 0 0 10  #eeeeee; 
}
.toggleWrap input:active + div label > span, input:checked + div label > span {
	left: 18px; 
}

