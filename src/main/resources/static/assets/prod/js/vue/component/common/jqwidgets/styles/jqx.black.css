.jqx-rc-tl-black {
    -moz-border-radius-topleft: 5px;
    -webkit-border-top-left-radius: 5px;
    border-top-left-radius: 5px;
}
/*top-right rounded Corners*/
.jqx-rc-tr-black {
    -moz-border-radius-topright: 5px;
    -webkit-border-top-right-radius: 5px;
    border-top-right-radius: 5px;
}
/*bottom-left rounded Corners*/
.jqx-rc-bl-black {
    -moz-border-radius-bottomleft: 5px;
    -webkit-border-bottom-left-radius: 5px;
    border-bottom-left-radius: 5px;
}
/*bottom-right rounded Corners*/
.jqx-rc-br-black {
    -moz-border-radius-bottomright: 5px;
    -webkit-border-bottom-right-radius: 5px;
    border-bottom-right-radius: 5px;
}
/*top rounded Corners*/
.jqx-rc-t-black {
    -moz-border-radius-topleft: 5px;
    -webkit-border-top-left-radius: 5px;
    border-top-left-radius: 5px;
    -moz-border-radius-topright: 5px;
    -webkit-border-top-right-radius: 5px;
    border-top-right-radius: 5px;
}
/*bottom rounded Corners*/
.jqx-rc-b-black {
    -moz-border-radius-bottomleft: 5px;
    -webkit-border-bottom-left-radius: 5px;
    border-bottom-left-radius: 5px;
    -moz-border-radius-bottomright: 5px;
    -webkit-border-bottom-right-radius: 5px;
    border-bottom-right-radius: 5px;
}
/*right rounded Corners*/
.jqx-rc-r-black {
    -moz-border-radius-topright: 5px;
    -webkit-border-top-right-radius: 5px;
    border-top-right-radius: 5px;
    -moz-border-radius-bottomright: 5px;
    -webkit-border-bottom-right-radius: 5px;
    border-bottom-right-radius: 5px;
}
/*left rounded Corners*/
.jqx-rc-l-black {
    -moz-border-radius-topleft: 5px;
    -webkit-border-top-left-radius: 5px;
    border-top-left-radius: 5px;
    -moz-border-radius-bottomleft: 5px;
    -webkit-border-bottom-left-radius: 5px;
    border-bottom-left-radius: 5px;
}
/*all rounded Corners*/
.jqx-rc-all-black {
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
}

.jqx-widget-black {
    color: inherit;
}

.jqx-grid-selectionarea-black {
    background-color: #262626;
    border: 1px solid #262626;
    opacity: 0.5;
}

.jqx-grid-group-cell-black {
    border-color: #262626;
    background-color: #fff;
}

.jqx-grid-cell-sort-black, .jqx-grid-cell-filter-black, .jqx-grid-cell-pinned-black {
    background-color: #eaf8ff;
}

.jqx-grid-cell-alt-black, .jqx-grid-cell-sort-alt-black, .jqx-grid-cell-filter-alt-black {
    background-color: #deedf5;
}

.jqx-widget-black .jqx-grid-cell-black {
    background: #000;
    border-color: #555;
}

.jqx-widget-black .jqx-grid-group-cell-black {
    background-color: #000;
    border-color: #555;
}

.jqx-widget-black .jqx-grid-cell-selected-black {
    color: #fff !important;
}

.jqx-widget-black .jqx-grid-cell-hover-black {
    color: #fff !important;
}

.jqx-widget-black {
    font-family: Tahoma,"Lucida Sans",Verdana,Helvetica,Arial,sans-serif;
    border-color: #222;
}

.jqx-widget-content-black {
    font-family: Tahoma,"Lucida Sans",Verdana,Helvetica,Arial,sans-serif;
    color: #fff;
    border-color: #262626;
    background: #111;
}

.jqx-fill-state-normal-black, .jqx-widget-header-black, .jqx-input-button-content-black {
    font-family: Tahoma,"Lucida Sans",Verdana,Helvetica,Arial,sans-serif;
    color: #fff;
    border-color: #000;
    background: #a4a4a4;
    background: -moz-linear-gradient(top, #a4a4a4 0%, #757575 4%, #4f4f4f 37%, #464646 48%, #000000 52%, #000000 56%, #0c0c0c 67%, #1e1e1e 78%, #414141 96%, #666666 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#a4a4a4), color-stop(4%,#757575), color-stop(37%,#4f4f4f), color-stop(48%,#464646), color-stop(52%,#000000), color-stop(56%,#000000), color-stop(67%,#0c0c0c), color-stop(78%,#1e1e1e), color-stop(96%,#414141), color-stop(100%,#666666));
    background: -webkit-linear-gradient(top, #a4a4a4 0%,#757575 4%,#4f4f4f 37%,#464646 48%,#000000 52%,#000000 56%,#0c0c0c 67%,#1e1e1e 78%,#414141 96%,#666666 100%);
    background: -o-linear-gradient(top, #a4a4a4 0%,#757575 4%,#4f4f4f 37%,#464646 48%,#000000 52%,#000000 56%,#0c0c0c 67%,#1e1e1e 78%,#414141 96%,#666666 100%);
    background: -ms-linear-gradient(top, #a4a4a4 0%,#757575 4%,#4f4f4f 37%,#464646 48%,#000000 52%,#000000 56%,#0c0c0c 67%,#1e1e1e 78%,#414141 96%,#666666 100%);
    background: linear-gradient(top, #a4a4a4 0%,#757575 4%,#4f4f4f 37%,#464646 48%,#000000 52%,#000000 56%,#0c0c0c 67%,#1e1e1e 78%,#414141 96%,#666666 100%);
}

.jqx-fill-state-hover-black, .jqx-input-button-header-black, .jqx-widget-black .jqx-grid-cell-hover-black {
    background: #f87948;
    background: -moz-linear-gradient(top, #f87948 0%, #fb4e0b 4%, #f44501 11%, #e24000 19%, #983600 44%, #983600 48%, #5c2900 52%, #692b00 67%, #c23700 96%, #bd3800 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f87948), color-stop(4%,#fb4e0b), color-stop(11%,#f44501), color-stop(19%,#e24000), color-stop(44%,#983600), color-stop(48%,#983600), color-stop(52%,#5c2900), color-stop(67%,#692b00), color-stop(96%,#c23700), color-stop(100%,#bd3800));
    background: -webkit-linear-gradient(top, #f87948 0%,#fb4e0b 4%,#f44501 11%,#e24000 19%,#983600 44%,#983600 48%,#5c2900 52%,#692b00 67%,#c23700 96%,#bd3800 100%);
    background: -o-linear-gradient(top, #f87948 0%,#fb4e0b 4%,#f44501 11%,#e24000 19%,#983600 44%,#983600 48%,#5c2900 52%,#692b00 67%,#c23700 96%,#bd3800 100%);
    background: -ms-linear-gradient(top, #f87948 0%,#fb4e0b 4%,#f44501 11%,#e24000 19%,#983600 44%,#983600 48%,#5c2900 52%,#692b00 67%,#c23700 96%,#bd3800 100%);
    background: linear-gradient(top, #f87948 0%,#fb4e0b 4%,#f44501 11%,#e24000 19%,#983600 44%,#983600 48%,#5c2900 52%,#692b00 67%,#c23700 96%,#bd3800 100%);
    border-color: #262626;
    color: #fff;
}

.jqx-fill-state-pressed-black, .jqx-menu-item-top-hover-black, .jqx-widget-black .jqx-grid-cell-selected-black {
    background: #db4200;
    background: -moz-linear-gradient(top, #db4200 0%, #902e00 4%, #7d2a01 19%, #531c03 44%, #531c03 48%, #0f0906 52%, #221105 63%, #2b1705 67%, #592903 89%, #642e02 96%, #902e00 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#db4200), color-stop(4%,#902e00), color-stop(19%,#7d2a01), color-stop(44%,#531c03), color-stop(48%,#531c03), color-stop(52%,#0f0906), color-stop(63%,#221105), color-stop(67%,#2b1705), color-stop(89%,#592903), color-stop(96%,#642e02), color-stop(100%,#902e00));
    background: -webkit-linear-gradient(top, #db4200 0%,#902e00 4%,#7d2a01 19%,#531c03 44%,#531c03 48%,#0f0906 52%,#221105 63%,#2b1705 67%,#592903 89%,#642e02 96%,#902e00 100%);
    background: -o-linear-gradient(top, #db4200 0%,#902e00 4%,#7d2a01 19%,#531c03 44%,#531c03 48%,#0f0906 52%,#221105 63%,#2b1705 67%,#592903 89%,#642e02 96%,#902e00 100%);
    background: -ms-linear-gradient(top, #db4200 0%,#902e00 4%,#7d2a01 19%,#531c03 44%,#531c03 48%,#0f0906 52%,#221105 63%,#2b1705 67%,#592903 89%,#642e02 96%,#902e00 100%);
    background: linear-gradient(top, #db4200 0%,#902e00 4%,#7d2a01 19%,#531c03 44%,#531c03 48%,#0f0906 52%,#221105 63%,#2b1705 67%,#592903 89%,#642e02 96%,#902e00 100%);
    border-color: #262626;
    color: #fff;
}

.jqx-fill-state-disabled-black {
    cursor: default;
    opacity: .55;
    filter: Alpha(Opacity=45);
}

.jqx-checkbox-check-checked-black {
    background: transparent url(images/check_white.png) left top no-repeat;
}

.jqx-checkbox-check-indeterminate-black {
    background: transparent url(images/check_indeterminate_white.png) left top no-repeat;
}

.jqx-grid-black, .jqx-grid-header-black, .jqx-grid-cell-black {
    border-color: #262626;
}

.jqx-widget-black .jqx-grid-column-menubutton-black, .jqx-widget-black .jqx-grid-column-sortascbutton-black, .jqx-widget-black .jqx-grid-column-sortdescbutton-black, .jqx-widget-black .jqx-grid-column-filterbutton-black {
    border-color: #262626;
}

.jqx-widget-black .jqx-grid-column-header-black {
    border-color: #262626;
}

.jqx-calendar-cell-today-black {
    color: #35353A;
}

.jqx-grid-bottomright-black, .jqx-panel-bottomright-black, .jqx-listbox-bottomright-black {
    background-color: #262626;
}

.jqx-widget-black .jqx-grid-column-menubutton-black, .jqx-menu-vertical-black {
    background-color: #262626;
    border-color: #262626;
}

.jqx-menu-vertical-black {
    background: #222;
    color: #fff;
}

.jqx-scrollbar-state-normal-black {
    background: #111;
    border: 1px solid #111;
}

.jqx-scrollbar-button-state-normal-black {
    border: 1px solid #111;
    background: #111;
}

.jqx-scrollbar-button-state-hover-black {
    background: #f87948;
    background: -moz-linear-gradient(top, #f87948 0%, #fb4e0b 4%, #f44501 11%, #e24000 19%, #983600 44%, #983600 48%, #5c2900 52%, #692b00 67%, #c23700 96%, #bd3800 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f87948), color-stop(4%,#fb4e0b), color-stop(11%,#f44501), color-stop(19%,#e24000), color-stop(44%,#983600), color-stop(48%,#983600), color-stop(52%,#5c2900), color-stop(67%,#692b00), color-stop(96%,#c23700), color-stop(100%,#bd3800));
    background: -webkit-linear-gradient(top, #f87948 0%,#fb4e0b 4%,#f44501 11%,#e24000 19%,#983600 44%,#983600 48%,#5c2900 52%,#692b00 67%,#c23700 96%,#bd3800 100%);
    background: -o-linear-gradient(top, #f87948 0%,#fb4e0b 4%,#f44501 11%,#e24000 19%,#983600 44%,#983600 48%,#5c2900 52%,#692b00 67%,#c23700 96%,#bd3800 100%);
    background: -ms-linear-gradient(top, #f87948 0%,#fb4e0b 4%,#f44501 11%,#e24000 19%,#983600 44%,#983600 48%,#5c2900 52%,#692b00 67%,#c23700 96%,#bd3800 100%);
    background: linear-gradient(top, #f87948 0%,#fb4e0b 4%,#f44501 11%,#e24000 19%,#983600 44%,#983600 48%,#5c2900 52%,#692b00 67%,#c23700 96%,#bd3800 100%);
    border: 1px solid #000;
}

.jqx-scrollbar-button-state-pressed-black {
    background: #555 url(images/darkred_gradient.png) left top scroll repeat-x;
    border: 1px solid #000;
}

.jqx-scrollbar-thumb-state-normal-horizontal-black {
    background: #555;
    background-image: -webkit-gradient(linear,0 0,0 100%,from(#717171),to(#494949));
    background-image: -moz-linear-gradient(top,#717171,#494949);
    background-image: -o-linear-gradient(top,#717171,#494949);
    border: 1px solid #000;
}

.jqx-scrollbar-thumb-state-hover-horizontal-black {
    background: #983600;
    background-image: -webkit-gradient(linear,0 0,0 100%,from(#fb4e0b),to(#983600));
    background-image: -moz-linear-gradient(top,#fb4e0b,#983600);
    background-image: -o-linear-gradient(top,#fb4e0b,#983600);
    border: 1px solid #000;
}

.jqx-scrollbar-thumb-state-pressed-horizontal-black {
    background: #902e00;
    background-image: -webkit-gradient(linear,0 0,0 100%,from(#902e00),to(#531c03));
    background-image: -moz-linear-gradient(top,#902e00,#531c03);
    background-image: -o-linear-gradient(top,#902e00,#531c03);
    border: 1px solid #000;
}

.jqx-scrollbar-thumb-state-normal-black {
    background: #555;
    background-image: -webkit-gradient(linear,left top,left top,from(#717171),to(#494949));
    background-image: -moz-linear-gradient(left,#717171,#494949);
    background-image: -o-linear-gradient(left,#717171,#494949);
    border: 1px solid #000;
}

.jqx-scrollbar-thumb-state-hover-black {
    background: #983600;
    background-image: -webkit-gradient(linear,left top,left top,from(#fb4e0b),to(#983600));
    background-image: -moz-linear-gradient(left,#fb4e0b,#983600);
    background-image: -o-linear-gradient(left,#fb4e0b,#983600);
    border: 1px solid #000;
}

.jqx-scrollbar-thumb-state-pressed-black {
    background: #902e00;
    background-image: -webkit-gradient(linear,left top,left top,from(#902e00),to(#531c03));
    background-image: -moz-linear-gradient(left,#902e00,#531c03);
    background-image: -o-linear-gradient(left,#902e00,#531c03);
    border: 1px solid #000;
}

.jqx-splitter-splitbar-horizontal-black, .jqx-splitter-splitbar-vertical-black, .jqx-splitter-splitbar-hover-black, .jqx-splitter-splitbar-hover-horizontal-black {
    background: #555;
}

.jqx-splitter-collapse-button-horizontal-black, .jqx-splitter-collapse-button-vertical-black {
    background: #7f7f7f;
}

.jqx-grid-column-sortascbutton-black, .jqx-expander-arrow-bottom-black, .jqx-window-collapse-button-black, .jqx-menu-item-arrow-up-black, .jqx-menu-item-arrow-up-selected-black, .jqx-menu-item-arrow-top-up-black, .jqx-icon-arrow-up-black, .jqx-icon-arrow-up-hover-black, .jqx-icon-arrow-up-selected-black {
    background-image: url('images/icon-up-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-black .jqx-grid-group-expand-black, .jqx-grid-group-expand-black, .jqx-grid-column-menubutton-black, .jqx-grid-column-sortdescbutton-black, .jqx-expander-arrow-top-black, .jqx-window-collapse-button-collapsed-black, .jqx-menu-item-arrow-down-black, .jqx-menu-item-arrow-down-selected-black, .jqx-menu-item-arrow-down-black, .jqx-icon-arrow-down-black, .jqx-icon-arrow-down-hover-black, .jqx-icon-arrow-down-selected-black {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tabs-arrow-left-black, .jqx-menu-item-arrow-left-selected-black, .jqx-menu-item-arrow-top-left, .jqx-icon-arrow-left-black, .jqx-icon-arrow-down-left-black, .jqx-icon-arrow-left-selected-black {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-widget-black .jqx-grid-group-collapse-black, .jqx-grid-group-collapse-black, .jqx-tabs-arrow-right-black, .jqx-menu-item-arrow-right-selected-black, .jqx-menu-item-arrow-top-right-black, .jqx-icon-arrow-right-black, .jqx-icon-arrow-right-hover-black, .jqx-icon-arrow-right-selected-black {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-window-close-button-black, .jqx-icon-close-black, .jqx-tabs-close-button-black, .jqx-tabs-close-button-hover-black, .jqx-tabs-close-button-selected-black {
    background-image: url(images/close_white.png);
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-tree-item-arrow-collapse-rtl-black, .jqx-tree-item-arrow-collapse-hover-rtl-black {
    background-image: url(images/icon-left.png);
}

.jqx-listitem-state-group-black {
    background: #222;
}

.jqx-calendar-cell-specialDate-black {
    border: 1px solid black;
}

.jqx-tabs-selection-tracker-top-black, .jqx-tabs-selection-tracker-bottom-black {
    background-color: #000;
    border-color: #000;
}

.jqx-widget-black .jqx-grid-cell-sort-black, .jqx-widget-black .jqx-grid-cell-alt-black, .jqx-widget-black .jqx-grid-cell-filter-black, .jqx-widget-black .jqx-grid-cell-pinned-black {
    background-color: #333;
}

.jqx-listbox-feedback-black {
    border-top: 1px dashed #ccc;
}

.jqx-combobox-input-black, .jqx-combobox-content-black, .jqx-input-black, .jqx-input-content-black {
    background: #f7f7f7;
    color: #000;
}

.jqx-widget-black, .jqx-widget-header-black, .jqx-widget-content-black {
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}

.jqx-fill-state-focus-black .jqx-widget-content {
    border-color: #000;
}

.jqx-radiobutton-check-checked-black {
    background: #fff;
}

.jqx-icon-arrow-first-black {
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-arrow-last-black {
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-black.jqx-grid-cell-selected-black > .jqx-grid-group-expand-black,
.jqx-grid-cell-black.jqx-grid-cell-hover-black > .jqx-grid-group-expand-black {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-black.jqx-grid-cell-selected-black > .jqx-grid-group-collapse-black,
.jqx-grid-cell-black.jqx-grid-cell-hover-black > .jqx-grid-group-collapse-black {
    background-image: url('images/icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-black.jqx-grid-cell-selected-black > .jqx-grid-group-collapse-rtl-black,
.jqx-grid-cell-black.jqx-grid-cell-hover-black > .jqx-grid-group-collapse-rtl-black {
    background-image: url('images/icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-grid-cell-black.jqx-grid-cell-selected-black > .jqx-grid-group-expand-rtl-black,
.jqx-grid-cell-black.jqx-grid-cell-hover-black > .jqx-grid-group-expand-rtl-black {
    background-image: url('images/icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}

.jqx-icon-calendar-black, .jqx-icon-calendar-hover-black, .jqx-icon-calendar-pressed-black {
    background-image: url('images/icon-calendar-white.png');
}

.jqx-icon-time-black, .jqx-icon-time-hover-black, .jqx-icon-time-pressed-black {
    background-image: url('images/icon-time-white.png');
}

.jqx-icon-delete-black {
    background-image: url('images/icon-delete-white.png');
}

.jqx-icon-edit-black {
    background-image: url('images/icon-edit-white.png');
}

.jqx-icon-save-black {
    background-image: url('images/icon-save-white.png');
}

.jqx-icon-cancel-black {
    background-image: url('images/icon-cancel-white.png');
}

.jqx-icon-search-black {
    background-image: url(images/search_white.png);
}

.jqx-icon-plus-black {
    background-image: url(images/plus_white.png);
}

.jqx-menu-minimized-button-black {
    background-image: url('images/icon-menu-minimized-white.png');
}

.jqx-editor-toolbar-icon-black {
    background: url('images/html_editor_white.png') no-repeat;
}

.jqx-layout-black {
    background-color: #000;
}

.jqx-layout-pseudo-window-pin-icon-black {
    background-image: url("images/pin-white.png");
}

.jqx-layout-pseudo-window-pinned-icon-black {
    background-image: url("images/pinned-white.png");
}

.jqx-scheduler-month-cell-black, .jqx-scheduler-time-column-black, .jqx-scheduler-toolbar-black {
    background: #35353A !important;
    color: #fff !important;
}

.jqx-widget-black .jqx-scheduler-middle-cell-black, .jqx-scheduler-middle-cell-black {
    border-bottom-color: #35353A !important;
}

.jqx-kanban-item-black {
    box-shadow: none;
}


.jqx-grid-column-menubutton-black {
    border-style: solid;
    border-width: 0px 0px 0px 1px;
    border-color: transparent;
    background-image: url('images/icon-menu-small-white.png') !important;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
}

.jqx-item-black .jqx-grid-sortasc-icon {
    background-image: url('images/icon-sort-asc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
}
/*applied to the sort ascending menu item in the Grid's Context Menu*/
.jqx-item-black .jqx-grid-sortdesc-icon {
    background-image: url('images/icon-sort-desc-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
}
/*applied to the grid menu's sort remove item/*/
.jqx-item-black .jqx-grid-sortremove-icon {
    background-image: url('images/icon-sort-remove-white.png');
    background-repeat: no-repeat;
    background-position: left center;
    width: 16px;
    height: 16px;
    float: left;
    margin-left: -4px;
    margin-right: 4px;
}
/*applied to the timepicker labels*/
.jqx-label-black {
    fill: darkgray;
}
/*applied to the timepicker needle*/
.jqx-needle-black {
    fill: darkgray;
}
/*applied to the timepicker needle circle*/
.jqx-needle-central-circle-black {
    fill: darkgray;
}
