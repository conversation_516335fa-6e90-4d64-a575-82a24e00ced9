var simulationPopup = {
  template: `
        <div class="modal-mask">
            <div class="modal-wrapper">
                <div class="simulation_popup">
                    <div class="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full">
                      <div class="flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-orange-600">{{$t("가배차 설정")}}</h2>
                        <button class="text-orange-600 hover:text-orange-800" @click="clickCancel">
                          <svg xmlns="http://www.w3.org/2000/svg;" class=" h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                      <div class="mt-4">
                        <label class="block text-gray-700">{{$t("기사수 기준(명):")}}
                          <span class="text-red-600"> * </span>
                        </label>
                        <input type="text" v-model="simulationData.riderCount" class="w-full mt-1 p-2 border border-gray-300 rounded-lg" :placeholder="$t('명')" oninput="this.value = Math.min(150, this.value.replace(/[^0-9]/g, ''));">
                      </div>
                      <div class="mt-4">
                        <label class="block text-gray-700">{{$t("호차 시작 번호:")}}
                          <span class="text-red-600"> * </span>
                        </label>
                        <input type="text" v-model="simulationData.carStartNumber" class="w-full mt-1 p-2 border border-gray-300 rounded-lg" :placeholder="$t('호차 번호')" oninput="this.value = this.value.replace(/[^0-9]/g, '');"
                        @blur="clickRiderInfo">
                      </div>
                      <div class="mt-4">
                        <label class="block text-gray-700">{{$t("근무 시작 주소:")}}
                          <span class="text-red-600"> * </span>
                        </label>
                        <input type="text" v-model="simulationData.address" class="w-full mt-1 p-2 border border-gray-300 rounded-lg" :placeholder="$t('주소 입력')">
                      </div>
                      <div class="mt-6 flex gap-4">
                        <button class="flex-1 flex items-center justify-center px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400" @click="clickCancel">
                          <svg xmlns="http://www.w3.org/2000/svg;" class=" h-5 w-5 mr-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                          </svg> {{$t("취소")}} </button>
                        <button class="flex-1 flex items-center justify-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:bg-gray-300" :disabled="getButtonDisable" @click="clickSimulation">
                          <svg xmlns="http://www.w3.org/2000/svg;" class="h-5 w-5 mr-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                          </svg> {{$t("확인")}} </button>
                      </div>
                    </div>                
                </div>
            </div>
        </div>
    `,
  props: {
    cancelFunction: {
      type: Function,
      required: true
    },
  },
  computed: {
    getButtonDisable() {
      return this.simulationData.riderCount.length === 0 || this.simulationData.carStartNumber.length === 0 || this.simulationData.address.length === 0;
    },
    getRiderButtonDisable() {
      return this.simulationData.carStartNumber.length === 0;
    }
  },
  data: function () {
    return {
      simulationData: {
        riderCount: '',
        carStartNumber: '',
        address: '',
      },
    }
  },

  mounted() {
    if (Util.isHyundaiUserCompany())
      this.getHyundaiDepartmentAddress();
    else if(Util.isWelStoryUserCompany())
      this.simulationData.address = "경기도 용인시 기흥구 용구대로 2442-1";
  },

  methods: {
    clickSimulation() {
      this.$emit('confirm-clicked', this.simulationData);
    },
    clickCancel() {
      if (this.cancelFunction != null) {
        this.cancelFunction();
      }

      this.$emit('close');
    },

    getHyundaiDepartmentAddress() {
      DEPARTMENT_API.getUserDepartmentsList({
        onSuccess: (response) => {
          console.log(response);
          if (!_.isNil(response.data))
            this.simulationData.address = response.data[0].addressBase;
        },
        onError: (error) => {
          PopupUtil.showErrorPopup(error);
        }
      })
    },

    clickRiderInfo() {
      const getUserInfo = this.$store.getters.getLoginUserInfo;
      let mobileStr = getUserInfo.id.toString() + this.simulationData.carStartNumber.toString();
      if (!Util.isHyundaiUserCompany() && !Util.isWelStoryUserCompany() && !Util.isToHomeUserCompany()) {
        this.simulationData.address = '';
        RIDER_API.getFindMobileRider(mobileStr, {
          onSuccess: (response) => {
            console.log(response);
            this.simulationData.address = response.data.workingStartAddress;
          },
          onError: (error) => {
            console.log(error);
            PopupUtil.showErrorPopup(error);
          }
        });
      }
    }
  }
};