@charset "UTF-8";
@import url("/assets/common/css/font.css");


/** {	*/
/*    margin: 0px;*/
/*    padding: 0px;*/
/*    font-family: Noto Sans KR;*/
/*    font-weight: 500;*/
/*!*    border: 1px solid red;*!*/
/*}*/

/*body{*/
/*	width: 100%;*/
/*	height: 100%;*/
/*}*/

#report_driver_chart_2_body {
	position: absolute;
	top: 41px;
	width: 100%;
	height: calc(100% - 41px);
}

#report_driver_chart_2_body .or02_window_close {
	position: absolute;
	top: 0px;
	right: 30px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #0B3138;
	background-image: url("/assets/image/popup/bt_popup_close_n.svg");
}

#report_driver_chart_2_body .bg{
	position: absolute;
	width: 	100%;
	height: 100%;
	background-color: #ccc;
}



/*--------------------프로젝트 관리-------------------------*/


#report_driver_chart_2_body .report_page_4{
	position: absolute;	
	top: 0px;
	bottom: 0px;
	left: 0px;
	width: 100%;
	min-width: 1280px;
	height: 100%;
	background-color: #0B3138 ;
	z-index: 80;
	transform: translateY(0px);
    transition: transform 1s cubic-bezier(0.165, 0.84, 0.44, 1);
/*  border: 1px solid blue;*/
/*	display: none;*/

}

#report_driver_chart_2_body .report_info_4{
	position: absolute;
	top:0px;
	left: 0px;
	width: 100%;
	height: 80px;
	background-color: #113E46;
}


#report_driver_chart_2_body .report_data_4{
	position: relative;
	display: flex;
	width: 500px;
	height: 34px;
	margin-top: 23px;
	margin-left: 30px;
/*	border: 1px solid;*/
}

#report_driver_chart_2_body .project_manage_date_area {
	position: relative;
	display: flex;
	top: 0px;
	right: 0px;
	width: 300px;
	height: 25px;
	border-radius: 2px;
	border: 0px solid #D5D5D5;
}

#report_driver_chart_2_body .project_manage_inputdate1 {
	position: relative;
	top: 5px;
	left: 5px;
	width: 120px;
	height: 25px;
	border: 0px solid red;
}

#report_driver_chart_2_body .project_manage_date_middle {
	position: relative;
	top: 5px;
	left: 10px;
	width: 10px;
	height: 30px;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	color: #D5D5D5;
	border: 0px solid;
}

#report_driver_chart_2_body .project_manage_inputdate2 {
	position: relative;
	top: 5px;
	left: 15px;
	width: 120px;
	height: 25px;
	border: 0px solid red;
}

/**/
#report_driver_chart_2_body .report_data_icon_4 {
	position: relative;
	top: 0px;
	width: 36px;
	height: 36px;
	background-image: url("/assets/image/report/bt_project_cal_rx_n.svg");
	background-repeat: no-repeat;
	background-size: 100%;
	border: 0px solid;
	outline: none;
}

#report_driver_chart_2_body .report_data_search{
	position: absolute;
	top:22px;
	right: 190px;
	width: 250px;
	height: 36px;
	padding-left: 15px;
	outline: none;	
	background: url("/assets/image/report/ic_project_input_search.svg") right center no-repeat;
	background-color: #0B3138;
	border-radius: 3px;
	border: 1px solid #113E46;


}

#report_driver_chart_2_body .report_data_search::placeholder{
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
	color: #889FA3;
}


#report_driver_chart_2_body .report_data_num_1_4{
	position: relative;
	left: 5px;
	width: 98px;
	height: 30px;
	padding-top:3px;
	padding-left: 12px;
	color: #6DEBAE;
	background-color: #0B3138;
	border: 1px solid #113E46;
	border-radius: 2px;
	font-size: 16px;
}

#report_driver_chart_2_body .report_data_num_1_1_4{
	position: relative;
	left: 5px;
	margin-left: 5px;
	padding-top: 4px;
	padding-left: 5px;
	width: 15px;
	height: 30px;
/*	border: 1px solid #fff;*/
}

#report_driver_chart_2_body .report_data_num_2_4{
	position: relative;
	left: 10px;
	width: 98px;
	height: 30px;
	padding-top:3px;
	padding-left: 12px;
	color: #6DEBAE;
	background-color: #0B3138;
	border: 1px solid #113E46;
	border-radius: 2px;
	font-size: 16px;
}

#report_driver_chart_2_body .report_data_num_3_4{
	position: relative;
	left: 15px;
	width: 100px;
	height: 35px;
	padding-left: 10px;
	background-color: #0B3138;
	border: 1px solid #113E46;
	outline: none;
	color: #fff;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
	border-radius: 2px;
}

#report_driver_chart_2_body .report_data_line_4{
	position: absolute;
	top: 10px;
	right: 168px;
	width: 1px;
	height: 60px;
	background-color: #0B3138;
}

#report_driver_chart_2_body .report_data_view_area_4{
	position: absolute;
	top: 30px;
	right: 30px;
	width: 122px;
	height: 20px;
	color: #fff;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
	border:0px solid;
	outline: none;
	background-color: rgb(0,0,0);
}

#report_driver_chart_2_body .report_data_view_icon_4{
	position: absolute;
	top:1px;
	right: 0px;
	width: 19px;
	height: 19px;
	background-image: url("/assets/image/report/ic_projectmenu.svg");
	background-repeat: no-repeat;
}

#report_driver_chart_2_body .report_chart_area_1_4{
	position: absolute;
	bottom: 0px;
	width: 100%;
	height: calc( 100% - 80px);
	background-color: #0B3138;
}

#report_driver_chart_2_body .report_chart_area_right_4{
	position: absolute;
	left: 40px;
	width: calc(100% - 80px);
	height: 100%;
/*	border: 1px solid red;*/
}

#report_driver_chart_2_body .report_bar_chart_area_4{
	position: relative;
	width: calc(100% - 20px);
	top:65px;
	left: 10px;
	height: 630px;
	overflow: hidden;
	border: 1px solid;
}

#report_driver_chart_2_body .report_bar_chart_label_4{
	top: 0px;
	width: 100%;
	height: 27px;

/*	border: 1px solid;*/
}

#report_driver_chart_2_body .report_bar_chart_label_1_4{
	position: absolute; 
	display: flex;
	left: 0px;
	width: 275px;
	height: 27px;
	text-align: left;
	color: #fff;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
}

#report_driver_chart_2_body .report_bar_chart_label_1_1_4{
	position: absolute; 
	left: 140px;
	height: 27px;
	text-align: left;
	color:#6DEBAE;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
/*	border: 1px solid;*/
}

#report_driver_chart_2_body .report_b_chart_tab_area_4{
	position: absolute; 
	right: 39px;
	width: 230px;
	height: 27px;
	color: #fff;
/*	border: 1px solid;*/
}

#report_driver_chart_2_body .report_b_chart_tab{

}

#report_driver_chart_2_body #b_chart_1, #b_chart_2, #b_chart_3, #b_chart_4{
	display: none;
}

#report_driver_chart_2_body .report_b_chart_tab_1{
	width: 46px;
	color: #B2B2B2;
/*	border: 1px solid red;*/
}

#report_driver_chart_2_body .report_b_chart_tab_2{
	width: 46px;
	color: #B2B2B2;
/*	border: 1px solid red;*/
}

#report_driver_chart_2_body .report_b_chart_tab_3{
	width: 46px;
	color: #B2B2B2;
/*	border: 1px solid red;*/
}

#report_driver_chart_2_body .report_b_chart_tab_4{
	width: 46px;
	color: #B2B2B2;
	/*	border: 1px solid red;*/
}

#report_driver_chart_2_body #b_chart_1[type="radio"]:checked ~ .report_b_chart_tab_1{
	color: #6DEBAE;
}

#report_driver_chart_2_body #b_chart_2[type="radio"]:checked ~ .report_b_chart_tab_2{
	color: #6DEBAE;
}

#report_driver_chart_2_body #b_chart_3[type="radio"]:checked ~ .report_b_chart_tab_3{
	color: #6DEBAE;
}

#report_driver_chart_2_body #b_chart_4[type="radio"]:checked ~ .report_b_chart_tab_4{
	color: #6DEBAE;
}

#report_driver_chart_2_body .chart_bar_1{
	position: absolute;
	top:-5px;
	left: -148px;
	width: 500px;
	height: 500px;
	border: 1px solid;	
	z-index: 50;
}

#report_driver_chart_2_body .chart_bar_2{
	position: absolute;
	width: 50%;
	height: 300px;
	border: 1px solid;	
	z-index: 50;
}


#report_driver_chart_2_body .chart-container{
/*width: 100%; 
height: 300px;*/
 position: relative;
 top:70px;

}

#report_driver_chart_2_body .chart-container canvas{
	position: relative;
	width: 100%; 
	height:530px;
}

#report_driver_chart_2_body .project_manage_page_first {
	position: relative;
	top: 10px;
	left: 0px;
	width: 15px;
	height: 45px;
	outline: 0px;
	background-color: #113E46;
	background-image: url("/assets/image/report/bt_report_page_first.svg");
	border: 0px solid;
}

#report_driver_chart_2_body .project_manage_page_end {
	position: relative;
	top: 10px;
	left: 0px;
	width: 15px;
	height: 45px;
	outline: 0px;
	background-color: #113E46;
	background-image: url("/assets/image/report/bt_report_page_end.svg");
	border: 0px solid;
}

#report_driver_chart_2_body .project_manage_page_area {
	position: relative;
	top: 60px;
	margin: 0 auto;
	width: fit-content;
	height: 45px;
	padding-bottom: 6px;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
	border: 0px solid;
}

#report_driver_chart_2_body .project_manage_page_pre {
	position: relative;
	top: 10px;
	left: 0px;
	width: 15px;
	height: 45px;
	outline: 0px;
	background-color: #113E46;
	background-image: url("/assets/image/leftpanel/bt_leftlist_close.svg");
	border: 0px solid;
}

#report_driver_chart_2_body .st_search_list_pageNum {
	position: relative;
	top: -8px;
	width: -moz-fit-content;
	width: fit-content;
	padding: 5px;
	height: 17px;
	font-size: 13px;
	font-weight: 500;
	outline: none;
	background-color: rgba(0, 0, 0, 0);
	font-family: 'Noto Sans KR', sans-serif;
	color: #fff;
	border: 0px solid;
}

#report_driver_chart_2_body .st_search_list_pageNum.pick {
	color: #6DEBAE;
}

#report_driver_chart_2_body .project_manage_page_next {
	position: relative;
	top: 10px;
	left: 0px;
	width: 15px;
	height: 45px;
	outline: 0px;
	background-color: #113E46;
	background-image: url("/assets/image/leftpanel/bt_leftlist_open.svg");
	border: 0px solid;
}

#report_driver_chart_2_body .project_page_number {
	position: relative;
	top: -9px;
	left: 0px;
	width: 110px;
	height: 45px;
	outline: 0px;
	background-color: #083338;
	border: 0px solid;
}

#report_driver_chart_2_body .menu_font {
	color: #fff;
	font-weight: 600;
}