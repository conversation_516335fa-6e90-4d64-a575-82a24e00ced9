
:root {
    --jqx-primary-rgb: 51, 173, 255;
    --jqx-primary: rgb(var(--jqx-primary-rgb));
    --jqx-background: #252526;
    --jqx-background-color: #E7E7E7;
    --jqx-surface:#252526;
    --jqx-surface-color: #969690;
    --jqx-border: #414141;
    --jqx-background-hover-rgb: 60, 60, 60;
    --jqx-background-hover: rgb(var(--jqx-background-hover-rgb));
    --jqx-background-color-hover: rgba(255,255,255,.54);
    --jqx-scrollbar-background: #3E3E42;
    --jqx-scrollbar-border: #1E1E1E;
    --jqx-scrollbar-thumb-background: #686868;
    --jqx-scrollbar-thumb-border: #3E3E42;
    --jqx-scrollbar-thumb-background-hover: #9E9E9E;
    --jqx-scrollbar-thumb-border-hover: #9E9E9E;
    --jqx-scrollbar-thumb-background-pressed: #f5f5f5;
    --jqx-scrollbar-thumb-border-pressed: #f5f5f5;
    --jqx-scrollbar-button-color-hover: #333;
    --jqx-scrollbar-button-background-hover: #9E9E9E;
    --jqx-scrollbar-button-border-hover: #9E9E9E;
    --jqx-scrollbar-button-color-pressed: #333;
    --jqx-scrollbar-button-background-pressed: #f5f5f5;
    --jqx-scrollbar-button-border-pressed: #f5f5f5;

}

.jqx-icon-search-dark,
.jqx-icon-close-dark {
    background-image: none;
    font-family: jqx-icons;
}
.jqx-icon-close-dark:after {
    content: var(--jqx-icon-close);
}
.jqx-icon-search-dark:after {
    content: var(--jqx-icon-search);
    color:

}
.jqx-calendar-dark {
    width: 280px !important;
    height: 280px !important;
}
.jqx-fill-state-normal-dark {
    background: var(--jqx-background);
    color: var(--jqx-background-color);
    border-color: var(--jqx-border);
}
.jqx-fill-state-hover-dark {
    background: var(--jqx-background-hover);
    color: var(--jqx-background-color-hover);
    border-color: var(--jqx-background-hover);
}
.jqx-fill-state-pressed-dark {
    background: var(--jqx-primary);
    color: var(--jqx-primary-color);
    border-color: var(--jqx-primary);
}

@font-face {
    font-family: jqx-icons;
    src: local('./font/jqx-icons'), url('./font/jqx-icons.woff2') format('woff2'), url('./font/jqx-icons.woff') format('woff'), url('./font/jqx-icons.ttf') format('truetype'), url('./font/jqx-icons.eot') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}
/*Rounded Corners*/
/*top-left rounded Corners*/
.jqx-rc-tl-dark {
    border-top-left-radius: var(--jqx-border-radius);
}
/*top-right rounded Corners*/
.jqx-rc-tr-dark {
    border-top-right-radius: var(--jqx-border-radius);
}
/*bottom-left rounded Corners*/
.jqx-rc-bl-dark {
    border-bottom-left-radius: var(--jqx-border-radius);
}
/*bottom-right rounded Corners*/
.jqx-rc-br-dark {
    border-bottom-right-radius: var(--jqx-border-radius);
}
/*top rounded Corners*/
.jqx-rc-t-dark {
    border-top-left-radius: var(--jqx-border-radius);
    border-top-right-radius: var(--jqx-border-radius);
}
/*bottom rounded Corners*/
.jqx-rc-b-dark {
    border-bottom-left-radius: var(--jqx-border-radius);
    border-bottom-right-radius:var(--jqx-border-radius);
}
/*right rounded Corners*/
.jqx-rc-r-dark {
    border-top-right-radius: var(--jqx-border-radius);
    border-bottom-right-radius: var(--jqx-border-radius);
}
/*left rounded Corners*/
.jqx-rc-l-dark {
    border-top-left-radius: var(--jqx-border-radius);
    border-bottom-left-radius: var(--jqx-border-radius);
}
/*all rounded Corners*/
.jqx-rc-all-dark {
    border-radius: var(--jqx-border-radius);
}

.jqx-widget-dark, .jqx-widget-header-dark, .jqx-fill-state-normal-dark,
.jqx-widget-content-dark, .jqx-fill-state-hover-dark, .jqx-fill-state-pressed-dark {
    font-family: var(--jqx-font-family);
    font-size: var(--jqx-font-size);
}

.jqx-widget-dark {
    font-family: var(--jqx-font-family);
    font-size: var(--jqx-font-size);
    color: inherit;
    border-color:var(--jqx-border);
}

.jqx-widget-content-dark {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: var(--jqx-background-color);
    background-color: var(--jqx-background);
    border-color: var(--jqx-border);
}
.jqx-grid-table-dark.jqx-grid-table-one-cell {
    border-right-color: var(--jqx-border);
}
.jqx-widget-header-dark {
    background-color: var(--jqx-surface);
    border-color: var(--jqx-border);
    font-weight: 500;
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}

.jqx-calendar-title-header-dark {
    text-transform: uppercase;
}
.jqx-window-header-dark {
    padding: 10px;
    text-transform: uppercase;
    color: var(--jqx-surface-color);
    background: var(--jqx-surface);
}
.jqx-calendar tr {
    border-bottom-color: var(--jqx-border);
}


.jqx-widget-dark input::selection, input.jqx-input-widget-dark::selection, .jqx-widget-content-dark input::selection {
    background: var(--jqx-primary);
    color: var(--jqx-primary-color);
}
.jqx-toolbar-dark{
     border-color: var(--jqx-border);
}

.jqx-toolbar-dark {
    height: auto !important;
    display: flex;
    align-items: center;
}

.jqx-button-dark, .jqx-button-dark.jqx-fill-state-normal-dark {
    color: var(--jqx-background-color);
    background-color: var(--jqx-background);
    border-color: var(--jqx-border);
    text-transform: uppercase;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
    outline: none;
    transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 4px;
    box-shadow: 0 0 0 0 rgba(0,0,0,.2), 0 0 0 0 rgba(0,0,0,.14), 0 0 0 0 var(--jqx-border);
}
.jqx-button-dark.jqx-fill-state-hover-dark,
.jqx-button-dark.jqx-fill-state-pressed-dark {
    background-color: var(--jqx-background-hover);
    border-color: var(--jqx-border);
    color: var(--jqx-background-color-hover);
}

.jqx-button-dark.primary {
    color: var(--jqx-primary-color);
    background-color: var(--jqx-primary);
    border-color: var(--jqx-primary);
}
.jqx-button-dark.jqx-fill-state-hover-dark.primary,
.jqx-button-dark.jqx-fill-state-pressed-dark.primary  {
    color: var(--jqx-primary-color);
    background-color: var(--jqx-primary);
    border-color: var(--jqx-primary);
}
.jqx-button-dark.secondary,
.jqx-button-dark.jqx-toolbar-tool {
    background-color: var(--jqx-surface);
    border-color: var(--jqx-border);
    color: var(--jqx-surface-color) !important;
}
.jqx-button-dark.secondary:hover,
.jqx-button-dark.jqx-toolbar-tool:hover {
    background-color: var(--jqx-background-hover);
    border-color: var(--jqx-border);
    color: var(--jqx-background-color-hover) !important;
}
.jqx-button-dark.secondary:active,
.jqx-button-dark.jqx-toolbar-tool:active {
    background-color: var(--jqx-surface);
    border-color: var(--jqx-border);
    color: var(--jqx-surface-color) !important;
}
.jqx-scheduler-edit-dialog-field .jqx-button-dark {
    padding: 6px 16px;
    text-transform: uppercase;
}

.jqx-button-dark button, jqx-button-dark input {
    background: transparent;
    color: inherit;
    border:none;
    outline: none;
}
.jqx-group-button-normal-dark{
    box-shadow: none;
    background: var(--jqx-background);
    border-color: var(--jqx-border);
    color: var(--jqx-background-color) !important;
    border-radius:0px;
}
.jqx-group-button-normal.jqx-fill-state-hover {
  box-shadow: none !important;
 }
.jqx-group-button-normal.jqx-fill-state-pressed {
    box-shadow: none !important;
    background: var(--jqx-primary) !important;
    border-color: var(--jqx-primary) !important;
    color: var(--jqx-primary-color)!important;
    border-radius:0px;
}


.jqx-slider-button-dark {
    padding:3px;
    background: transparent;
    border:transparent;
}
    .jqx-button-dark.float {
        border-radius: 100%;
        min-height: 48px;
        min-width: 48px;
        width: 48px;
        height: 48px;
        max-height: 48px;
        max-width:48px;
    }

    .jqx-button-dark.outlined {
        background: transparent;
        color: var(--jqx-primary);
        border-width: 2px;
    }

    .jqx-button-dark.flat {
        background: transparent;
        color: var(--jqx-primary);
        border: none;
    }

.jqx-fill-state-hover-dark, .jqx-fill-state-focus-dark {
    text-decoration: none;
}

 .jqx-expander-header.jqx-fill-state-hover-dark,
 .jqx-expander-header.jqx-fill-state-normal-dark,
 .jqx-expander-header.jqx-fill-state-pressed-dark
 {
      background: var(--jqx-background-hover);
      border-color: var(--jqx-border);
      color:var(--jqx-background-color-hover);
}
.jqx-expander-header.jqx-fill-state-hover-dark {
    background: var(--jqx-background-hover);
}

.jqx-expander-header-dark {
    padding:10px;
}
.jqx-button-dark.jqx-fill-state-hover {
    opacity: 0.9;
    cursor: pointer;
    box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
    *zoom: 1;
}
.jqx-expander-content-dark{
	padding:0px;
}

    .jqx-button-dark.jqx-fill-state-hover.outlined,
    .jqx-button-dark.jqx-fill-state-hover.flat {
        color: var(--jqx-primary);
        box-shadow: none;
    }

.jqx-button-dark.jqx-fill-state-pressed {
    cursor: pointer;
    box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}

    .jqx-button-dark.jqx-fill-state-pressed.float {
        box-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);
    }

    .jqx-slider-button-dark.jqx-fill-state-pressed-dark,
    .jqx-button-dark.jqx-fill-state-pressed.outlined,
    .jqx-button-dark.jqx-fill-state-pressed.flat {
        background: rgba(179,229,252,0.15);
        box-shadow: none;
        color: var(--jqx-primary);
    }

.jqx-button-dark.jqx-fill-state-focus {
    box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}
  .jqx-slider-button-dark.jqx-fill-state-focus-dark {
      background: transparent;
      border-color: transparent;
      box-shadow:none;
  }

    .jqx-button-dark.jqx-fill-state-focus.outlined,
    .jqx-button-dark.jqx-fill-state-focus.flat {
        box-shadow: none;
        background: rgba(rgb(var(--jqx-primary-rgb)),0.15);
        color: var(--jqx-primary);
    }

    .jqx-dropdownlist-content-dark {
        display: flex;
        align-items: center;
        height: 100% !important;
        margin-top: 0px !important;
    }

    .jqx-dropdownlist-content-dark span {
        top: 0px !important;
    }
.jqx-dropdownlist-state-normal-dark, .jqx-dropdownlist-state-hover-dark, .jqx-dropdownlist-state-selected-dark,
.jqx-scrollbar-button-state-hover-dark, .jqx-scrollbar-button-state-normal-dark, .jqx-scrollbar-button-state-pressed-dark,
.jqx-scrollbar-thumb-state-normal-horizontal-dark, .jqx-scrollbar-thumb-state-hover-horizontal-dark, .jqx-scrollbar-thumb-state-pressed-horizontal-dark,
.jqx-scrollbar-thumb-state-normal-dark, .jqx-scrollbar-thumb-state-pressed-dark, .jqx-tree-item-hover-dark, .jqx-tree-item-selected-dark,
.jqx-tree-item-dark, .jqx-menu-item-dark, .jqx-menu-item-hover-dark, .jqx-menu-item-selected-dark, .jqx-menu-item-top-dark, .jqx-menu-item-top-hover-dark,
.jqx-menu-item-top-selected-dark, .jqx-slider-button-dark, .jqx-slider-slider-dark {
    -webkit-transition: background-color 100ms linear;
    -moz-transition: background-color 100ms linear;
    -o-transition: background-color 100ms linear;
    -ms-transition: background-color 100ms linear;
    transition: background-color 100ms linear;
}


.jqx-primary-dark.jqx-input-label-dark {
   color: var(--jqx-primary) !important;
}
.jqx-primary-dark.jqx-input-bar-dark:before {
   background: var(--jqx-primary) !important;
}
.jqx-success-dark.jqx-input-label-dark {
   color: #5cb85c !important;
}
.jqx-success-dark.jqx-input-bar-dark:before {
   background: #5cb85c !important;
}
.jqx-inverse-dark.jqx-input-label-dark {
   color: #666 !important;
}
.jqx-inverse-dark.jqx-input-bar-dark:before {
   background: #666 !important;
}
.jqx-danger-dark.jqx-input-label-dark {
   color: #d9534f !important;
}
.jqx-danger-dark.jqx-input-bar-dark:before {
   background: #d9534f !important;
}
.jqx-warning-dark.jqx-input-label-dark {
   color: #f0ad4e !important;
}
.jqx-warning-dark.jqx-input-bar-dark:before {
   background: #f0ad4e !important;
}
.jqx-info-dark.jqx-input-label-dark {
   color: #5bc0de !important;
}
.jqx-info-dark.jqx-input-bar-dark:before {
   background: #5bc0de !important;
}

.jqx-slider-tooltip-dark.jqx-primary-slider, .jqx-slider-tooltip-dark.jqx-primary-slider .jqx-fill-state-normal-dark {
    border-color: var(--jqx-primary);
    background: var(--jqx-primary);
}
.jqx-slider-tooltip-dark.jqx-success-slider, .jqx-slider-tooltip-dark.jqx-success-slider .jqx-fill-state-normal-dark {
    border-color: #5cb85c;
    background: #5cb85c;
}
.jqx-slider-tooltip-dark.jqx-inverse-slider, .jqx-slider-tooltip-dark.jqx-inverse-slider .jqx-fill-state-normal-dark {
    border-color: #666;
    background: #666;
}
.jqx-slider-tooltip-dark.jqx-danger-slider, .jqx-slider-tooltip-dark.jqx-danger-slider .jqx-fill-state-normal-dark {
    border-color: #d9534f;
    background: #d9534f;
}
.jqx-slider-tooltip-dark.jqx-warning-slider, .jqx-slider-tooltip-dark.jqx-warning-slider .jqx-fill-state-normal-dark {
    border-color: #f0ad4e;
    background: #f0ad4e;
}
.jqx-slider-tooltip-dark.jqx-info-slider, .jqx-slider-tooltip-dark.jqx-info-slider .jqx-fill-state-normal-dark {
    border-color: #5bc0de;
    background: #5bc0de;
}


.jqx-primary-dark {
    color: var(--jqx-primary) !important;
    background: #fff !important;
    border-color: var(--jqx-primary) !important;
    text-shadow: none !important;
}

    .jqx-primary-dark.jqx-dropdownlist-state-normal-dark,
    .jqx-primary-dark.jqx-slider-button-dark,
    .jqx-primary-dark.jqx-slider-slider-dark,
    .jqx-primary-dark.jqx-combobox-arrow-normal-dark,
    .jqx-primary-dark.jqx-combobox-arrow-hover-dark,
    .jqx-primary-dark.jqx-action-button-dark,
    .jqx-primary-dark:hover,
    .jqx-primary-dark:focus,
    .jqx-primary-dark:active,
    .jqx-primary-dark.active,
    .jqx-primary-dark.disabled,
    .jqx-primary-dark[disabled] {
        color: #fff !important;
        background: var(--jqx-primary) !important;
        border-color: var(--jqx-primary) !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-dark.jqx-primary-dark,
    .jqx-primary-dark:active,
    .jqx-primary-dark.active {
        color: #fff !important;
        background-color: var(--jqx-primary) !important;
        border-color: var(--jqx-primary) !important;
        text-shadow: none !important;
    }

.jqx-success-dark {
    color: #5cb85c !important;
    background: #fff !important;
    border-color: #5cb85c !important;
    text-shadow: none !important;
}

    .jqx-success-dark.jqx-dropdownlist-state-normal-dark,
    .jqx-success-dark.jqx-slider-button-dark,
    .jqx-success-dark.jqx-slider-slider-dark,
    .jqx-success-dark.jqx-combobox-arrow-normal-dark,
    .jqx-success-dark.jqx-combobox-arrow-hover-dark,
    .jqx-success-dark.jqx-action-button-dark,
    .jqx-success-dark:hover,
    .jqx-success-dark:focus,
    .jqx-success-dark:active,
    .jqx-success-dark.active,
    .jqx-success-dark.disabled,
    .jqx-success-dark[disabled] {
        color: #fff !important;
        background: #5cb85c !important;
        border-color: #5cb85c !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-dark.jqx-success-dark,
    .jqx-success-dark:active,
    .jqx-success-dark.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #5cb85c !important;
        border-color: #5cb85c !important;
    }

.jqx-inverse-dark {
    text-shadow: none !important;
    color: #666 !important;
    background: #fff !important;
    border-color: #cccccc !important;
}

    .jqx-inverse-dark.jqx-dropdownlist-state-normal-dark,
    .jqx-inverse-dark.jqx-slider-button-dark,
    .jqx-inverse-dark.jqx-slider-slider-dark,
    .jqx-inverse-dark.jqx-combobox-arrow-hover-dark,
    .jqx-inverse-dark.jqx-combobox-arrow-normal-dark,
    .jqx-inverse-dark.jqx-action-button-dark,
    .jqx-inverse-dark:hover,
    .jqx-inverse-dark:focus,
    .jqx-inverse-dark:active,
    .jqx-inverse-dark.active,
    .jqx-inverse-dark.disabled,
    .jqx-inverse-dark[disabled] {
        text-shadow: none !important;
        color: #666 !important;
        background: #cccccc !important;
        border-color: #cccccc !important;
    }

    .jqx-fill-state-pressed-dark.jqx-inverse-dark,
    .jqx-inverse-dark:active,
    .jqx-inverse-dark.active {
        text-shadow: none !important;
        color: #666 !important;
        background: #cccccc !important;
        border-color: #cccccc !important;
    }


.jqx-danger-dark {
    text-shadow: none !important;
    color: #d9534f !important;
    background: #fff !important;
    border-color: #d9534f !important;
}

    .jqx-danger-dark.jqx-dropdownlist-state-normal-dark,
    .jqx-danger-dark.jqx-slider-button-dark,
    .jqx-danger-dark.jqx-slider-slider-dark,
    .jqx-danger-dark.jqx-combobox-arrow-hover-dark,
    .jqx-danger-dark.jqx-combobox-arrow-normal-dark,
    .jqx-danger-dark.jqx-action-button-dark,
    .jqx-danger-dark:hover,
    .jqx-danger-dark:focus,
    .jqx-danger-dark:active,
    .jqx-danger-dark.active,
    .jqx-danger-dark.disabled,
    .jqx-danger-dark[disabled] {
        text-shadow: none !important;
        color: #fff !important;
        background: #d9534f !important;
        border-color: #d9534f !important;
    }

    .jqx-fill-state-pressed-dark.jqx-danger-dark,
    .jqx-danger-dark:active,
    .jqx-danger-dark.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #d9534f !important;
        border-color: #d9534f !important;
    }

.jqx-validator-error-label-dark {
    color: #d9534f !important;
}

.jqx-warning-dark {
    text-shadow: none !important;
    color: #f0ad4e !important;
    background: #fff !important;
    border-color: #f0ad4e !important;
}

    .jqx-warning-dark.jqx-dropdownlist-state-normal-dark,
    .jqx-warning-dark.jqx-slider-button-dark,
    .jqx-warning-dark.jqx-slider-slider-dark,
    .jqx-warning-dark.jqx-combobox-arrow-hover-dark,
    .jqx-warning-dark.jqx-combobox-arrow-normal-dark,
    .jqx-warning-dark.jqx-action-button-dark,
    .jqx-warning-dark:hover,
    .jqx-warning-dark:focus,
    .jqx-warning-dark:active,
    .jqx-warning-dark.active,
    .jqx-warning-dark.disabled,
    .jqx-warning-dark[disabled] {
        text-shadow: none !important;
        color: #fff !important;
        background: #f0ad4e !important;
        border-color: #f0ad4e !important;
    }

    .jqx-fill-state-pressed-dark.jqx-warning-dark,
    .jqx-warning-dark:active,
    .jqx-warning-dark.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #f0ad4e !important;
        border-color: #f0ad4e !important;
    }


.jqx-info-dark {
    text-shadow: none !important;
    color: #5bc0de !important;
    background: #fff !important;
    border-color: #5bc0de !important;
}

    .jqx-info-dark.jqx-dropdownlist-state-normal-dark,
    .jqx-info-dark.jqx-slider-button-dark,
    .jqx-info-dark.jqx-slider-slider-dark,
    .jqx-info-dark.jqx-combobox-arrow-hover-dark,
    .jqx-info-dark.jqx-combobox-arrow-normal-dark,
    .jqx-info-dark.jqx-action-button-dark,
    .jqx-info-dark:hover,
    .jqx-info-dark:focus,
    .jqx-info-dark:active,
    .jqx-info-dark.active,
    .jqx-info-dark.disabled,
    .jqx-info-dark[disabled] {
        color: #fff !important;
        background: #5bc0de !important;
        border-color: #5bc0de !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-dark.jqx-info-dark,
    .jqx-info-dark:active,
    .jqx-info-dark.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #5bc0de !important;
        border-color: #5bc0de !important;
    }

.jqx-fill-state-pressed-dark {
    background-image: none;
    outline: 0;
}

.jqx-grid-group-column-dark {
    border-color: transparent;
}
.jqx-grid-column-menubutton-dark {
    border-width: 0px;
}
.jqx-grid-groups-row-dark > span {
    padding-left: 4px;
}
.jqx-grid-column-filterbutton-dark,
.jqx-grid-column-menubutton-dark{
    background-image: none;
    font-family: 'jqx-icons';
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 0px;
}

.jqx-grid-column-filterbutton-dark:after {
    content: var(--jqx-icon-filter);
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}
.jqx-grid-column-menubutton-dark:after {
    content: var(--jqx-icon-menu) !important;
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}

.jqx-datatable-dark .jqx-widget-header-dark .jqx-grid-column-header-dark {
    border-right-color: var(--jqx-border);
}
.jqx-datatable-dark .jqx-widget-header-dark:not(:hover) .jqx-grid-column-header-dark {
    border-right-color: transparent !important;
}

.jqx-datatable-dark td.jqx-grid-cell-dark,
.jqx-treegrid-dark .jqx-grid-cell-dark{
    padding-top: 10px;
    padding-bottom: 9px;
    font-size: 14px;
    border-left: none !important;
}

.jqx-grid-cell-dark {
    background: var(--jqx-background);
    color: var(--jqx-background-color);
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.jqx-grid-cell-alt-dark {
	background: #333;
	color: var(--jqx-surface-color);
}

.jqx-grid-pager-top-dark .jqx-button-dark,
.jqx-grid-pager-dark .jqx-button-dark {
    color: inherit !important;
    background-color: transparent !important;
    border-color: transparent !important;
    position: relative;
    top: 0px;
    border-radius: 4px;
    display: flex;
    font-size: 16px;
    justify-content: center;
    align-content: center;
}

.jqx-grid-pager-input-dark  {
    padding:0px !important;
}

.jqx-grid-pager-top-dark .jqx-button-dark > div,
.jqx-grid-pager-dark .jqx-button-dark > div {
    top: 0px;
    position: relative;
    left: 0px;
    display: flex;
    align-items: center;
    margin-left: 0px !important;
}

.jqx-grid-pager-top-dark .jqx-button-dark.jqx-fill-state-hover,
.jqx-grid-pager-dark .jqx-button-dark.jqx-fill-state-hover
{
    color: var(--jqx-background-color-hover);
    background: var(--jqx-background-hover);
    border-color: var(--jqx-background-hover);
    box-shadow: none;
}
.jqx-grid-pager-top-dark .jqx-button-dark.jqx-fill-state-pressed,
.jqx-grid-pager-dark .jqx-button-dark.jqx-fill-state-pressed
{
    background: var(--jqx-primary);
}
.jqx-grid-pager-dark .jqx-button-dark:hover:after,
.jqx-grid-pager-top-dark .jqx-button-dark:hover:after,
.jqx-grid-pager-top-dark .jqx-button-dark.jqx-fill-state-pressed-dark:after,
.jqx-grid-pager-dark .jqx-button-dark.jqx-fill-state-pressed-dark:after {
    content: '';
    position: absolute;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    border: 2px solid var(--jqx-background);
    border-radius: 4px;
    left:0px;
    top:0px;
}
.jqx-grid-pager-top-dark .jqx-grid-pager-number-dark,
.jqx-grid-pager-dark .jqx-grid-pager-number-dark {
    background-color: transparent;
    border-color: transparent;
    color: inherit;
    font-size:14px;
    padding: 6px 10px;
    border-radius: 4px;

    position: relative;
}

.jqx-grid-pager-top-dark .jqx-grid-pager-number-dark:hover,
.jqx-grid-pager-dark .jqx-grid-pager-number-dark:hover {
    background: var(--jqx-background-hover);
    color:var(--jqx-background-color-hover) !important;
    font-size: var(--jqx-font-size);
}
.jqx-grid-pager-dark .jqx-grid-pager-number-dark:hover:after,
.jqx-grid-pager-top-dark .jqx-grid-pager-number-dark:hover:after,
.jqx-grid-pager-top-dark .jqx-grid-pager-number-dark.jqx-fill-state-pressed-dark:after,
.jqx-grid-pager-dark .jqx-grid-pager-number-dark.jqx-fill-state-pressed-dark:after {
    content: '';
    position: absolute;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    border: 2px solid var(--jqx-background);
    border-radius: var(--jqx-border-radius);
    left:0px;
    top:0px;
}
.jqx-grid-pager-top-dark .jqx-grid-pager-number-dark.jqx-fill-state-pressed-dark ,
.jqx-grid-pager-dark .jqx-grid-pager-number-dark.jqx-fill-state-pressed-dark {
    font-weight: bold !important;
    background: var(--jqx-primary);
    color:var(--jqx-background) !important;
}

.jqx-grid-column-menubutton-dark {
    background-color: transparent;
    border-color: var(--jqx-border) !important;
}

.jqx-cell-dark {
    font-size: 13px;
}

.jqx-calendar-dark > div {
    padding: 0px;
    box-sizing: border-box;
}
.jqx-calendar-month-dark {
    width: 90%;
    position: relative;
    left: 5%;
}
.jqx-calendar-title-navigation-dark {
    font-size: 20px;
    padding: 0px 20px;
}
.jqx-calendar-row-header-dark, .jqx-calendar-top-left-header-dark {
    background-color: var(--jqx-background);
    border: 0px solid var(--jqx-background);
}

.jqx-calendar-column-header-dark {
    background-color: var(--jqx-background);
    border-top: 1px solid var(--jqx-background);
    border-bottom: 1px solid var(--jqx-border);
    font-size: 12px;
    color: var(--jqx-background-color);
}

.jqx-expander-header-dark {
    padding-top: 10px;
    padding-bottom: 10px;
}

.jqx-ribbon-header-vertical-dark, .jqx-widget-header-vertical-dark {
    background: var(--jqx-background);
}

.jqx-scrollbar-state-normal-dark {
    background-color: var(--jqx-scrollbar-background);
    border: 1px solid var(--jqx-scrollbar-background);
    border-left-color: var(--jqx-scrollbar-border);
}

.jqx-scrollbar-thumb-state-normal-dark, .jqx-scrollbar-thumb-state-normal-horizontal-dark {
    background: var(--jqx-scrollbar-thumb-background);
    border-color: var(--jqx-scrollbar-thumb-border);
    border-radius: 0px;
}

.jqx-scrollbar-thumb-state-hover-dark, .jqx-scrollbar-thumb-state-hover-horizontal-dark {
    background: var(--jqx-scrollbar-thumb-background-hover);
    border-color: var(--jqx-scrollbar-thumb-border-hover);
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-progressbar-dark {
    background: var(--jqx-background) !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-progressbar-value-dark, .jqx-splitter-collapse-button-horizontal-dark {
    background: var(--jqx-primary);
}

.jqx-splitter-collapse-button-vertical-dark, .jqx-progressbar-value-vertical-dark {
    background: var(--jqx-primary);
}
.jqx-scrollbar-mobile-dark .jqx-scrollbar-button-state-normal {
    display: none !important;
}
.jqx-scrollbar-button-state-hover-dark {
    color: var(--jqx-scrollbar-button-color-hover);
    background: var(--jqx-scrollbar-button-background-hover);
    border-color: var(--jqx-scrollbar-button-border-hover);
}


.jqx-scrollbar-button-state-pressed-dark {
    color: var(--jqx-scrollbar-button-color-pressed);
    background: var(--jqx-scrollbar-button-background-pressed);
    border-color: var(--jqx-scrollbar-button-border-pressed);
}

.jqx-splitter-splitbar-vertical-dark,
.jqx-splitter-splitbar-horizontal-dark {
    background: var(--jqx-scrollbar-thumb-background);
    border-color: var(--jqx-scrollbar-thumb-border);
}

.jqx-scrollbar-thumb-state-pressed-dark,
.jqx-scrollbar-thumb-state-pressed-horizontal-dark,
.jqx-scrollbar-button-state-pressed-dark {
    background: var(--jqx-scrollbar-thumb-background-pressed);
    border-color: var(--jqx-scrollbar-thumb-border-pressed);
    box-shadow: none;
}

.jqx-grid-column-sortdescbutton-dark, jqx-grid-column-filterbutton-dark, .jqx-grid-column-sortascbutton-dark {
    background-color: transparent;
    border-style: solid;
    border-width: 0px 0px 0px 0px;
    border-color: var(--jqx-border);
}

.jqx-menu-vertical-dark {
    background: var(--jqx-background);
    filter: none;
}

.jqx-grid-bottomright-dark, .jqx-panel-bottomright-dark, .jqx-listbox-bottomright-dark {
    background-color: var(--jqx-scrollbar-background);
}

.jqx-window-dark, .jqx-tooltip-dark {
    box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
}
.jqx-tooltip-dark, .jqx-tooltip-dark.jqx-popup-dark, .jqx-tooltip-dark .jqx-fill-state-normal-dark {
    background: var(--jqx-primary);
    border-color: var(--jqx-primary);
    box-shadow:none;
    color: var(--jqx-primary-color);
}
.jqx-docking-dark .jqx-window-dark {
    box-shadow: none;
}

.jqx-docking-panel-dark .jqx-window-dark {
    box-shadow: none;
}

.jqx-checkbox-dark {
    line-height:20px;
    overflow: visible;
}
.jqx-radiobutton-dark {
    overflow: visible;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    background-repeat: no-repeat;
    background: none;
    line-height:20px;
}

.jqx-radiobutton-dark-dark, .jqx-radiobutton-hover-dark {
    border-radius: 100%;
    background-repeat: no-repeat;
    transition: background-color ease-in .3s;
}

.jqx-radiobutton-check-checked-dark {
    filter: none;
    background: var(--jqx-background);
    background-repeat: no-repeat;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-dark {
    filter: none;
    background: var(--jqx-background);
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-disabled-dark {
    filter: none;
    background: var(--jqx-background);
    opacity: 0.7;
    border-radius: 100%;
}

.jqx-checkbox-default-dark,
.jqx-radiobutton-default-dark
 {
    border-width: 1px;
    border-color: var(--jqx-border);
    background-color: var(--jqx-background);
    overflow: visible;
}

.jqx-tree-grid-expand-button-dark,
.jqx-tree-grid-collapse-button-dark {
    font-size: 16px;
}
.jqx-grid-table-dark .jqx-grid-cell:first-child {
    padding-left: 10px;
}
.jqx-tree-grid-title-dark {
    margin-left: 5px;
}
.jqx-tree-dark .jqx-checkbox-dark .jqx-checkbox-default-dark,
.jqx-checkbox-dark[checked] .jqx-checkbox-default-dark,
.jqx-tree-grid-checkbox[checked].jqx-checkbox-default-dark,
.jqx-radiobutton-dark[checked] .jqx-radiobutton-default-dark
 {
    color: var(--jqx-primary-color);
    background-color: var(--jqx-primary);
    border-color: var(--jqx-primary);
}
.jqx-menu-item-disabled-dark {
    color: inherit;
}

.jqx-grid-dark .jqx-checkbox-default-dark {
    border-radius: 0px;
}
.jqx-checkbox-check-checked-dark {
    background: none;
    font-family: jqx-icons;
    display: flex;
    justify-content: center;
}

.jqx-checkbox-check-checked-dark:after {
    content: var(--jqx-icon-check);
}
.jqx-checkbox-check-indeterminate-dark {
    width:14px !important;
    height:14px !important;
    position:relative;
    top: 1px;
    left: 1px;
    background: var(--jqx-background);
}
.jqx-tree-dark .jqx-checkbox-check-indeterminate-dark {
    width:12px !important;
    height:12px !important;
    top: 2px;
    left:2px;
}

.jqx-checkbox-hover-dark,
.jqx-radiobutton-hover-dark {
    background-color: var(--jqx-primary);
    border-color: var(--jqx-primary);
}


.jqx-slider-slider-dark {
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease;
}

.jqx-slider-slider-dark:active {
    transform: scale(1.2);
    box-shadow: rgba(0,0,0,0.3) 0 0 10px;
}
.jqx-slider-dark[discrete] .jqx-slider-slider-dark:active
 {
    transform: scaleX(0);

}
.jqx-slider-slider-horizontal-dark {
    background: var(--jqx-primary);
}
.jqx-slider-slider-vertical-dark {
    background: var(--jqx-primary);
}
.jqx-slider-tooltip-dark {
    width: 25px;
    height: 25px;
    transform-origin: 50% 100%;
    border-radius: 50%;
    transform: scale(0) rotate(45deg);
    padding:0px;
    background: transparent !important;
}
.jqx-slider-tooltip-dark.init {
     transform: scale(1) rotate(45deg);
}
.jqx-slider-tooltip-dark.hide {
     transition: transform 0.2s ease;
     transform-origin:50% 100%;
     transform: scale(0) rotate(45deg);
}
.jqx-slider-tooltip-dark.show {
     transition: transform 0.2s ease;
     transform: scale(1) rotate(45deg);
}


.jqx-slider-tooltip-dark .jqx-tooltip-arrow-t-b,
.jqx-slider-tooltip-dark .jqx-tooltip-arrow-l-r {
    display:none;
    visibility:hidden;
}

.jqx-slider-tooltip-dark, .jqx-slider-tooltip-dark .jqx-fill-state-normal-dark {
    border-radius: 15px 15px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--jqx-primary);
    color: var(--jqx-primary-color);
    font-size:11px;
}
.jqx-slider-tooltip-dark.far, .jqx-slider-tooltip-dark.far .jqx-fill-state-normal-dark {
   border-radius: 0px 15px 15px 15px;
}
.jqx-slider-tooltip-dark.vertical, .jqx-slider-tooltip-dark.vertical .jqx-fill-state-normal-dark {
   border-radius: 15px 0px 15px 15px;
}
.jqx-slider-tooltip-dark.vertical.far, .jqx-slider-tooltip-dark.vertical.far .jqx-fill-state-normal-dark {
   border-radius: 15px 15px 15px 0px;
}
.jqx-slider-tooltip-dark {
    background:transparent;
    border:none !important;
    box-shadow:none;
}
.jqx-slider-tooltip-dark .jqx-tooltip-main-dark {
    top: -7px;
    right: 11px;
}
.jqx-slider-tooltip-dark.far .jqx-tooltip-main-dark {
    top: 3px;
    right: 4px;
}
.jqx-slider-tooltip-dark.vertical .jqx-tooltip-main-dark {
    top: -3px;
    right: 3px;
}
.jqx-slider-tooltip-dark .jqx-tooltip-text {
    background: transparent;
    border:none;
    padding: 0px;
    overflow:visible;
}
.jqx-slider-tooltip-dark .jqx-tooltip-text>span {
     transform: rotate(-45deg);
}
.jqx-slider-tooltip-dark.range {
    width: 35px;
    height:35px;
}

.jqx-slider-rangebar-dark {
    border-color: var(--jqx-primary);
    background: var(--jqx-primary);
}

.jqx-slider-track-horizontal-dark, .jqx-slider-track-vertical-dark {
    border-color: var(--jqx-border);
    background: var(--jqx-background);
}

.jqx-slider-button-dark {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}
.jqx-slider-button-dark.jqx-fill-state-normal-dark,
.jqx-slider-button-dark.jqx-fill-state-hover-dark,
.jqx-slider-button-dark.jqx-fill-state-pressed-dark
{
    background: transparent !important;
}

.jqx-tree-item-dark,
.jqx-tree-item-selected {
    padding: 6px;
    border-radius: 4px;
}
.jqx-listitem-element-dark .jqx-checkbox-default-dark {
    border-radius: 0px;
}
.jqx-listitem-state-hover-dark,
.jqx-listitem-state-selected-dark,
.jqx-listitem-state-normal-dark {
    border-radius: 0;
    margin:0px;
}

.jqx-scheduler-edit-dialog-label-dark {
  font-size: 12px;
  text-transform: uppercase;
  padding-top: 6px;
  padding-bottom: 6px;

}
.jqx-scheduler-edit-dialog-field-dark {
  padding-top: 6px;
  padding-bottom: 6px;
}
.jqx-scheduler-edit-dialog-label-rtl-dark {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.jqx-scheduler-edit-dialog-field-rtl-dark {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.jqx-menu-horizontal-dark {
    height: auto !important;
}
.jqx-menu-horizontal-dark .jqx-menu-item-top-dark {
    padding: 8px;
}
.jqx-menu-item-top-dark,
.jqx-menu-item-dark {
    padding: 8px;
}
/*applied to a list item when the item is selected.*/
.jqx-listitem-state-hover-dark, .jqx-menu-item-hover-dark, .jqx-tree-item-hover-dark, .jqx-calendar-cell-hover-dark, .jqx-grid-cell-hover-dark,
.jqx-input-popup-dark .jqx-fill-state-hover-dark,
.jqx-input-popup-dark .jqx-fill-state-pressed-dark {
    color: var(--jqx-background-color-hover) !important;
    border-color: var(--jqx-background-hover);
    text-decoration: none;
    background-color: var(--jqx-background-hover);
    background-repeat: repeat-x;
    outline: 0;
    background: var(--jqx-background-hover);
    box-shadow: none;
    background-position: 0 0;
}

.jqx-scheduler-cell-hover-dark {
    border-color: var(--jqx-primary) !important;
    background: var(--jqx-primary) !important;
    color: var(--jqx-background) !important;
}

.jqx-listitem-state-selected-dark, .jqx-menu-item-selected-dark, .jqx-tree-item-selected-dark, .jqx-calendar-cell-selected-dark, .jqx-grid-cell-selected-dark,
.jqx-menu-item-top-selected-dark, .jqx-grid-selectionarea-dark, .jqx-input-button-header-dark, .jqx-input-button-innerHeader-dark {
    color: var(--jqx-primary-color) !important;
    border-color: var(--jqx-primary) !important;
    background: var(--jqx-primary) !important; /* Old browsers */
    box-shadow: none;
}

.jqx-grid-cell-dark .jqx-button-dark, .jqx-grid-cell-dark .jqx-button-dark.jqx-fill-state-hover-dark, .jqx-grid-cell-dark .jqx-button-dark.jqx-fill-state-pressed-dark {
    box-shadow: none;
    transition: none;
}

.jqx-menu-popup-dark{
    opacity: 0;
    transform-origin: top left;
    box-shadow: 0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 3px rgba(0,0,0,.12),0 4px 15px 0 rgba(0,0,0,.2);
    background: var(--jqx-background) !important;
}
.jqx-menu-popup-dark.top {
    transform: scaleY(0);
    transition: transform 0.2s ease-in-out, opacity 0.3s ease-in-out;
}

.jqx-menu-popup-dark.horizontal {
    transform: scaleX(0);
    transition: transform 0.2s ease-in-out, opacity 0.3s ease-in-out;
}

.jqx-menu-popup-dark.show {
    transform: scale(1);
    opacity: 1;
}
.jqx-popup-dark {
    border: 1px solid var(--jqx-border);
    background: var(--jqx-background);
    box-shadow: 0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 3px rgba(0,0,0,.12),0 4px 15px 0 rgba(0,0,0,.2);
}
.jqx-menu-popup-dark .jqx-popup-dark {
    box-shadow: none;
    border: none;
}

.jqx-datatable-dark .jqx-grid-column-sortdescbutton-dark,
.jqx-datatable-dark .jqx-grid-column-sortascbutton-dark {
	display: flex;
	align-items: center;
}

.jqx-grid-column-sortascbutton-dark,
.jqx-expander-arrow-bottom-dark,
.jqx-window-collapse-button-dark,
.jqx-menu-item-arrow-up-dark,
.jqx-menu-item-arrow-up-selected-dark,
.jqx-menu-item-arrow-top-up-dark,
.jqx-icon-arrow-up-dark,
.jqx-icon-arrow-up-hover-dark,
.jqx-icon-arrow-up-selected-dark {
    background-image: none;
}

.jqx-grid-column-sortascbutton-dark,
.jqx-expander-arrow-bottom-dark,
.jqx-window-collapse-button-dark,
.jqx-menu-item-arrow-up-dark,
.jqx-menu-item-arrow-up-selected-dark,
.jqx-menu-item-arrow-top-up-dark,
.jqx-icon-arrow-up-dark,
.jqx-icon-arrow-up-hover-dark,
.jqx-icon-arrow-up-selected-dark {
    background-image: none;
    font-family: jqx-icons;
}
.jqx-grid-column-sortascbutton-dark:after,
.jqx-expander-arrow-bottom-dark:after,
.jqx-window-collapse-button-dark:after,
.jqx-menu-item-arrow-up-dark:after,
.jqx-menu-item-arrow-up-selected-dark:after,
.jqx-menu-item-arrow-top-up-dark:after,
.jqx-icon-arrow-up-dark:after,
.jqx-icon-arrow-up-hover-dark:after,
.jqx-icon-arrow-up-selected-dark:after {
    content: var(--jqx-icon-arrow-up);
}

.jqx-widget-dark .jqx-grid-group-expand-dark,
.jqx-grid-group-expand-dark,
.jqx-grid-column-sortdescbutton-dark,
.jqx-expander-arrow-top-dark,
.jqx-window-collapse-button-collapsed-dark,
.jqx-menu-item-arrow-down-dark,
.jqx-menu-item-arrow-down-selected-dark,
.jqx-menu-item-arrow-down-dark,
.jqx-icon-arrow-down-dark,
.jqx-icon-arrow-down-hover-dark,
.jqx-icon-arrow-down-selected-dark {
    background-image: none;
    font-family: jqx-icons;
}
.jqx-widget-dark .jqx-grid-group-expand-dark:after,
.jqx-grid-group-expand-dark:after,
.jqx-grid-column-sortdescbutton-dark:after,
.jqx-expander-arrow-top-dark:after,
.jqx-window-collapse-button-collapsed-dark:after,
.jqx-menu-item-arrow-down-dark:after,
.jqx-menu-item-arrow-down-selected-dark:after,
.jqx-menu-item-arrow-down-dark:after,
.jqx-icon-arrow-down-dark:after,
.jqx-icon-arrow-down-hover-dark:after,
.jqx-icon-arrow-down-selected-dark:after {
    content: var(--jqx-icon-arrow-down);
}

.jqx-tabs-arrow-left-dark,
.jqx-menu-item-arrow-left-selected-dark,
.jqx-menu-item-arrow-top-left,
.jqx-icon-arrow-left-dark,
.jqx-icon-arrow-down-left-dark,
.jqx-icon-arrow-left-selected-dark {
    background-image: none;
    font-family: jqx-icons;
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tabs-arrow-left-dark:after,
.jqx-menu-item-arrow-left-selected-dark:after,
.jqx-menu-item-arrow-top-left:after,
.jqx-icon-arrow-left-dark:after,
.jqx-icon-arrow-down-left-dark:after,
.jqx-icon-arrow-left-selected-dark:after {
    content: var(--jqx-icon-arrow-left);
}

.jqx-widget-dark .jqx-grid-group-collapse-dark,
.jqx-grid-group-collapse-dark,
.jqx-tabs-arrow-right-dark,
.jqx-menu-item-arrow-right-selected-dark,
.jqx-menu-item-arrow-top-right-dark,
.jqx-icon-arrow-right-dark,
.jqx-icon-arrow-right-hover-dark,
.jqx-icon-arrow-right-selected-dark {
    background-image: none;
    font-family: jqx-icons;
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-widget-dark .jqx-grid-group-collapse-dark:after,
.jqx-grid-group-collapse-dark:after,
.jqx-tabs-arrow-right-dark:after,
.jqx-menu-item-arrow-right-selected-dark:after,
.jqx-menu-item-arrow-top-right-dark:after,
.jqx-icon-arrow-right-dark:after,
.jqx-icon-arrow-right-hover-dark:after,
.jqx-icon-arrow-right-selected-dark:after {
    content: var(--jqx-icon-arrow-right);
}

.jqx-tree-item-arrow-collapse-rtl-dark,
.jqx-tree-item-arrow-collapse-hover-rtl-dark {
    background-image: none;
}

.jqx-tree-item-arrow-collapse-rtl-dark:after,
.jqx-tree-item-arrow-collapse-hover-rtl-dark:after {
    content: var(--jqx-icon-arrow-left);
}

.jqx-menu-item-arrow-left-selected-dark {
    background-image: none;
}

.jqx-menu-item-arrow-right-selected-dark {
    background-image: none;
}

.jqx-input-button-content-dark {
    font-size: 10px;
}

.jqx-widget .jqx-grid-column-header-cell-dark {
    padding-top: 8px;
    padding-bottom: 8px;
    height:30px;
}

.jqx-widget .jqx-grid-row-cell-dark {
    padding-top: 8px;
    padding-bottom: 8px;
    height:30px;
}

.jqx-listbox-container-dark,
.jqx-calendar-container-dark {
    margin-left: -10px;
}
.jqx-calendar-container-dark .jqx-popup,
.jqx-calendar-dark.jqx-popup,
.jqx-listbox-dark.jqx-popup {
    margin-left: 9px;
}

.jqx-dropdownbutton-popup,
.jqx-calendar-dark.jqx-popup,
.jqx-listbox-dark.jqx-popup,
.jqx-grid-menu.jqx-popup  {
    transition: transform 0.25s ease-in-out, opacity 0.35s ease-in-out;
    transform: scaleY(0);
    opacity: 0;
    transform-origin: top left;
    display: block !important;
}

.jqx-dropdownbutton-popup.jqx-popup-show,
.jqx-calendar-dark.jqx-popup-show,
.jqx-listbox-dark.jqx-popup-show,
.jqx-grid-menu.jqx-popup-show {
    transform: scaleY(1);
    opacity: 1;
}

.jqx-widget-dark .jqx-grid-cell {
    border-color: var(--jqx-border);
    color: var(--jqx-background-color);
}

.jqx-widget-dark .jqx-grid-column-header, .jqx-widget-dark .jqx-grid-group-cell {
    border-color: var(--jqx-border);
    color: var(--jqx-surface-color);
    background: var(--jqx-surface);
}

.jqx-widget-dark .jqx-grid-column-header-dark {
    border-color: var(--jqx-border);
    font-size: 12px;
    color: var(--jqx-surface-color);
    font-weight: 500;
}
.jqx-widget-dark .jqx-grid-cell-dark {
    border-color: var(--jqx-border);
}

.jqx-widget-dark .jqx-widget-header-dark:hover .jqx-grid-column-header-dark {
    border-right-color: var(--jqx-border) !important;
    border-bottom-color: var(--jqx-border) !important;
}

.jqx-widgets-dark .jqx-scheduler-cell-selected span{
    color: var(--jqx-background) !important;
}
.jqx-scheduler-time-column-dark,
.jqx-scheduler-toolbar-dark {
    background: var(--jqx-surface) !important;
    color: var(--jqx-surface-color) !important;
    border-color: var(--jqx-border) !important;
}

.jqx-widget-dark.jqx-scheduler-dark .jqx-grid-cell-dark,
.jqx-widget-dark.jqx-scheduler-dark .jqx-grid-column-header-dark {
    border-color: transparent;
    border-bottom-color: var(--jqx-border);
}

.jqx-widget-dark.jqx-scheduler-dark td.jqx-grid-cell-dark span{
    font-size: 10px;
    color: var(--jqx-background-color);
}
.jqx-widget-dark.jqx-scheduler-dark td.jqx-grid-cell-dark.jqx-scheduler-cell-hover span,
.jqx-widget-dark.jqx-scheduler-dark td.jqx-grid-cell-dark.jqx-scheduler-cell-selected span{
    color:var(--jqx-primary-color);
}

.jqx-passwordinput-password-icon-dark,
.jqx-passwordinput-password-icon-rtl-dark {
    background-image: none;
    font-family: jqx-icons;
    color:var(--jqx-background-color);
}
.jqx-passwordinput-password-icon-dark:after,
.jqx-passwordinput-password-icon-rtl-dark:after {
    content: var(--jqx-icon-visibility);
}

.jqx-combobox-dark .jqx-icon-close-dark {
    background-image: none;
    font-family: jqx-icons;
}
.jqx-combobox-multi-item-dark {
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.jqx-combobox-multi-item-dark a {
    margin-right: 10px;
}
.jqx-combobox-dark .jqx-icon-close-dark:after {
    content: var(--jqx-icon-close);
}
.jqx-combobox-dark, .jqx-input-dark {
    border-color: var(--jqx-border);
    color: var(--jqx-background-color);
    background-color: var(--jqx-background);
}

.jqx-combobox-content-dark,
.jqx-datetimeinput-content-dark
 {
    border-color: transparent;
}


.jqx-combobox-content-focus-dark,
.jqx-combobox-state-focus-dark,
.jqx-numberinput-focus-dark {
    outline: none;
}

.jqx-input-group-dark {
    position: relative;
    display: inline-block;
    overflow: visible;
    border: none;
    box-shadow: none;
}

    .jqx-input-group-dark input {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
    }
    .jqx-input-group-dark textarea {
        width: 100%;
        height: 100%;
        outline: none;
        resize: none;
        border-left: none;
        border-right: none;
        border-top: none;
        border-bottom-color: var(--jqx-border);
    }
.jqx-numberinput-dark,
.jqx-maskedinput-dark
 {
    position:relative;
}
.jqx-numberinput-dark input {
    height:100% !important;
}

.jqx-input-dark.jqx-validator-error-element {
    border-color: transparent !important;
    border-bottom: 1px solid #df2227 !important;
}
.jqx-input-dark input,
.jqx-dropdownlist-state-normal-dark,
.jqx-combobox-state-normal-dark,
.jqx-numberinput-dark,
.jqx-maskedinput-dark,
.jqx-datetimeinput-dark
 {
    background: var(--jqx-surface);
    border-color: var(--jqx-surface);
    border-radius: 0;
    color: var(--jqx-surface-color);
    box-shadow: none;
    border-bottom: 1px solid var(--jqx-border);
    outline: none;
}
.jqx-numberinput-dark .jqx-action-button-dark{
    border-radius: 0;
    font-size:12px;
}
.jqx-numberinput-dark .jqx-action-button-dark > div {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.jqx-maskedinput-dark,
.jqx-combobox-dark,
.jqx-datetimeinput-dark {
    background: var(--jqx-background);
    color: var(--jqx-background-color);
    border-color: var(--jqx-background);
    border-bottom: 1px solid var(--jqx-border);
}
.jqx-combobox-dark .jqx-combobox-arrow-normal-dark,
.jqx-datetimeinput-dark .jqx-action-button-dark,
.jqx-datetimeinput-dark .jqx-action-button-rtl-dark
 {
    background-color: var(--jqx-surface);
    border-color: var(--jqx-surface);
    color:var(--jqx-surface-color);
}
    .jqx-datetimeinput-dark, .jqx-datetimeinput-dark > div,
    .jqx-numberinput-dark, .jqx-numberinput-dark > div,
    .jqx-maskedinput-dark, .jqx-maskedinput-dark > div,
    .jqx-dropdownlist-state-normal-dark, .jqx-dropdownlist-state-normal-dark > div, .jqx-dropdownlist-state-normal-dark > div > div,
    .jqx-combobox-state-normal-dark, .jqx-combobox-state-normal-dark > div, .jqx-combobox-state-normal-dark > div > div {
        overflow: visible !important;
    }

    .jqx-input-dark input:focus {
        border-radius: 0;
        box-shadow: none;
    }

.jqx-input-dark input, input[type="text"].jqx-input-dark, input[type="password"].jqx-input-dark, input[type="text"].jqx-widget-content-dark, input[type="textarea"].jqx-widget-content-dark, textarea.jqx-input-dark {
    font-size: var(--jqx-font-size);
    font-family: var(--jqx-font-family);
    resize: none;
    background: var(--jqx-background);
    color: var(--jqx-background-color);
    border: none;
    border-radius: 0;
    box-sizing:border-box;
    box-shadow: none;
    border-bottom: 1px solid var(--jqx-border);
}

input[type="text"].jqx-widget-content-dark,
input[type="textarea"].jqx-widget-content-dark {
    height: 100%;
}


.jqx-input-label {
    visibility:inherit;
}
.jqx-input-bar{
    visibility:inherit;
}
input:focus ~ .jqx-input-label-dark,
textarea:focus ~ .jqx-input-label-dark,
.jqx-input-widget-dark[hint=true] .jqx-input-label,
.jqx-text-area-dark[hint=true] .jqx-input-label,
.jqx-dropdownlist-state-selected-dark .jqx-input-label,
.jqx-dropdownlist-state-normal-dark[hint=true] .jqx-input-label,
.jqx-combobox-state-normal-dark[hint=true] .jqx-input-label,
.jqx-combobox-dark .jqx-input-label.focused,
.jqx-dropdownlist-dark .jqx-input-label.focused,
.jqx-datetimeinput-dark[hint=true] .jqx-input-label,
.jqx-maskedinput-dark[hint=true] .jqx-input-label,
.jqx-numberinput-dark[hint=true] .jqx-input-label,
.jqx-formattedinput-dark[hint=true] .jqx-input-label
 {
    top: -15px;
    font-size: 12px;
    color: var(--jqx-primary);
    opacity: 1;
}
.jqx-dropdownlist-dark[default-placeholder="true"] .jqx-input-label {
    visibility: hidden;
}


input:focus ~ .jqx-input-bar:before,
textarea:focus ~ .jqx-input-bar:before,
.jqx-dropdownlist-state-selected-dark .jqx-input-bar:before,
.jqx-dropdownlist-dark .jqx-input-bar.focused:before,
.jqx-combobox-dark .jqx-input-bar.focused:before,
.jqx-complex-input-group-dark .jqx-input-bar.focused::before,
.jqx-combobox-state-selected-dark .jqx-input-bar:before {
    width: 100%;
}
.jqx-complex-input-group-dark .jqx-fill-state-normal-dark {
    border-color: var(--jqx-border);
}


.jqx-input-widget-dark.jqx-rtl > input {
    direction: rtl
}

.jqx-input-label-dark {
    color: var(--jqx-background-color);
    font-size: 14px;
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    left: 2px;
    top:10px;
    opacity: 0;
    top: calc(50% - 7px);
    transition: 300ms ease all;
}
.jqx-input-label.initial {
    transition: none;
}
.jqx-input-bar-dark {
    position: relative;
    display: block;
    z-index:1;
}

    .jqx-input-bar-dark:before {
        content: '';
        height: 2px;
        width: 0;
        bottom: 0px;
        position: absolute;
        background: var(--jqx-primary);
        transition: 300ms ease all;
        left: 0%;
    }
.jqx-formatted-input-spin-button-dark, .jqx-input-group-addon-dark {
    border-color: var(--jqx-background);
    color: var(--jqx-background-color);
    background: var(--jqx-background);
}
.jqx-dropdownlist-state-selected-dark,
.jqx-combobox-state-selected-dark {
    color: var(--jqx-primary);
    background: var(--jqx-primary-color);
    border-color: var(--jqx-primary-color);
}


.jqx-window-collapse-button-dark {
    position: relative;
    right: 10px;
    font-weight: bold;
    font-size: 18px;
    margin-top:0px;
}

.jqx-dropdownlist-state-normal-dark .jqx-icon-arrow-down-dark,
.jqx-combobox-state-normal-dark .jqx-icon-arrow-down-dark,
.sorticon.descending .jqx-grid-column-sorticon-dark,
.jqx-tree-item-arrow-expand-dark,
 .jqx-expander-header-dark .jqx-icon-arrow-down
 {
    transform: rotate(0deg);
    display: flex;
    align-items: center;
    transition: transform 0.2s ease-out;
}
.jqx-expander-header-dark .jqx-icon-arrow-up {
   transform: rotate(180deg);
   transition: transform 0.2s ease-out;
   font-family: jqx-icons;
   background-image: none;
}
.jqx-expander-header-dark .jqx-icon-arrow-up:after {
    content: var(--jqx-icon-arrow-down);
}
.jqx-tree-item-arrow-expand-dark,
.jqx-tree-item-arrow-collapse-dark
 {
    font-size: 16px;
}
.jqx-tree-item-arrow-expand-dark {
    transform: rotate(180deg);
}

.jqx-tree-item-arrow-expand-dark:after {
    content: var(--jqx-icon-arrow-up);
    margin-left: 2px;
}
.jqx-tree-item-arrow-collapse-dark
{
    transform: rotate(0deg);
    background-image: none;
    background-repeat: no-repeat;
    background-position: center;
    transition: transform 0.2s ease-out;
}
.jqx-dropdownlist-state-selected-dark .jqx-icon-arrow-down-dark,
.jqx-combobox-state-selected-dark .jqx-icon-arrow-down-dark,
.sorticon.ascending .jqx-grid-column-sorticon-dark
 {
    display: flex;
    align-items: center;
    transform: rotate(180deg);
    transition: transform 0.2s ease-out;
    left: -1px;
}
.jqx-combobox-state-selected-dark .jqx-icon-arrow-down-dark{
    left:-1px;
}
.jqx-listbox-container {
    margin-top: 1px;
}

input[type="text"].jqx-input-dark:-moz-placeholder, input[type="text"].jqx-widget-content-dark:-moz-placeholder, input[type="textarea"].jqx-widget-content-dark:-moz-placeholder, textarea.jqx-input-dark:-moz-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-dark:-webkit-input-placeholder, input[type="text"].jqx-widget-content-dark:-webkit-input-placeholder, input[type="textarea"].jqx-widget-content-dark:-webkit-input-placeholder, textarea.jqx-input-dark:-webkit-input-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-dark:-ms-input-placeholder, input[type="text"].jqx-widget-content-dark:-ms-input-placeholder, input[type="textarea"].jqx-widget-content-dark:-ms-input-placeholder, textarea.jqx-input-dark:-ms-input-placeholder {
    color: #999999;
}

.jqx-combobox-content-focus-dark, .jqx-combobox-state-focus-dark, .jqx-fill-state-focus-dark,
.jqx-numberinput-focus-dark {
    outline: none;
}

.jqx-popup-dark.jqx-fill-state-focus-dark {
    outline: none;
    border-color: var(--jqx-border) !important;
}

.jqx-datetimeinput-content, .jqx-datetimeinput-container {
    overflow: visible !important;
}
.jqx-text-area-dark, .jqx-text-area-dark > div {
    overflow:visible !important;
}
.jqx-text-area-element-dark {
   box-sizing: border-box;
}
.jqx-pivotgrid-content-wrapper.jqx-fill-state-normal-dark {
    border-color: var(--jqx-border);
}

.jqx-grid-cell-dark.jqx-grid-cell-selected-dark > .jqx-grid-group-expand-dark {
    background-image: none;
}

.jqx-grid-cell-dark.jqx-grid-cell-selected-dark > .jqx-grid-group-collapse-dark {
    background-image: none;
}

.jqx-grid-cell-dark.jqx-grid-cell-selected-dark > .jqx-grid-group-collapse-rtl-dark {
    background-image: none;
}

.jqx-grid-cell-dark.jqx-grid-cell-selected-dark > .jqx-grid-group-expand-rtl-dark {
    background-image: none;
}
.jqx-tabs-title-selected-top-dark, .jqx-tabs-selection-tracker-top-dark {
    border-color: transparent;
    filter: none;
    background: inherit;
    color: inherit;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.jqx-grid-cell-filter-row-dark {
    background-color: var(--jqx-surface);
}

.jqx-tabs-title-dark, .jqx-ribbon-item-dark {
    color: inherit;
}
.jqx-ribbon-item-selected-dark {
    background: inherit;
}
.jqx-tabs-title-selected-bottom-dark,
.jqx-tabs-title-selected-top-dark
 {
    color: var(--jqx-primary);
    font-weight:500;
    padding-top:5px;
    padding-bottom:5px;
}
.jqx-tabs-title.jqx-fill-state-hover-dark {
    border-color: transparent;
}
.jqx-ribbon-item-dark {
    cursor: pointer;
}
.jqx-ribbon-item-selected-dark {
    color: var(--jqx-primary);
    font-weight:500;
    border-color: transparent;
}

.jqx-ribbon-item-hover-dark {
    background: transparent;
}

.jqx-ribbon-header-top-dark {
    border-color: transparent;
    border-bottom-color: var(--jqx-border);
}

.jqx-ribbon-header-bottom-dark {
    border-color: transparent;
    border-top-color: var(--jqx-border);
}

.jqx-ribbon-header-right-dark {
    border-color: transparent;
    border-left-color:var(--jqx-border);
}

.jqx-ribbon-header-left-dark {
    border-color: transparent;
    border-right-color:var(--jqx-border);
}

.jqx-tabs-title-selected-bottom-dark, .jqx-tabs-selection-tracker-bottom-dark {
    border-color: transparent;
    border-top: 1px solid var(--jqx-background);
    filter: none;
    background: var(--jqx-background);
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-tabs-dark, .jqx-ribbon-dark {
    border-color: transparent;
}

.jqx-tabs-header-dark {
    background: transparent;
}
.jqx-ribbon-header-dark {
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}
.jqx-tabs-position-bottom .jqx-tabs-header-dark {
    border-color: transparent;
}
.jqx-layout-dark .jqx-tabs-header-dark, .jqx-layout-dark .jqx-ribbon-header-dark {
    background: var(--jqx-background);
    border-color: var(--jqx-border);
}
.jqx-tabs-title-bottom {
    border-color: transparent;
}
.jqx-tabs-title-hover-top-dark, .jqx-tabs-title-hover-bottom-dark, .jqx-tabs-header-dark {
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}

.jqx-tabs-content-dark {
    box-sizing: border-box;
    border: 1px solid var(--jqx-border);
    border-top-color: transparent;
    padding:5px;
}
.jqx-tabs-bar-dark {
    position: absolute;
    bottom: 0;
    background: var(--jqx-primary);
    height: 2px;
    z-index:10;
    transition: .5s cubic-bezier(.35,0,.25,1);
}
.jqx-tabs-bar-dark.vertical {
    width: 2px;
}
.jqx-tabs-position-bottom .jqx-tabs-bar-dark {
    top: 0;
}


.jqx-layout-dark {
    background-color: var(--jqx-background);
}

.jqx-kanban-column-header-collapsed-dark {
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}

.jqx-calendar-cell-dark {
    border-radius: 0px;
    font-size:12px;
}
.jqx-calendar-cell-dark.jqx-fill-state-pressed-dark {
    outline: 2px var(--jqx-primary);
    overflow: hidden;
    position: relative;
}
.jqx-calendar-cell-dark.jqx-fill-state-pressed-dark:after {
    content: '';
    width:  calc(100% - 4px);
    position: absolute;
    left: 0px;
    top: 0px;
    height: calc(100% - 4px);
    border: 2px solid var(--jqx-background);
}
.jqx-calendar-cell-year-dark,
.jqx-calendar-cell-decade-dark {
    border-radius: 0x;
}
.jqx-calendar-title-content-dark {
    font-weight:bold;
}
.jqx-calendar-column-cell-dark {
    color: var(--jqx-background-color);
    font-size:12px;
}

.jqx-icon-time-dark,
.jqx-icon-time-hover-dark,
.jqx-icon-time-pressed-dark {
    background-image: none !important;
    font-family: 'jqx-icons';
    display: flex;
    font-family: 'jqx-icons';
    font-size: 16px;
    align-content: center;
    justify-content: center;
    left: initial !important;
    margin-top: 0px;
    top: 0px;
    left: 0px;
    margin: 0;
    align-items: center;
    width: 100%;
    height: 100%;
}

.jqx-icon-time-dark:after,
.jqx-icon-time-hover-dark:after,
.jqx-icon-time-pressed-dark:after {
    content: var(--jqx-icon-arrow-down);
}

.jqx-icon-calendar-dark,
.jqx-icon-calendar-hover-dark,
.jqx-icon-calendar-pressed-dark {
	background-image: none !important;
    font-family: 'jqx-icons';
    left: 0;
    top: 0 !important;
    margin: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100% !important;
    height: 100% !important;
}
.jqx-icon-calendar-dark:after,
.jqx-icon-calendar-hover-dark:after,
.jqx-icon-calendar-pressed-dark:after {
    content: var(--jqx-icon-calendar);
}

.jqx-tabs-close-button-dark,
.jqx-tabs-close-button-selected-dark,
.jqx-tabs-close-button-hover-dark {
    background-image: none;
}

.jqx-tabs-close-button-dark:after {
    content: var(--jqx-icon-close);
}

.jqx-scrollbar-button-state-pressed-dark .jqx-icon-arrow-up-selected-dark {
    background-image: none;
}

.jqx-scrollbar-button-state-pressed-dark .jqx-icon-arrow-down-selected-dark {
    background-image: none;
}

.jqx-scrollbar-button-state-pressed-dark .jqx-icon-arrow-left-selected-dark {
    background-image: none;
}

.jqx-scrollbar-button-state-pressed-dark .jqx-icon-arrow-right-selected-dark {
    background-image: none;
}

.jqx-grid-cell-dark.jqx-grid-cell-selected-dark > .jqx-grid-group-expand-dark {
    background-image: none;
}

.jqx-grid-cell-dark.jqx-grid-cell-selected-dark > .jqx-grid-group-collapse-dark {
    background-image: none;
}

.jqx-grid-cell-dark.jqx-grid-cell-selected-dark > .jqx-grid-group-collapse-rtl-dark {
    background-image: none;
}

.jqx-grid-cell-dark.jqx-grid-cell-selected-dark > .jqx-grid-group-expand-rtl-dark {
    background-image: none;
}

.jqx-grid-group-collapse-dark {
    background-image: none;
}

.jqx-grid-group-collapse-rtl-dark {
    background-image: none;
}

.jqx-grid-group-expand-dark, .jqx-grid-group-expand-rtl-dark {
    background-image: none;
}

.jqx-icon-arrow-first-dark,
.jqx-icon-arrow-last-dark  {
    background-image: none;
    font-family: jqx-icons;
}
.jqx-icon-arrow-first-dark:after {
    content: var(--jqx-icon-first-page);
}
.jqx-icon-arrow-last-dark:after {
    content: var(--jqx-icon-last-page);
}

/* Ripple effect */
.ripple {
    position: relative;
    transform: translate3d(0, 0, 0);
    overflow:hidden;
}

.ink {
    display: block;
    position: absolute;
    pointer-events: none;
    border-radius: 0%;
    transform: scaleX(0);
    background: rgba(var(--jqx-primary-rgb),0.5);
    opacity: 0.25;
}


.jqx-scrollbar-dark .jqx-icon-arrow-up,
.jqx-scrollbar-dark .jqx-icon-arrow-down,
.jqx-scrollbar-dark .jqx-icon-arrow-right,
.jqx-scrollbar-dark .jqx-icon-arrow-left {
    display: flex;
    justify-content: center;
    align-items: center;
}

.outlined .ink, .flat .ink {
    background: rgba(var(--jqx-primary-rgb),0.5);
    overflow:hidden;
}

.ink.animate {
    animation: ripple .7s ease;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.jqx-tree-dark .jqx-checkbox-dark {
    margin-top: 6px !important;
    border-radius: 0px !important;
}
.jqx-tree-item-arrow-expand-dark,
.jqx-tree-item-arrow-collapse-dark {
    margin-top: 6px !important;
}

.jqx-checkbox-dark .ripple,
.jqx-radiobutton-dark .ripple
 {
    overflow:visible;
}
.jqx-checkbox-dark .ink,
.jqx-radiobutton-dark .ink
 {
    transform: scale(0);
    background: var(--jqx-primary);
    border-radius: 50%;
}
.jqx-checkbox-dark .ink.animate,
.jqx-radiobutton-dark .ink.animate
 {
    animation: checkRipple 0.3s ease;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.jqx-checkbox-dark .ink.active,
.jqx-radiobutton-dark .ink.active
 {
    opacity: 0.2;
    transform: scale(2);
}
.jqx-checkbox-default-dark.active .ink,
.jqx-radiobutton-default-dark.active .ink
 {
    opacity: 0.2;
    transform: scale(2);
}

/* Ripple effect */
/* Ripple effect */
.buttonRipple {
    background-position: center;
    transition: background 0.8s;
  }
  .buttonRipple.jqx-button-dark.jqx-fill-state-hover {
    color: var(--jqx-background-color-hover);
    background: var(--jqx-background-hover) radial-gradient(circle, transparent 1%, var(--jqx-background-hover) 1%) center/15000%;
  }
  .buttonRipple.jqx-button-dark.jqx-fill-state-pressed {
    color: var(--jqx-background-color-hover);
    background-color: rgba(var(--jqx-background-hover-rgb), 0.5);
    background-size: 100%;
    transition: background 0s;
  }
  .buttonRipple.jqx-button-dark.jqx-fill-state-hover.primary {
    color: var(--jqx-primary-color);
    background: var(--jqx-primary) radial-gradient(circle, transparent 1%, var(--jqx-primary) 1%) center/15000%;
  }
  .buttonRipple.jqx-button-dark.jqx-fill-state-pressed.primary {
    color: var(--jqx-primary-color);
    background-color: rgba(var(--jqx-primary-rgb), 0.8);
    background-size: 100%;
    transition: background 0s;
  }

@keyframes ripple {
    100% {
        opacity: 0;
        transform: scale(5);
        animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
}
@keyframes checkRipple {
    100% {
        opacity: 0.2;
        transform: scale(2);
        animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
}

.jqx-fill-state-pressed-dark .jqx-icon-delete-dark
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-fill-state-pressed-dark .jqx-icon-edit-dark
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-fill-state-pressed-dark .jqx-icon-save-dark
{
    background-image: url('images/icon-save-white.png');
}
.jqx-fill-state-pressed-dark .jqx-icon-cancel-dark
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-fill-state-pressed-dark .jqx-icon-search-dark
{
    background-image: url('images/search_white.png');
}
.jqx-fill-state-pressed-dark .jqx-icon-plus-dark
{
    background-image: url('images/plus_white.png');
}
.jqx-menu-minimized-button-dark {
    padding: 0px !important;
}
.jqx-fill-state-pressed-dark .jqx-menu-minimized-button-dark {
   background-image: url('images/icon-menu-minimized-white.png');
}


.jqx-editor-toolbar-icon-dark {
    background: url('images/html_editor_white.png') no-repeat;
}

.jqx-fill-state-hover-dark .jqx-editor-toolbar-icon-fontsize-dark,
.jqx-fill-state-pressed-dark .jqx-editor-toolbar-icon-fontsize-dark,
.jqx-fill-state-hover-dark .jqx-editor-toolbar-icon-forecolor-dark,
.jqx-fill-state-pressed-dark .jqx-editor-toolbar-icon-forecolor-dark
{
        background: url('images/html_editor.png') no-repeat;
}

.jqx-editor-toolbar-button-dark{
    border-color: var(--jqx-border);
    box-shadow: none !important;
	color: var(--jqx-background-color);
}

.jqx-time-picker .jqx-main-container {
    background: var(--jqx-background);
}

/*applied to the timepicker*/
.jqx-needle-central-circle-dark {
	fill: var(--jqx-primary);
}
.jqx-time-picker-dark .jqx-label-dark {
    fill: var(--jqx-background-color);
}
.jqx-needle-dark {
	fill: var(--jqx-primary);
}
.jqx-time-picker .jqx-header .jqx-selected-dark:focus {
    outline: 2px solid var(--jqx-primary);
	box-shadow: 0px 0px 4px 2px rgba(0, 119, 190, 0.125);
}
.jqx-svg-picker-dark:focus {
	border: 1px solid var(--jqx-primary) !important;
}
.jqx-validator-hint-dark {
    background: #D94F43;
    border-color: #D94F43;
    padding: 10px;
}
.jqx-validator-hint-dark img {
    display: none;
}

.jqx-grid-group-expand-dark:after,
.jqx-grid-group-collapse-dark:after {
	display: flex;
    justify-content: center;
    align-content: center;
    align-items: center;
    height: 100%;
}
