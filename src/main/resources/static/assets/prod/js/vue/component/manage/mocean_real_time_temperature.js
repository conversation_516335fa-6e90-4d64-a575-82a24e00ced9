var moceanRealTimeTemperature = {
    template: `
        <div id="manage_list_body">    
            <div class="project_manage1">
            <dv class="vr"></dv>
                <div class="project_title">
                    <div class="product_title_tool">
                        <button class="project_edit_exited" @click="moceanVehicleListPopupClosed">{{$t("나가기")}}</button>
                    </div>                    
                </div>
                <div class="mocean_title">
                    <label> 차량 실시간 온도</label> <button class="bt_coldChainButton" @click="goColdChainWindow" >{{$t("온도 설정")}}</button>
                </div>
                <br>
                <div class="mocean_tree_grid">                
                    <jqx-chart  ref="chart" style="width: 90%; height: 80%"
                              :title="'실시간 차량 온도 정보'"
                              :description="'차량의 냉장 및 냉동 콜드 체인'"
                              :showLegend="true" :enableAnimations="true" :padding="padding"
                              :titlePadding="titlePadding" :source="temperatureData" :xAxis="xAxis"
                              :seriesGroups="seriesGroups" :colorScheme="'scheme01'"> 
                    </jqx-chart>
                </div>
                <div>
                    <jqx-windows ref="coldChainwindow" 
                        :width="400" :height="450" :position="{ x: 500, y: 150 }"
                        :isModal="true"
                        :autoOpen="false"
                        :resizable="false">
                        <div style="height:30px">
                            콜드 체인 관리
                        </div>
                        <div>
                            <div style="height:30px"></div>
                            <div style="font-size: 15px; height:50px; width: 100%; text-align: center; font-family: 'Noto Sans KR', sans-serif">냉장 온도</div>
                            <div style="height:40px">
                                <div style="float: left; margin-left: 20px">기준 온도 최고값 : </div>
                                <div style="float: left; margin-left: 10px"><input id="temperatureFirstUpperBound" @keypress="isNumber($event)" v-model="temperature.temperatureFirstUpperBound" style="width: 220px; type=number; min=-40; max=40; text-align:right;"></div>
                            </div>
                            <div style="height:40px">
                                <div style="float: left; margin-left: 20px">기준 온도 최저값 : </div>
                                <div style="float: left; margin-left: 10px"><input id="temperatureFirstLowerBound" @keypress="isNumber($event)" v-model="temperature.temperatureFirstLowerBound" style="width: 220px; type=number; min=-40; max=40; text-align:right;"></div>
                            </div>
                            <div style="height:40px"></div>
                            <div style="font-size: 15px; height:50px; width: 100%; text-align: center; font-family: 'Noto Sans KR', sans-serif">냉동 온도</div>
                            <div style="height:40px">
                                <div style="float: left; margin-left: 20px">기준 온도 최고값 : </div>
                                <div style="float: left; margin-left: 10px"><input id="temperatureSecondUpperBound" @keypress="isNumber($event)" v-model="temperature.temperatureSecondUpperBound" style="width: 220px; type=number; min=-40; max=40; text-align:right;"></div>
                            </div>
                            <div style="height:40px">
                                <div style="float: left; margin-left: 20px">기준 온도 최저값 : </div>
                                <div style="float: left; margin-left: 10px"><input id="temperatureSecondLowerBound" @keypress="isNumber($event)" v-model="temperature.temperatureSecondLowerBound" style="width: 220px; type=number; min=-40; max=40; text-align:right;"></div>
                            </div>
                            
                            <div style="margin-top: 10px">
                                <jqx-buttons :template="'success'" :width="80" style="float: left; margin-left: 100px" @click="onSave($event)">저장하기</jqx-buttons>
                                <jqx-buttons :template="'warning'" :width="80" style="float: left; margin-left: 24px" @click="onClose($event)">취소하기</jqx-buttons>
                            </div>
                        </div>
                    </jqx-windows>
                </div>   
            </div>
        </div>
    `,
    props: {
        vinnumber:String,
    },

    data: function () {
        return {
            vinnumber: this.vinnumber,
            baseMoment : null,
            timer: null,
            fromMoment : null,

            temperatureData: [ ],
            padding: { left: 5, top: 5, right: 5, bottom: 5 },
            titlePadding: { left: 90, top: 0, right: 0, bottom: 10 },
            xAxis: {
                dataField: 'TemperTime',
                showGridLines: true,
                formatFunction: (value) => {
                    return jqx.dataFormat.formatdate(value, 'hh:mm:ss', 'en-us');
                },
            },
            seriesGroups:
            [
                {
                    type: 'line',
                    columnsGapPercent: 50,
                    seriesGapPercent: 0,
                    valueAxis:
                        {
                            unitInterval: 5,
                            minValue: -40,
                            maxValue: 40,
                            displayValueAxis: true,
                            description: 'Temperature',
                            axisSize: 'auto',
                            tickMarksColor: '#888888'
                        },
                    series: [
                        {dataField: 'coldTemp', displayText: '냉장 온도'},
                        {dataField: 'coldHigh', displayText: '냉장 상 온도', lineColor: '#FF0000'},
                        {dataField: 'coldLow', displayText: '냉장 하 온도', lineColor: '#FF0000'},
                        {dataField: 'freezeTemp', displayText: '냉동 온도'},
                        {dataField: 'freezeHigh', displayText: '냉동 상 온도', lineColor: '#006cb7'},
                        {dataField: 'freezeLow', displayText: '냉동 하 온도', lineColor: '#006cb7'},
                    ]
                }
            ],


            temperature: {
                temperatureFirstUpperBound: 0,
                temperatureFirstLowerBound: 0,
                temperatureSecondUpperBound: 0,
                temperatureSecondLowerBound: 0,
            },
        }
    },

    created: function () {
    },

    mounted: function () {

        let today = new Date();
        let fromMoment = moment(today).format('YYYY-MM-DD[T00:00:00]');
        let toMoment = moment(today).format('YYYY-MM-DD[T]HH:mm:ss');

        // 처음에 API를 이용하여 처음 값을 가지고 오게 수정함
        RIDER_API.getMoceanTemperatures(this.vinnumber.vinnumber, null, fromMoment, toMoment, 0, 1500, null, null,{
            onSuccess: (response) => {
                response.data.content.forEach(vehicle => {
                    this.temperatureData.push({
                        TemperTime: vehicle.createdAt.slice(11, 16),
                        coldTemp: vehicle.temperatureFirst,
                        coldHigh: vehicle.temperatureFirstUpperBound,
                        coldLow: vehicle.temperatureFirstLowerBound,
                        freezeTemp: vehicle.temperatureSecond,
                        freezeHigh: vehicle.temperatureSecondUpperBound,
                        freezeLow: vehicle.temperatureSecondLowerBound
                    });
                });
                this.$refs.chart.refresh();
            },
            onError: (error) => {
                console.log(error);
            }
        });

        this.realTimeRefersh(toMoment);
    },

    beforeCreate: function () {
    },

    watch: {
    },

    computed: {

    },

    methods: {
        moceanVehicleListPopupClosed: function(){
            clearInterval(this.timer);
            this.$refs.coldChainwindow.close();
            window.parent.app.$emit(EVENT.MAIN.CLOSE_MOCEAN_REALTIME_TEMPERATURE_POPUP);
        },

        realTimeRefersh:function (fromMoment)
        {
            console.log(fromMoment);
            this.fromMoment = fromMoment;

            // 60초에 한번씩 API를 이용하여 update 하게 수정함.
            this.timer = setInterval(() => {

                let today = new Date();
                let toMoment = moment(today).format('YYYY-MM-DD[T]HH:mm:ss');

                this.getMoceanTemperatures(this.fromMoment, toMoment);
            }, 60000);
        },

        getMoceanTemperatures: function (fromMoment, toMoment) {

            RIDER_API.getMoceanTemperatures(this.vinnumber.vinnumber, null, fromMoment, toMoment, 0, 1500, null, null,{
                onSuccess: (response) => {
                    console.log(response.data.content.length);
                    this.fromMoment = toMoment;
                    if(response.data.content.length) {
                        response.data.content.forEach(vehicle => {
                            this.temperatureData.push({
                                TemperTime: vehicle.createdAt.slice(11, 16),
                                coldTemp: vehicle.temperatureFirst,
                                coldHigh: vehicle.temperatureFirstUpperBound,
                                coldLow: vehicle.temperatureFirstLowerBound,
                                freezeTemp: vehicle.temperatureSecond,
                                freezeHigh: vehicle.temperatureSecondUpperBound,
                                freezeLow: vehicle.temperatureSecondLowerBound
                            });
                        });

                        this.$refs.chart.update();
                    }

                },
                onError: (error) => {
                    console.log(error);
                }
            });
        },

        onRowClick: function (event) {

        },

        onRowSelect:function (event){

        },

        goColdChainWindow: function () {
            var _this = this;

            RIDER_API.getMoceanVinInfo(this.vinnumber.vinnumber,{
                onSuccess: (response) => {
                    console.log(response.data);
                    _this.temperature = response.data;
                    _this.$refs.coldChainwindow.open();
                },
                onError: (error) => {
                    console.log(error);
                }
            });
        },

        onSave: function (event) {
            let t1_Up = this.temperature.temperatureFirstUpperBound;
            let t1_Lo = this.temperature.temperatureFirstLowerBound;
            let t2_Up = this.temperature.temperatureSecondUpperBound;
            let t2_Lo = this.temperature.temperatureSecondLowerBound;

            if ((parseFloat(t1_Up) < parseFloat(t1_Lo)) || (parseFloat(t2_Up) < parseFloat(t2_Lo))) {
                PopupUtil.alertPopup("온도 설정이 잘못 됬습니다.");
            } else {
                RIDER_API.putMoceanTemperaturesBounds(this.vinnumber.vinnumber, null,
                    t1_Lo, t1_Up, t2_Lo, t2_Up, {
                        onSuccess: (response) => {
                            console.log(response);
                            PopupUtil.alertPopup("저장 되었습니다.");
                            this.$refs.coldChainwindow.close();
                        },
                        onError: (error) => {
                            PopupUtil.alertPopup("저장에 실패하였습니다.");
                        }
                    }
                );
            }
        },

        onClose:function(event) {
            this.$refs.coldChainwindow.close();
        },

        isNumber: function(evt) {
            evt = (evt) ? evt : window.event;
            var charCode = (evt.which) ? evt.which : evt.keyCode;
            if(charCode === 45 || charCode === 46){
                return true;
            }
            else if ((charCode > 31 && (charCode < 48 || charCode > 57))) {
                evt.preventDefault();;
            } else {
                return true;
            }
        },

    },
}