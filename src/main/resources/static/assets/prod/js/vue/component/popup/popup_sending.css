@charset "UTF-8";
@import url("/assets/common/css/font.css");

* {
	font-family: Noto Sans KR;
	padding: 0px;
	margin: 0px; 
}

.or33_window{
	position: absolute;
	top:50%;
	left: 50%;
	margin-top: -125px;
	margin-left: -100px;
	width: 250px;
	height: 200px;
	background-color: #fff;	
	border-radius: 3px;
/*	border: 1px solid red;*/
}

.or33_title{
	position: absolute;
	top: 140px;	
	left: 50%;
	margin-left: -90px;
	width: 180px;
	height: 20px;
	padding-top: 0px;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color:#0D3A41;
	/*border: 1px solid red;*/
}


.or33_circle{
	position: absolute;
	top:56px;
	left: 96px;
	width: 58px;
	height: 58px;
}

#load {
  width: 50px;
  display:inline-block;
  animation: loading 2s linear infinite;
}
#load #loading-inner {
  stroke-dashoffset: 1;
  stroke-dasharray: 300;
  stroke-width: 10;
  stroke-miterlimit: 10;
  stroke-linecap: butt;
  animation: loading-circle 2s linear infinite;
  stroke: #f7941d;
  fill: transparent;
}

@keyframes loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes loading-circle {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -600;
  }
}
