Vue.component('vehicle-list', {
	template: 
	`
	<div class="vehicle-list-content">
		{{$t("전체 차량 대수")}}: {{ allVehiclesData.available }} <br>
		<table id="vehicle-list-table">
	        <thead>
	            <tr>
		            <td><strong>{{$t("기사 ID")}}</strong></td>
		            <td><strong>{{$t("차량번호")}}</strong></td>
		            <td><strong>{{$t("모델")}}</strong></td>
	            </tr>
	        </thead>
	        <tbody>
	            <tr v-for="vehicle in allVehiclesData.vehicles">
		            <td>{{vehicle.driverId}}</td>
		            <td>{{vehicle.vehicleNo}}</td>
		            <td>{{vehicle.modelName}}</td>
	            </tr>
	        </tbody>
		</table>
	</div>
	`,
	props:{ 

	},
	data: function() {
		return {
			allVehiclesData: ""
		}
	}, 
	
	methods: {
        async getVehicleList(start, size, callback) {
    	    axios.get(Url.VEHICLES.GET_LIST + "?start=" + start + "&size=" + size) //사용하지않음...
    	    .then(function (response) {
    	      // handle success
    	    	if (callback) {
    	    		callback(response.data);
    	    	}
    	    })
    	    .catch(function (error) {
    	      // handle error
    	      console.log(error);
    	    })
    	    .finally(function () {
    	      // always executed
    	    });
        },
        
        async updateVehicleList(data) {
        	this.allVehiclesData = data;
        }
	},
	mounted() {
		this.getVehicleList(0, 50, this.updateVehicleList);
	}	
});

var app = new Vue({
    el: '#vehicleList'
});