var riderDetailPopup = {
  template: `
   <div id="rider-detail-popup-container">
     <jqx-windows ref="jpxWin" :width="624" :height="750" :isModal="false" :position="position" :resizable="false" :showCloseButton="false" @close="onClose($event)">
       <div> {{ getTitle }}</div>
       <content class="relative h-full flex flex-col">
         <!---------------------------------------------------TITLE---------------------------------------------------->
          <header class="relative top-0 left-0 right-0 bg-white shadow z-10">
              <div class="flex justify-between items-center my-8 px-4">
                    <section class="inline-flex items-baseline">
                        <div class="or02_driver_pic"></div>
                        <div class="or27_left_list_circle relative w-2 h-2 ml-5 " :class="getRiderShowStatus()"></div>
                        <span class="inline text-blueGray-700 text-base font-bold mx-3">
                            {{ $t(riderDetail.status) }}
                        </span>
                        <span class="px-2 inline text-white bg-blue-500 text-sm font-bold mx-3 border-2 border-blue-500 rounded-lg items-center" v-if="riderDetail.dispatchNumber && viewMode && isRiderDispatchNumber">
                            {{$t("호차번호")}} : {{ riderDetail.dispatchNumber }}
                        </span>
                        <div v-if="!viewMode && isRiderDispatchNumber" class="flex">
                            <span class="px-2 inline text-black text-sm font-bold ml-4 items-center"> {{$t("호차번호")}} </span>
                            <input class="border-0 px-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-32 ease-linear transition-all duration-150"  
                                type="text" ref="dispatchNumber" v-model="form.dispatchNumber" oninput="this.value = this.value.replace(/[^0-9]/g, '');"/>
                        </div>
                    </section>
                    <section class="inline-flex items-baseline">
                        <button class="bg-yellow-500 text-white active:bg-pink-300 font-bold text-xs px-4 py-2 rounded shadow hover:shadow-md outline-none focus:outline-none mr-1 ease-linear transition-all duration-150" 
                            v-if="viewMode" @click="sendRiderPushMessage"> {{$t("메시지 전달")}}</button>
                        <button class="bg-yellow-500 text-white active:bg-pink-300 font-bold text-xs px-4 py-2 rounded shadow hover:shadow-md outline-none focus:outline-none mr-1 ease-linear transition-all duration-150" 
                            v-if="viewMode" :disabled="!sendRiderBtn" @click="performRiderSending"> {{$t("기사에게 전송")}}</button>
                        <button class="px-4 py-2 mx-2 text-xs text-white rounded shadow-md ease-in-out transform hover:shadow-xl focus:outline-none focus:ring-blue-500 focus:ring-opacity-50
                            button-gradient disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-none disabled:hover:translate-y-0 disabled:hover:bg-blue-700 disabled:focus:ring-0 disabled:focus:ring-opacity-0 flex items-center justify-center" 
                            @click="saveRiderAndVehicle" v-if="!viewMode" :disabled="enabledSave == false">{{$t("저장")}}</button>
                        <button class="px-4 py-2 mx-2 text-xs text-white rounded shadow-md ease-in-out transform hover:shadow-xl focus:outline-none focus:ring-blue-500 focus:ring-opacity-50
                            button-gradient disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-none disabled:hover:translate-y-0 disabled:hover:bg-blue-700 disabled:focus:ring-0 disabled:focus:ring-opacity-0 flex items-center justify-center" 
                            @click="onEdit" v-if="isEditButtonVisible()" >
                            {{$t("수정")}}
                        </button>
                        <button class="bg-gray-500 text-white active:bg-pink-600 font-bold text-xs px-4 py-2 rounded shadow hover:shadow-md outline-none focus:outline-none mr-1 ease-linear transition-all duration-150" 
                            @click="riderDetailPopupClosed" >
                            {{$t("닫기")}}
                        </button>
                    </section>                
            </div>
        </header>

         <!-- Scrollable Content -->          
         <main class="pt-20 bg-gray-100 overflow-auto flex-1">
         <!---------------------------------------------------RIDER INFO---------------------------------------------------->
         <h6 class="text-blueGray-400 text-sm mt-1 mb-3 font-bold mx-4"> {{$t("기사 정보")}} </h6>
         <div class="flex flex-wrap">
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-3">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("기사명")}}
                 <span v-if="!viewMode" class="text-red-600"> * </span>
               </label>
               <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"  
                    type="text" ref="name" v-model="form.name" :disabled="viewMode" />
             </div>
           </section>
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-5">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("기사업무")}}
               </label>
                  <div class="flex">
                    <div class="flex items-center">
                      <label :class="['text-sm', { 'text-gray-400': viewMode }]">{{$t("배송 업무")}}</label>
                      <label :class="[getDeliveryWork ? 'work_setting_on' : 'work_setting_off', { 'pointer-events-none': viewMode }]" @click="onDeliverySet()"></label>
                    </div>
                    <div class="x-4">
                      <div class="flex items-center">
                        <label :class="['text-sm', { 'text-gray-400': viewMode }]">{{$t("스캔 업무")}}</label>
                        <label :class="[getScanWork ? 'work_setting_on' : 'work_setting_off', { 'pointer-events-none': viewMode }]" @click="onScanSet()"></label>
                      </div>
                    </div>
                  </div>
             </div>           
           </section>
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-3">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("기사번호")}}
                 <span v-if="!viewMode" class="text-red-600"> * </span>
               </label>
               <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"  
                type="text" ref="licensePlate" v-model="form.licensePlate" :disabled="viewMode" />
             </div>
           </section>
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-3">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("전화번호")}}
                 <span v-if="!viewMode" class="text-red-600"> * </span>
               </label>
<!--               <label class="text-red-600 text-xs ml-1 mb-2" v-if="mobileFormatError == true">{{$t("전화번호 포맷 오류")}}</label>-->
               <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"  
                type="text" ref="mobile" placeholder="00000000000" v-model="form.mobile" :disabled="viewMode" oninput="this.value = this.value.replace(/[^0-9]/g, '');"/>
             </div>
           </section>
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-5">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("직급")}}
               </label>
               <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"  
                type="text" ref="position" v-model="form.position" :disabled="viewMode" />
             </div>
           </section>
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-5">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("담당유형")}}
                 <span v-if="!viewMode" class="text-red-600"> * </span>
               </label>
               <select class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150" 
                ref="milesType" v-model="form.milesType" :disabled="true">
                 <option value="" disabled selected>{{$t("담당유형 선택")}}</option>
                 <option v-for="milesType in milesTypes" v-bind:value="milesType">{{convertMilesTypeText(milesType)}}</option>
               </select>
             </div>
           </section>
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-5">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("담당권역")}}
               </label>
               <select class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150" 
                ref="groupName" v-model="groupKey" @change="onChange($event)" :disabled="viewMode">
                 <option v-for="option in groupNameList" v-bind:value="option">
                   <div> {{convertGroupText(option)}} </div>
                 </option>
               </select>
             </div>
           </section>
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-5">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("기사유형")}}
               </label>
               <select class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150" 
                    v-model="form.autoCreate" :disabled="viewMode">
                 <option value="" disabled selected>{{$t("기사유형 선택")}}</option>
                 <option v-for="isRFC in [false,true]" v-bind:value="isRFC">{{ changedTranRiderType(isRFC) }}</option>
               </select>
             </div>
           </section>
           <section v-show="groupTextShow" class=" w-1/2 px-4">
             <div class="relative w-auto mb-5">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("권역입력")}}
               </label>
               <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"  
                  type="text" ref="position" v-model="form.groupName" :disabled="viewMode" />
             </div>
           </section>
         </div>
         <!---------------------------------------------------WORK INFO---------------------------------------------------->
         <hr class="mt-1 border-b-1 border-blueGray-300">
         <h6 class="text-blueGray-400 text-sm mt-3 mb-6 font-bold"> {{$t("근무 정보")}} </h6>
         <div class="flex flex-wrap">
           <section class="w-full  px-4">
             <div class="relative w-full mb-3">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("근무시작")}}
                 <span v-if="!viewMode" class="text-red-600"> * </span>
               </label>
               <div class="flex justify-around">
                 <input class="inline border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"
                    type="text" ref="workingStartAddress" :placeholder="$t('주소입력')" v-model="form.workingStartAddress" :disabled="viewMode" >
                 <button class="inline mx-3" v-bind:class="viewMode? 'or27_startpoint':'or02_add_search1'" @click=" onClickSearchAddress(form.workingStartAddress, true) " /></button>
               </div>
             </div>
           </section>
           <section class="w-full px-4">
             <div class="relative w-full mb-3">
               <label class="block  text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("근무종료")}}
               </label>
               <div class="flex justify-around">
                 <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"
                    type="text" ref="workingEndAddress" :placeholder="$t('주소입력')" v-model="form.workingEndAddress" :disabled="viewMode" >
                 <button class="inline mx-3" v-bind:class="viewMode? 'or27_endpoint':'or02_add_search1'" @click=" onClickSearchAddress(form.workingEndAddress, true) " /></button>
               </div>
             </div>
           </section>
           <section class="w-full px-4">
             <div class="relative w-full mb-3">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("근무시간")}}
                 <span v-if="!viewMode" class="text-red-600"> * </span>
               </label>
               <div class="flex justify-between">
                 <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-2/5 ease-linear transition-all duration-150" 
                        type="time" v-model="form.workingStartTime" :disabled="viewMode" />
                 <span> ~ </span>
                 <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-2/5 ease-linear transition-all duration-150" 
                        type="time" v-model="form.workingEndTime" :disabled="viewMode" />
               </div>
             </div>
           </section>
           <section class="w-full  px-4">
             <div class="relative w-full mb-3">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("점심시간")}}
               </label>
               <div class="flex justify-between">
                 <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-2/5 ease-linear transition-all duration-150" 
                  type="time" v-model="form.lunchStartTime" :disabled="viewMode" />
                 <span> ~ </span>
                 <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-2/5 ease-linear transition-all duration-150"  
                   type="time" v-model="form.lunchEndTime" :disabled="viewMode" />
               </div>
             </div>
           </section>
         </div>
         <!---------------------------------------------------VEHICLE INFO---------------------------------------------------->
         <hr class="mt-6 border-b-1 border-blueGray-300">
         <h6 class="text-blueGray-400 text-sm mt-3 mb-6 font-bold    "> {{$t("차량 정보")}} </h6>
         <div class="flex flex-wrap">
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-3">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("차량타입")}}
                 <span v-if="!viewMode" class="text-red-600"> * </span>
               </label>
               <select class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150" 
                    ref="vehicleType" v-model="form.vehicleType" :disabled="viewMode">
                 <option value="" disabled selected>{{$t("차량타입 선택")}}</option>
                 <option v-for="vehicleType in vehicleTypes" v-bind:value="vehicleType">{{convertVehicleTypeText(vehicleType)}}</option>
               </select>
             </div>
           </section>
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-3">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("연료타입")}}
               </label>
               <select class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150" 
                ref="fuelType" v-model="form.fuelType" :disabled="viewMode">
                 <option value="" disabled selected>{{$t("연료타입 선택")}}</option>
                 <option v-for="fuelType in fuelTypes" v-bind:value="fuelType">{{convertFuelTypeText(fuelType)}}</option>
               </select>
             </div>
           </section>
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-5">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("적재량")}}(kg) </label>
               <input ref="payload" placeholder="0" class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150" 
                  type="text" v-model="form.payload" :disabled="viewMode" oninput="this.value = this.value.replace(/[^0-9]/g, '');"/>
             </div>
           </section>
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-5">
               <label class="block text-blueGray-600 text-xs font-bold mb-2"> {{$t("적재공간길이")}}(cm) </label>
               <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150" 
                type="text" v-model="form.loadingLength" :disabled="viewMode" oninput="this.value = Math.min(10000, this.value.replace(/[^0-9]/g, ''));"/>
             </div>
           </section>
           
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-5">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">{{$t("적재공간너비")}}(cm) </label>
               <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150" 
                  type="text" v-model="form.loadingWidth" :disabled="viewMode" oninput="this.value = Math.min(3000, this.value.replace(/[^0-9]/g, ''));"/>
             </div>
           </section>
           
           <section class="w-6/12 px-4">
             <div class="relative w-full mb-5">
               <label class="block text-blueGray-600 text-xs font-bold mb-2"> {{$t("적재공간높이")}}(cm) </label>
               <input class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150" 
                  type="text" v-model="form.loadingHeight" :disabled="viewMode" oninput="this.value = Math.min(10000, this.value.replace(/[^0-9]/g, ''));"/>
             </div>
           </section>
                      
         </div>
         <hr class="mt-6 border-b-1 border-blueGray-300">
         <h6 class="text-blueGray-400 text-sm mt-3 mb-6 font-bold"> {{$t("추가 정보")}} </h6>
         <div class="flex flex-wrap">
           <section class="w-full px-4">
             <div class="relative w-full mb-3">
               <label class="block text-blueGray-600 text-xs font-bold mb-2">
                 {{$t("메모")}}
               </label>
               <textarea class="border-0 px-2 py-2 placeholder-blueGray-300 text-blueGray-600 disabled:bg-gray-100 bg-white rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150" 
                    type="text" v-model="form.note" ref="note" :placeholder="$t('기사관련 상세내용 추가')"  v-html="form.note" :disabled="viewMode"></textarea>
             </div>
           </section>
         </div>
         </main>
       </content>
     </jqx-windows>
   </div>
`,
  props: {
    rider: Object,
    editMode: Object,
    position: Object,
    popupIndex: Number,
  },
  //props: [ 'rider', 'mode',
  //],
  data: function () {

    return {
      mode: this.editMode,
      index: this.popupIndex,
      componentCreated: false,
      groupTextShow: false,
      groupKey: _t(Constant.NO_GROUP_KEYWORD_NAME),
      groupNameList: [_t(Constant.NO_GROUP_KEYWORD_NAME)],
      //groupNameList:[Constant.NO_GROUP_KEYWORD_NAME],
      vehicleModelList: [{
        vehicleModelId: 0, modelName: _t('차량모델선택')
      }],
      form: {
        "workingStartTime": (this.rider != null) ? (this.rider.workingStartTime != null ? this.rider.workingStartTime : "") : "",
        "workingEndTime": (this.rider != null) ? (this.rider.workingEndTime != null ? this.rider.workingEndTime : "") : "",
        "lunchStartTime": (this.rider != null) ? (this.rider.lunchStartTime != null ? this.rider.lunchStartTime : "") : "",
        "lunchEndTime": (this.rider != null) ? (this.rider.lunchEndTime != null ? this.rider.lunchEndTime : "") : "",
        "workingStartAddress": (this.rider != null) ? (this.rider.workingStartAddress != null ? this.rider.workingStartAddress : "") : "",
        "workingEndAddress": (this.rider != null) ? (this.rider.workingEndAddress != null ? this.rider.workingEndAddress : "") : "",
        "milesType": (this.rider != null) ? (this.rider.milesType != null ? this.rider.milesType : "") : "",
        "modelName": (this.rider != null) ? (this.rider.modelName != null ? this.rider.modelName : "") : "",
        "vehicleType": (this.rider != null && this.rider.vehicle != null) ? (this.rider.vehicle.vehicleType != null ? this.rider.vehicle.vehicleType : "") : "",
        "fuelType": (this.rider != null && this.rider.vehicle != null) ? (this.rider.vehicle.fuelType != null ? this.rider.vehicle.fuelType : "") : "",
        "name": (this.rider != null) ? (this.rider.name != null ? this.rider.name : "") : "",
        "licensePlate": (this.rider != null && this.rider.vehicle != null) ? (this.rider.vehicle.licensePlate != null ? this.rider.vehicle.licensePlate : "") : "",
        "mobile": (this.rider != null) ? (this.rider.mobile != null ? this.rider.mobile : "") : "",
        "position": (this.rider != null) ? (this.rider.position != null ? this.rider.position : "") : "",
        "groupName": (this.rider != null) ? (this.rider.groupName != null ? this.rider.groupName : "") : "",
        "payload": (this.rider != null) ? (this.rider.payload ? this.rider.payload : "-") : "-",
        "note": (this.rider != null) ? (this.rider.note != null ? this.rider.note : "---") : "---",
        "riderId": (this.rider != null) ? (this.rider.riderId != null ? this.rider.riderId : null) : null,
        "autoCreate": (this.rider != null) ? (this.rider.autoCreate != null ? this.rider.autoCreate : false) : false,

        //this.rider 또는 this.riderDetail을 쓸경우 multi일때 문제가 되니 form에 있는 데이터를 사용해야 한다
        "projectId": (this.rider != null) ? this.rider.projectId : null,
        "projectStatus": (this.rider != null) ? this.rider.projectStatus : null,
        "projectUserId": (this.rider != null) ? this.rider.projectUserId : null,
        "projectAttribute": MapUtil.copyObject(this.rider.projectAttribute),
        "vehicle": MapUtil.copyObject(this.rider.vehicle),//this.rider는 멀티 일경우 이후 윈도우로 덮으므로 복사해서 관리한다
        "workAuthority": (this.rider != null) ? this.rider.workAuthority : '', 			//기사 업무 (배송 : 1, 스캔 : 2, 배송+스캔 : 3)
        "dispatchNumber": (this.rider != null) ? this.rider.dispatchNumber : null,
        "loadingLength" : (this.rider != null) ? this.rider.loadingLength : null,
        "loadingWidth" : (this.rider != null) ? this.rider.loadingWidth : null,
        "loadingHeight" : (this.rider != null) ? this.rider.loadingHeight : null,
      },
      mobileExists: false,
      licensePlateExists: false,
      mobileFormatError: false,

      milesTypes: null,
      vehicleTypes: null,
      fuelTypes: null,

      profileImgFileUrl: null,
      moceanVehicleStatus: null,

      placeHolderPosition: '사원',//[demo]
      demoViewMode: false,//for Demo

      autoCreate: false,

      workAuthority: "",
      workAuthorityData: {
        delivery: false,
        scan: false
      }
    }
  },

  async created() {

    if (this.mode == 'r') {
      await this.getMoceanLastVehicleStatus();
    }

    this.componentCreated = true;
  },

  mounted: function () {
    console.log('popup_driver_detail Mounted !!!');

    this.getVehicleModelInfo();
    /*
    if(this.mode != 'r') {
      this.getGroupNameList();
      this.getMilesTypes();
      this.getVehicleTypes();
      this.getFuelTypes();
    }
*/
    //this.mode=>'r':보기, 'w':편집, 'a':추가

    this.getMilesTypes();
    this.getVehicleTypes();
    this.getFuelTypes();

    if (this.form.groupName) {
      this.groupTextShow = false;
      this.groupKey = this.form.groupName;
    } else {
      this.groupKey = Constant.NO_GROUP_KEYWORD_NAME;
    }

    this.getGroupNameList();

    if (this.mode == 'a' || this.mode == 'ra') { //기사 추가 할때 default 값을 저장한다
      this.rider.fuelType = "경유";
      this.form.fuelType = this.rider.fuelType;

      this.rider.vehicleType = "트럭_카고";
      this.form.vehicleType = this.rider.vehicleType;

      this.rider.milesType = Constant.DestinationType.FINAL;
      this.form.milesType = this.rider.milesType;
    }

    if (!this.demoViewMode) {
      this.getRiderProfileImage();
    }
  },

  beforeDestroy() {
  },

  computed: {
    viewMode: function () {
      if (this.mode == 'r') {
        return true;
      } else {
        return false;
      }
    },

    isRiderDispatchNumber: function() {
      return Util.isHyundaiUserCompany() || Util.isWelStoryUserCompany() || Util.isToHomeUserCompany();
    },

    sendRiderBtn: function () {
      // [현대백화점] 근거리 프로젝트는 배차, 경로생성없이도 기사에게 전송 버튼을 활성화
      if (Util.isHyundaiUserCompany() && this.rider.projectName && this.rider.projectName.startsWith("TheHyundai_근거리_")) {
        console.log("[현대백화점] 프로젝트: " + this.rider.projectName + " 기사에게 전송 버튼 활성화");
        return true;
      }else if ( !this.rider.projectAttribute ){
        return false;
      }

      console.log(this.rider.projectAttribute.isClusterDone);
      return !!this.rider.projectAttribute.isClusterDone;
    },

    viewMoceanVehicleStatus: function () {
      return (this.mode == 'r' && this.moceanVehicleStatus);
    },

    getTitle: function () {
      if (this.mode === 'a')
        return _t("기사 추가");
      else {
        return _t("기사 정보") + " - " + (this.rider && this.form.name ? this.form.name : "");
      }
    },

    getDriverInfoTitle: function () {
      let ret = "";
      if (this.mode === 'r') {
        ret = _t("기사 정보 보기");
      } else if (this.mode === 'a' || this.mode === 'ra') {
        ret = _t("기사 정보 추가");
      } else {
        ret = _t("기사 정보 수정");
      }

      return ret;
    },

    riderDetail: function () {
      console.log('popup_driver_detail computed !!!');
      //	if (this.mode == 'w') {
      //		return null;
      //	}
      let licensePlate = "---";
      let modelName = "---";
      let milesType = "---";
      let vehicleType = "---";
      let fuelType = "---";
      let fuelEfficiency = "---";
      let workAuthority = "";

      if (isAnonymous()) {
        if (this.rider.type === Constant.RiderType.EV) {//[demo]TODO: 나중에 디폴트값 넣는 좋은 방법을 찾아보자
          fuelType = "전기";
          fuelEfficiency = "3.1";
          vehicleType = "트럭_카고";
        } else {
          fuelType = "경유";
          fuelEfficiency = "9.9";
          vehicleType = "트럭_카고";
        }
      }

      if (this.rider) {
        if (this.rider.milesType) {
          milesType = this.rider.milesType;
          console.log("담당유형: " + milesType);
        }

        if (this.rider.vehicle) {
          if (this.rider.vehicle.licensePlate) {
            licensePlate = this.rider.vehicle.licensePlate;
          }
          if (this.rider.vehicle.modelName) {
            modelName = this.rider.vehicle.modelName;
            console.log("차량모델: " + modelName);
          }
          if (this.rider.vehicle.vehicleType) {
            vehicleType = this.rider.vehicle.vehicleType;
            console.log("차량타입: " + vehicleType);
          }
          if (this.rider.vehicle.fuelType) {
            fuelType = this.rider.vehicle.fuelType;
            console.log("연료타입: " + fuelType);
          }
          if (this.rider.vehicle.fuelEfficiency) {
            fuelEfficiency = this.rider.vehicle.fuelEfficiency;
          }
        }

        if (this.rider.workAuthority) {
          if (_.isEqual(this.rider.workAuthority, Constant.WorkAuthority.DELIVERY)) {
            this.workAuthorityData.delivery = true;
            this.workAuthority = Constant.WorkAuthority.DELIVERY;
          } else if (_.isEqual(this.rider.workAuthority, Constant.WorkAuthority.SCANNING)) {
            this.workAuthorityData.scan = true;
            this.workAuthority = Constant.WorkAuthority.SCANNING;
          } else if (_.isEqual(this.rider.workAuthority, Constant.WorkAuthority.DELIVERY_AND_SCANNING)) {
            this.workAuthorityData.delivery = true;
            this.workAuthorityData.scan = true;
            this.workAuthority = Constant.WorkAuthority.DELIVERY_AND_SCANNING;
          }
        }

      }
      //console.log("working startTime: " + this.rider.workingStartTime.substring(0,5));
      return {
        'groupName': this.rider.groupName ? this.rider.groupName : "",
        'riderId': this.rider.riderId,
        'mobile': this.rider.mobile ? this.rider.mobile : "--",
        'position': this.rider.position ? this.rider.position : "--",
        'workingStartAddress': this.rider.workingStartAddress ? this.rider.workingStartAddress : "--",
        'workingEndAddress': this.rider.workingEndAddress ? this.rider.workingEndAddress : "--",
        'licensePlate': licensePlate,
        'milesType': milesType,
        'modelName': modelName,
        'vehicleType': vehicleType,
        'fuelType': fuelType,
        'fuelEfficiency': fuelEfficiency,
        'workingStartTime': this.rider.workingStartTime ? this.rider.workingStartTime.substring(0, 5) : "",
        'workingEndTime': this.rider.workingEndTime ? this.rider.workingEndTime.substring(0, 5) : "",
        'lunchStartTime': this.rider.lunchStartTime ? this.rider.lunchStartTime.substring(0, 5) : "",
        'lunchEndTime': this.rider.lunchEndTime ? this.rider.lunchEndTime.substring(0, 5) : "",
        'payload': this.rider.payload ? this.rider.payload : "-",
        'skillLevel': this.rider.skillLevel ? this.rider.skillLevel : "--",
        'note': this.rider.note ? this.rider.note : "---",
        'status': this.rider.status,
        'name': this.rider.name,
        'type': this.rider.type,
        'imgUrl': this.rider.imgUrl,
        'dispatchNumber': this.rider.dispatchNumber ? this.rider.dispatchNumber : null,
        'loadingLength' : this.rider.loadingLength || null,
        'loadingWidth' : this.rider.loadingWidth || null,
        'loadingHeight' : this.rider.loadingHeight || null,
      };


    },
    enabledSave: function () {
      // if (this.groupKey=='권역선택') {
      // 	return false;
      // }
      if (this.groupKey == Constant.NEW_GROUP_KEYWORD_NAME && !this.form.groupName) {
        return false;
      }
      if (isAnonymous()) {//[demo]
        return true;
      }

      if (!this.form.name) {
        return false;
      }

      if (!this.form.licensePlate) {
        return false;
      }

      if (!this.form.mobile) {
        return false;
      }

      if (!this.form.milesType || this.form.milesType === _t('담당유형 선택')) {
        return false;
      }

      if (!this.form.workingStartAddress) {
        return false;
      }
      if (!this.form.vehicleType || this.form.vehicleType === _t('차량타입 선택')) {
        //if ( !this.form.modelName || this.form.modelName==='차량모델선택' ) {
        return false;
      }

      if (this.licensePlateExists == true || this.mobileExists == true) {
        return false;
      }

      if (this.mobileFormatError == true) { //전화번호 포맷오류
        return false;
      }

      // if(!this.form.workingStartTime || !this.form.workingEndTime || !this.form.lunchStartTime || !this.form.lunchEndTime)
      if (!this.form.workingStartTime || !this.form.workingEndTime) {
        return false;
      }

      return true;
    },

    randomDemoRiderImage: function () {
      const RIDER_IMAGE_COUNT = 10;
      const index = (this.form.riderId % RIDER_IMAGE_COUNT) + 1;
      const imagePath = "/assets/image/popup/img_ex" + Util.pad(index, 2) + ".png";
      return {'background-image': "url( " + imagePath + " )"};
    },

    getDeliveryWork: function () {
      console.log(this.form.workAuthority);
      if (_.isEqual(this.workAuthority, Constant.WorkAuthority.DELIVERY))
        return true;
      else if (_.isEqual(this.workAuthority, Constant.WorkAuthority.DELIVERY_AND_SCANNING))
        return true;
      else
        return false;
    },

    getScanWork: function () {
      console.log(this.workAuthority);
      if (_.isEqual(this.workAuthority, Constant.WorkAuthority.SCANNING))
        return true;
      else if (_.isEqual(this.workAuthority, Constant.WorkAuthority.DELIVERY_AND_SCANNING))
        return true;
      else
        return false;
    },
  },

  methods: {

    getPopupMode: function () {
      console.log('popup_driver_detail popupMode=' + this.mode);
      return this.mode;
    },

    riderDetailPopupClosed: function () {
      window.parent.app.$emit(EVENT.MAIN.CLOSE_DRIVER_DETAIL_POPUP, {
        riderId: this.form.riderId,
        index: this.index,
        fromCallback: false /* no popupIndex */
      });
      // this.$emit('dismiss'); //불필요 2회 호출됨
    },
    onChange: function (event) {
      console.log("수신이벤트.." + event.target.value);
      if (_.isEqual(event.target.value, Constant.NEW_GROUP_KEYWORD_NAME)) {
        this.groupTextShow = true;
      } else {
        this.groupTextShow = false;
      }
    },

    getGroupNameList: function () {
      this.groupNameList = this.rider.groupNameList;

      if (this.groupNameList != null && !this.groupNameList.includes(Constant.NEW_GROUP_KEYWORD_NAME)) {
        this.groupNameList.push(_t(Constant.NEW_GROUP_KEYWORD_NAME));//신규 Group 이름 기능 추가
      }

      //그룹 이름에 groupKey가 없으면  리스트가 비어 있는 문제가 있다. 이때 꼭 넣어준다. 
      if (this.groupKey && this.groupKey.length > 0 && this.groupNameList && !this.groupNameList.includes(this.groupKey)) {
        this.groupNameList.push( this.groupKey ) ;
      }
    },

    getMilesTypes: function () {
      TMS_WEB_API.getMilesTypes({
        onSuccess: (response) => {
          this.milesTypes = response.data;
        },
        onError: (error) => {
          console.log("담당유형 목록 조회에 실패 하였습니다.");
          PopupUtil.alertPopup(error.response.data);
        }
      })
    },

    getVehicleTypes: function () {
      TMS_WEB_API.getVehicleTypes({
        onSuccess: (response) => {
          this.vehicleTypes = response.data;
        },
        onError: (error) => {
          console.log("차량타입 목록 조회에 실패 하였습니다.");
          PopupUtil.alertPopup(error.response.data);
        }
      })
    },

    getFuelTypes: function () {
      TMS_WEB_API.getFuelTypes({
        onSuccess: (response) => {
          this.fuelTypes = response.data;
        },
        onError: (error) => {
          console.log("연료타입 목록 조회에 실패 하였습니다.");
          PopupUtil.alertPopup(error.response.data);
        }
      })
    },

    getVehicleModelInfo: function () { // 차량 모델 정보 읽어와서 view에 매핑...
      // console.log("@~~ getVehicleModelInfo " + this.mode);
      //let resultVM = [];

      VEHICLE_API.getVehicleInfo({   //web/vehicleModel
        onSuccess: (response) => {

          response.data.vehicleModelList.forEach((vm) => {
            this.vehicleModelList.push({
              vehicleModelId: vm.vehicleModelId,
              modelName: vm.modelName
            });
          });

        },
        onError: (error) => {
          console.error('vehicle 모델 조회 실패');
          PopupUtil.alertPopup(error.response.data);
        }
      });
    },

    getRiderProfileImage: function () {
      if (this.riderDetail.riderId) {
        RIDER_API.getRiderProfileImage(this.riderDetail.riderId, {
          onSuccess: (response) => {
            if (response.data && response.data.length > 0) {
              // 프로필 사진은 변경했으나 URL은 동일하게 내려와 Cache된 이전 사진이 디스플레이되는 문제가 있어
              // url에 "?date=현재시간"을 붙여서 cache되지 않도록 수정했습니다.
              this.profileImgFileUrl = response.data + "?date=" + new Date().toISOString();
            }
          },
          onError: (error) => {
            // 등록된 프로필 사진이 없는 경우
            // alert(error.response.data);
          }
        });
      }
    },

    async getMoceanLastVehicleStatus() {
      if (this.riderDetail.licensePlate) {
        const response = await RIDER_API.getMoceanLastVehicleStatusSync(this.riderDetail.licensePlate);
        if (response && response.data) {
          this.moceanVehicleStatus = response.data;

          this.moceanVehicleStatus.engineText = Util.getTextFromMoceanEngineValue(this.moceanVehicleStatus.engine);
          this.moceanVehicleStatus.chargingText = Util.getTextFromMoceanChargingValue(this.moceanVehicleStatus.charging);
          this.moceanVehicleStatus.chargingRemainTime = Util.getMoceanChargingRemainTime(
              this.moceanVehicleStatus.charging,
              this.moceanVehicleStatus.evCharging,
              this.moceanVehicleStatus.fastChargingRemainTime,
              this.moceanVehicleStatus.slowChargingRemainTime
          );
          if (this.moceanVehicleStatus.temperatureFirst == null) {
            this.moceanVehicleStatus.temperatureFirst = "-";
          }
          if (this.moceanVehicleStatus.temperatureSecond == null) {
            this.moceanVehicleStatus.temperatureSecond = "-";
          }
        }
      }
    },


    fillOutDemoFormData: function () {
      if (!this.form.name) {
        this.form.name = DemoUtil.getRandomEnglishName();
      }

      if (!this.form.licensePlate) {
        this.form.licensePlate = DemoUtil.getRandomCarPlateNumberEnglish();
      }

      if (!this.form.mobile) {
        this.form.mobile = DemoUtil.getRandomMobileNumber();
      }
    },

    saveRiderAndVehicle: function () {
      console.log(" saveRiderAndVehicle ");
      // if (this.groupKey=='권역선택') {
      // 	alert('권역을 선택해 주세요.');
      // 	this.$refs.groupKey.focus();
      // 	return;
      // }

      if (isAnonymous()) {//[demo]
        this.fillOutDemoFormData();
      }

      // if ( !this.form.name ) {
      // 	PopupUtil.alertPopup(_t('기사이름을 입력해 주세요.'));
      // 	this.$refs.name.focus();
      // 	return;
      // }
      //
      // if ( !this.form.licensePlate)
      // {
      // 	PopupUtil.alertPopup(_t('기사번호를 입력해 주세요.'));
      // 	this.$refs.licensePlate.focus();
      // 	return;
      // }
      //
      // if ( !this.form.mobile ) {
      // 	PopupUtil.alertPopup(_t('전화번호를 입력해 주세요.'));
      // 	this.$refs.mobile.focus();
      // 	return;
      // }

      if (!this.form.milesType || this.form.milesType === _t('담당유형 선택')) {
        PopupUtil.alertPopup(_t('담당유형을 선택해 주세요.'));
        this.$refs.milesType.focus();
        return;
      }

      if (this.groupKey == Constant.NEW_GROUP_KEYWORD_NAME && !this.form.groupName) {
        PopupUtil.alertPopup(_t('권역을 입력해 주세요.'));
        this.$refs.groupName.focus();
        return;
      }

      if (this.form.groupName === Constant.NEW_GROUP_KEYWORD_NAME && this.form.autoCreate) {
        PopupUtil.alertPopup(_t('자동 배차 기사는 항상 그룹명이 존재해야 합니다.'));
        return;
      }

      if (!this.form.workingStartAddress) {
        PopupUtil.alertPopup(_t('근무시작위치를 입력해 주세요.'));
        this.$refs.workingStartAddress.focus();
        return;
      }
      if (!this.form.vehicleType || this.form.vehicleType === _t('차량타입 선택')) {
        PopupUtil.alertPopup(_t('차량타입을 선택해 주세요.'));
        this.$refs.vehicleType.focus();
        return;
      }

      if (this.mode === "ra" || this.mode === "aw")
        this.form.projectId = null;

      if (this.groupKey != Constant.NEW_GROUP_KEYWORD_NAME) {
        // ALOA-240 권역에 묶여 있지 않은 기사들은 "권역선택안함" 권역에 속하도록 하게 한다.
        this.form.groupName = this.groupKey;
      }

      if (this.form.groupName == Constant.NO_GROUP_KEYWORD_NAME) {
        this.form.groupName = null;
      }

      window.parent.app.$emit(EVENT.MAIN.CLOSE_DRIVER_DETAIL_POPUP, {
        riderId: this.form.riderId,
        index: this.index,
        fromCallback: false
      });

      //기어 업무 추가함.
      this.form.workAuthority = this.workAuthority;

      const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap
      if (this.mode === "a") {// 목적지 추가
        aloaMap.$emit(EVENT.MAP.ADD_PROJECT_RIDER, {'rider': this.form, 'isRiderAdd': false});
      } else if (this.mode === "ra") {// Manager Rider (Rider add)
        aloaMap.$emit(EVENT.MAP.ADD_RIDER, {'rider': this.form, 'isRiderAdd': true, 'isEdit': false});
      } else if (this.mode === "aw") {// Manager Rider (Rider Edit)
        aloaMap.$emit(EVENT.MAP.ADD_RIDER, {'rider': this.form, 'isRiderAdd': false, 'isEdit': true});
      } else { // 목적지 수정
        aloaMap.$emit(EVENT.MAP.MODIFY_PROJECT_RIDER, this.form);
      }
    },

    checkLicensePlate: function () {
      if (this.mode == 'r')
        return;
      if (this.form.licensePlate && this.form.licensePlate.length > 0) {
        let _this = this;
        TMS_WEB_API.checkLicensePlateExists(this.form.licensePlate, {
          onSuccess: (response) => {
            _this.licensePlateExists = response.data.data;
          },
          onError: (error) => {
            console.error('기사번호 조회 실패');
          }
        });
      }
    },

    checkRiderMobile: function () {
      //숫자이외의 값은 못넣도록제어
      if (this.mode == 'r')
        return;
      this.$refs.mobile.value = this.$refs.mobile.value.replace(/[^0-9]/g, "");

      //backEnd에 이 전화번호가 존재 하는지 체크
      if (this.form.mobile && this.form.mobile.length > 0) {
        let _this = this;

        TMS_WEB_API.checkRiderMobileExists(this.form.mobile, {
          onSuccess: (response) => {
            _this.mobileExists = response.data.data;
          },
          onError: (error) => {
            console.error('기사 전화번호 조회 실패');
          }
        });
      }
    },

    getFocus: function () {
      this.mobileFormatError = false;
    },

    losFocus: function () {
      if (!this.isCellPhone(this.form.mobile)) {
        this.mobileFormatError = true;
      } else {
        this.mobileFormatError = false;
      }
    },

    isCellPhone: function (p) {
      p = p.split('-').join('');
      var regPhone = /^((01[1|6|7|8|9])[1-9]+[0-9]{6,7})|(010[1-9][0-9]{7})$/;
      return regPhone.test(p);
    },


    isEditButtonVisible: function () {
      return (this.mode == 'r')
          && (this.form.projectStatus !== Constant.PROJECT_STATUS.DONE)
          && (!this.form.projectAttribute || !this.form.projectAttribute.isReadOnly)
          && ((Util.isHyundaiUserCompany() || Util.isToHomeUserCompany()) ? true : Util.isThisProjectUserSameToMe(this.form.projectUserId));
    },

    onClickSearchAddress: async function (addr, showSuccessPopup) {
      if (!addr || addr.length == 0)
        return true;

      const response = await SEARCH_API.getSmartSearchByString("kr", addr);
      if (response && response.data) {
        if (showSuccessPopup) {
          PopupUtil.alertPopup(_t("올바른 주소 형식입니다."));
        }
        return true;

      } else {
        PopupUtil.alertPopup(_t("주소 형식이 올바르지 않습니다. 다시 입력해주세요."));
        return false;
      }
    },

    getRiderShowStatus: function () {
      return RiderUtil.getRiderShowStatus(this.rider);
    },

    onEdit: function () {
      // alert( this.rider.name);
      if (this.mode == 'w') {
        alert(_t("알 수 없는 오류가 발생하였습니다. 다시 시도해주세요."));
        this.riderDetailPopupClosed();
      }

      this.mode = 'w';

    },

    onClose: function (event) {
      window.parent.app.$emit(EVENT.MAIN.CLOSE_DRIVER_DETAIL_POPUP, {
        riderId: this.form.riderId,
        index: this.index  /* no popupIndex */,
        fromCallback: true
      });
    },

    convertMilesTypeText: function (milesType) {
      let ret = "";
      if (milesType === _t("거점")) {
        ret = _t("물류거점");
      } else {
        ret = _t("최종방문지");
      }

      return ret;
    },

    convertGroupText: function (option) {
      return Util.convertGroupText(option);
    },

    convertVehicleTypeText: function (vehicleType) {
      let ret = "";
      switch (vehicleType) {
        case "트럭_카고":
          ret = _t("트럭_카고");
          break;
        case "트럭_탑":
          ret = _t("트럭_탑");
          break;
        case "트럭_윙바디":
          ret = _t("트럭_윙바디");
          break;
        case "트럭_냉장":
          ret = _t("트럭_냉장");
          break;
        case "트럭_냉동":
          ret = _t("트럭_냉동");
          break;
        case "트럭_리프트":
          ret = _t("트럭_리프트");
          break;
        case "트럭_무진동":
          ret = _t("트럭_무진동");
          break;
        case "승합차":
          ret = _t("승합차");
          break;
        case "승용차":
          ret = _t("승용차");
          break;
        case "다마스":
          ret = _t("다마스");
          break;
        case "이륜차":
          ret = _t("이륜차");
          break;
        case "도보":
          ret = _t("도보");
          break;
        default:
          ret= vehicleType;
          break;
      }

      return ret;
    },

    convertFuelTypeText: function (fuelType) {
      let ret = "";
      switch (fuelType) {
        case "휘발유":
          ret = _t("휘발유");
          break;
        case "경유":
          ret = _t("경유");
          break;
        case "LPG":
          ret = _t("LPG");
          break;
        case "LNG":
          ret = _t("LNG");
          break;
        case "CNG":
          ret = _t("CNG");
          break;
        case "수소전지":
          ret = _t("수소전지");
          break;
        case "전기":
          ret = _t("전기");
          break;
        default:
          ret = fuelType;
          break;
      }

      return ret;
    },

    performRiderSending() {
      this.sendEventToMap(EVENT.MAP.PERFORM_RIDER_SENDING, this.rider);
      this.clearSelectionMode();
    },

    sendRiderPushMessage() {
      console.log(this.rider);
      console.log(this.form.projectId);
      window.parent.app.$emit(EVENT.MAIN.OPEN_RIDER_SEND_MESSAGE_POPUP, this.rider);
    },

    sendEventToMap(event, data) {
      try {
        const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;
        aloaMap.$emit(event, data);
      } catch (e) {
        console.error("sendEventToMap exception: " + e);
      }
    },

    clearSelectionMode() {
      this.sendEventToMap(EVENT.MAP.SELECTION_MODE_CHANGED, 0);
      window.parent.app.$refs.leftPanel.$emit(EVENT.PANEL.SELECTION_MODE_CHANGED, 0);//[demo] tab모드가 0이되면 left panel에서도 닫아줘야 한다.
    },

    riderStatusText() {
      return RiderUtil.getRiderShowStatusText(this.rider)
    },

    onDeliverySet() {
      console.log("onDeliverySet");
      this.workAuthorityData.delivery = !this.workAuthorityData.delivery;
      this.getWorkAuthority();
    },

    onScanSet() {
      console.log("onScanSet");
      this.workAuthorityData.scan = !this.workAuthorityData.scan;
      this.getWorkAuthority();
    },

    getWorkAuthority() {
      if (this.workAuthorityData.delivery && this.workAuthorityData.scan)
        this.workAuthority = "DELIVERY_AND_SCANNING";
      else if (this.workAuthorityData.delivery)
        this.workAuthority = "DELIVERY";
      else if (this.workAuthorityData.scan)
        this.workAuthority = "SCANNING";
      else
        this.workAuthority = "";
    },

    changedTranRiderType(isRFC){
      if(isRFC)
        return _t('자동배차기사')
      else
      return _t('일반기사');
    },
  },
};
