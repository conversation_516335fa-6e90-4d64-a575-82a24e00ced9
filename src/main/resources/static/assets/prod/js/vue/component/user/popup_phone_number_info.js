var popupPhoneNumberInfo = {
    template: `
    <div id="popup-account-info-container">
        <div class="modal-mask">
            <div class="modal_full"></div>
            <div class="popup_number_changed_info" v-if="!isChangePwSuccess">
                <div class="phone_number_info_title">{{$t("사용자 핸드폰 번호 입력")}}</div>
                <button class="phone_number_info_window_close" @click="hidePasswordChangedInfoPopup"></button>
                <div class="phone_number_info_title_line"></div>

                <div class="phone_changed_info_pw_area1">
                    <div class="phone_changed_info_index">{{$t("로그인 아이디")}}</div>
                    <input readonly disabled v-model="user.email" class="phone_changed_info_pw_input" ref="email">
                </div>
                
                <div class="phone_changed_info_pw_area2">
                    <div class="phone_changed_info_index">{{$t("신규 핸드폰 번호")}}</div>
                    <div class="phone_changed_info_auth_icon"></div>
                    <input type="text" name="" maxlength="11" :placeholder="$t('- 넣지마세요')" class="phone_changed_info_phone_input" ref="phoneNumber" v-model="phoneNumber">
                    <button class="phone_changed_info_phone_btn_r" @click="sendAuthKey" v-if="isPhoneInput" style="cursor: pointer">{{$t("인증 요청")}}</button>
                    <button class="phone_changed_info_phone_btn_g" v-else style="cursor: text">{{$t("인증 요청")}}</button>
                </div>
       
                <div class="phone_changed_info_pw_area2">
                    <div class="phone_changed_info_index">{{$t("인증 번호")}}</div>
                    <div style="">
                    <div class="phone_changed_info_auth_icon"></div>
                    <input type="text" name="" maxlength="5" :placeholder="$t('인증 번호 넣어주세요')" class="phone_changed_info_pw_input" ref="authKey" v-model="authKey">
                    </div>
                </div>
                       
                <div class="phone_changed_info_pw_alert" v-if="isError">{{errorMessage}}</div>
                <button class="phone_changed_info_button_r" @click="changePhoneNumber" v-if="isAllInputPhoneNumber" style="cursor: pointer">{{$t("저장")}}</button>
                <button class="phone_changed_info_button_g" v-else style="cursor: text">{{$t("저장")}}</button>
            </div>
            
            <div v-if="isChangePwSuccess">
                <div class="modal_full"></div>        
                <div class="popup_account_check">
                    <div class="account_check_icon"></div>
                    <div class="account_check_text1">{{$t("핸드폰 입력")}}</div>
                    <div class="account_check_text2">{{$t("핸드폰 번호가 변경되었습니다.")}}</div>
                </div>
            </div>
        </div>
    </div>
    `,
    props: {
        user: Object,
    },
    data: function() {
        return {
            userInfo : null,
            isChangePwSuccess: false,
            phoneNumber : '',
            authKey : '',
            isError: false,
            errorMessage: '',
        };
    },
    created: function() {

    },
    mounted() {
        console.log(this.user)
        this.phoneNumber = this.user.phoneNumber ? this.user.phoneNumber : '';
    },
    computed: {
        isAllInputPhoneNumber : function() {
            this.invalidPhoneNumber(this.phoneNumber)
            this.invalidAuthKey(this.authKey)
            if ((this.phoneNumber.length === 11 || this.phoneNumber.length === 10) &&
              (this.authKey.length === 5)) {
                return true;
            }
            return false;
        },
        isPhoneInput : function () {
            this.invalidPhoneNumber(this.phoneNumber)
            if (this.phoneNumber.length === 11 || this.phoneNumber.length === 10) {
                return true;
            }
            return false;
        }
    },
    methods: {
        sendAuthKey() {
            const _this = this;

            USER_API.sendUserAuthKey(this.phoneNumber, {
                onSuccess: (response) => {
                    this.$refs.phoneNumber.setAttribute('disabled', true);
                    this.$refs.phoneNumber.setAttribute('readonly', true);
                    this.$refs.authKey.focus();
                },
                onError: (error) => {
                    _this.showErrorMessage(error.response.data);
                }
            })
        },

        changePhoneNumber() {
            if (!this.phoneNumber) {
                this.showErrorMessage(_t('전화번호 입력해주세요'), 5);
                this.$refs.phoneNumber.focus();
                return;
            }

            const _this = this;
            const body = {
                email : this.user.email,
                phoneNumber : this.phoneNumber,
                authKey : this.authKey,
            }

            USER_API.changeUserPhoneNumber(body, {
                onSuccess: (response) => {
                    _this.isChangePwSuccess = true;
                    _this.$store.dispatch('setPhoneNumber', this.phoneNumber);
                    setTimeout(() => {
                        _this.hidePasswordChangedInfoPopup();
                    }, 2000);
                },
                onError: (error) => {
                    _this.showErrorMessage(error.response.data);
                }
            })
        },

        hidePasswordChangedInfoPopup() {
            this.$emit('show-popup-phone-number-info', false);
        },

        showErrorMessage(msg, hideDelaySecond) {
            const _this = this;
            _this.isError = true;
            _this.errorMessage = msg;

            if (!hideDelaySecond) return;

            setTimeout(() => {
                _this.isError = false;
                _this.errorMessage = "";
            }, hideDelaySecond * 1000)
        },
        invalidPhoneNumber(phoneNumber) {
            const strongRegex = /[^0-9]/g;
            this.phoneNumber = phoneNumber.replace(strongRegex, "");
            console.log(this.phoneNumber)
        },
        invalidAuthKey(authKey) {
            const strongRegex = /[^0-9]/g;
            this.authKey = authKey.replace(strongRegex, "");
            console.log(this.authKey)
        }
    }
};