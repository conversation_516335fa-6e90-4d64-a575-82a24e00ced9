var reportGroupNameListPopup = {
    template: `
<div>
    <div id="report_driver_list_body">
        <div class="bg"></div>
        <div class="report_page_2">
            <div class="report_info_2">
                <button class="or02_window_close" @click="closePopup"></button>
                <div class="report_data_2">
                    <div class="report_data_label_1_2">{{$t("모든 권역")}}</div>
                    <div class="report_data_label_2_2">{{statGroupNameTotalDeliveries.totalElements}}</div>
                </div>
                <input type="search" name="" class="report_data_search_2" :placeholder="$t('권역명')" v-model="searchKeyword" @keyup.enter="searchKeywordSubmit">
                <button class="report_data_search_2_icon" @click="searchKeywordSubmit"></button>
                <div class="report_data_line_2"></div>
               <button class="report_data_view_area_2" @click="showReportDriverChart2Popup(statGroupNameTotalDeliveries, totalPages, currentPage, pages, searchKeyword)">
                    {{$t("전체 요약보기")}}
                    <div class="report_data_view_icon_2"></div>
                </button>
            </div>

            <div style="position: relative; height: calc(100% - 80px); top: 80px; overflow: scroll;">
        
                <div class="report_bar_chart_area_1_2" v-for="(statGroupNameTotalDelivery, index) in statGroupNameTotalDeliveries.content" :key="statGroupNameTotalDelivery.groupName">
                    <ul>
                        <li>
                            <div class="report_bar_chart_area_u_2">
                                <div class="report_li_title">
                                    <div class="report_li_icon_1" @click="showReportDriverChart1Popup(statGroupNameTotalDelivery)"></div>
                                    <div class="report_li_title_5">{{statGroupNameTotalDelivery.groupName}}</div>
                                </div>
    
                                <div class="report_li_group_name_contents_area">
                                    <div class="report_li_contents">
                                        <div class="report_li_c_label_1">
                                            <div class="report_li_c_icon_1"></div>
                                                &nbsp{{$t("총 참여 프로젝트")}} &nbsp
                                            <div class="report_li_c_label_1_1">{{statGroupNameTotalDelivery.numOfProjects}}</div>
                                                &nbsp | &nbsp
                                            <div class="report_li_c_icon_2"></div>
                                                &nbsp{{$t("배송")}} &nbsp
                                            <div class="report_li_c_label_1_1">{{statGroupNameTotalDelivery.numOfAllDeliveries}}</div>
                                                &nbsp | &nbsp
                                            <div class="report_li_c_icon_3"></div>
                                                &nbsp{{$t("주문 총 금액")}} &nbsp
                                            <div class="report_li_c_label_1_1">{{statGroupNameTotalDelivery.totalOrderAmountDeliveries.toLocaleString()}}{{$t("원")}}</div>
                                        </div>
    
                                        <div class="report_li_c_label_2">
                                            <div class="report_li_c_text_1_1">
                                                <div class="report_circle_1"></div>
                                                <div class="report_li_c_text_1_2">
                                                    {{$t("배송완료")}}
                                                </div>
                                                <div class="report_li_c_text_1_3">{{statGroupNameTotalDelivery.numOfCompletedDeliveries}}</div>
                                            </div>
                                            |
                                            <div class="report_li_c_text_1_1">
                                                <div class="report_circle_2"></div>
                                                <div class="report_li_c_text_1_2">
                                                    {{$t("미배송")}}
                                                </div>
                                                <div class="report_li_c_text_1_3">{{statGroupNameTotalDelivery.numOfWaitingDeliveries + statGroupNameTotalDelivery.numOfReadyDeliveries + statGroupNameTotalDelivery.numOfGoingDeliveries + statGroupNameTotalDelivery.numOfServicingDeliveries + statGroupNameTotalDelivery.numOfRejectedDeliveries}}</div>
                                            </div>
                                            |
                                            <div class="report_li_c_text_1_1">
                                                <div class="report_circle_3"></div>
                                                <div class="report_li_c_text_1_2">
                                                    {{$t("배송실패")}}
                                                </div>
                                                <div class="report_li_c_text_1_3">{{statGroupNameTotalDelivery.numOfFailedDeliveries}}</div>
                                            </div>
                                        </div>
    
                                        <div class="report_li_c_chart">
                                            <div class="chart-container">
                                                <div id="app2">
                                                    <horizontal-bar-chart
                                                        :chartData="statGroupNameTotalDelivery.chartData"
                                                        :options="chartOptions"
                                                    />
                                                </div>
                                            </div>
                                            <input type="checkbox" name="" :id="'c_input_1' + index" class="report_li_c_input" v-model="statGroupNameTotalDelivery.showDetail" @change="getStatGroupNameDailyProjectDeliveries(statGroupNameTotalDelivery)">
    
                                            <label :for="'c_input_1' + index" class="report_li_c_label">                    
                                            </label>
    
                                            <div class="report_li_c_button"></div>
                                        </div>
                                    </div>            
                                </div>
                            </div>
                            
                            <div class="report_li_c_contents_1" v-if="statGroupNameTotalDelivery.showDetail">
                                <div class="report_li_c_tab_area">
                                    <div class="report_li_c_tab_1">
                                        <div class="report_li_c_tab_icon_1"></div>
                                            {{$t("총 근무시간")}}
                                    </div>
                                    <div class="report_li_c_tab_num">{{(statGroupNameTotalDelivery.workingSeconds / 60).toFixed(0)}} {{$t("분")}}</div>
                                        &nbsp|&nbsp
                                    <div class="report_li_c_tab_1">
                                        <div class="report_li_c_tab_icon_2"></div>
                                            {{$t("총 누적거리")}}
                                    </div>
                                    <div class="report_li_c_tab_num">{{(statGroupNameTotalDelivery.workingMeters/1000).toFixed(4)}}Km</div>
                                        &nbsp|&nbsp
                                    <div class="report_li_c_tab_1">
                                        <div class="report_li_c_tab_icon_3"></div>
                                            {{$t("총 배송계획 준수율")}}
                                    </div>
                                    <div class="report_li_c_tab_num">{{statGroupNameTotalDelivery.numOfAllDeliveries == 0 ? 0 : (statGroupNameTotalDelivery.numOfCompletedDeliveries * 100 / statGroupNameTotalDelivery.numOfAllDeliveries).toFixed(0)}}%</div>
                                    <div class="report_li_c_tab_２">
                                        {{$t("권역 결과 엑셀로 내려받기")}}
                                        <button class="report_li_c_tab_icon_4"  @click="exportStatExcel(statGroupNameTotalDelivery)"></button>
                                    </div>
                                </div>
                                <div class="report_li_c_tab_area_1">
                                    <table>
                                        <thead>
                                            <tr>
                                                <td class="report_li_c_td_1">{{$t("날짜")}}</td>
                                                <td class="report_li_c_td_7">{{$t("프로젝트")}}</td>
                                                <td class="report_li_c_td_8">{{$t("주문총액")}}</td>
                                                <td class="report_li_c_td_3">{{$t("근무시간")}}</td>
                                                <td class="report_li_c_td_3">{{$t("누적거리")}}</td>
                                                <td class="report_li_c_td_2">{{$t("준수율")}}</td>
                                            </tr> 
                                        </thead>
                                    </table>
                                </div>
                                <div class="report_li_c_tab_area_1_1">
                                    <table>
                                        <tbody>
                                            <tr v-for="(statGroupNameDailyProjectDelivery, index) in statGroupNameTotalDelivery.statGroupNameDailyProjectDeliveries.content" :key="statGroupNameDailyProjectDelivery.projectId">
                                                <td class="report_li_c_td_4">{{statGroupNameDailyProjectDelivery.date}}</td>
                                                <td class="report_li_c_td_9">{{statGroupNameDailyProjectDelivery.projectName}}</td>
                                                <td class="report_li_c_td_10">{{statGroupNameDailyProjectDelivery.totalOrderAmountDeliveries.toLocaleString()}} {{$t("원")}}</td>
                                                <td class="report_li_c_td_6">{{(statGroupNameDailyProjectDelivery.workingSeconds / 60).toFixed(0)}} {{$t("분")}}</td>
                                                <td class="report_li_c_td_6">{{(statGroupNameDailyProjectDelivery.workingMeters / 1000).toFixed(4)}}Km</td>
                                                <td class="report_li_c_td_5">
                                                    <div class="report_li_c_label_2">
                                                        <div class="report_li_c_text_1_1">
                                                            <div class="report_circle_1"></div>
                                                            <div class="report_li_c_text_1_2">
                                                                {{$t("배송완료")}}
                                                            </div>
                                                            <div class="report_li_c_text_1_3">{{statGroupNameDailyProjectDelivery.numOfCompletedDeliveries}}</div>
                                                        </div>
                                                            |
                                                        <div class="report_li_c_text_1_1">
                                                            <div class="report_circle_2"></div>
                                                            <div class="report_li_c_text_1_2">
                                                                {{$t("미배송")}}
                                                            </div>
                                                            <div class="report_li_c_text_1_3">{{statGroupNameDailyProjectDelivery.numOfWaitingDeliveries + statGroupNameDailyProjectDelivery.numOfReadyDeliveries + statGroupNameDailyProjectDelivery.numOfGoingDeliveries + statGroupNameDailyProjectDelivery.numOfServicingDeliveries + statGroupNameDailyProjectDelivery.numOfRejectedDeliveries}}</div>
                                                        </div>
                                                            |
                                                        <div class="report_li_c_text_1_1">
                                                            <div class="report_circle_3"></div>
                                                            <div class="report_li_c_text_1_2">
                                                                {{$t("배송실패")}}
                                                            </div>
                                                            <div class="report_li_c_text_1_3">{{statGroupNameDailyProjectDelivery.numOfFailedDeliveries}}</div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="project_manage_page_area" v-if="totalPages > 0">
                    <button class="project_manage_page_first" @click="firstPage"></button>
                    <button class="project_manage_page_pre" @click="prevPage"></button>
                    <button class="st_search_list_pageNum" :id="'searchRiderPage_'+(currentIndex-1)" :class="(currentIndex-1) == currentPage ? 'pick' : ''" v-for="currentIndex in pages" @click="movePage(currentIndex-1)">{{currentIndex}}</button>
                    <button class="project_manage_page_next" @click="nextPage"></button>
                    <button class="project_manage_page_end" @click="endPage"></button>
                </div>	
            </div>
        </div>
    </div>

    <report-group-name-chart-1-popup
        v-if="popup.reportGroupNameChart1.isShow" 
        :statGroupNameTotalDelivery="popup.reportGroupNameChart1.statGroupNameTotalDelivery" 
        :ref="reportGroupNameChart1Popup"
        @dismiss="dismissReportDriverChart1Popup"/>
        
    <report-group-name-chart-2-popup
        v-if="popup.reportGroupNameChart2.isShow" 
        :statGroupNameTotalDeliveries="popup.reportGroupNameChart2.statGroupNameTotalDeliveries"
        :totalPages="popup.reportGroupNameChart2.totalPages" 
        :currentPage="popup.reportGroupNameChart2.currentPage"
        :pages="popup.reportGroupNameChart2.pages"
        :searchKeyword ="popup.reportGroupNameChart2.searchKeyword"
        :ref="reportGroupNameChart2Popup"
        @dismiss="dismissReportDriverChart2Popup"/>
        
</div>
    `,

    props: {},

    components: {
        'report-group-name-chart-1-popup': reportGroupNameChart1Popup,
        'report-group-name-chart-2-popup': reportGroupNameChart2Popup,
    },

    beforeCreate: function () {
    },

    created: function () {
        EVENT.registerEventHandler(this, this.eventHandlers);
    },

    mounted: function () {
        console.log('Report Driver List Popup Mounted!!!');
        this.getStatGroupNameTotalDeliveries();
    },

    data: function () {
        return {
            eventHandlers: [
                {event: EVENT.REPORT_DRIVER_LIST_POPUP.SHOW, handler: this.setShow},
            ],
            isShow: false,
            popup: {
                reportGroupNameChart1: {
                    statGroupNameTotalDelivery: null,
                    isShow: false,
                },
                reportGroupNameChart2: {
                    statGroupNameTotalDeliveries: null,
                    totalPages:0,
                    currentPage:0,
                    pages : [],
                    isShow: false,
                    searchKeyword: null,
                },
            },
            searchKeyword: null,

            statGroupNameTotalDeliveries: {content: null},

            //page로 변경 하자.............
            totalPages: 0,
            page: 0,
            pageDisplaySize:10,

            currentPage : 0,
            pages : [],

            chartOptions: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    display: false,
                },
                tooltips: {
                    enabled: false,
                },
                scales: {
                    xAxes: [{
                        display: false,
                        gridLines: {
                            display: false,
                        },
                        stacked: true,
                        ticks: {
                            stepSize: 1,
                        },
                    }],
                    yAxes: [{
                        display: false,
                        gridLines: {
                            display: false,
                        },
                        stacked: true,
                    }],
                },
            },
        };
    },

    computed: {
    },

    methods: {

        sendEventToMap: function(event, data) {
            try {
                const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;
                aloaMap.$emit(event, data);
            } catch(e) {
                console.error("sendEventToMap Exception: " + e);
            }
        },

        closePopup: function () {
            this.$root.popup.reportGroupNameList.isShow = false;
        },

        onCloseBtnClick: function () {
            this.setShow(false);
        },

        setShow: function (isShow) {
            this.isShow = isShow;
        },

        showReportDriverChart1Popup: function (statGroupNameTotalDelivery) {
            this.popup.reportGroupNameChart1.statGroupNameTotalDelivery = statGroupNameTotalDelivery;
            this.popup.reportGroupNameChart1.isShow = true;
        },

        dismissReportDriverChart1Popup: function () {
            this.popup.reportGroupNameChart1.isShow = false;
            this.popup.reportGroupNameChart1.statGroupNameTotalDelivery = null;
        },

        showReportDriverChart2Popup: function (statGroupNameTotalDeliveries, totalPages, currentPage, pages, searchKeyword ) {
            this.popup.reportGroupNameChart2.statGroupNameTotalDeliveries = statGroupNameTotalDeliveries;
            this.popup.reportGroupNameChart2.totalPages = totalPages;
            this.popup.reportGroupNameChart2.currentPage = currentPage;
            this.popup.reportGroupNameChart2.pages = pages;
            this.popup.reportGroupNameChart2.searchKeyword = searchKeyword;
            this.popup.reportGroupNameChart2.isShow = true;
        },

        dismissReportDriverChart2Popup: function (currentPage) {
            this.popup.reportGroupNameChart2.isShow = false;
            this.popup.reportGroupNameChart2.statGroupNameTotalDeliveries = null;
            if(currentPage !== this.currentPage){
                this.currentPage = currentPage;
                this.getStatGroupNameTotalDeliveries();
            }
        },

        searchKeywordSubmit: function () {
            this.getStatGroupNameTotalDeliveries(true);
        },

        getStatGroupNameTotalDeliveries: function (isSearchKeyWord = false) {
            console.log('getStatRiderTotalDeliveries');

            const _this = this;
            PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

            if (isSearchKeyWord) {
                _this.currentPage = 0;
                _this.pages = [];
            }

            STAT_API.getStatGroupNameTotalDeliveries(null, this.currentPage, this.pageDisplaySize, this.searchKeyword, {
                onSuccess: (response) => {
                    response.data.content.forEach(s => s.showDetail = false);
                    response.data.content.forEach(_this.makeChartData);
                    response.data.content.forEach(s => {
                        s.statGroupNameDailyProjectDeliveries = {
                            content: null,
                        };
                    });

                    _this.statGroupNameTotalDeliveries = response.data;
                    _this.totalPages = response.data.totalPages;

                    _this.pages = PageUtil.getDisplayedPageIndexList(_this.currentPage, _this.totalPages, PageUtil.const.DISPLAYED_10PAGES);

                    // if(_this.pages.length == 0)
                    // {
                    //     for (let index = this.currentPage + 1; index < this.currentPage + 11; index++) {
                    //         if (index < _this.totalPages() + 1)
                    //             _this.pages.push(index);
                    //     }
                    // }

                    PopupUtil.dismissLoadingPopup();
                },
                onError: (error) => {
                    PopupUtil.dismissLoadingPopup();
                    PopupUtil.alertPopup('권역의 전체 배송 통계 조회 실패');
                }
            });
        },

        getStatGroupNameDailyProjectDeliveries: function (statGroupNameTotalDelivery) {
            console.log("getStatGroupNameDailyProjectDeliveries: " + statGroupNameTotalDelivery.groupName + ", " + statGroupNameTotalDelivery.showDetail);

            if (statGroupNameTotalDelivery.statGroupNameDailyProjectDeliveries.content == null && statGroupNameTotalDelivery.showDetail == true) {
                // PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

                STAT_API.getStatGroupNameDailyProjectDeliveries(statGroupNameTotalDelivery.groupName, null, {
                    onSuccess: (response) => {
                        statGroupNameTotalDelivery.statGroupNameDailyProjectDeliveries = response.data;

                        // PopupUtil.dismissLoadingPopup();
                    },
                    onError: (error) => {
                        // PopupUtil.dismissLoadingPopup();
                        PopupUtil.alertPopup('권역의 일간/프로젝트별 배송 통계 조회 실패');
                    }
                });
            }
        },

        makeChartData: function (statGroupNameTotalDelivery, index) {
            statGroupNameTotalDelivery.chartData = {};
            statGroupNameTotalDelivery.chartData.datasets = [];
            statGroupNameTotalDelivery.chartData.datasets.push({
                data: [statGroupNameTotalDelivery.numOfCompletedDeliveries],
                backgroundColor: '#CAF419',
                stack: 'Stack' + statGroupNameTotalDelivery.groupName,
            });
            statGroupNameTotalDelivery.chartData.datasets.push({
                data: [statGroupNameTotalDelivery.numOfWaitingDeliveries + statGroupNameTotalDelivery.numOfReadyDeliveries + statGroupNameTotalDelivery.numOfGoingDeliveries + statGroupNameTotalDelivery.numOfServicingDeliveries + statGroupNameTotalDelivery.numOfRejectedDeliveries],
                backgroundColor: '#FFA346',
                stack: 'Stack' + statGroupNameTotalDelivery.groupName,
            });
            statGroupNameTotalDelivery.chartData.datasets.push({
                data: [statGroupNameTotalDelivery.numOfFailedDeliveries],
                backgroundColor: '#F95F7B',
                stack: 'Stack' + statGroupNameTotalDelivery.groupName,
            });
        },
        exportStatExcel: function(statGroupNameTotalDelivery) {
            console.log("exportStatExcel: " + statGroupNameTotalDelivery.groupName);
            PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");
            API.downloadFileByGetEx(Url.WEB.STAT_GROUP_NAME_DAILY_PROJECT_EXCEL_DELIVERIES + "?groupName="
                + statGroupNameTotalDelivery.groupName, null, null, null, {
                onSuccess: (response) => {
                    PopupUtil.dismissLoadingPopup();
                    if(response.status == 204) {
                        PopupUtil.alertPopup("권역의 통계가 없습니다.");
                    }
                },
                onError: (error) => {
                    //console.log('error.response.data:', JSON.stringify(error.response.data));
                    PopupUtil.dismissLoadingPopup();
                    if(error.message) {
                        PopupUtil.alertPopup(error.name, error.message, () => {
                        });
                    } else {
                        PopupUtil.alertPopup(error.response.data);
                    }
                }
            });
            //window.location.href = Url.WEB.STAT_RIDER_DAILY_PROJECT_EXCEL_DELIVERIES + "?riderId="
            //    + statGroupNameTotalDelivery.riderId;
        },

        firstPage: function () {
            const firstPageIndex = PageUtil.getFirstPageIndex(this.currentPage, this.totalPages);
            if (firstPageIndex != null && firstPageIndex !== this.currentPage) {
                this.currentPage = firstPageIndex;
                this.getStatGroupNameTotalDeliveries();
            }
        },

        prevPage: function () {
            const prevPageIndex = PageUtil.getPrevPageIndex(this.currentPage, this.totalPages, PageUtil.const.DISPLAYED_10PAGES);
            if (prevPageIndex != null && prevPageIndex !== this.currentPage) {
                this.currentPage = prevPageIndex;
                this.getStatGroupNameTotalDeliveries();
            }
        },

        movePage(pageIndex) {
            if (pageIndex != null && pageIndex !== this.currentPage) {
                document.getElementById('searchRiderPage_' + this.currentPage).classList.remove('pick');
                this.currentPage = pageIndex;
                this.getStatGroupNameTotalDeliveries();
                document.getElementById('searchRiderPage_' + this.currentPage).classList.add('pick');
            }
        },

        nextPage: function () {
            const nextPageIndex = PageUtil.getNextPageIndex(this.currentPage, this.totalPages, PageUtil.const.DISPLAYED_10PAGES);
            if (nextPageIndex != null && nextPageIndex !== this.currentPage) {
                this.currentPage = nextPageIndex;
                this.getStatGroupNameTotalDeliveries();
            }
        },

        endPage: function () {
            const lastPageIndex = PageUtil.getLastPageIndex(this.currentPage, this.totalPages);
            if (lastPageIndex != null && lastPageIndex !== this.currentPage) {
                this.currentPage = lastPageIndex;
                this.getStatGroupNameTotalDeliveries();
            }
        },
    },
};