var invoiceWelstoryPrintPopup = {
    template: `
    <div>
        <div class="modal-mask">
            <div class="modal-wrapper">
                <div class="confirm_popup">
                    <div class="confirm_popup_header" v-html="$t(header)"></div>
                    <button class="confirm_popup_close" @click="closePrintPopup"></button>
                    <div class="confirm_popup_message" v-html="$t(message)"></div>
                    <div class="confirm_popup_button">
                        <button class="confirm_popup_cancel_button" @click="closePrintPopup">{{$t("닫기")}}</button>
                        <button class="confirm_popup_confirm_button" @click="printStart">{{$t("송장 출력")}}</button>
                    </div>
                </div>
            </div>
            <div style="display:none;" v-model="printFormOption" v-if="printFormOption == 0 ? true : false">
                <div id="printPages" style="width: 1050px;">
                    <div style="-webkit-transform: width: 1050px; height: 700px; page-break-after: always;" v-for="destination in sortingWelstoryDestination(destinations)">
                          <!-- 상품확인서 -->
                      <div style="display: flex;">
                        <!-- 상품확인서 첫 번째 테이블 -->
                        <table style="width: 440px; margin-top: 5px">
                          <tbody>
                            <tr>
                              <td rowspan="3" style="width: 80px; height: 35px; position: relative;">
                                <div style="margin-left: 5px; margin-top: 5px; width: 80px; max-width: 80px; height: 70px; max-height: 70px; background: yellow; position: absolute;top: 0;">
                                  <qr-code :text=destination.qrBarCode :margin="5" size="55"/>
                                </div>
                              </td>
                              <td colspan="2" style="height: 145px;"></td>
                            </tr>
                            <tr>
                              <td style="height: 35px;"></td>
                              <td style="width: 220px; height: 35px; font-size: 19px; font-weight: 700;">1588-6789</td>
                            </tr>
                            <tr style="height: 80px">
                              <td style="height: 35px;"></td>
                              <td style="height: 35px;">
                                <div style="margin-left: 45px; font-size: 19px; font-weight: 700;"> 09 ~ 17시 </div>
                                <div style="margin-top: 35px; margin-left: 75px; font-size: 16px; font-weight: 700;"> 11시 30분 ~ 13시 </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                
                        <!-- 상품확인서 두 번째 테이블 -->
                        <table style="width: 610px; margin-top: 5px">
                          <tbody>
                            <tr>
                              <td style="height: 35px;"></td>
                              <td style="width: 350px;height: 35px; font-size: 19px; font-weight: 700;">{{getDeliveryDate(destination.welstoryDeliveryDate)}}</td>
                              <td rowspan="2" style="width: 115px;height: 35px;font-size: 19px; font-weight: 700;">{{destination.orderNum}} ({{destination.dispatchNumber}})</td>
                            </tr>
                            <tr>
                              <td style="height: 35px;"></td>
                              <td style="height: 35px;font-size: 19px; font-weight: 700;">{{destination.senderCompanyName}} {{getPersonName(setRemoveTrailingNumber(destination.senderName))}}</td>
                            </tr>
                            <tr style="height: 90px">
                              <td style="height: 35px;"></td>
                              <td style="height: 35px;font-size: 19px; font-weight: 700;" colspan="2">
                                <div>{{destination.productName}}</div>
                                <div style="display: flex; width: 60% ;justify-content: space-between; ">
                                    <div>( {{destination.welstoryProductQuantity}} / {{destination.welstoryStorageType}} )</div>
                                </div>  
                              </td>
                            </tr>
                            <tr>
                              <td style="height: 35px;"></td>
                              <td style="height: 35px;font-size: 19px; font-weight: 700;" colspan="2">{{getPersonName(destination.receiverName)}} {{getTelephone(destination.receiverPhoneNumber)}}</td>
                            </tr>
                            <tr style="height: 90px">
                              <td style="height: 35px;"></td>
                              <td style="height: 35px;font-size: 19px; font-weight: 700;" colspan="2">
                                <div>{{destination.baseAddress}}</div>
                                <div>{{destination.detailAddress}}</div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    
                      <!-- 공급자용 -->
                      <div style="display: flex; margin-top: 100px">
                        <!-- 공급자용 첫 번째 테이블 -->
                        <table style="width: 580px; height: 35px;">
                          <tbody>
                            <tr>
                              <td rowspan="3" style="width: 80px; height: 35px; position: relative;">
                                <div style="margin-left: 5px; margin-top: 5px; width: 80px; max-width: 80px; height: 70px; max-height: 70px; background: yellow; position: absolute;top: 0;">
                                  <qr-code :text=destination.qrBarCode :margin="5" size="55"/>
                                </div>
                              </td>
                              <td style="height: 80px;"></td>
                              <td style="width: 360px; height: 35px; font-size: 19px; font-weight: 700;word-break: break-all;"> {{destination.deliveryMessage}}</td>
                            </tr>
                            <tr style="height: 110px">
                              <td style="height: 35px;"></td>
                              <td>
                              </td>
                            </tr>
                            <tr>
                              <td style="height: 100px;"></td>
                              <td>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                
                        <!-- 공급자용 두 번째 테이블 -->
                        <table style="width: 470px">
                          <tbody>
                            <tr>
                              <td style="width: 145px; height: 35px; "></td>
                              <td style="height: 35px;font-size: 19px; font-weight: 700;">{{destination.senderCompanyName}} {{destination.senderName}}</td>
                              <td rowspan="2" style="width: 115px; height: 35px; font-size: 19px; font-weight: 700;"> {{destination.orderNum}} ({{destination.dispatchNumber}})</td>
                            </tr>
                            <tr>
                              <td style="height: 35px;"></td>
                              <td style="height: 35px; font-size: 19px; font-weight: 700;">{{getCardString(destination.welstoryBusinessCard)}}</td>
                            </tr>
                            <tr style="height: 70px">
                              <td style="height: 35px;"></td>
                              <td colspan="2" style="height: 35px; font-size: 19px; font-weight: 700;">
                                <div>{{destination.productName}}</div>
                                <div style="display: flex; width: 80%; ">
                                    <div>( {{destination.welstoryProductQuantity}} / {{destination.welstoryStorageType}} )</div>
                                </div>                                  
                              </td>
                            </tr>
                            <tr>
                              <td style="height: 35px;"></td>
                              <td colspan="2" style="height: 35px; font-size: 19px; font-weight: 700;">{{destination.receiverName}} {{destination.receiverPhoneNumber}}</td>
                            </tr>
                            <tr style="height: 80px">
                              <td style="height: 35px; font-size: 19px; font-weight: 700;"></td>
                              <td colspan="2" style="height: 35px;">
                                <div style="font-size: 19px; font-weight: 700; word-break: break-all;">{{destination.baseAddress}}</div>
                                <div style="font-size: 19px; font-weight: 700; word-break: break-all;">{{destination.detailAddress}}</div>                                  
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                </div>
            </div>
        </div>            
    </div>
    `,
    computed: {

        printFormOption: {
            get() {
                return this.$store.getters.getPrintFormIndexOf;
            },
        },

    },

    props: {
        destinations: Array,
    },

    mounted() {
    },

    data: function () {
        return {
            destinations: this.destinations,
            isPrintStart: false,
            header: '송장 출력',
            message: '선택하신 화물에 대해서 송장 출력하시겠습니까?',
        }
    },

    methods: {
        closePrintPopup: function () {
            //기사 관리 페이지 UI 업데이트
            if (this.isPrintStart) {
                const projectPrintList = window.parent.app.$root.popup.projectDeliveryPrintList;
                if (projectPrintList && projectPrintList.isShow) {
                    this.$root.$refs.projectDeliveryPrintList.projectDeliveryList();
                }
            }
            this.$root.popup.invoicePrintPopup.isShow = false;
            this.$emit('dismiss');
        },

        printStart: function () {
            const dleiverIdList = [];
            let _this = this;

            this.destinations.forEach(destination => {
                dleiverIdList.push(destination.deliveryId);
            });

            console.log(this.destinations);

            DELIVERY_API.setDeliveryDeliveryInvoicePrintCount(dleiverIdList, {
                onSuccess: (response) => {
                    console.log(response);
                    _this.isPrintStart = true;
                    printJS({
                        printable: 'printPages',
                        scanStyles: false,
                        type: 'html'
                    });
                    this.closePrintPopup();
                },
                onError: (error) => {
                    console.log(error);
                    this.closePrintPopup();
                    PopupUtil.alertPopup("배송지 정보 문제로 프린터 사용을 할 수 없습니다.");
                }
            });
        },
        getPersonName(name){
            let noSpacesStr = name.replace(/\s/g, "");
            return Util.maskingPersonName(noSpacesStr);
        },
        getTelephone(mobile) {
            return Util.maskingTelephone(mobile);
        },

        getCardString(isCardString){
            return Util.getWelstoryCardString(isCardString);
        },
        getDeliveryDate(welstoryDeliveryDate){
            return TimeUtil.getDateFormattedString(welstoryDeliveryDate, "YYYY-MM-DD");
        },
        setRemoveTrailingNumber(name){
            return Util.removeTrailingNumber(name)
        },
        //송장 데이터 응답 시 보내는분 이름으로 정렬하여 프린터 함.
        sortingWelstoryDestination(destinations){
            let printDestinations = [];
            printDestinations = _.sortBy(this.destinations, "senderName");
            return printDestinations;
        }
    }
};