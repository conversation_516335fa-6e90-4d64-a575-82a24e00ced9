@charset "UTF-8";
@import url("/assets/common/css/font.css");

/*=================팝업 프레임==========================*/

.rider_popup_or02_window_w   {
	/*
	position: absolute;
	top: 50%;
	left: 50%;
	margin-top: -365px;
	margin-left: -312px;
	width: 624px;
	height: 750 px;
	background-color: #fff;
	border-radius: 3px;
		border: 1px solid red;
	 */
}

.rider_popup_or02_window_w .or02_window_close {
	position: absolute;
	top: 15px;
	right: 30px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	background-image: url("/assets/image/popup/bt_popup_close_n.svg");
}


/*=================상단 타이틀==========================*/

.rider_popup_or02_window_w  .or02_title {
	position: relative;
	width: 100%;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	float:left;
	color: #083338;
	/*	border: 1px solid red;*/
}

.rider_popup_or02_window_w  .or02_title_line {
	position: relative;
	height: 1px;
	background-color: #D5D5D5;
}


/*=================좌측 프로필==========================*/

.rider_popup_or02_window_w  .or02_driver_pic_area {
	position: relative;
	top: -190px;
	left: 20px;
	width: 120px;
	/*	border: 1px solid red;*/
}

.rider_popup_or02_window_w  .or02_driver_pic_area_expand {
	position: relative;
	top: -310px;
	left: 20px;
	width: 120px;
	/*	border: 1px solid red;*/
}

.rider_popup_or02_window_w  .or02_driver_pic {
	position: relative;
	top: 0px;
	left: 0px;
	width: 120px;
	height: 120px;
	background-image: url("/assets/image/popup/img_input_nophoto.svg");
	background-color: rgba(0, 0, 0, 0.3);
}

.rider_popup_or02_window_w  .or02_driver_pic_button {
	position: relative;
	top: 10px;
	left: 50%;
	margin-left: -40px;
	width: 79px;
	height: 19px;
	background-color: #fff;
	border: 1px solid #33485a;
	border-radius: 3px;
	outline-color: #33485a;
	outline: 0;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 12px;
	color: #33485A;
}


/*=================우측 리스트==========================*/

.rider_popup_or02_window_w  .or02_left_list_area {
	position: absolute;
	padding-top: 9px;
	left: 200px;
	width: 374px;
	height: 580px;
}

.rider_popup_or02_window_w  .or02_left_list_section1 {
	position: relative;
	width: 374px;
	height: 33px;
	margin-top: 10px;
}

.rider_popup_or02_window_w  .or02_left_list_section_title {
	position: relative;
	left: 0px;
	float:left;
	width: 80px;
	height: 23px;
	padding-top: 7px;
	padding-right: 5px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}

.rider_popup_or02_window_w  .or02_left_list_section1_1 {
	position: absolute;
	left: 0px;
	width: 185px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}

.rider_popup_or02_window_w  .or02_left_list_section1_2 {
	position: absolute;
	right: 0px;
	width: 185px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}

.rider_popup_or02_window_w  .or02_left_list_section2 {
	position: relative;
	top: 14px;
	width: 423px;
	height: 35px;
}

.rider_popup_or02_window_w  .or02_left_list_circle {
	position: absolute;
	top: 7px;
	right: 0px;
	width: 4px;
	height: 4px;
	border-radius: 2px;
	background-color: #FF5A00;
}

.rider_popup_or02_window_w  .or02_left_list_text1 {
	position: relative;
	top: 1px;
	right: 0px;
	width: 335px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border-radius: 2px;
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .or02_left_list_text_icon {
	position: relative;
	top: 1px;
	right: 0px;
	width: 300px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border-radius: 2px;
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .or02_left_list_text1_line {
	position: absolute;
	top: 32px;
	right: 0px;
	width: 300px;
	height: 1px;
	background-color: #D5D5D5;
}

.rider_popup_or02_window_w  .or02_left_list_select1 {
	position: relative;
	top: 1px;
	right: 0px;
	width: 120px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	border: 1px solid #D5D5D5;
	border-radius: 2px;
}

.rider_popup_or02_window_w  .or02_left_list_text2 {
	position: relative;
	top: 1px;
	width: 120px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border-radius: 2px;
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .or02_left_list_text2_line {
	position: absolute;
	top: 32px;
	right: 0px;
	width: 113px;
	height: 1px;
	background-color: #D5D5D5;
}

.rider_popup_or02_window_w  .or02_left_list_select2 {
	position: absolute;
	top: 1px;
	right: 10px;
	width: 103px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	border: 1px solid #D5D5D5;
	border-radius: 2px;
}

.rider_popup_or02_window_w  .or02_left_list_text2_label {
	position: absolute;
	top: 7px;
	right: 0px;
	color: #000;
	opacity: 0.5;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	z-index: 5;
	border: 0px solid;
}

.rider_popup_or02_window_w  .or02_left_list_text3_1 {
	position: absolute;
	top: 6px;
	left: 70px;
	width: 45px;
	height: 30px;
	outline: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 0px solid;
}

.rider_popup_or02_window_w  .or02_left_list_text3_2 {
	position: absolute;
	top: 1px;
	right: 10px;
	width: 45px;
	height: 30px;
	outline: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 0px solid;
}

.rider_popup_or02_window_w  .or02_left_list_text3_label {
	position: absolute;
	top: 3px;
	right: 60px;
	color: #000;
	opacity: 0.5;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 18px;
	color: #000;
	z-index: 5;
	border: 0px solid;
}

.rider_popup_or02_window_w  .or02_left_list_text3_label_w {
	position: relative;
	top: 3px;
	text-align: center;
	color: #000;
	opacity: 0.5;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 18px;
	color: #000;
	z-index: 5;
	border: 0px solid;
}

.rider_popup_or02_window_w  .or02_left_list_text4 {
	position: relative;
	right: 0px;
	width: 300px;
	height: 70px;
	overflow: hidden;
	border: 1px solid #D5D5D5;
	background-color: rgba(0, 0, 0, 0);
	border-radius: 2px;
}

.rider_popup_or02_window_w  .or02_left_list_text4_disabled {
	position: relative;
	right: 0px;
	width: 300px;
	height: 70px;
	overflow: hidden;
	border: 1px solid #D5D5D5;
	background-color: #e2e2e2;
	border-radius: 2px;
}


.rider_popup_or02_window_w  .or02_textarea {
	position: relative;
	right: 0px;
	width: 290px;
	height: 65px;
	padding: 5px;
	resize: none;
	outline: 0px;
	border: 0px solid;
	background-color: rgba(0, 0, 0, 0);
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
}

.rider_popup_or02_window_w .or02_button {
	position: relative;
	bottom: px;
	left: 65%;
	width: 260px;
	height: 45px;
	border: 0px;
	border-radius: 4px;
	outline: 0px;
	background-color: #FF5A00;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	color: #fff;
}

.rider_popup_or02_window_w  .or02_button:disabled {
	background-color: #C3C3C3;
}

.rider_popup_or02_window_w  .or02_add_search1 {
	position: relative;
	top: 10px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	background-image: url("/assets/image/popup/ic_input_search.svg");
}

/* 추가 비 활성화때 배경색 추가 */

.rider_popup_or02_window_w  :disabled{
	background-color: #e2e2e2;
}

/*=====================================================*/


/*=======================or17==========================*/


/*=====================================================*/


/*=================팝업 프레임==========================*/

.rider_popup_or02_window_w  .or17_window {
	position: absolute;
	top: 50%;
	left: 50%;
	margin-top: -299px;
	margin-left: -242px;
	width: 484px;
	height: 598px;
	background-color: #fff;
	border-radius: 3px;
	/*	border: 1px solid red;*/
}

.rider_popup_or02_window_w  .or17_window_close {
	position: relative;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	float:left;
	background-color: #fff;
	background-image: url("/assets/image/popup/bt_popup_close_n.svg");
}

.rider_popup_or02_window_w  .or17_window_edit {
	position: absolute;
	top: 15px;
	left: 30px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	background-image: url("/assets/image/popup/bt_popup_edit_n.svg");
}

.rider_popup_or02_window_w  .or17_window_fix {
	position: relative;
	left: 20px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	float:left;
	background-color: #fff;
	background-image: url("/assets/image/popup/bt_popup_edit_n.svg");
}

/*=================상단 타이틀==========================*/

.rider_popup_or02_window_w  .or17_title {
	position: relative;
	height: 41px;
	padding-top: 19px;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	color: #083338;
	/*	border: 1px solid red;*/
}

.rider_popup_or02_window_w  .or17_title_line {
	position: relative;
	height: 1px;
	background-color: #D5D5D5;
}

.rider_popup_or02_window_w  .or02_left_list_circle {
	position: absolute;
	top: 7px;
	right: 0px;
	width: 4px;
	height: 4px;
	border-radius: 2px;
	background-color: #FF5A00;
}

.rider_popup_or02_window_w  .guide {
	position: absolute;
	width: 50px;
	height: 50px;
	background-color: #D5D5D5;
}


/*=====================================================*/


/*=======================or27==========================*/


/*=====================================================*/

.rider_popup_or02_window_w   {
	position: absolute;
	top: 50%;
	left: 50%;
	margin-top: -338px;
	margin-left: -312px;
	width: 624px;
	height: 750px;
	background-color: #fff;
	border-radius: 3px;
	/*	border: 1px solid red;*/
}

.rider_popup_or02_window_w  table {
	border-collapse: collapse;
	width: 624px;
	height: 750px;
}

.rider_popup_or02_window_w  thead {
	display: block;
	width: 624px;
	height: 45px;
	align: center;
}

.rider_popup_or02_window_w  .thead_edit {
	width: 60px;
	height: 45px;
	align: center;
}

.rider_popup_or02_window_w  .thead_title {
	width: 504px;
	height: 45px;
	vertical-align: middle;
	text-align: center;
}

.rider_popup_or02_window_w  .thead_exit {
	width: 60px;
	height: 45px;
	align: center;
}

.rider_popup_or02_window_w   tbody {
	display: block;
	width: 624px;
	height: 610px;
	overflow: auto;
}
.rider_popup_or02_window_w    tfoot {
	display: block;
	width: 624px;
	bottom: 20px;
	height: 55px;
	overflow: auto;
}

.rider_popup_or02_window_w   .pm_tr_title {
	position: relative;
	top: 0px;
	height: 45px;
	/*background-color: #104348;*/
	border: 0px solid red;
}

.rider_popup_or02_window_w  .pm_tr_title_line {
	position: relative;
	top: 0px;
	height: 2px;
	/*background-color: #104348;*/
	border: 0px solid red;
}

.rider_popup_or02_window_w  .pm_tr {
	position: relative;
	top: 0px;
	height: 47px;
	/*background-color: #104348;*/
	border: 0px solid red;
}

.rider_popup_or02_window_w  .pm_tr_note {
	position: relative;
	top: 0px;
	height: 75px;
	/*background-color: #104348;*/
	border: 0px solid red;
}

.rider_popup_or02_window_w  .rider_td {
	position: relative;
	top: 0px;
	height: 41px;
	width : 132px;
	/*background-color: #104348;*/
	border: 0px solid red;
}

.rider_popup_or02_window_w  .or27_left_list_text1 {
	position: absolute;
	top: 6px;
	right: 0px;
	width: 295px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 0px solid;
}

.rider_popup_or02_window_w  .or27_left_list_text1_w {
	position: absolute;
	top: 6px;
	right: 0px;
	width: 295px;
	height: 20px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .or27_left_list_text_address_w {
	position: absolute;
	top: 6px;
	right: 27px;
	width: 270px;
	height: 20px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .or27_left_list_text2 {
	position: absolute;
	top: 6px;
	right: 10px;
	width: 100px;
	height: 30px;
	outline: 0px;
	text-align: left;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 0px solid;
}

.rider_popup_or02_window_w  .or27_left_list_text2_w {
	position: absolute;
	top: 6px;
	right: 15px;
	width: 95px;
	height: 20px;
	outline: 0px;
	text-align: left;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .or27_left_list_text3_1 {
	position: absolute;
	top: 6px;
	left: 70px;
	width: 45px;
	height: 30px;
	outline: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 0px solid;
}

.rider_popup_or02_window_w  .or27_left_list_text3_2 {
	position: absolute;
	top: 6px;
	right: 10px;
	width: 45px;
	height: 30px;
	outline: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 0px solid;
}

.rider_popup_or02_window_w  .or17_left_list_section1 {
	position: relative;
	width: 390px;
	height: 35px;
	margin-top: 13px;
	/*			border: 1px solid red;*/
}

.rider_popup_or02_window_w  .or17_left_list_section1_1_1 {
	position: absolute;
	left: 0px;
	width: 192px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}

.rider_popup_or02_window_w  .or17_left_list_text1_line {
	position: absolute;
	top: 32px;
	right: 0px;
	width: 100%;
	height: 1px;
	background-color: #D5D5D5;
}

.rider_popup_or02_window_w  .or17_left_list_text1_line1 {
	position: absolute;
	top: 32px;
	right: 0px;
	width: 122px;
	height: 1px;
	background-color: #D5D5D5;
}

.rider_popup_or02_window_w  .or17_left_list_section1_1_2 {
	position: relative;
	right: 0px;
	width: 50px;
	height: 30px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}

.rider_popup_or02_window_w  .or17_new_group_input_text1 {
	position: relative;
	top: 1px;
	right: 0px;
	width: 50px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 0px solid blue;
}

.rider_popup_or02_window_w  .or17_left_list_text1_line3 {
	position: relative;
	top: 32px;
	right: 0px;
	width: 190px;
	height: 1px;
	background-color: #D5D5D5;
}

.rider_popup_or02_window_w  .or17_left_list_section_title {
	position: relative;
	left: 0px;
	width: 55px;
	height: 23px;
	padding-top: 7px;
	padding-right: 5px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
}

.rider_popup_or02_window_w  .or17_left_list_section_title_disabled {
	position: absolute;
	left: 0px;
	width: 55px;
	height: 23px;
	padding-top: 7px;
	padding-right: 5px;
	font-weight: 500;
	text-align: right;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #e2e2e2;
}

.rider_popup_or02_window_w  .or17_left_list_text1 {
	position: relative;
	top: 1px;
	right: 0px;
	width: 335px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border-radius: 2px;
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .or17_left_list_text2 {
	position: relative;
	top: 1px;
	right: 0px;
	width: 120px;
	height: 30px;
	outline: 0px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border-radius: 2px;
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .or02_textarea {
	position: relative;
	right: 0px;
	width: 290px;
	height: 65px;
	padding: 5px;
	resize: none;
	outline: 0px;
	border: 0px solid;
	background-color: rgba(0, 0, 0, 0);
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
}

.rider_popup_or02_window_w  .or27_driver_pic_button {
	position: relative;
	top: 10px;
	left: 50%;
	margin-left: -40px;
	width: 79px;
	height: 19px;
	background-color: #fff;
	border: 1px solid #33485a;
	border-radius: 3px;
	outline-color: #33485a;
	outline: 0;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 12px;
	color: #33485A;
}

.rider_popup_or02_window_w  .or27_driver_send_button {
	position: relative;
	top: 20px;
	left: 50%;
	margin-left: -50px;
	width: 100px;
	height: 24px;
	padding-top: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
	color: #fff;
	outline: none;
	border: 0px solid;
	border-radius: 3px;
	background-color: #FF5A00;
}

.or27_driver_send_button:hover {
	background-color: #faa77b;
}

.or27_driver_send_button:disabled {
	color: #83999D;
	background-color: #4A6B71;
}

.rider_popup_or02_window_w  .or27_left_list_circle {
	position: relative;
	top: 7px;
	left: 7px;
	width: 4px;
	height: 4px;
	border-radius: 2px;
}

.rider_popup_or02_window_w  .or27_left_list_circle.connected {
	background-color: #6DE621;
}

.rider_popup_or02_window_w  .or27_left_list_circle.start_work {
	background-color: #00EEFF;
}

.rider_popup_or02_window_w  .or27_left_list_circle.end_work {
	background-color: #C6CACE;
}

.rider_popup_or02_window_w  .or27_left_list_circle.disconnected {
	background-color: #FF6077;
}

.rider_popup_or02_window_w  .or27_startpoint {
	position: relative;
	top: 10px;
	width: 24px;
	height: 24px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	background-image: url("/assets/image/popup/ic_popup_startad.svg");
}

.rider_popup_or02_window_w  .or27_endpoint {
	position: relative;
	top: 10px;
	width: 24px;
	height: 24px;
	border: 0px solid;
	outline: 0px;
	background-color: #fff;
	background-image: url("/assets/image/popup/ic_popup_endad.svg");
}

.rider_popup_or02_window_w  .driver_popup_select1 {
	position: absolute;
	top: 5px;
	right: 0px;
	width: 300px;
	height: 24px;
	padding-left: 10px;
	border-radius: 2px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .driver_popup_select2 {
	position: absolute;
	top: 5px;
	right: 12px;
	width: 100px;
	height: 24px;
	padding-left: 10px;
	border-radius: 2px;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .driver_popup_time1 {
	position: relative;
	width: 120px;
	height: 26px;
	outline: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .driver_popup_time2 {
	position: relative;
	width: 120px;
	height: 26px;
	outline: 0px;
	text-align: center;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #000;
	background-color: rgba(0, 0, 0, 0);
	border: 1px solid #D5D5D5;
}

.rider_popup_or02_window_w  .validate {
	font-weight: 400;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 10px;
    color: #FF5A00;
    position: relative;
    text-align: left;
    width: 140px;
}

.work_setting_on{
	position: relative;
	width: 54px;
	height: 34px;
	cursor:pointer;
	background-image: url("/assets/image/prod/toptoolbar/bt_se_toggle_on.png");
}

.work_setting_on:hover{
	background-image: url("/assets/image/prod/toptoolbar/bt_se_toggle_on.png");
}


.work_setting_off{
	position: relative;
	width: 54px;
	height: 34px;
	cursor:pointer;
	background-image: url("/assets/image/prod/toptoolbar/bt_se_toggle_off.png");
}

.work_setting_off:hover{
	background-image: url("/assets/image/prod/toptoolbar/bt_se_toggle_off.png");
}

.pointer-events-none {
	pointer-events: none;
}