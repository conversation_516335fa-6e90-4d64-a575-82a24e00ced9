<!DOCTYPE html>
<html lang="en" xmlns:sec="" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8">
    <link href="/css/owl.carousel.css" rel="stylesheet"/>
    <link href="/css/font-awesome.css" rel="stylesheet"/>

    <!-- Custom Style -->
    <link href="/css/main.css" rel="stylesheet"/>
    <!-- CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css">

    <!-- JS -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>
</head>
<body>
<div th:replace="bo/common/header :: header"/>

<nav aria-label="Page navigation example">
    <ul class="pagination justify-content-center">
        <li class="page-item">
            <a class="page-link" th:href="@{/bo/privacyRiderAccessRecord(page=${ start - 10 < 0 ?  0 : start - 10 })}" aria-label="First">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>

        <li class="page-item" th:class="${isFirst} ? 'disabled'">
            <a class="page-link" th:href="${isFirst} ? '#':@{/bo/privacyRiderAccessRecord(page=${number-1})}" aria-label="Previous">
                <span aria-hidden="true">&lt;</span>
            </a>
        </li>

        <th:block th:each="index: ${#numbers.sequence(start, last-1)}">
            <li th:class="${number == index} ? 'page-item active' : 'page-item' ">
                <a class="page-link" th:text="${index+1}" th:href="@{/bo/privacyRiderAccessRecord(page=${index})}"></a>
            </li>
        </th:block>

        <li class="page-item" th:class="${isLast} ? 'disabled'">
            <a class="page-link" th:href="${isLast} ? '#':@{/bo/privacyRiderAccessRecord(page=${number+1})}" aria-label="Next">
                <span aria-hidden="true">&gt;</span>
            </a>
        </li>

        <li class="page-item">
            <a class="page-link" th:href="@{/bo/privacyRiderAccessRecord(page=${ start + 10 > totalPages-1 ? totalPages-1 : start + 10  })}" aria-label="Last">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    </ul>
</nav>

<div class="container-fluid">
    <br>
    <br>
    <table class="table table-bordered"
           style="border: 1px solid red; height: 100px; margin: auto; text-align: center; font-size: 12px; font-family: 'Noto Sans KR', sans-serif;">
        <tr bgcolor="#e1e8d6">
            <td th:align="center">종류</td>

            <td th:align="center">기사아이디</td>
            <td th:align="center">기사</td>
            <td th:align="center">기사정보</td>

            <td th:align="center">날짜</td>
            <td th:align="center">회사코드</td>

            <td th:align="center">실행유저 </td>
            <td th:align="center">실행유저정보 </td>
        </tr>

        <!--				 th:onClick="'gogogo('+${user.userId}+')'"-->
        <tr th:each="content, state : ${contentList}">
            <td th:align="center" th:text="${content.type}"></td>

            <td th:align="center" th:text="${content.riderId}"></td>
            <td th:align="center" th:text="${content.rider}"></td>
            <td th:align="center" th:text="${content.riderInfo}"></td>

            <td th:align="center" th:text="${content.createAt}"></td>
            <td th:align="center" th:text="${content.companyCode}"></td>

            <td th:align="center" th:text="${content.runUserId}"></td>
            <td th:align="center" th:text="${content.runUserName}"></td>

        </tr>
    </table>
</div>
<br>


<!--<div th:replace="bo/common/footer :: footer"/>-->
</body>

<script>
</script>
</html>