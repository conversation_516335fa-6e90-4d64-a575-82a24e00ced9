<!DOCTYPE html>
<html lang="en" xmlns:sec="" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <title>Title</title>

    <link href="/css/owl.carousel.css" rel="stylesheet"/>
    <link href="/css/font-awesome.css" rel="stylesheet"/>

    <!-- Custom Style -->
    <link href="/css/main.css" rel="stylesheet"/>
    <!-- CSS --> 
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css">

    <!-- JS -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>
	<script type="text/javascript">

		function checkAll() {
			if ($('input[name=name]').val() == "") {
				alert("이름을 입력 입력하세요");
				document.organizationAdminRegister.name.focus();
				return false;
			}
			if ($('input[name=email]').val() == "") {
				alert("email을 입력 입력하세요");
				document.organizationAdminRegister.email.focus();
				return false;
			}
			if ($('input[name=password]').val() == "") {
				alert("password를 입력 입력하세요");
				document.organizationAdminRegister.password.focus();
				return false;
			}
			if ($('input[name=password_Confirm]').val() == "") {
				alert("password_Confirm를 입력 입력하세요");
				document.organizationAdminRegister.password_Confirm.focus();
				return false;
			}
			if($('input[name=password_Confirm]').val() != $('input[name=password]').val())
			{
				alert("password와 password_confirm이 서로 다르게 입력하셨습니다.");
				document.organizationAdminRegister.password.focus();
				return false;
			}
		}
	</script>

</head>
<body>

<div th:replace="bo/common/header :: header"></div>
<div class="container">

		<form th:action="@{/api/bo/organizationAdminRegister}" method="post" name="organizationAdminRegister" onsubmit="return checkAll()">
			<table class="table table-bordered" style="border: 1px solid red; height: 100px; margin: auto; text-align: center; font-size: 12px; font-family: 'Noto Sans KR', sans-serif;">
				<tr>
					<td><label class=" control-label">사업자 ID</label></td>
					<td><input type="text" class="form-control" name="OrganizationId"  th:value="${orgId}" readOnly></td>
				</tr>
				<tr>
					<td><label class=" control-label">사업자 고유코드</label></td>
					<td><input type="text" class="form-control" name="OrganizationId"  th:value="${projectName}" readOnly></td>
				</tr>
	            <tr>
	                <td><label class=" control-label">이름</label></td>
	                <td><input type="text" class="form-control" name="name"></td>
	            </tr>
	            <tr>
	                <td><label class=" control-label">이메일</label></td>
	                <td><input type="text" class="form-control" name="email" ></td>
	            </tr>
	            <tr>
	                <td><label class=" control-label">암호</label></td>
	                <td><input type="password" class="form-control" name="password" ></td>
	            </tr>
				<tr>
					<td><label class=" control-label">암호 재확인</label></td>
					<td><input type="password" class="form-control" name="password_Confirm" ></td>
				</tr>

			</table>

			<br>
			<button style="font-size: 12px; font-family: 'Noto Sans KR', sans-serif" class="btn btn-info mx-3" type="submit">저장</button>
		    <button style="font-size: 12px; font-family: 'Noto Sans KR', sans-serif" class="btn btn-warning mx-3" type="button" onclick="location.href='/bo/organizationList'">목록으로</button>
		</form>

</div>


<br>
<div th:replace="bo/common/footer :: footer"></div>

</body>

</html>