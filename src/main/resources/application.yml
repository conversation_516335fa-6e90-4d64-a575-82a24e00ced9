spring:
  main:
    allow-bean-definition-overriding: true

  application:
    name: tms-service

  task:
    # org.springframework.boot.autoconfigure.task.TaskSchedulingProperties.java
    scheduling:
      pool:
        # Maximum allowed number of threads. (Default: 1)
        size: 10
    # org.springframework.boot.autoconfigure.task.TaskExecutionProperties.java
    execution:
      pool:
        # Queue capacity. (Default: Integer.MAX_VALUE)
        queue-capacity: 0x7fffffff
        # Core number of threads. (Default: 8)
        core-size: 8
        # Maximum allowed number of threads. (Default: Integer.MAX_VALUE)
        max-size: 0x7fffffff
        # Whether core threads are allowed to time out. This enables dynamic growing and shrinking of the pool. (Default: true)
        allow-core-thread-timeout: true
        # Time limit for which threads may remain idle before being terminated. (Default: 60secs)
        keep-alive: 60s

  security:
    user:
      name: admin
      password: logisteq9090
    oauth2-client:
      app:
        client-id: aloa-app
        client-secret: aloaSecret7373!

  datasource:
    platform: mariadb
    driver-class-name: org.mariadb.jdbc.Driver
    testWhileIdle: true
    validationQuery: SELECT 1
    column:
      secret-key: none
    hikari:
      maxLifetime: 600000
      maximumPoolSize: 100
      data-source-properties:
        rewriteBatchedStatements: true
#      leak-detection-threshold: 2000
#      connection-timeout: 10000
    # https://velog.io/@wlsh44/Spring-%EB%8D%B0%EC%9D%B4%ED%84%B0%EB%B2%A0%EC%9D%B4%EC%8A%A4-%EC%B4%88%EA%B8%B0%ED%99%94
    initialization-mode: always
    data: classpath:sql/data-${spring.datasource.platform}.sql
    schema: classpath:sql/schema-${spring.datasource.platform}.sql
    custom-init: classpath:sql/custom-init-${spring.datasource.platform}.sql
    sql-script-encoding: utf-8
    separator: $$

  jpa:
    generate-ddl: true 
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        highlight_sql: false
        use_sql_comments: false
        session:
          events:
            log:
              LOG_QUERIES_SLOWER_THAN_MS: 3000
        jdbc:
          batch_size: 1000
        order_inserts: true
        order_updates: true
        generate_statistics: false
        dialect: org.hibernate.spatial.dialect.mysql.MySQL56SpatialDialect
    hibernate:
      ddl-auto: update

  mail:
    host: smtps.hiworks.com
    port: 465
    username: <EMAIL>
    password: logisteq9090!
    properties:
      mail:
        smtp:
          ssl:
            enable : true
            trust : smtps.hiworks.com
            checkserveridentity: true
          starttls:
            enabled: true
            required : true
          auth: true
          connectiontimeout: 5000
          timeout: 5000
          writetimeout: 5000
        debug: false

  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: -1    #무제한
      max-request-size: -1 #무제한

  thymeleaf:
    cache: false
    check-template: true
    check-template-location: true
    enabled: true
    encoding: UTF-8
    prefix : classpath:templates/
    suffix : .html

  messages:
    # message 파일의 기본 이름 (콤마로 구분해서 사용 가능) (default messages)
    # /resources/messages/messages_{언어}.properties를 바라보도록 설정
    basename: messages/messages
    # 인코딩 설정 (default UTF-8)
    encoding: UTF-8
    # 메세지 코드를 찾지 못했을 때 메세지 코드를 그대로 반환하는 설정 (default false)
    # true: 코드 값 그대로 반환
    # false: exception 발생
    use-code-as-default-message: true
    # 언어별 message 파일을 찾지 못한 경우에 대한 설정 (default true)
    # true: 시스템 설정 (I18nConfig)에서 설정한 언어에 맞는 message 파일 활용
    # false: default 파일 (messages.properties) 활용
    fallback-to-system-locale: true
    # 캐시 주기 설정 (default forever)
    cache-duration: 3600
    # messageFormat 항상 사용 여부 (arguments에 따른 format 처리 방식) (default false)
    always-use-message-format: false

  session:
    store-type: redis

  redis:
    session:
      host: 127.0.0.1
      port: 6379
    tracks:
      host: 127.0.0.1
      port: 6379
    lettuce:
      pool:
        max-active: 10
        max-idle: 10
        min-idle: 2

  web:
    version: v0.1.2

eureka:
  instance:
    leaseRenewalIntervalInSeconds: 10
    metadataMap:
      instanceId: ${vcap.application.instance_id:${spring.application.name}:${spring.application.instance_id:${server.port}}}
  client:
    enabled: true
    registerWithEureka: true
    fetchRegistry: true
    serviceUrl:
      defaultZone: ****************************************/eureka/

# org.springframework.boot.autoconfigure.web.ServerProperties.java
server:
  port: 5800
  compression:
    enabled: true
    min-response-size: 2KB
  error:
    include-stacktrace: never
  tomcat:
    use-relative-redirects: true
    # Maximum amount of worker threads. (Default: 200)
    max-threads: 200
    # Minimum amount of worker threads. (Default: 10)
    min-spare-threads: 10
    # Maximum number of connections that the server accepts and processes at anygiven time. (Default: 8192)
    max-connections: 8192
    # Maximum queue length for incoming connection requests when all possible request processing threads are in use. (Default: 100)
    accept-count: 100
    # Access log configuration.
    accesslog:
      # Enable tomcat access log (Default: false)
      enabled: false

feign:
  client:
    config:
      track-service:  # #3288 https://www.baeldung.com/feign-timeout
        connectTimeout: 2000
        readTimeout: 10000
      common-service: # #3697 https://www.baeldung.com/feign-timeout
        connectTimeout: 1800000   # #4748 2000 -> 1800_000(30분)
        readTimeout: 1800000      # #4748 30000 -> 1800_000(30분)
      privacy-record-service:
        connectTimeout: 10000
        readTimeout: 30000
      default:
        connectTimeout: 5000    # default: 10000
        readTimeout: 150000     # default: 60000
        errorDecoder: com.logisteq.common.feign.CustomErrorDecoder
        decode404: false
        loggerLevel: full       # none(default), basic, headers, full (https://www.baeldung.com/java-feign-logging)

# https://techblog.woowahan.com/9232/
management:
#  server:
#    port: -1 # If you do not want to expose endpoints over HTTP, you can set the management port to -1
  endpoints:
    enabled-by-default: false
    jmx:
      exposure:
        exclude: '*'
    web:
      base-path: /monitor
      discovery:
        enabled: false
      exposure:
        include: health
  endpoint:
    health:
      enabled: true
      show-details: never

springdoc:
  api-docs:
    path: /v3/api-docs
    groups:
      enabled: true
  swagger-ui:
    path: /index.html
    doc-expansion: none
    tags-sorter: alpha
    operations-sorter: alpha
    disable-swagger-default-url: true
    display-request-duration: true
  default-consumes-media-type:
    - application/json;charset=UTF-8
    - application/x-www-form-urlencoded
  default-produces-media-type:
    - application/json;charset=UTF-8
  packages-to-scan:
    - com.logisteq.tms.web.controller
    - com.logisteq.tms.delivery.controller
    - com.logisteq.tms.rider.controller
#    - com.logisteq.tms.openapi.controller
    - com.logisteq.tms.product.controller
    - com.logisteq.tms.project.controller
    - com.logisteq.tms.user.controller
    - com.logisteq.tms.vehicle.controller
    - com.logisteq.tms.incident.controller
    - com.logisteq.tms.app.controller
    - com.logisteq.tms.auth.controller
#    - com.logisteq.tms.external.kep.controller

mqtt:
  host: ws://localhost
  port: 8083
  path: /mqtt
  keepAliveSeconds: 60
  reconnectPeriodMS: 3000
  connectTimeoutSeconds: 30
  maxInflight: 200

my-auth-client-info:
  client-id: tms-web
  client-secret: 1234

sms:
  solapi:
    base-url: https://api.solapi.com/
    api-key: NCSTWTHJ212UYHES
    api-secret: DNN3M2NIWPXJUQDXLADF9DBGFSOLSCER

service-auth-secret: aloa1234567890abcdefghij # 서비스 인증 비밀 문자열

vehicle:
  modelFile: vehicle_model.json
  characteristicsFile: vehicle_characteristics.json

aws:
  access-key: ********************
  secret-key: lmsil8cxPuJkqfFd58XPLiX1Q8M0V5kecW/YT4CH
  bucket-name: tms-file
  private-bucket-name: tms-private-file
  root-folder-name: ${spring.profiles.active}

# https://lion-king.tistory.com/entry/SpringJPA-Spring-transaction-logging-jpa-entity-manager-logging
# https://github.com/inbonk/exception-handling-in-nested-transactional-classes/blob/master/src/main/resources/application.yml
# https://www.baeldung.com/hibernate-logging-levels
logging:
  level:
#    org.apache.http: debug
#    org.hibernate: debug
#    org.hibernate.engine.transaction.internal.TransactionImpl: debug
    org.hibernate.SQL: debug
    org.hibernate.SQL_SLOW: info
#    org.hibernate.type: trace
#    org.hibernate.type.descriptor.sql: trace
    org.hibernate.type.descriptor.sql.BasicBinder: off
#    org.springframework.transaction: debug
#    org.springframework.orm: debug
#    org.springframework.orm.jpa: debug
#    org.springframework.orm.jpa.JpaTransactionManager: debug
#    com.zaxxer.hikari.HikariConfig: debug
    org.springframework.jdbc.datasource.init.ScriptUtils: debug
    org.springframework.web.client.RestTemplate: debug
    org.springframework.web.HttpLogging: debug
    com.logisteq.common.feign: debug

log:
  entrypoint:
    enabled: true
    slowResponseInMs: 5000
  request-body:
    enabled: true
    max-length: 1000
  response-body:
    enabled: true
    max-length: 1000
  performance:
    enabled: true
    lowPerformanceInMs: 5000

log-history:
  user-access:
    enabled: true
  location-offer:
    enabled: true

scheduler:
  joins-delificate:
    enabled: true
    cron: '0 0/30 18-22 * * *'    # 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30
  rider-attendance:
    enabled: true
    fixed-rate: 60_000            # 매 1분마다
  project-auto-create:
    enabled: true
    cron: '0 0 9 * * *'           # 매일 9시에
  department-project-auto-create:
    enabled: true
    cron: '0 0 1 * * *'           # 매일 1시에
  notice:
    enabled: false
    cron: '0 * * * * *'           # 매분 0초에
  privacy-disposed:
    enabled: true
    cron: '0 0/10 1-8 * * *'      # 매일 01시~08시 사이 10분마다
    older-than-minutes: 129600    # 60분 * 24시 * 90일
    older-than-hours-from: 960    # 40일치까지 처리
  get-24h-order:
    enabled: true
    cron: '0 0/5 * * * *'    # 매 5분마다

event-listener:
  joins-delificate:
    enabled: true
  hmg:
    enabled: false
  glovis:
    enabled: false
  efoodist:
    enabled: true
  oliveyoung:
    enabled: true
  thehyundai:
    enabled: true

aloa:
  system:
    on-error:
      email-from: ${spring.mail.username}
      email-to: <EMAIL>
      email-subject-prefix: "[ALOA:${spring.profiles.active}] "
    privacy:
      hash-key: secret-logisteq-8768
      record-enabled: false
      vendor: aloa
      external:
        url: http://localhost:3001
    routing:
      in-parallel: true         # false이면 순차적으로 수행
      max-threads: 2            # 0이면 parallelStream에 그냥 맡김
  arch:
    proxy-server:
      url: #{null}
  policy:
    security:
      auth-code-expired-duration: 3m              # 인증코드 유효 기간 (3분)
      user-password-expired-duration: 90d         # 사용자 패스워드 변경 유효 기간 (90일)
      user-lock-login-fail-count: 5               # 사용자 계정 잠김까지의 로그인 실패 횟수 (5)
      user-lock-duration: 5m                     # 사용자 계정 잠김 기간 (60분 => 5분 )
      http-session-max-inactive-interval: 60m     # 비활성화 세션 타임아웃 (60분), 오동작한다면 server.servlet.session.timeout도 함께 설정해보자
  project:
    ondemand:
      max-delivery: 0
    joins:
      send-sms: false
    anonymous:
      disabled: true
  storage:
    upload:
      excel: true
  mqtt:
    enabled: true
  fcm:
    enabled: true
    topic-prefix-of-server: ${spring.profiles.active} # [a-zA-Z0-9-]로 구성해야 합니다.
