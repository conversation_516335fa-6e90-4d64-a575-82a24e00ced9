package com.logisteq.common.component.excel.exportdata;

import com.logisteq.common.component.excel.annotation.PxlColumn;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.*;
import java.util.Date;

@Setter
public final class ExportTest003Sheet1 {

    @NotNull(message = "String1이 비어있습니다.")
    @PxlColumn(name = "String1", exportSample = "문자열1")
    private String string1;

    @PxlColumn(name = "String2", exportSample = "문자열2")
    private String string2;

    @PxlColumn(name = "Date1", exportSample = "2020/01/02 03:04", exportPattern = "yyyy/MM/dd HH:mm")
    private Date date1;

    @PxlColumn(name = "LocalTime1", exportSample = "05:06", exportPattern = "HH:mm")
    private LocalTime localTime1;

    @PxlColumn(name = "LocalDate1", exportSample = "2020/07/08", exportPattern = "yyyy/MM/dd")
    private LocalDate localDate1;

    @PxlColumn(name = "LocalDateTime1", exportSample = "2020/09/10 11:12", exportPattern = "yyyy/MM/dd HH:mm")
    private LocalDateTime localDateTime1;

    @PxlColumn(name = "ZonedDateTime1", exportSample = "2020/09/10 11:12 KST", exportPattern = "yyyy/MM/dd HH:mm z")
    private ZonedDateTime zonedDateTime1;

    @PxlColumn(name = "OffsetTime1", exportSample = "05:06+09", exportPattern = "HH:mmx")
    private OffsetTime offsetTime1;

    @PxlColumn(name = "OffsetDateTime1", exportSample = "2020/09/10 11:12+09", exportPattern = "yyyy/MM/dd HH:mmx")
    private OffsetDateTime offsetDateTime1;

    @PxlColumn(name = "Duration1", exportSample = "PT20M", exportPattern = "HH:mm:ss")
    private Duration duration1;

    @PxlColumn(name = "Enum1", exportSample = "열거형이름1", exportEnumDropDownList = true)
    private ExportTest003Enum1 enum1;

    @PxlColumn(name = "Enum2", exportSample = "열거형이름2", exportEnumDropDownList = false)
    private ExportTest003Enum1 enum2;

    @PxlColumn(name = "Object1", exportSample = "1 객체이름1")
    private ExportTest003Object1 object1;

    @PxlColumn(name = "Object2", exportSample = "2 객체이름2")
    private ExportTest003Object2 object2;

}
