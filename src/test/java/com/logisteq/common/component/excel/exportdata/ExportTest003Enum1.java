package com.logisteq.common.component.excel.exportdata;

import com.logisteq.common.component.excel.annotation.PxlExportValue;
import com.logisteq.common.component.excel.annotation.PxlImportValue;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ExportTest003Enum1 {

    ENUM_NAME_1("열거형이름1"),
    ENUM_NAME_2("열거형이름2"),
    ENUM_NAME_3("열거형이름3"),
    ;

    private final String description;

    ExportTest003Enum1(final String description) {
        this.description = description;
    }

    private static final Map<String, ExportTest003Enum1> descriptionToEnum = Stream
            .of(values())
            .collect(Collectors.toMap(e -> StringUtils.deleteWhitespace(e.description), e -> e));

    @PxlImportValue
    public static ExportTest003Enum1 toPxlImportValue(final String str) {
        return descriptionToEnum.get(StringUtils.deleteWhitespace(str));
    }

    @PxlExportValue
    public String toPxlExportValue() {
        return description;
    }

    @Override
    public String toString() {
        return description;
    }

}
