package com.logisteq.common.component.excel.exportdata;

import com.logisteq.common.component.excel.annotation.PxlSheet;
import com.logisteq.common.component.excel.annotation.PxlWorkbookName;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.util.List;

@Getter
@Setter
public final class ExportTest007Workbook {

    @PxlWorkbookName
    private String workbookName;

    @Valid
    @PxlSheet(name = "시트")
    private List<ExportTest007SubSheet> sheet;

}
