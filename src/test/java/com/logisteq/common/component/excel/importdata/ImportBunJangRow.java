package com.logisteq.common.component.excel.importdata;

import com.logisteq.common.component.excel.annotation.PxlColumn;
import com.logisteq.common.component.excel.annotation.PxlRowIndex;
import lombok.Getter;

import java.time.LocalDate;

@Getter
public final class ImportBunJangRow {

    @PxlRowIndex
    private Integer rowIndex;

    @PxlColumn(name = "방문날짜")
    private LocalDate pickupDate;

    @PxlColumn(name = "방문시간")
    private String pickupTime;

    @PxlColumn(name = "연락처")
    private String phoneNumber;

    @PxlColumn(name = "우편번호")
    private String zipCode;

    @PxlColumn(name = "송장번호")
    private String invoiceNumber;

}
