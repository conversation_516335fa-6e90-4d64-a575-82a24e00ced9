package com.logisteq.common.util;

import org.junit.*;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.Assert.*;

public class RegExTests {

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
    }

    @AfterClass
    public static void tearDownAfterClass() throws Exception {
    }

    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void test01() {
        Pattern pattern = Pattern.compile("^(?=(?:[^\\d]*\\d){8,11}[^\\d]*$)[\\d\\s.+-]*$");
        assertFalse(pattern.matcher("").matches());
        assertTrue(pattern.matcher("202 555 0125").matches());
        assertFalse(pattern.matcher("551 0125").matches());
        assertTrue(pattern.matcher("5551 0125").matches());
        assertTrue(pattern.matcher("5551-0125").matches());
        assertTrue(pattern.matcher("0102259169-").matches());
        assertTrue(pattern.matcher(" 0102259169-").matches());
        assertTrue(pattern.matcher(" 0102259169-").matches());
        assertFalse(pattern.matcher("0102259169a").matches());
        System.out.println(" 0..1.0  -.2.2  5 . 9-..  16  .96.".replaceAll("[\\s.+-]", ""));
    }

}
