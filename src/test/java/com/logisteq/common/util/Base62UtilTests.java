package com.logisteq.common.util;

import org.junit.Test;

import java.util.Random;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @created 2021-10-15
 * @project tms-service
 */
public class Base62UtilTests {

    @Test
    public void testEncodeAndDecode() {
        final long value = 10_000_000_163L;
        testBase62(value);
    }

    @Test
    public void testRandomEncodeAndDecode() {

        for (int i = 0; i < 10000; i++) {
            final Random r = new Random();
            final long value = Math.abs(r.nextLong() % 10000000000L);
            testBase62(value);
        }
    }

    private void testBase62(final long value) {
        final String encodedValue = Base62Util.encode(value);
        final long decodedValue = Base62Util.decode(encodedValue);

        System.out.println("ORIGINAL: " + value + ", ENCODE : " + encodedValue + ", DECODE : " + decodedValue);
        assertEquals(value, decodedValue);
    }

}
