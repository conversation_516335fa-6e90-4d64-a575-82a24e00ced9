package com.logisteq.common.util;

import org.junit.*;

import javax.imageio.ImageIO;
import javax.imageio.spi.IIORegistry;
import javax.imageio.spi.ImageWriterSpi;
import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Locale;

/**
 * <AUTHOR>
 * @created 2021-01-27
 * @project tms-service
 */
public class BarcodeUtilTests {

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
    }

    @AfterClass
    public static void tearDownAfterClass() throws Exception {
    }

    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void testBarcode4J() throws Exception {

        final String contents = "H0123456789";
        final String fileFormatName = "png";

        {
            final BufferedImage barcodeImage = BarcodeUtil.getBarcode4JBarcode39Image(contents);
            final String filePath = getResourceFilePath("barcode/barcode4J_barcode39_01.png.png");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        {
            final BufferedImage barcodeImage = BarcodeUtil.getBarcode4JBarcode128Image(contents);
            final String filePath = getResourceFilePath("barcode/barcode4J_barcode128_01.png.png");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }
    }

    @Test
    public void testBarbecue() throws Exception {

        final String contents = "H0123456789";
        final String fileFormatName = "png";

        {
            final BufferedImage barcodeImage = BarcodeUtil.getBarbecueBarcode39Image(contents);
            final String filePath = getResourceFilePath("barcode/barbecue_barcode39_01.png");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        {
            final BufferedImage barcodeImage = BarcodeUtil.getBarbecueBarcode128Image(contents);
            final String filePath = getResourceFilePath("barcode/barbecue_barcode128_01.png");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }
    }

    @Test
    public void testVariousMethods() throws Exception {

        final String contents = "H0123456789";
        final String fileFormatName = "png";
        final BufferedImage barcodeImage = BarcodeUtil.getZXingQRcodeImage(contents);

        // BufferedImage -> File 생성
        {
            final String filePath = getResourceFilePath("barcode/zxing_qrcode_01_01.png");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        // BufferedOutputStream 생성
        {
            final String filePath = getResourceFilePath("barcode/zxing_qrcode_01_02.png");
            final OutputStream outputStream = new BufferedOutputStream(new FileOutputStream(filePath));
            ImageIO.write(barcodeImage, fileFormatName, outputStream);
        }

        /*
        {
            HttpServletResponse response;
            response.setHeader("Cache-control", "no-cache");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("content-type", MediaType.IMAGE_PNG_VALUE);
            response.setContentType(MediaType.IMAGE_PNG_VALUE);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setDateHeader("Expires", 0);
            ImageIO.write(barcodeImage, fileFormatName, response.getOutputStream());
        }
        */
    }

    @Test
    public void testQRCode() throws Exception {

        // 숫자 테스트
        {
            final String contents = "0123456789";
            final String fileFormatName = "png";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingQRcodeImage(contents);
            final String filePath = getResourceFilePath("barcode/zxing_qrcode_02_01.png");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        // 영문 테스트
        {
            final String contents = "English Test";
            final String fileFormatName = "jpg";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingQRcodeImage(contents);
            final String filePath = getResourceFilePath("barcode/zxing_qrcode_02_02.jpg");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        // 한글 테스트
        {
            final String contents = "한글 테스트";
            final String fileFormatName = "bmp";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingQRcodeImage(contents);
            final String filePath = getResourceFilePath("barcode/zxing_qrcode_02_03.bmp");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        // 긴 문자열 테스트 (제한이 없는 듯)
        {
            final String contents = "01234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                    "01234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                    "01234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                    "01234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                    "01234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                    "01234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                    "01234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                    "01234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                    "01234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                    "01234567890123456789012345678901234567890123456789012345678901234567890123456789";
            final String fileFormatName = "bmp";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingQRcodeImage(contents);
            final String filePath = getResourceFilePath("barcode/zxing_qrcode_02_04.bmp");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }
    }

    @Test
    public void testBarCode39() throws Exception {

        // 숫자 테스트
        {
            final String contents = "0123456789";
            final String fileFormatName = "png";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingBarcode39Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode39_01.png");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        // 영문 테스트
        {
            final String contents = "English Test";
            final String fileFormatName = "jpg";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingBarcode39Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode39_02.jpg");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }


        /* 한글은 지원되지 않음
        {
            final String contents = "한글 테스트";
            final String fileFormatName = "bmp";
            final BufferedImage barcodeImage = BarcodeUtil.getBarCode39Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode39_03.bmp");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }
        */

        // 지원하는 모든 문자 테스트
        {
            final String contents = "01234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%";
            final String fileFormatName = "bmp";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingBarcode39Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode39_04.bmp");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        // 긴 문자열 테스트 (80자까지만 지원)
        {
            final String contents = "01234567890123456789012345678901234567890123456789012345678901234567890123456789";
            final String fileFormatName = "bmp";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingBarcode39Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode39_05.bmp");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }
    }

    @Test
    public void testBarCode128() throws Exception {

        // 숫자 테스트
        {
            final String contents = "0123456789";
            final String fileFormatName = "png";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingBarcode128Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode128_01.png");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        // 영문 테스트
        {
            final String contents = "English Test";
            final String fileFormatName = "jpg";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingBarcode128Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode128_02.jpg");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        /* 한글은 지원되지 않음
        {
            final String contents = "한글 테스트";
            final String fileFormatName = "bmp";
            final BufferedImage barcodeImage = BarcodeUtil.getBarCode128Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode128_03.bmp");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }
        */

        // 지원하는 모든 문자 테스트 1
        {
            final String contents = " !\"#$%&\\'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            final String fileFormatName = "bmp";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingBarcode128Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode128_04.bmp");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        // 지원하는 모든 문자 테스트 2
        {
            final String contents = "[\\\\]^_`abcdefghijklmnopqrstuvwxyz{|}~";
            final String fileFormatName = "bmp";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingBarcode128Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode128_05.bmp");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }

        // 긴 문자열 테스트 (80자까지만 지원)
        {
            final String contents = "01234567890123456789012345678901234567890123456789012345678901234567890123456789";
            final String fileFormatName = "bmp";
            final BufferedImage barcodeImage = BarcodeUtil.getZXingBarcode128Image(contents);
            final String filePath = getResourceFilePath("barcode/zxing_barcode128_06.bmp");
            ImageIO.write(barcodeImage, fileFormatName, new File(filePath));
        }
    }

    @Test
    public void testSupportedImageFormats() {

        final IIORegistry registry = IIORegistry.getDefaultInstance();
        final Iterator<ImageWriterSpi> serviceProviders = registry.getServiceProviders(ImageWriterSpi.class, false);
        while (serviceProviders.hasNext()) {
            final ImageWriterSpi next = serviceProviders.next();
            System.out.printf("description: %-27s   format names: %s%n",
                    next.getDescription(Locale.ENGLISH),
                    Arrays.toString(next.getFormatNames())
            );
        }
    }

    private static String getResourceFilePath(final String filename) {
        return "src/test/resources/" + filename;
    }

}
