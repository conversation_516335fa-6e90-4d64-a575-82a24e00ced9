package com.logisteq.common.util;

import org.junit.*;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @created 2024-06-03
 * @project common-service
 */
public class NumberUtilTests {

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
    }

    @AfterClass
    public static void tearDownAfterClass() throws Exception {
    }

    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void testRoundDecimal() throws Exception {
        final double x = 127.13687997311354;
        final double y = 37.531689992174506;

        final double x1 = NumberUtil.roundDouble(x, 5);
        final double y1 = NumberUtil.roundDouble(y, 5);
        final double x2 = NumberUtil.roundDouble2(x, 5);
        final double y2 = NumberUtil.roundDouble2(y, 5);

        assertEquals("127.13688", String.valueOf(x1));
        assertEquals("37.53169", String.valueOf(y1));
        assertEquals("127.13688", String.valueOf(x2));
        assertEquals("37.53169", String.valueOf(y2));
    }

}
