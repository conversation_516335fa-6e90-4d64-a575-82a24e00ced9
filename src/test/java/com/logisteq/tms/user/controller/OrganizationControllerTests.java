package com.logisteq.tms.user.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logisteq.tms.address.dto.AddressDTO;
import com.logisteq.tms.user.dto.OrganizationDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultMatcher;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@ActiveProfiles("dev")
@Slf4j
public class OrganizationControllerTests {

	@Autowired
	private MockMvc mvc;
	
	@Before
	public void setUp() {
		assertNotNull(mvc);
	}
	
	@After
	public void tearDown() {
		//todo list
	}
	
	@Test
	public void getListOrganization() {
		Map<String, String> params = new HashMap<String, String>();
		params.put("userId", "1");
		
		String res = controllerTest(HttpMethod.GET, "/api/organization", params, null, status().isOk());
		
		System.out.println("응답:"+ res);
	}
	
	@Test
	public void createOrganization() {
		OrganizationDTO orgDTO = OrganizationDTO.builder()
													.organizationName( "오알지테스트1")
													.organizationOwner("김하하")
													.telephone("0215158888")
													.address(AddressDTO.builder()
																				.base("서울 중구 서소문로 100")
																				.detail("중앙일보빌딩")
																				.build())
													.build();
		try {
			System.out.println("orgDto=" + new ObjectMapper().writeValueAsString(orgDTO));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		String res = controllerTest(HttpMethod.POST, "/api/organization", null, orgDTO, status().isOk());
		
		System.out.println("응답:"+ res);
	}
	
	//===================================================================================================================
	private <T> T jsonStringToObject(String content, Class<T> valueType) throws JsonMappingException, JsonProcessingException {
		return new ObjectMapper().readValue(content ,  valueType);
	}
	
	@SuppressWarnings("deprecation")
	private String controllerTest(HttpMethod method, String url, Map<String, String> params, Object body, ResultMatcher resultMatcher) {
		MockHttpServletRequestBuilder requestBuilder = null;
		
		if (HttpMethod.GET.equals(method)) {
			requestBuilder = get(url);
		} else if (HttpMethod.POST.equals(method)) {
			requestBuilder = post(url);
		} else if (HttpMethod.PUT.equals(method)) {
			requestBuilder = put(url);
		} else if (HttpMethod.DELETE.equals(method)) {
			requestBuilder = delete(url);
		}
		
		requestBuilder.accept(MediaType.APPLICATION_JSON_UTF8);
		requestBuilder.contentType(MediaType.APPLICATION_JSON_UTF8);
		
		if (params != null) {
			for (Map.Entry<String, String> entry : params.entrySet()) {
			    requestBuilder.param(entry.getKey(), entry.getValue());
			}
		}
		
		if (body != null) {
			try {
				requestBuilder.content(new ObjectMapper().writeValueAsString(body));
			} catch (JsonProcessingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}	
		}

		MvcResult result = null;
		try {
			result = mvc.perform(requestBuilder)
											.andExpect(resultMatcher)
											.andDo(print())
											.andReturn();
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		if ( result != null && result.getResponse() != null ) {
			try {
				
				return result.getResponse().getContentAsString();
				
			} catch (UnsupportedEncodingException e) {			
				e.printStackTrace();
			}
		}
		return null;
	}
}