package com.logisteq.tms.rider.service;

import com.logisteq.common.exception.InvalidParameterException;
import com.logisteq.common.exception.ItemNotFoundException;
import com.logisteq.tms.rider.TestData;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.domain.RiderActivity;
import com.logisteq.tms.rider.repository.RiderActivityRepository;
import com.logisteq.tms.rider.repository.RiderRepository;
import com.logisteq.tms.rider.repository.RiderWorkStatusRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Rider 활동 상태 서비스 테스트
 * 
 * <AUTHOR>
 *
 */
@RunWith(SpringRunner.class)
@ActiveProfiles("test")
@SpringBootTest
@Slf4j
public class RiderActivityServiceTests {
	@Autowired
	private RiderActivityRepository riderActivityRepository;
	
	@Autowired
	private RiderActivityService riderActivityService;
	
	@Autowired
	private RiderWorkStatusRepository riderWorkStatusRepository;
	
	@Autowired
	private RiderRepository riderRepository;
	
	@Autowired
	private RiderService riderService;
	
	private Rider rider1;
	private Rider rider2;
	
	@Before
	public void setUp() throws Exception {
		assertNotNull(riderActivityRepository);
		assertNotNull(riderActivityService);
		assertNotNull(riderRepository);
		assertNotNull(riderService);
		assertNotNull(riderWorkStatusRepository);
		
		rider1 = TestData.createTestRider(TestData.TEST_DRIVER_NAME + "1", TestData.TEST_DRIVER_MOBILE);
		rider1 = riderService.registerRider(rider1.getEmail(), rider1.getPassword(), rider1.getName(), rider1.getMobile(), 1L, null, null, null, null);
		assertNotNull(rider1);
		rider1 = riderService.addProjectId(rider1.getId(), TestData.TEST_PROJECT_ID);
		assertNotNull(rider1);

		rider2 = TestData.createTestRider(TestData.TEST_DRIVER_NAME + "2", TestData.TEST_DRIVER_MOBILE.replace("1",  "2"));
		rider2 = riderService.registerRider(rider2.getEmail(), rider2.getPassword(), rider2.getName(), rider2.getMobile(), 1L, null, null, null, null);
		assertNotNull(rider2);
		rider2 = riderService.addProjectId(rider2.getId(), TestData.TEST_PROJECT_ID);
		assertNotNull(rider2);
	}
	
	
	@After
	public void tearDown() {
		deleteAll();
	}
	
	/**
	 * Rider 활동 상태 저장 테스트 (1)
	 * 
	 * @throws Exception
	 */
	@Test
	public void test_saveRiderActivity() throws Exception {
		RiderActivity riderActivity = TestData.createTestRiderInVehicleActivity(null);
		
		RiderActivity savedActivity = riderActivityService.saveRiderActivity(rider1.getId(), TestData.TEST_PROJECT_ID,
				riderActivity.getActivity().getShortName(), riderActivity.getTimestamp());
		
		assertNotNull(savedActivity);
		assertNotNull(savedActivity.getId());
		assertEquals(riderActivity.getTimestamp(), savedActivity.getTimestamp());
	}

	/**
	 * Rider 활동 상태 저장 테스트 (3)
	 * @throws Exception
	 */
	@Test(expected = InvalidParameterException.class)
	public void test_saveRiderActivity_Without_Rider() throws Exception {
		RiderActivity riderActivity =  TestData.createTestRiderInVehicleActivity(rider1.getId());
		
		riderActivityService.saveRiderActivity(null, TestData.TEST_PROJECT_ID,
				riderActivity.getActivity().getShortName(), riderActivity.getTimestamp());
	}
	
	/**
	 * Rider 활동 상태 저장 테스트 (4)
	 * @throws Exception
	 */
	@Test(expected = InvalidParameterException.class)
	public void test_saveRiderActivity_With_Invalid_Rider() throws Exception {
		RiderActivity riderActivity =  TestData.createTestRiderInVehicleActivity(rider1.getId());
		
		riderActivityService.saveRiderActivity(TestData.TEST_INVALID_ID, TestData.TEST_PROJECT_ID,
				riderActivity.getActivity().getShortName(), riderActivity.getTimestamp());
	}
	
	/**
	 * Rider 활동 상태 저장 테스트 (5)
	 * @throws Exception
	 */
	@Test(expected = InvalidParameterException.class)
	public void test_saveRiderActivity_Without_Activity() throws Exception {
		RiderActivity riderActivity =  TestData.createTestRiderInVehicleActivity(rider1.getId());
		
		riderActivityService.saveRiderActivity(rider1.getId(), TestData.TEST_PROJECT_ID,
				null, riderActivity.getTimestamp());
	}
	
	/**
	 * Rider 활동 상태 저장 테스트 (6)
	 * 
	 * @throws Exception
	 */
	@Test(expected = InvalidParameterException.class)
	public void test_saveRiderActivity_Without_Timestamp() throws Exception {
		RiderActivity riderActivity =  TestData.createTestRiderInVehicleActivity(rider1.getId());
		
		riderActivityService.saveRiderActivity(rider1.getId(), TestData.TEST_PROJECT_ID, riderActivity.getActivity().getShortName(), null);
	}
	
	/**
	 * Rider 활동 상태 저장 테스트 (7)
	 * 
	 * @throws Exception
	 */
	@Test(expected = InvalidParameterException.class)
	public void test_saveRiderActivity_With_Invalid_ProjectId() throws Exception {
		RiderActivity riderActivity =  TestData.createTestRiderInVehicleActivity(rider1.getId());
		riderActivityService.saveRiderActivity(rider1.getId(), TestData.TEST_INVALID_ID, riderActivity.getActivity().getShortName(), null);
	}
	
	/**
	 * Rider 활동 상태 저장 테스트 (8)
	 * 
	 * @throws Exception
	 */
	@Test(expected = InvalidParameterException.class)
	public void test_saveRiderActivity_Without_ProjectId() throws Exception {
		RiderActivity riderActivity =  TestData.createTestRiderInVehicleActivity(rider1.getId());
		riderActivityService.saveRiderActivity(rider1.getId(), null, riderActivity.getActivity().getShortName(), null);
	}
		
	/**
	 * Rider 활동 상태 조회 테스트 (2)
	 * @throws Exception
	 */
	@Test(expected = ItemNotFoundException.class)
	public void test_getRiderActivites_With_Invalid_Rider() throws Exception {
		riderActivityService.getRiderActivites(TestData.TEST_INVALID_ID, null, null, null, null);
	}
	
	/**
	 * Rider 활동 상태 조회 테스트 (3)
	 * @throws Exception
	 */
	@Test(expected = ItemNotFoundException.class)
	public void test_getRiderActivites_With_Invalid_ProjectId() throws Exception {
		riderActivityService.getRiderActivites(null, TestData.TEST_INVALID_ID, null, null, null);
	}
	
	private void deleteAll() {
		riderActivityRepository.deleteAll();
		riderWorkStatusRepository.deleteAll();
		riderRepository.deleteAll();
	}
}
