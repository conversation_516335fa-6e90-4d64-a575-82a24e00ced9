package com.logisteq.tms.incident;

import com.logisteq.tms.incident.domain.Incident;
import com.logisteq.tms.incident.domain.suppl.IncidentType;
import com.logisteq.tms.incident.dto.IncidentDTO;
import com.logisteq.tms.rider.domain.Rider;

import java.util.ArrayList;
import java.util.List;

public final class TestData {
	private TestData() {
		throw new AssertionError();
	}
	
	public static final String TEST_DRIVER_NAME			= "홍길동";
	public static final String TEST_DRIVER_PASSWORD		= "qwer1234";
	public static final String TEST_DRIVER_MOBILE		= "91011111111";
	
	public static final Long TEST_PROJECT_ID			= 1L;
	public static final Long TEST_VEHICLE_ID			= 1L;
	public static final Long TEST_DELIVERY_ID			= 1L;
	public static final Long TEST_INVALID_ID			= 999999L;
	
	
	public static IncidentDTO createTestRiderIncidentDTO(Rider rider) {
		return createTestRiderIncidentDTO(rider.getId(), TEST_PROJECT_ID, TEST_VEHICLE_ID, TEST_DELIVERY_ID, IncidentType.DELIVERY_DELAYED, 100, false);
	}

	public static IncidentDTO createTestRiderIncidentDTO(Long riderId, Long projectId, Long vehicleId, Long deliveryId, IncidentType incidentType, Integer angle, Boolean isExpressway) {
		return IncidentDTO.builder()
				.riderId(riderId)
				.projectId(projectId)
				.vehicleId(vehicleId)
				.deliveryId(deliveryId)
				.incidentType(incidentType)
				.detail("TEST ISSUE")
				.angle(angle)
				.build();
	}

	public static Incident createTestRiderIncident(Rider rider) {
		return createTestRiderIncident(rider, TEST_PROJECT_ID, TEST_VEHICLE_ID, TEST_DELIVERY_ID, IncidentType.DELIVERY_DELAYED, 100, false);
	}

	public static Incident createTestRiderIncident(Rider rider, Long projectId, Long vehicleId, Long deliveryId, IncidentType incidentType, Integer angle, Boolean isExpressway) {
		return Incident.builder()
				.rider(rider)
				.projectId(projectId)
				.vehicleId(vehicleId)
				.deliveryId(deliveryId)
				.incidentType(incidentType)
				.detail("TEST ISSUE")
				.angle(angle)
				.build();
	}
	
	public static Rider createTestRider(String name, String mobile) {
		return Rider.builder()
				.email(name + "@logisteq.com")
				.password(TEST_DRIVER_PASSWORD)
				.name(name)
				.mobile(mobile).build();
	}
	
	public static List<IncidentDTO> createTestRiderIncidentDTOList(Rider rider, int count) {
		IncidentDTO defaultIncident = TestData.createTestRiderIncidentDTO(rider);
		
		List<IncidentDTO> incidentList = new ArrayList<>();
		
		for (int i = 0; i < count; i++) {
			incidentList.add(TestData.createTestRiderIncidentDTO(rider.getId(),
					defaultIncident.getProjectId() + i, 
					defaultIncident.getVehicleId() + i,
					defaultIncident.getDeliveryId() + i,
					defaultIncident.getIncidentType(), defaultIncident.getAngle(), defaultIncident.getIsExpressway()));
		}
		
		return incidentList;
	}

	public static List<Incident> createTestRiderIncidentList(Rider rider, int count) {
		Incident defaultIncident = TestData.createTestRiderIncident(rider);

		List<Incident> incidentList = new ArrayList<>();

		for (int i = 0; i < count; i++) {
			incidentList.add(TestData.createTestRiderIncident(rider,
					defaultIncident.getProjectId() + i,
					defaultIncident.getVehicleId() + i,
					defaultIncident.getDeliveryId() + i,
					defaultIncident.getIncidentType(), defaultIncident.getAngle(), defaultIncident.getIsExpressway()));
		}

		return incidentList;
	}
}
