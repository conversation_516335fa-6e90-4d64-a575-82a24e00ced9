package com.logisteq.tms.web.controller;

import okhttp3.*;
import org.junit.Test;

public class ApiTests {
    @Test
    public void ebayTest01(){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\r\n    \"data\": [{\r\n        \"companyId\": \"000001\",\r\n        \"companySecretKey\": \"7634c0dc92e11b2f5766f357553c5e70d1ed3484de610bfa0608119294c2ccfb\",\r\n        \"invoiceNumber\": \"123456787892\",\r\n        \"customId\": \"000001\",\r\n        \"receptionDate\": \"********\",\r\n        \"customUseNo\": \"000001\",\r\n        \"receptionDivision\": \"01\",\r\n        \"workDivisonCode\": \"01\",\r\n        \"requestDivisonCode\": \"01\",\r\n        \"multiPackingKey\": \"\",\r\n        \"multiPackingSequence\": 1,\r\n        \"calculateDivisionCode\": \"01\",\r\n        \"fareDivisionCode\": \"03\",\r\n        \"contractItemCode\": \"01\",\r\n        \"boxTypeCode\": \"01\",\r\n        \"boxQty\": 1,\r\n        \"fare\": 0,\r\n        \"customManageAccountCode\": \"000001\",\r\n        \"senderName\": \"스마트배송물류센터\",\r\n        \"senderTelephoneNo1\": \"031\",\r\n        \"senderTelephoneNo2\": \"1234\",\r\n        \"senderTelephoneNo3\": \"5678\",\r\n        \"senderCellphoneNo1\": \"\",\r\n        \"senderCellphoneNo2\": \"\",\r\n        \"senderCellphoneNo3\": \"\",\r\n        \"senderZipCode\": \"17180\",\r\n        \"senderAddress\": \"경기도 용인시 처인구\",\r\n        \"senderAddressDetail\": \"백암면 죽양대로 904번길 46\",\r\n        \"receiverName\": \"홍길동\",\r\n        \"receiverTelephoneNo1\": \"031\",\r\n        \"receiverTelephoneNo2\": \"1234\",\r\n        \"receiverTelephoneNo3\": \"5678\",\r\n        \"receiverCellphoneNo1\": \"\",\r\n        \"receiverCellphoneNo2\": \"\",\r\n        \"receiverCellphoneNo3\": \"\",\r\n        \"receiverZipCode\": \"17180\",\r\n        \"receiverAddress\": \"경기도 용인시 처인구\",\r\n        \"receiverAddressDetail\": \"백암면 죽양대로 904번길 46\",\r\n        \"ordererName\": \"홍길동\",\r\n        \"ordererTelephoneNo1\": \"031\",\r\n        \"ordererTelephoneNo2\": \"1234\",\r\n        \"ordererTelephoneNo3\": \"5678\",\r\n        \"ordererZipCode\": \"17180\",\r\n        \"ordererAddress\": \"경기도 용인시 처인구\",\r\n        \"ordererAddressDetail\": \"백암면 죽양대로 904번길 46\",\r\n        \"OriginalInvoiceNumber\": \"\",\r\n        \"printStatus\": \"02\",\r\n        \"articleAmount\": 0,\r\n        \"remark1\": \"비고1\",\r\n        \"remark2\": \"비고2\",\r\n        \"remark3\": \"비고3\",\r\n        \"codeYesNo\": \"N\",\r\n        \"skuDescription\": \"상품1 1,상품2 2,상품3 3\",\r\n        \"skuQty\": \"0\",\r\n        \"etc1\": \"\",\r\n        \"etc2\": \"\",\r\n        \"deliveryDivision\": \"01\"\r\n    }]\r\n}");
        Request request = new Request.Builder()
                .url("https://eBay.logisteq.com/openapi/glovis/order-invoice-info")
                .method("POST", body)
                .addHeader("Authorization", "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            Response response = client.newCall(request).execute();
            ResponseBody responseBody = response.body();
            System.out.println("Response:" + responseBody.string());
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
