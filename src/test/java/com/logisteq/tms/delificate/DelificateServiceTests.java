package com.logisteq.tms.delificate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logisteq.common.util.Aes256Util;
import com.logisteq.tms.address.domain.Address;
import com.logisteq.tms.address.repository.AddressRepository;
import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.Receiver;
import com.logisteq.tms.delivery.repository.DeliveryRepository;
import com.logisteq.tms.delivery.repository.ReceiverRepository;
import com.logisteq.tms.external.joins.constant.DelificateConstant;
import com.logisteq.tms.external.joins.domain.Circulation;
import com.logisteq.tms.external.joins.domain.Delificate;
import com.logisteq.tms.external.joins.dto.DeliveryGoingRequestDTO;
import com.logisteq.tms.external.joins.dto.DeliveryGoingResponseDTO;
import com.logisteq.tms.external.joins.repository.CirculationRepository;
import com.logisteq.tms.external.joins.service.DelificateService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;
import java.util.Objects;

@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
@SpringBootTest
public class DelificateServiceTests {

    @Autowired
    private DelificateService delificateService;
    @Autowired
    private DeliveryRepository deliveryRepository;
    @Autowired
    private AddressRepository addressRepository;
    @Autowired
    private ReceiverRepository receiverRepository;
    @Autowired
    private CirculationRepository circulationRepository;

    @Test
    @Transactional
    public void testDeliveryGoingAPI() {
        final Circulation circulation = circulationRepository.findById(71723L).orElse(null);
        final DeliveryGoingRequestDTO deliveryGoingRequestDTO = DeliveryGoingRequestDTO.of(circulation);

        final HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(DelificateConstant.JOINS_CONNECT_TIMEOUT);
        factory.setReadTimeout(DelificateConstant.JOINS_READ_TIMEOUT);

        final RestTemplate restTemplate = new RestTemplate(factory);
        restTemplate.setErrorHandler(new DefaultResponseErrorHandler() {
            @Override
            public boolean hasError(HttpStatus statusCode) {
                return false;
            }
        });

        final URI uri = UriComponentsBuilder.newInstance()
                .scheme(DelificateConstant.JOINS_SCHEME)
                .host(DelificateConstant.JOINS_PROD_HOST + DelificateConstant.JOINS_PROD_DELIERY_GOING_API_URL)
                .build()
                .toUri();

        final HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        final HttpEntity<DeliveryGoingRequestDTO> requestEntity = new HttpEntity<>(deliveryGoingRequestDTO, httpHeaders);

        final ResponseEntity<DeliveryGoingResponseDTO> response = restTemplate.exchange(
                uri,
                HttpMethod.POST,
                requestEntity,
                DeliveryGoingResponseDTO.class);

        final HttpStatus httpStatus = response.getStatusCode();
        if (!httpStatus.is2xxSuccessful()) {
            final DeliveryGoingResponseDTO deliveryGoingResponseDTO = response.getBody();
            if (Objects.nonNull(deliveryGoingResponseDTO)) {
                System.out.println(httpStatus + ": " + deliveryGoingResponseDTO.getMessage());
            } else {
                System.out.println(httpStatus);
            }
        }
    }

    /**
     * joins의 raw data를 가져와서 송달증 및 프로젝트(rider, delivery, vehicle, group등)생성하는 배치 test
     */
/*
    @Test
    public void joinsInBatch() {
        StopWatch sw = new StopWatch();
        sw.start();
        try {
            delificateService.batchJoinsProjectTest(null,null,null,null );
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        sw.stop();
        System.out.println("토탈시간:" + sw.getTotalTimeMillis() + "ms.");
    }

*/
    @Test
    public void delivery중복없이한개찾기() {
        Long projectId = 6L;
        Long riderId = 2L;
//		List<Delivery> deliveryList = deliveryRepository.findByProjectIdAndAllocationsRiderId( projectId,  riderId);
//		
//		for (Delivery d: deliveryList) {
//			System.out.println("" + d.getId() + ", " + d.getAllocations().get(0).getRiderId());
//		}

        Address address = Address.of("강원도 평창군 평창읍 하리 101-7번지", null);
        Address addressDomain = addressRepository.findByBaseAndDetail(address.getBase(), null);
        String centerName = "평창"; //일단 센터네임은 중복이 없어서 이걸(receiver)로 진행
        String centerOwner = "평창";
        String centerTel = null;
        Receiver rcv = receiverRepository.findByReceiverNameAndReceiverOwnerAndReceiverPhoneNumber(centerName, centerOwner, centerTel);

//		List<Delivery> deliveryList = deliveryRepository.findByProjectIdAndAllocationsRiderIdAndDetailDestinationAddressAndDetailReceiver( projectId,riderId, addressDomain);
        Delivery d = deliveryRepository.findByProjectIdAndAllocationsRiderIdAndDetailReceiverAndDetailDestinationAddress(projectId, riderId, rcv, addressDomain);

        System.out.println("~~~~~~~");
        System.out.println("" + d.getId() + ", " + d.getAllocations().get(0).getRiderId());

    }

    @Test
    public void getSongdal() {
        List<Delificate> delificateList = delificateService.getJoinsProject(1L, 2L);
        try {
            for (Delificate d : delificateList) {
                String result = new ObjectMapper().writeValueAsString(d);

                System.out.println("결과:: " + result);
            }
        } catch (JsonProcessingException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }

    @Test
    public void realMeterSecondsListener() {
        Delivery d = deliveryRepository.findById(1L).get();
        d.getAllocations().get(0).setRealSeconds(100L);
        d.getAllocations().get(0).setRealMeters(100.0);
        deliveryRepository.save(d);
    }

    @Test
    public void stringExtractTest() {
        String str = "20200921__{{속초}}";
        String COURSE_PREFIX = "{{";
        String COURSE_POSTFIX = "}}";

        String coursName = subStringBetween(str, COURSE_PREFIX, COURSE_POSTFIX);

        System.out.println("코스명 :: " + coursName);
    }

    @Test
    public void stringExtractTest2() {
        String callbackUri = "http://openapi.its.go.kr/api/NTrafficInfo";

        String tokenizer = "://"; //자르고자하는 문자열...
        int idx = callbackUri.indexOf(tokenizer);
        String scheme = callbackUri.substring(0, idx);
        String url = callbackUri.substring(idx + tokenizer.length());


        System.out.println("scheme:   " + scheme);
        System.out.println("url:   " + url);
    }

    @Test
    public void callbackUriProcessor() {

//		customerService.callbackUriProcessor(1L, "id_01", DeliveryStatus.GOING);

        for (int i = 0; i < 100000000; i++) {
//			System.out.println();
        }

        System.out.println("terminated......");
    }

    @Test
    public void decrytTest() {
        System.out.println("@~~ 이름: " + decryptAES256("EZw17GNfqfvhaq0E+27EEg=="));
        System.out.println("@~~ 전번: " + decryptAES256("U6aa6piF6BJYdzTBOojL5g=="));
        System.out.println("@~~ 이름: " + decryptAES256("bYGlOzO7ACbQ4/w6VwlLcA=="));
    }

    @Test
    public void encryptTest() throws Exception {

        System.out.println("@~~ 정은정: " + encryptAES256("정은정"));
        System.out.println("@~~ 010-2501-2143: " + encryptAES256("010-2501-2143"));

        System.out.println("@~~ 이름: " + decryptAES256("jxJG1MryK59eHAvQU1O4tw=="));
        System.out.println("@~~ 전번: " + decryptAES256("sySfdA+IPc0P6i4PaKpuOw=="));
    }

    private String decryptAES256(String encrypt) {
        try {
            return Aes256Util.decrypt(DelificateConstant.JOINS_AES256_KEY, encrypt);
        } catch (Exception e) {
            return null;
        }
    }

    private String encryptAES256(String str) {
        try {
            return Aes256Util.encrypt(DelificateConstant.JOINS_AES256_KEY, str);
        } catch (Exception e) {
            return null;
        }
    }

    @Test
    public void 자리수맞추기() {
        int courSeq = 7854;
        int relaySeq = 3;

        String resultGroupingKey = String.format("%04d%04d", courSeq, relaySeq);

        Integer groupingKey = Integer.parseInt(resultGroupingKey);
        System.out.println("groupingKey = " + groupingKey);
    }

    /**
     * open과 close문자열 사이의 문자를 잘라서 리턴
     *
     * @param str
     * @param open  시작문자열
     * @param close 종료문자열
     * @return
     */
    private String subStringBetween(String str, String open, String close) {
        if (str == null || open == null || close == null) {
            return null;
        }
        int start = str.indexOf(open);
        if (start != -1) {
            int end = str.indexOf(close, start + open.length());
            if (end != -1) {
                return str.substring(start + open.length(), end);
            }
        }
        return null;
    }

}
